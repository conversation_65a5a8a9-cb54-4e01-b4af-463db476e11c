<import src="../template/backTop/backTop.wxml" />
<import src="../template/swiper/swiper.wxml" />
<import src="../template/search-card/search-card.wxml" />
<import src="../template/goods-item/groom/groom.wxml" />
<import src="../template/goods-item/meal-card/meal-card.wxml" />
<import src="../template/shop-card/shop-card.wxml" />
<import src="../template/loading/loading.wxml" />
<import src="../template/goods-item/time-limit-buy/time-limit-buy.wxml" />
<template is="loading" wx:if="{{is_loading}}"></template>
<view class="page">
  <!-- <image src='{{imgUri}}/all/层级商品/实物/手压泵/分类图.png'></image>
  <view style='background:url("{{imgUri}}/all/层级商品/实物/手压泵/分类图.png");width:100rpx;height:100rpx;'></view> -->
  <!-- 搜索框+地区定位 -->
  <!-- <view class='home-top'>
    <template is="search-header" data="{{location,locationName, backColor,imgUri}}" />
  </view> -->
  <!-- add by yanxingwang -->
  <view style="height:50rpx"></view>
  <!-- 原提醒的位置 -->
  <view class="top-bg"></view>
  <!-- 首页轮播图 banner-->
  <!-- <template is="swiper" data="{{imgUrls,indicatorDots,autoplay,interval,duration}}"></template> -->
  <!-- 店铺信息 shop -->
  <view class='shop_index'>
    <template is="shop-card" data="{{shop:shop.szmCStore,distance,is_kefu,imgUri,phone: shop.phone,shopTips: shopTips}}"></template>
  </view>
  <view class='home-top-index'>
    <template is="search-card" data="{{location,backColor}}" />
  </view>
  <swiper class='banner swiper-box' indicator-dots="{{indicatorDots}}" autoplay="{{autoplay}}" interval="{{interval}}" duration="{{duration}}" bindchange="swiperChange" circular indicator-active-color="#fff">
    <block wx:for="{{imgUrls2}}" wx:key="key" wx:for-index="index">
      <swiper-item>
        <view wx:if="{{item.type == 2 && item.id != 13}}" style="position: absolute;top: 5rpx;width: 250rpx;left:220rpx;height: 46rpx;background: rgba(0,0,0,0.7);border-radius: 23rpx;font-family: PingFangSC;font-weight: 600;font-size: 23rpx;color: #FFE6A9;line-height: 46rpx;letter-spacing: 2px;text-align: center;">
        <view >返现抵扣金 {{jifen}}</view>
        </view>
        <image data-uri="{{item.r1}}" data-id='{{item.r2}}' data-realid='{{item.id}}' data-type='{{item.type}}' catchtap='gotoActivityPage' src="{{item.path}}" class="slide-image" />
      </swiper-item>
    </block>
  </swiper>
  <!-- add by yanxingwang -->
  <view class="page__bd">
    <!-- 顶部分类 item-list   wx:if="{{bigClassify.length == 4 || bigClassify.length == 9}}"-->
    <view class='content item-list df'>
      <view wx:if="{{shop && shop.szmCStore && shop.szmCStore.shuipiao}}" class='w20 ' bindtap='goShuipiao'>
        <view class='img-box'>
          <image class='img ' src='{{imgUri}}/szmc/images/shuipiao.png'></image>
        </view>
        <view class='text'>水票专区</view>
      </view>
      <!-- <view class='w20 ' bindtap='goBuck'> -->
      <view  wx:if="{{shop && shop.szmCStore && shop.szmCStore.yatong}}" class='w20 ' bindtap='goBuck' style="position: relative;">
        <view class='redNumTips' wx:if="{{bucketApplyCount}}">
          <text>{{bucketApplyCount}}</text>
        </view>
        <view class='img-box'>
          <image class='img ' src='{{imgUri}}/classify/tzs.png'></image>
        </view>
        <view class='text'>押桶</view>
      </view>
      <view class='w20 ' bindtap='goCompany'>
        <view class='img-box'>
          <image class='img ' src='{{imgUri}}/classify/gongsidingshui.png'></image>
        </view>
        <view class='text'>公司订水</view>
      </view>
      <view class='w20 ' bindtap='goTicket'  style="position: relative;">
        <view class='redNumTips' wx:if="{{ticketCount}}">
          <text>{{ticketCount}}</text>
        </view>
        <view class='img-box'>
          <image class='img ' src='{{imgUri}}/classify/youhuiquan.png'></image>
        </view>
        <view class='text'>优惠券</view>
      </view>
      <view class='w20 ' bindtap='goJianshe'>
        <view class='img-box'>
          <image class='img ' src='{{imgUri}}/classify/gd.png'></image>
        </view>
        <view class='text'>更多展示</view>
      </view>
      <!-- <view class='w20 ' wx:for="{{bigClassify}}" wx:key="key" data-id="{{item.productClassifyId}}" data-cangoto="{{item.canGotoNext}}" bindtap='goClassfiy'>
        <view class='img-box' wx:if="{{item.r2}}">
          <image class='img ' src='{{item.r2}}?v={{imgV}}'></image>
        </view>
        <view class='img-box' wx:else>
          <image class='img ' src='{{imgUri}}/images/zidingyi.png'></image>
        </view>
        <view class='text'>{{item.productClassifyName}}</view>
      </view> -->
    </view>
    <!-- 店铺头条 guanggao-->
    <!-- <view class='content guanggao df' style="padding:20rpx 15rpx;margin: 30rpx 30rpx 0 30rpx;border-radius: 16rpx;" wx:if="{{shopTips.length > 0}}">
      <view class='df guanggao toutiao' style='width:100%;'>
        <image src="{{imgUri}}/images/shop_first_tip.png" style="width:114rpx;height:40rpx;margin-right:15rpx;"></image>
        <view class="line-toutiao"></view>
        <swiper class='color-grey' style='height:36rpx;width:calc(100% - 114rpx);' bindchange="bindchange" autoplay="{{autoplay}}" vertical circular>
          <block>
            <swiper-item wx:for="{{shopTips}}" wx:key="key" data-id="{{item.headlineId}}" catchtap="goShopTips" class=" flex align-items-center">
              <text class="adtag font-size-26" wx:if="{{index == 0}}">最新</text>
              <text class="adtag font-size-26" wx:if="{{index == 1}}">热门</text>
              <text class="ellipsis font-size-26" style="color:#333333">{{item.headlineContent}}</text>
            </swiper-item>
          </block>
        </swiper>
      </view>
    </view> -->
    <!-- 广告区域 -->
    <view>
      <view style="display: flex;align-items: center;gap: 10rpx;flex-wrap: wrap;margin:0 30rpx;margin-top: 20rpx;">
        <image bindtap="goKepu" wx:if="{{shop && shop.szmCStore.kepu}}" style="width: 340rpx;" src="https://waterstation.com.cn:10000/szm/upload/20250730091229107.png" mode="widthFix"/>
        <!-- <image bindtap="goKepu" wx:if="{{shop && shop.szmCStore.kepu}}" style="width: 340rpx;" src="https://waterstation.com.cn:10000/szm/szmc/kepu.png" mode="widthFix"/> -->
        <image bindtap="goFanxian" wx:if="{{shop && shop.szmCStore.fanxian}}" style="width: 340rpx;" src="https://waterstation.com.cn:10000/szm/szmc/fanxian.png" mode="widthFix"/>
        <image bindtap="goTuanzhang" wx:if="{{shop && shop.szmCStore.tuanzhang}}" style="width: 340rpx;" src="https://waterstation.com.cn:10000/szm/szmc/tuanzhang.png" mode="widthFix"/>
      </view>
    </view>
    <view wx:if="{{showActivity}}">
      <block wx:if="{{announcementText.length > 0 && shop.nearbyShops === 0}}">
        <swiper class="index-tip" autoplay="{{autoplay}}" vertical circular>
          <swiper-item class="flex align-items-center" wx:for="{{announcementText}}" wx:key="key">
            <image style="width:48rpx;height:48rpx;" src="{{imgUri}}/images/bell_message.png" />
            <text catchtap="gotoMyMsg" data-id="{{item.myMsgId}}" class="ellipsis" style="color:#FFFFFF;font-size:28rpx;width:calc(100% - 100rpx)">{{item.msgInfo}}</text>
            <image catchtap="changeMyNews" data-id="{{item.myMsgId}}" style="width:48rpx;height:48rpx;" src="{{imgUri}}/images/tipClose.png" />
          </swiper-item>
        </swiper>
      </block>
    </view>
    <!-- <view class="flex align-items-center index-tip" style="display:{{showTip?'':'none'}};">
      <image style="width:48rpx;height:48rpx;" src="{{imgUri}}/images/bell_message.png" />
      <text catchtap="gotoMyMsg" class="ellipsis" style="color:#FFFFFF;font-size:28rpx;width:calc(100% - 100rpx)">{{announcementText}}</text>
      <image catchtap="changeMyNews" style="width:48rpx;height:48rpx;" src="{{imgUri}}/images/tipClose.png" />
    </view> -->
    <view wx:if="{{shop.nearbyShops === 1}}" class="back-select-shop">
      <view class="flex align-items-center justify-content-center back-shop-btn" bindtap="backSelectShop">
        <text class="back-shop-text">重新选择附近的店铺</text>
        <image class="back-shop-icon" src="{{imgUri}}/images/right.png?v={{imgV}}" alt=""></image>
      </view>
    </view>
    <!-- 购买水票，押桶按钮-->
    <!-- <view wx:if="{{isShowGMSP && homeDiscounts.length > 0}}" class="flex padding-tb-20 justify-content-between">
      <image catchtap="goShuipiao" src="{{imgUri}}/images/buy_ticket_long.png?v={{imgV}}" style="width:100vw;height:188rpx;"></image>
    </view> -->
    <view style="margin: 20rpx 30rpx;">
      <van-tabs id="tabs" active="{{ active }}" ellipsis="{{false}}" bind:change="onChangeTab">
        <van-tab wx:for="{{bigClassify}}" name="{{index}}" title="{{item.productClassifyName}}" desc="{{item.biref ? item.biref : ''}}">
        </van-tab>
      </van-tabs>
    </view>
    <!-- 推荐商品 groom -->
    <view class='content groom content_shop_list' wx:if="{{groom.length > 0 }}">
      <!-- <view wx:if="{{!indexClass.r4 ||indexClass.r4 != 6}}" class='item-box-index flex align-items-center justify-content-between'> -->
      <view wx:if="{{!indexClass.r4 ||indexClass.r4 != 6}}" style="box-sizing: border-box;   display: flex;align-items: flex-start; justify-content: space-around;">
        <view class="container-left" id="left">
          <view wx:if="{{userNearOrder != 0 && isLoginUser && active == 0}}" catchtap='anotherOrder' data-order='{{userNearOrder.orderNumber}}' class='item-index' style="background: linear-gradient(225deg, #1971F2 0%, rgba(25,137,250,0.16) 100%);border-radius: 16rpx;position: relative;">
            <view style="padding: 0 15rpx;">
              <view style="height: 40rpx;font-size: 32rpx;font-family: PingFangSC, PingFang SC;font-weight: 600;color: #FFFFFF;line-height: 40rpx;letter-spacing: 3px;">最近买过</view>
              <image style="width: 60rpx;height: 54rpx;" src="{{imgUri}}/szmc/home/<USER>" mode="" />
              <view style="position: absolute;right: 0;top: 30rpx;right: 20rpx;width: 123rpx;text-align: center;height: 44rpx;line-height: 44rpx;background: rgba(255,255,255,0.3);border-radius: 15rpx;font-size: 22rpx;font-family: PingFangSC, PingFang SC;font-weight: 600;color: #FFFFFF;letter-spacing: 2px;" data-order="{{userNearOrder.orderNumber}}" catchtap='anotherOrder'>再来一单</view>
              <view wx:for="{{userNearOrder.list}}" style="background-color: white;border-radius: 8rpx;padding: 10rpx;margin: 5rpx 0;display: flex;align-items: center;justify-content: center;" wx:key="key">
                <image style="width: 80rpx;height: 80rpx;" src="{{item.img}}" mode="" />
                <view style="margin-left: 10rpx;width: 70%;overflow:hidden; text-overflow:ellipsis; white-space:nowrap;font-family: PingFangSC, PingFang SC;font-weight: 400;color: #323233;">{{item.name}}</view>
              </view>
            </view>
          </view>
          <view class='item-index' wx:for="{{groomleft}}" wx:key="key">
            <view class='marker' wx:if="{{item.location=='左上'}}">{{item.lableName}}</view>
            <view class='item-text'>
              <view data-id="{{item.productModelId}}" bindtap='goGoodsInfo'>
                <view class='item-img' style="position:relative;">
                  <image src='{{item.img}}' mode="widthFix"></image>
                </view>
                <view class='title'>{{item.productTitle}}</view>
              </view>
              <view class="item-box-index-tip" style="visibility:{{item.location=='左下'||item.buckType==1?'':'hidden'}};">
                <text wx:if="{{item.location=='左下'}}" class="item-box-index-tip-text margin-right-20">{{item.lableName}}</text>
                <text wx:if="{{item.buckType==1}}" class="one-use-bucket">一次性桶</text>
              </view>
              <!-- 规格 -->
              <view class="item-box-index-tip" wx:if="{{item.specificationsDescribe}}">
                <text class="one-use-bucket">{{item.specificationsDescribe}}</text>
              </view>
              <view class='info df'>
                <!-- 价钱 sbwy begin -->
                <view wx:if="{{item.marketPrice != null}}">
                  <view wx:if="{{item.vipGradePrice != null}}">
                    <text class='new-price'><text style="font-size:18rpx;">￥</text>{{item.vipGradePrice}}</text>
                    <text class='old-price'><text style="font-size:18rpx;">￥</text>{{item.marketPrice}}</text>
                  </view>
                  <view wx:else>
                    <text class='new-price'><text style="font-size:18rpx;">￥</text>{{item.productOriginalPrice}}</text>
                    <text class='old-price'><text style="font-size:18rpx;">￥</text>{{item.marketPrice}}</text>
                  </view>
                </view>
                <view wx:else>
                  <view wx:if="{{item.vipGradePrice != null}}">
                    <text class='new-price'><text style="font-size:18rpx;">￥</text>{{item.vipGradePrice}}</text>
                    <text class='old-price'><text style="font-size:18rpx;">￥</text>{{item.productOriginalPrice}}</text>
                  </view>
                  <view wx:else>
                    <text class='new-price'><text style="font-size:18rpx;">￥</text>{{item.productOriginalPrice}}</text>
                  </view>
                </view>
                <!-- 价钱 sbwy begin -->
                <view class='df' data-index="{{index}}" data-id="{{item.productModelId}}" data-price="{{item.productOriginalPrice}}" bindtap="touchOnGoods" style='width:100rpx;height:50rpx;text-align:right;justify-content:flex-end;'>
                  <image data-id="{{item.id}}" src='{{imgUri}}/images/cart.png'></image>
                </view>
              </view>
            </view>
            <view wx:if="{{item.discount}}" style="color: red;white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">
              <view wx:for="{{item.discount}}" wx:for-item="aa" style="display: inline;">
              满{{aa[0]}}件,打{{aa[1]}}折;
              </view>
            </view>
            <view class="good_box" hidden="{{groomSmall[index].hide_good_box}}" style="left: {{bus_x}}px; top: {{bus_y}}px;">
              <image src="{{item.img}}"></image>
            </view>
            <view class='dowm' wx:if="{{item.waterCouponId && shop && shop.szmCStore && shop.szmCStore.shuipiao}}" catchtap="goWaterCoupon" data-id="{{item.waterCouponId}}" data-brandid="{{item.brandId}}">点击购买水票</view>
          </view>
        </view>
        <view class="container-right" id="right">
          <view class='item-index' wx:for="{{groomright}}" wx:key="key">
            <view class='marker' wx:if="{{item.location=='左上'}}">{{item.lableName}}</view>
            <view class='item-text'>
              <view data-id="{{item.productModelId}}" bindtap='goGoodsInfo'>
                <view class='item-img' style="position:relative;">
                  <image src='{{item.img}}'></image>
                </view>
                <view class='title'>{{item.productTitle}}</view>
              </view>
              <view class="item-box-index-tip" style="visibility:{{item.location=='左下'||item.buckType==1?'':'hidden'}};">
                <text wx:if="{{item.location=='左下'}}" class="item-box-index-tip-text margin-right-20">{{item.lableName}}</text>
                <text wx:if="{{item.buckType==1}}" class="one-use-bucket">一次性桶</text>
              </view>
              <!-- 规格 -->
              <view class="item-box-index-tip" wx:if="{{item.specificationsDescribe}}">
                <text class="one-use-bucket">{{item.specificationsDescribe}}</text>
              </view>
              <view class='info df'>
                <!-- 价钱 sbwy begin -->
                <view wx:if="{{item.marketPrice != null}}">
                  <view wx:if="{{item.vipGradePrice != null}}">
                    <text class='new-price'><text style="font-size:18rpx;">￥</text>{{item.vipGradePrice}}</text>
                    <text class='old-price'><text style="font-size:18rpx;">￥</text>{{item.marketPrice}}</text>
                  </view>
                  <view wx:else>
                    <text class='new-price'><text style="font-size:18rpx;">￥</text>{{item.productOriginalPrice}}</text>
                    <text class='old-price'><text style="font-size:18rpx;">￥</text>{{item.marketPrice}}</text>
                  </view>
                </view>
                <view wx:else>
                  <view wx:if="{{item.vipGradePrice != null}}">
                    <text class='new-price'><text style="font-size:18rpx;">￥</text>{{item.vipGradePrice}}</text>
                    <text class='old-price'><text style="font-size:18rpx;">￥</text>{{item.productOriginalPrice}}</text>
                  </view>
                  <view wx:else>
                    <text class='new-price'><text style="font-size:18rpx;">￥</text>{{item.productOriginalPrice}}</text>
                  </view>
                </view>
                <!-- 价钱 sbwy begin -->
                <view class='df' data-index="{{index}}" data-id="{{item.productModelId}}" data-price="{{item.productOriginalPrice}}" bindtap="touchOnGoods" style='width:100rpx;height:50rpx;text-align:right;justify-content:flex-end;'>
                  <image data-id="{{item.id}}" src='{{imgUri}}/images/cart.png'></image>
                </view>
              </view>
            </view>
            <view wx:if="{{item.discount}}" style="color: red;white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">
              <view wx:for="{{item.discount}}" wx:for-item="aa" style="display: inline;">
              满{{aa[0]}}件,打{{aa[1]}}折;
              </view>
            </view>
            <view class="good_box" hidden="{{groomSmall[index].hide_good_box}}" style="left: {{bus_x}}px; top: {{bus_y}}px;">
              <image src="{{item.img}}"></image>
            </view>
            <view class='dowm' wx:if="{{item.waterCouponId && shop && shop.szmCStore && shop.szmCStore.shuipiao}}" catchtap="goWaterCoupon" data-id="{{item.waterCouponId}}" data-brandid="{{item.brandId}}">点击购买水票</view>
          </view>
        </view>
      </view>

      <view wx:else style="display: flex;flex-wrap: wrap;justify-content: space-around;">
        <template is="meal-card" data="{{meal,moreMeal,mealBackground,is_showCar,imgUri,mbus_x,mbus_y}}"></template>
      </view>
    </view>
    <view class="border_bottom_twenty" wx:if="{{groom.length > 0||!isShowGMSP}}"></view>
  </view>
  <view wx:if="{{showActivity}}" class="shadowbg">
    <view class="activitybg">
      <view class="activitycontent">{{announcementText2}}</view>
      <view style="position:absolute;color: #E46B00;font-size: 32rpx;font-weight: 400;bottom:300rpx;left:196rpx;">
        <view>活动时间：</view>
        <view>{{startTime}}~{{endTime}}</view>
      </view>
      <view style="position:absolute;width:70rpx;height:70rpx;left:340rpx;bottom:0rpx;" catchtap="changeStatus" data-type="1"></view>
    </view>
    <view class="pingbi" catchtap="pinbiClick">屏蔽此消息</view>
  </view>
  <view wx:if="{{showNotice}}" class="shadowbg">
    <view class="noticebg">
      <view class="noticecontent" catchtap="viewActivity" data-type="0">{{announcementText1}}</view>
      <view style="position:absolute;width:70rpx;height:70rpx;left:372rpx;bottom:0rpx;" catchtap="changeStatus" data-type="0"></view>
    </view>
    <view class="pingbi" catchtap="changeStatus" data-type="0">屏蔽此消息</view>
  </view>
  <view wx:if="{{showWater}}" class="shadowbg">
    <view class="noticebg11">
      <image style="width: 640rpx;height: 328rpx" src="https://mpjoy.oss-cn-beijing.aliyuncs.com/20240415/43378ee1de2e44e688b3d25945b3b15b.png" mode="" />
      <image style="width: 55rpx;height: 55rpx;position: absolute;top: 170rpx;right: 70rpx;" src="https://mpjoy.oss-cn-beijing.aliyuncs.com/20240415/0c076376d8f642249b8669923661b5eb.png" mode="" />
      <view style="background-color: white;width: 640rpx;border-bottom-left-radius: 16rpx;border-bottom-right-radius: 16rpx;">
        <view style="padding: 20rpx;">
          <view wx:for="{{waterNoConfirm}}" style="display: flex;justify-content: space-between;border-bottom: #a7a7a7 solid 1rpx;padding: 10rpx 0;">
            <view style="font-family: PingFangSC, PingFang SC;font-weight: 400;font-size: 30rpx;color: #0D0318;">
              <view>【水票】</view>
              <view>{{item.specification}}</view>
            </view>
            <view>
              <view style="font-family: PingFangSC, PingFang SC;font-weight: 600;font-size: 51rpx;color: #1971F2;">
              ￥{{item.disprice}}x{{item.count}}张</view>
            </view>
          </view>
          <view  style="display: flex;justify-content: space-around; padding: 20rpx 0 10rpx 0;">
          <view bindtap="waterconfirm" data-bbb="0" style="width: 200rpx;height: 80rpx;line-height: 80rpx;background: #19C66C;border-radius: 40rpx;font-family: PingFangSC, PingFang SC;font-weight: 500;font-size: 28rpx;color: #FFFFFF;text-align: center;">
            拒绝接收
          </view>
          <view  bindtap="waterconfirm" data-bbb="1" style="width: 200rpx;height: 80rpx;line-height: 80rpx;background: #1971F2;border-radius: 40rpx;font-family: PingFangSC, PingFang SC;font-weight: 500;font-size: 28rpx;color: #FFFFFF;text-align: center;">
            确认接收
          </view>
          </view>
        </view>
      </view>
    </view>
  </view>
  <view wx:if="{{showTicket}}" class="shadowbg">
    <view class="noticebg11">
      <image style="width: 640rpx;height: 328rpx" src="http://mpjoy.oss-cn-beijing.aliyuncs.com/20240627/cb4caa538aa94d5a9912571b2b3a5918.png" mode="" />
      <image style="width: 55rpx;height: 55rpx;position: absolute;top: 170rpx;right: 70rpx;" src="https://mpjoy.oss-cn-beijing.aliyuncs.com/20240415/0c076376d8f642249b8669923661b5eb.png" mode="" />
      <view style="background-color: white;width: 640rpx;border-bottom-left-radius: 16rpx;border-bottom-right-radius: 16rpx;">
        <view style="padding: 20rpx;">
          <view wx:for="{{ticketNoConfirm}}" style="display: flex;justify-content: space-between;border-bottom: #a7a7a7 solid 1rpx;padding: 10rpx 0;">
            <view style="font-family: PingFangSC, PingFang SC;font-weight: 400;font-size: 30rpx;color: #0D0318;">
              <view>【优惠券】</view>
              <view style="font-size: 22rpx;color: gray;">{{item.startTime +'~'+item.endTime}}</view>
            </view>
            <view>
              <!-- <view style="font-family: PingFangSC, PingFang SC;font-weight: 600;font-size: 40rpx;color: #1971F2;">
                满{{item.min}}减{{item.price}}</view> -->
              <view style="font-family: PingFangSC, PingFang SC;font-weight: 600;font-size: 40rpx;color: red;">
                {{item.price}}元</view>
            </view>
          </view>
          <view  style="display: flex;justify-content: space-around; padding: 20rpx 0 10rpx 0;">
          <view bindtap="ticketconfirm" data-bbb="0" style="width: 200rpx;height: 80rpx;line-height: 80rpx;background: #19C66C;border-radius: 40rpx;font-family: PingFangSC, PingFang SC;font-weight: 500;font-size: 28rpx;color: #FFFFFF;text-align: center;">
            拒绝接收
          </view>
          <view  bindtap="ticketconfirm" data-bbb="1" style="width: 200rpx;height: 80rpx;line-height: 80rpx;background: #1971F2;border-radius: 40rpx;font-family: PingFangSC, PingFang SC;font-weight: 500;font-size: 28rpx;color: #FFFFFF;text-align: center;">
            开心收下
          </view>
          </view>
        </view>
      </view>
    </view>
  </view>
  <view wx:if="{{ticketVisble}}" class="shadowbg">
    <view bindtap="turnTicket" class="noticebg11">
      <image style="width: 686rpx;height: 686rpx" src="https://mpjoy.oss-cn-beijing.aliyuncs.com/20240611/b61054f6f03c4146b697ac8a61ed32f2.png" mode="" />
      <image style="width: 55rpx;height: 55rpx;position: absolute;top: 170rpx;right: 70rpx;" src="https://mpjoy.oss-cn-beijing.aliyuncs.com/20240415/0c076376d8f642249b8669923661b5eb.png" mode="" />
      <image style="width: 412rpx;height: 76rpx" src="https://mpjoy.oss-cn-beijing.aliyuncs.com/20240611/01790691ef5647ae8684c90cb1dceafd.png" mode="" />
      <view style="width: 640rpx;border-bottom-left-radius: 16rpx;border-bottom-right-radius: 16rpx;">
        <view style="padding: 20rpx;">
          <view  style="display: flex;justify-content: center; padding: 20rpx 0 10rpx 0;">
          <view data-bbb="0" style="width: 200rpx;height: 80rpx;line-height: 80rpx;background: linear-gradient( 90deg, #9AC0F9 0%, #1971F2 100%);border-top-left-radius: 40rpx;border-bottom-left-radius: 40rpx;font-family: PingFangSC, PingFang SC;font-weight: 500;font-size: 28rpx;color: #FFFFFF;text-align: center;">
            ￥{{ticketPrice}}元
          </view>
          <view   data-bbb="1" style="width: 200rpx;height: 80rpx;line-height: 80rpx;background: linear-gradient( 270deg, #EE0B24 0%, #FF6034 100%);border-top-right-radius: 40rpx;border-bottom-right-radius: 40rpx;font-family: PingFangSC, PingFang SC;font-weight: 500;font-size: 28rpx;color: #FFFFFF;text-align: center;">
            点击领取
          </view>
          </view>
        </view>
      </view>
    </view>
  </view>
  <view wx:if="{{guanzhuVisible}}" class="shadowbg">
    <view bindtap="turnguanzhu" class="noticebg11221231">
      <image style="width: 400rpx;height: 400rpx" show-menu-by-longpress="{{true}}" src="https://mpjoy.oss-cn-beijing.aliyuncs.com/20240724/4d903046ce5f47d5bbae0dc70706217a.jpg" mode="" />
      <image style="width: 55rpx;height: 55rpx;position: absolute;top: 50rpx;right: 70rpx;" src="https://mpjoy.oss-cn-beijing.aliyuncs.com/20240415/0c076376d8f642249b8669923661b5eb.png" mode="" />
      <image style="width: 412rpx;height: 76rpx" src="https://mpjoy.oss-cn-beijing.aliyuncs.com/20240902/9681dc8989a1443f83a82dc3af93f424.png" mode="" />
      <view style="width: 640rpx;border-bottom-left-radius: 16rpx;border-bottom-right-radius: 16rpx;">
        <view style="padding: 20rpx;">
          <view  style="display: flex;justify-content: center; padding: 20rpx 0 10rpx 0;">
          <view data-bbb="0" style="width: 200rpx;height: 80rpx;line-height: 80rpx;background: linear-gradient( 90deg, #9AC0F9 0%, #1971F2 100%);border-top-left-radius: 40rpx;border-bottom-left-radius: 40rpx;font-family: PingFangSC, PingFang SC;font-weight: 500;font-size: 28rpx;color: #FFFFFF;text-align: center;">
            ￥{{guanzhuPrice}}元
          </view>
          <view   data-bbb="1" style="width: 200rpx;height: 80rpx;line-height: 80rpx;background: linear-gradient( 270deg, #EE0B24 0%, #FF6034 100%);border-top-right-radius: 40rpx;border-bottom-right-radius: 40rpx;font-family: PingFangSC, PingFang SC;font-weight: 500;font-size: 28rpx;color: #FFFFFF;text-align: center;">
            点击领取
          </view>
          </view>
        </view>
      </view>
    </view>
  </view>
  <image wx:if="{{login && login.length > 0 && login[0].r2 == 2}}" bindtap="gofenxiao" style="position: fixed;bottom: 100rpx;right: 0;width: 140rpx;" src="https://jovejewelry.oss-cn-beijing.aliyuncs.com/crmebimage/public/content/2025/03/04/ed87cd2d34744144a0f6e27e2403fce337tla15wuo.png" mode="widthFix"/>
  <!-- 底部加载状态提示 -->
  <view wx:if="{{groom.length > 0}}" class="loading-more-container">
    <view wx:if="{{groomHasMore}}" class="loading-text">上拉加载更多</view>
    <view wx:else class="loading-text">没有更多商品了</view>
  </view>
</view>