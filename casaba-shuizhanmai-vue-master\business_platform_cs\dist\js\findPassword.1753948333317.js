(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["findPassword"],{"01b1":function(e,s,o){"use strict";o("4268")},4268:function(e,s,o){},"54be":function(e,s,o){"use strict";o.r(s);var t=function(){var e=this,s=e._self._c;return s("div",{staticClass:"login-box"},[s("div",{staticClass:"login-form"},[s("p",{staticClass:"login-title"},[e._v("找回密码")]),s("p",{staticClass:"login-en"},[e._v("FIND PASSWORD")]),s("el-form",{ref:"saveFormElement",staticStyle:{"margin-top":"30px"},attrs:{rules:e.saveRules,model:e.saveForm,"label-width":"100px"}},[s("el-form-item",{attrs:{label:"手机号",prop:"phone"}},[s("el-input",{attrs:{placeholder:"请输入手机号码"},model:{value:e.saveForm.phone,callback:function(s){e.$set(e.saveForm,"phone",s)},expression:"saveForm.phone"}})],1),s("el-form-item",{staticClass:"img-code",attrs:{label:"图片验证码"}},[s("el-input",{model:{value:e.inputImgcode,callback:function(s){e.inputImgcode=s},expression:"inputImgcode"}},[s("template",{slot:"append"},[s("img",{staticStyle:{width:"100px",height:"30px",display:"block"},attrs:{src:e.imgcode,alt:""},on:{click:function(s){return s.stopPropagation(),e.requestImgCode.apply(null,arguments)}}})])],2)],1),s("el-form-item",{staticClass:"sen-msg",attrs:{label:"验证码",prop:"code"}},[s("el-input",{attrs:{oninput:"value=value.replace(/[^\\d]/g,'')"},model:{value:e.saveForm.code,callback:function(s){e.$set(e.saveForm,"code",s)},expression:"saveForm.code"}},[s("template",{slot:"append"},[s("el-button",{staticClass:"codeClass",attrs:{type:"primary",loading:"发送验证码"!=e.saveForm.codeText},on:{click:e.sendCode}},[e._v(e._s(e.saveForm.codeText))])],1)],2)],1),s("el-form-item",{attrs:{label:"设置密码",prop:"password"}},[s("el-input",{attrs:{value:e.password1},on:{input:e.getPassword1}})],1),s("el-form-item",{attrs:{label:"确认密码",prop:"password1"}},[s("el-input",{attrs:{value:e.password2},on:{input:e.getPassword2}})],1),s("el-form-item",[s("el-button",{staticClass:"float-right",attrs:{type:"text"},on:{click:e.toLogin}},[e._v("知道密码，马上登录")])],1),s("el-form-item",[s("el-button",{attrs:{type:"primary",loading:e.saveForm.isSubmit},on:{click:e.saveSetSubmit}},[e._v("重 置")])],1)],1)],1)])},a=[],r={data:function(){var e=this,s=this,o=function(s,o,t){o?o!==e.saveForm.password?t(new Error("两次输入密码不一致!")):t():t(new Error("请再次输入密码!"))},t=function(e,o,t){if(o){var a=s.$util.checkPhone(o);a?t():t(new Error("请输入有效的手机号码"))}else t(new Error("请输入手机号"))};return{saveForm:{phone:"",code:"",password:"",password1:"",codeText:"发送验证码",isSubmit:!1},imgcode:"",inputImgcode:"",imgCodeKey:"",password1:"",password2:"",saveRules:{phone:[{validator:t,trigger:"change"}],code:[{required:!0,message:"验证码不能为空",trigger:"blur"}],password:[{required:!0,message:"密码不能为空",trigger:"blur"},{min:6,max:20,message:"密码长度在 6 到 20 个字符",trigger:"blur"}],password1:[{validator:o,trigger:"blur"}]}}},methods:{requestImgCode:function(){var e=this;this.$get("/szmb/code/getImgCode").then((function(s){1===s.code?(e.imgcode=s.data.data,e.imgCodeKey=s.data.imgCodeKey):e.$message.error(s.data)})).catch((function(e){console.log(e)}))},toLogin:function(){var e=this;e.$router.push({name:"login",params:{}})},sendCode:function(){var e=this;if(""!=e.inputImgcode){var s=60;e.saveForm.codeText=s+"s";var o="/szmcuercontroller/sendcode",t={mobilPhone:e.saveForm.phone,imgCode:e.inputImgcode,imgCodeKey:e.imgCodeKey};e.$post(o,t).then((function(o){console.log(o),1==o.code?(e.$message({message:"发送成功",type:"success"}),e.saveForm.dTime=setInterval((function(){s--,s<0?(e.saveForm.codeText="发送验证码",clearInterval(e.saveForm.dTime)):e.saveForm.codeText=s+"s"}),1e3)):(e.saveForm.codeText="发送验证码",e.$message.error(o.data))}))}else e.$message({message:"请输入图片验证码",type:"warning"})},getPassword1:function(e){var s=this;if(e.length>s.password1.length){e=e.replace(/[\u4E00-\u9FA5]/g,"");var o=e.substring(e.length-1);s.saveForm.password=s.saveForm.password+o,s.password1="●".repeat(e.length)}else s.saveForm.password="",s.password1="";console.log(s.saveForm.password,"ppp111")},getPassword2:function(e){var s=this;if(e.length>s.password2.length){e=e.replace(/[\u4E00-\u9FA5]/g,"");var o=e.substring(e.length-1);s.saveForm.password1=s.saveForm.password1+o,console.log(s.saveForm.password1,"ppp222"),s.password2="●".repeat(e.length)}else s.saveForm.password1="",s.password2=""},saveSetSubmit:function(){var e=this;console.log(e.saveForm),e.$refs["saveFormElement"].validate((function(s){if(!s)return!1;console.log("bbb"),e.saveForm.isSubmit=!0;var o="/szmb/storedeatilcontroller/updatestorepassword",t={storeId:"",phone:e.saveForm.phone,code:e.saveForm.code,password:e.saveForm.password};e.$post(o,t).then((function(s){console.log(s),e.saveForm.isSubmit=!1,1==s.code?(e.$message({message:"设置成功！",type:"success"}),e.$alert("密码已重置，请重新登录","温馨提示",{confirmButtonText:"去登录",callback:function(s){e.$refs.saveFormElement.resetFields(),e.password1="",e.password2="",e.$router.push({name:"login"})}})):e.$message.error(s.data)}))}))}},created:function(){this.requestImgCode()},mounted:function(){}},n=r,i=(o("01b1"),o("0c7c")),l=Object(i["a"])(n,t,a,!1,null,"2c9b5059",null);s["default"]=l.exports}}]);