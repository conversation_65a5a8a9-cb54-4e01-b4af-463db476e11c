(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["about"],{f820:function(e,r,a){"use strict";a.r(r);var t=function(){var e=this,r=e._self._c;return r("div")},u=[],o={components:{},data:function(){return{}},computed:{},created:function(){},mounted:function(){var e=this,r=e.$route.params;1==r.source?e.$router.replace({name:r.url,params:{type:"msg",orderId:r.orderId}}):2==r.source?e.$router.replace({name:r.url,params:{type:"msg",source:"退款",retreatId:r.retreatId}}):12==r.source?e.$router.replace({name:r.url,params:{type:"msg",userId:r.userId,source:r.source,name:r.name,callNum:r.callNum}}):20==r.source?e.$router.replace({name:r.url,params:{type:r.type,state:r.state}}):(console.log("aabout111",e.$route.params),e.$router.replace({name:r.url,params:{type:"msg",userId:r.userId,source:r.source}}))},watch:{},methods:{}},s=o,c=a("0c7c"),n=Object(c["a"])(s,t,u,!1,null,"02e0e141",null);r["default"]=n.exports}}]);