/**
 * @姓名 系统生成
 * @版本号 1.0.0
 * @日期 2024/12/19
 */
package com.example.waterstationbuyproducer.szmb.controller.order;

import com.example.waterstationbuyproducer.dao.SzmCProductNewMapper;
import com.example.waterstationbuyproducer.entity.SzmCProductNew;
import com.example.waterstationbuyproducer.util.ResultBean;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 订单来源关联产品查询控制层
 * @date 2024/12/19
 */
@RestController
@RequestMapping("szmb/orderSourceConnect/product")
@Api(value = "szmb/orderSourceConnect/product/", description = "订单来源关联产品查询")
public class OrderSourceConnectProductController {
    
    private static final Logger logger = LoggerFactory.getLogger(OrderSourceConnectProductController.class);
    
    @Autowired
    private SzmCProductNewMapper productMapper;

    /**
     * 搜索产品 - 用于下拉框远程搜索
     */
    @RequestMapping(value = "search", method = {RequestMethod.GET, RequestMethod.POST})
    @ApiOperation(value = "搜索产品", response = ResultBean.class,
                  notes = "根据关键词搜索产品，用于下拉框远程搜索，支持GET和POST请求")
    public ResultBean search(@ApiParam(value = "门店ID", required = true) @RequestParam("storeId") Long storeId,
                            @ApiParam(value = "搜索关键词") @RequestParam(value = "keyword", required = false) String keyword,
                            @ApiParam(value = "页码", defaultValue = "1") @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
                            @ApiParam(value = "每页大小", defaultValue = "20") @RequestParam(value = "pageSize", defaultValue = "20") Integer pageSize) {
        ResultBean resultBean = new ResultBean();
        try {
            // 参数校验
            if (storeId == null) {
                return resultBean.error("门店ID不能为空");
            }

            // 直接调用mapper查询
            PageHelper.startPage(pageNo, pageSize);
            List<SzmCProductNew> productList = productMapper.selectProductListAll(null, keyword, null, storeId, null, null);
            PageInfo<SzmCProductNew> personPageInfo = new PageInfo<>(productList);
            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("list", productList);
            result.put("total", personPageInfo.getTotal());

            return resultBean.success(result);

        } catch (Exception e) {
            logger.error("搜索产品失败", e);
            return resultBean.error("搜索产品失败: " + e.getMessage());
        }
    }

    /**
     * 获取产品详情
     */
    @GetMapping("detail/{productId}")
    @ApiOperation(value = "获取产品详情", httpMethod = "GET", response = ResultBean.class,
                  notes = "根据产品ID获取产品详细信息")
    public ResultBean getProductDetail(@ApiParam(value = "产品ID", required = true) @PathVariable Long productId) {
        ResultBean resultBean = new ResultBean();
            // 参数校验
            if (productId == null) {
                return resultBean.error("产品ID不能为空");
            }

            // 查询产品详情
            SzmCProductNew product = productMapper.selectByPrimaryKey(productId);
            if (product == null) {
                return resultBean.error("产品不存在");
            }

            return resultBean.success(product);

    }


}
