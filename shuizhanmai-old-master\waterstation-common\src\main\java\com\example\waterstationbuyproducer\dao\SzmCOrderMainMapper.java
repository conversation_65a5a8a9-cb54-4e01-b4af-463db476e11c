package com.example.waterstationbuyproducer.dao;

import com.alipay.api.domain.KoubeiMemberDataItemNearbyQueryModel;
import com.example.waterstationbuyproducer.entity.BankTransferApp;
import com.example.waterstationbuyproducer.entity.SzmCOrderMain;
import com.example.waterstationbuyproducer.vo.RetuenOrderList;
import com.example.waterstationbuyproducer.vo.OrderWithSourceVO;
import com.example.waterstationbuyproducer.vo.StoreDoubleValueVo;
import com.example.waterstationbuyproducer.vo.Turnover.PayInfo;
import com.example.waterstationbuyproducer.vo.Turnover.TurnoverVO;
import org.apache.ibatis.annotations.Param;
import org.omg.CORBA.INTERNAL;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.awt.dnd.DropTargetEvent;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Repository
public interface SzmCOrderMainMapper {
    int deleteByPrimaryKey(Long orderMainId);
    int deleteByOrderNum(String orderNum);

    int insert(SzmCOrderMain record);

    SzmCOrderMain selectByPrimaryKey(Long orderMainId);

    List<SzmCOrderMain> selectByIds(@Param("orderMainId") List<Long> orderMainId);

    List<SzmCOrderMain> selectAll(@Param("userId") Long userId);

    List<SzmCOrderMain> selectAllOrders();

    List<SzmCOrderMain> selectStoreByUserAndStoreIdAllUserIdCanNo(@Param("userId") Long userId,
                                                       @Param("storeId") Long storeId,
                                                       @Param("addressId") Long addressId,
                                                       @Param("username") String username,
                                                       @Param("startTime") String startTime,
                                                       @Param("endTime") String endTime,
                                                       @Param("ordersource") Integer ordersource,
                                                       @Param("deliveryUserId") Long deliveryUserId,
                                                       @Param("startTimeFinish") String startTimeFinish,
                                                       @Param("endTimeFinish") String endTimeFinish,
                                                       @Param("orderStatus") Integer orderStatus);
    List<SzmCOrderMain> selectStoreByUserAndStoreIdAllStoreIdCanNo(@Param("userId") Long userId,
                                                       @Param("storeId") Long storeId,
                                                       @Param("addressId") Long addressId,
                                                       @Param("username") String username,
                                                       @Param("startTime") String startTime,
                                                       @Param("endTime") String endTime,
                                                       @Param("ordersource") Integer ordersource,
                                                       @Param("deliveryUserId") Long deliveryUserId,
                                                       @Param("startTimeFinish") String startTimeFinish,
                                                       @Param("endTimeFinish") String endTimeFinish);
    List<SzmCOrderMain> selectByParams(Map<String,Object> params);
    List<SzmCOrderMain> selectPingtaiByParams(Map<String,Object> params);
    Integer selectPingtaiCountByParams(Map<String,Object> params);
    List<SzmCOrderMain> selectAllByParams(Map<String,Object> params);
    Integer selectAllCountByParams(Map<String,Object> params);
    List<SzmCOrderMain> selectTransferByParams(Map<String,Object> params);
    List<SzmCOrderMain> selectTransferFrontByParams(Map<String,Object> params);
    List<SzmCOrderMain> selectTransferFrontCountByParams(Map<String,Object> params);

    List<SzmCOrderMain> selectStoreByUserAndStoreIdAll(@Param("userId") Long userId,
                                                       @Param("storeId") Long storeId,
                                                       @Param("addressId") Long addressId,
                                                       @Param("username") String username,
                                                       @Param("startTime") String startTime,
                                                       @Param("endTime") String endTime);
    List<SzmCOrderMain> selectTiaoJianResult();


    List<SzmCOrderMain> selectStoreByUserAndStoreIdAll1(@Param("userId") Long userId, @Param("storeId") String storeId);

    List<SzmCOrderMain> selectStoreByUserAndStoreIdAll2(@Param("userId") Long userId, @Param("storeId") String storeId, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    List<SzmCOrderMain> selectStoreByUserAndStoreIdAll3(@Param("userId") Long userId, @Param("storeId") Long storeId, @Param("limitDate") Date limitDate);

    List<SzmCOrderMain> selectStoreIdAll( @Param("storeId") Long storeId,@Param("userId") Long userId);

    List<SzmCOrderMain> selectStoreIdAll2( @Param("storeId") Long storeId,@Param("userId") Long userId);

    Integer selectStoreByUserAndStoreIdAllCount(@Param("userId") Long userId, @Param("storeId") String storeId);

    Double selectStoreByUserAndStoreIdAllMoney(@Param("userId") Long userId, @Param("storeId") String storeId);

    Double selectStoreByUserAndStoreIdAllR1(@Param("userId") Long userId, @Param("storeId") String storeId);

    int updateUserinfoByPrimaryKey(SzmCOrderMain record);

    int updateByPrimaryKey(SzmCOrderMain record);
    //取消订单
    int insertCancelOrder(SzmCOrderMain record);
    //根据订单的状态进行查询
    List<SzmCOrderMain> selectAllByState(@Param("userId") Long userId, @Param("state") Long state);

    List<SzmCOrderMain> selectByUserAndStoreId(@Param("userId") Long userId, @Param("state") Long state,
                                               @Param("storeId") Long storeId, @Param("addressId") Long addressId, @Param("ordersource") Integer ordersource);
    @Transactional(rollbackFor = {Exception.class})
    int updateByOrderNum(SzmCOrderMain record);
    /**
     * 根据名称查询订单
     */
    List<SzmCOrderMain> selectAllByName(@Param("name") String name);

    /**
     * 根据订单编号查询订单
     * @param orderNum
     * @return
     */
    @Transactional(rollbackFor = {Exception.class})
    SzmCOrderMain selectByOrderNum(@Param("orderNum") String orderNum);

    /**
     * 根据订单编号查询订单
     * @param orderNum
     * @return
     */
    @Transactional(rollbackFor = {Exception.class})
    SzmCOrderMain selectR5ByOrderNum(@Param("orderNum") String orderNum);



    SzmCOrderMain selectByOrderNumOptimizt(@Param("orderNum") String orderNum);

    /**
     * 根据订单编号查询订单1
     * @param orderNum
     * @return
     */
    SzmCOrderMain selectByOrderNum1(@Param("orderNum") String orderNum);

    SzmCOrderMain selectMemByOrderNum(@Param("orderNum") String orderNum);

    /**
     * 查看用户全部的订单信息
     * @param userId
     * @return
     */
    Long selectConut(@Param("userId") Long userId);

    Integer selectByStoreId(@Param("storeId") Long storeId);

    /**
     * 查看商家未处理的订单
     * @param storeId
     * @return
     */
    List<SzmCOrderMain> selectUntreated(@Param("storeId") Long storeId);
    /**
     * 查看商家未处理的订单
     * @param storeId
     * @return
     */
    List<SzmCOrderMain> selectUntreatedPC(@Param("storeId") Long storeId, @Param("userType") String userType,
                                        @Param("deliveryName") String deliveryName, @Param("payMent") String payMent,
                                        @Param("startTime") Date startTime, @Param("endTime") Date endTime);
    /**
     * 查看商家未处理的订单数量
     * @param storeId
     * @return
     */
    Long selectUntreatedCount(@Param("storeId") Long storeId);
    /**
     * 查看商家未处理的订单
     * @param storeId
     * @return
     */
    List<SzmCOrderMain> selectUntreated1(@Param("storeId") Long storeId);
    /**
     * 查看商家已完成的订单
     * @param storeId
     * @return
     */
    List<SzmCOrderMain> selectFinish(@Param("storeId") Long storeId);

    List<SzmCOrderMain> selectFinishPC(@Param("storeId") Long storeId, @Param("userType") String userType,
                                       @Param("deliveryName") String deliveryName, @Param("payMent") String payMent,
                                       @Param("startTime") Date startTime, @Param("endTime") Date endTime);
    /**
     * 查看商家已完成的订单
     * @param storeId
     * @return
     */
    List<SzmCOrderMain> selectFinish1(@Param("storeId") Long storeId);



    List<SzmCOrderMain> selectstoreOrder(SzmCOrderMain szmCOrderMain);

    /**
     * 根据状态查看订单
     * @param storeId
     * @param orderStatus
     * @return
     */
    List<SzmCOrderMain> selectState(@Param("storeId") Long storeId, @Param("orderStatus") Integer orderStatus);

    /**
     * 修改状态
     * @param orderMainId
     * @param orderStatus
     * @return
     */
    int updateByOrderId(@Param("orderMainId") Long orderMainId, @Param("orderStatus") Integer orderStatus);

    int updateGroupByOrderId(@Param("orderId") Long orderId, @Param("group") Integer group);
    int updatePirurlByOrderId(@Param("orderId") Long orderId, @Param("picurl") String picurl);

    int backByOrderId(@Param("orderId") Long orderId ,@Param("reason") String reason);
    int backAdminByOrderId(@Param("orderId") Long orderId,@Param("reason") String reason ,@Param("storeId") Long storeId);
    /**
     * 修改状态
     * @param orderId
     * @return
     */
    SzmCOrderMain selectOrderOne(@Param("orderId") Long orderId);

    List<SzmCOrderMain> selectStoreBillAll(@Param("storeId") Long storeId, @Param("statrTime") Date statrTime, @Param("endTime") Date endTime);

    Double selectStoreBillCount(@Param("storeId") Long storeId);


    Double selectStoreBillCountNew(@Param("storeId") Long storeId);

    Double selectStoreBillCountNewByTime(@Param("storeId") Long storeId, @Param("stateTime") Date stateTime, @Param("endTime") Date endTime);


    List<SzmCOrderMain> selectStoreBillList(@Param("storeId") Long storeId);

    /**
     * 根据订单编号查询订单
     * @param orderNum
     * @return
     */
    @Transactional(rollbackFor = {Exception.class})
    SzmCOrderMain selectByOrderNumAndState(@Param("orderNum") String orderNum);

    /**
     * 修改状态以及发单状态
     * @param orderId
     * @param orderStatus
     * @return
     */
    int updateState(@Param("orderId") Long orderId, @Param("orderStatus") Integer orderStatus, @Param("state") Integer state, @Param("total") Double total);

    List<SzmCOrderMain> selectByStoreIdAndSengMessage(@Param("storeId") String storeId);

    Integer selectByStoreIdOrderTotal(@Param("storeId") String storeId, @Param("date") Date date);



    /**
     * 根据状态和发单状态查看订单
     * @param storeId
     * @param orderStatus
     * @return
     */
    List<SzmCOrderMain> selectStateAndIs(@Param("storeId") Long storeId, @Param("orderStatus") Integer orderStatus, @Param("state") Integer state);
    /**
     * 查询配送员Id不为空的集合
     * @param storeId
     * @param orderStatus
     * @return
     */
    List<SzmCOrderMain> selectDelivery(@Param("storeId") Long storeId, @Param("orderStatus") Integer orderStatus);

    List<SzmCOrderMain> selectDeliveryPC(@Param("storeId") Long storeId, @Param("userName") String name,@Param("address") String address,@Param("orderStatus") Integer orderStatus, @Param("userType") String userType,
                                         @Param("deliveryName") String deliveryName, @Param("payMent") String payMent,
                                         @Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("selfStatus") Boolean selfStatus, @Param("ordersource") Integer ordersource);
                                         
    List<SzmCOrderMain> selectDeliveryPCSelf(@Param("storeId") Long storeId, @Param("userName") String name,@Param("orderStatus") Integer orderStatus, @Param("userType") String userType,
                                         @Param("deliveryName") String deliveryName, @Param("payMent") String payMent,
                                         @Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("selfStatus") Boolean selfStatus, @Param("ordersource") Integer ordersource);
    Integer selectDeliveryPCSelfCount(@Param("storeId") Long storeId, @Param("userName") String name,@Param("orderStatus") Integer orderStatus, @Param("userType") String userType,
                                         @Param("deliveryName") String deliveryName, @Param("payMent") String payMent,
                                         @Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("selfStatus") Boolean selfStatus, @Param("ordersource") Integer ordersource);

    Integer selectDeliveryPCCount(@Param("storeId") Long storeId, @Param("userName") String name,@Param("orderStatus") Integer orderStatus, @Param("userType") String userType,
                                         @Param("deliveryName") String deliveryName, @Param("payMent") String payMent,
                                         @Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("ordersource") Integer ordersource);

    List<SzmCOrderMain> selectNewDelivery(@Param("storeId") Long storeId, @Param("orderStatus") Integer orderStatus);

    List<SzmCOrderMain> selectNewDeliveryPC(@Param("storeId") Long storeId, @Param("userName") String name,@Param("address") String address,@Param("orderStatus") Integer orderStatus, @Param("userType") String userType,
                                            @Param("deliveryName") String deliveryName, @Param("payMent") String payMent,
                                            @Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("selfStatus") Boolean selfStatus, @Param("ordersource") Integer ordersource);

    Integer selectNewDeliveryPCCount(@Param("storeId") Long storeId, @Param("userName") String name,@Param("orderStatus") Integer orderStatus, @Param("userType") String userType,
                                            @Param("deliveryName") String deliveryName, @Param("payMent") String payMent,
                                            @Param("startTime") Date startTime, @Param("endTime") Date endTime
                                            , @Param("ordersource") Integer ordersource
                                            , @Param("back") Integer back
                                            );
    Double selectSumPCByTime(@Param("storeId") Long storeId, @Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("ordersource") Integer ordersource);

    Integer selectCountPCByTime(@Param("storeId") Long storeId,@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("ordersource") Integer ordersource);

    Integer selectByStoreIdOrderDate(@Param("storeId") String storeId, @Param("staterTime") Date staterTime, @Param("endTime") Date endTime);


    Integer selectNewOrderCount(@Param("storeId") String storeId, @Param("stateTime") Date stateTime, @Param("endTime") Date endTime);


    Double selectByStoreDateMoney(@Param("storeId") String storeId, @Param("staterTime") Date staterTime, @Param("endTime") Date endTime);

    Double selectNewOrderMoney(@Param("storeId") String storeId, @Param("staterTime") Date staterTime, @Param("endTime") Date endTime);

    List<Integer> selectByStoreOrderState(@Param("storeId") String storeId, @Param("staterTime") Date staterTime, @Param("endTime") Date endTime);

    Integer selectByStoreOrderStateTotal(@Param("storeId") String storeId, @Param("staterTime") Date staterTime, @Param("endTime") Date endTime, @Param("state") Integer state);

    Integer selectByStoreByStateTwo(@Param("storeId") String storeId, @Param("staterTime") Date staterTime, @Param("endTime") Date endTime, @Param("state") Integer state);


    Integer selectByStoreByStateTwoDelivery(@Param("storeId") String storeId, @Param("staterTime") Date staterTime, @Param("endTime") Date endTime, @Param("state") Integer state);
    Integer selectByStoreOrderStateDome(@Param("storeId") String storeId, @Param("stateTime") Date stateTime, @Param("endTime") Date endTime);

    Integer selectOrderAllCount(@Param("storeId") String storeId, @Param("stateTime") Date stateTime, @Param("endTime") Date endTime);

    Integer selectRemind(@Param("storeId") Long storeId);

    Integer selectRemind1(@Param("storeId") Long storeId);

    int updateRemind(@Param("orderId") Long orderId);


    List<SzmCOrderMain> selectByStoreAndSend(@Param("storeId") String storeId, @Param("deliveryId") Long deliveryId);

    List<SzmCOrderMain> selectByStoreAndUser(@Param("storeId") String storeId, @Param("user") Long user);


    List<SzmCOrderMain> selectByStateStar(@Param("storeId") String storeId, @Param("stateTime") Date stateTime, @Param("endTime") Date endTime, @Param("state") Integer state);

    List<String> selectTotal(@Param("storeId") Long storeId, @Param("stateTime") Date stateTime, @Param("endTime") Date endTime);


    List<Double> selectDeliveryIdAll(@Param("deliveryId") Long deliveryId);

    List<SzmCOrderMain> selectOneOrder(@Param("userId") Long userId);

    List<SzmCOrderMain> selectDelliveryId(@Param("deliveryId") Long deliveryId);

    List<SzmCOrderMain> selectDeliveryBydate(@Param("deliveryId") Long deliveryId, @Param("stateTime") Date stateTime, @Param("endTime") Date endTime);

    List<SzmCOrderMain> selectDeliveryState(@Param("deliveryId") Long deliveryId, @Param("stateTime") Date stateTime, @Param("endTime") Date endTime, @Param("storeId") Long storeId);

    List<SzmCOrderMain> selectDel(@Param("storeId") Long storeId, @Param("orderStatus") Integer orderStatus, @Param("state") Integer state);

    List<SzmCOrderMain> selectDelPC(@Param("storeId") Long storeId, @Param("orderStatus") Integer orderStatus, @Param("state") Integer state,@Param("userType") String userType,
                                    @Param("deliveryName") String deliveryName, @Param("payMent") String payMent,
                                    @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    List<SzmCOrderMain> selectReturnPC(@Param("storeId") Long storeId, @Param("userName")String name,@Param("address") String address,@Param("orderStatus") Integer orderStatus, @Param("state") Integer state, @Param("userType") String userType,
                                       @Param("deliveryName") String deliveryName, @Param("payMent") String payMent, @Param("returnState") String returnState,
                                       @Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("selfStatus") Boolean selfStatus
                                       , @Param("ordersource") Integer ordersource
                                       , @Param("sortOrder") String sortOrder
                                       );
    List<SzmCOrderMain> selectOtherPC(@Param("storeId") Long storeId, @Param("userName")String name, @Param("address") String address,@Param("orderStatus") Integer orderStatus, @Param("state") Integer state, @Param("userType") String userType,
                                       @Param("deliveryName") String deliveryName, @Param("payMent") String payMent, @Param("returnState") String returnState,
                                       @Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("selfStatus") Boolean selfStatus
                                       , @Param("ordersource") Integer ordersource
                                       , @Param("back") Integer back
                                       );

    Integer selectReturnPCCount(@Param("storeId") Long storeId, @Param("userName")String name,@Param("orderStatus") Integer orderStatus, @Param("state") Integer state, @Param("userType") String userType,
                                       @Param("deliveryName") String deliveryName, @Param("payMent") String payMent, @Param("returnState") String returnState,
                                       @Param("startTime") Date startTime, @Param("endTime") Date endTime
                                       , @Param("ordersource") Integer ordersource
                                       , @Param("back") Integer back
    );

    List<SzmCOrderMain> selectBuckPC(@Param("storeId") Long storeId, @Param("userName")String name,@Param("address") String address,@Param("orderStatus") Integer orderStatus, @Param("state") Integer state, @Param("userType") String userType,
                                       @Param("deliveryName") String deliveryName, @Param("payMent") String payMent, @Param("returnState") String returnState,
                                       @Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("selfStatus") Boolean selfStatus, @Param("ordersource") Integer ordersource);
    Integer selectBuckPCCount(@Param("storeId") Long storeId);

    List<SzmCOrderMain> selectAllOrder(@Param("storeId") Long storeId);

    List<SzmCOrderMain> selectAllOrderPC(@Param("storeId") Long storeId,@Param("userType") String userType,
                                       @Param("deliveryName") String deliveryName, @Param("payMent") String payMent,
                                       @Param("startTime") Date startTime, @Param("endTime") Date endTime
            , @Param("drainageUserId") Long drainageUserId
            , @Param("drainageid") Long drainageid
    );

    List<SzmCOrderMain> selectAllOrder1(@Param("storeId") Long storeId);

    List<SzmCOrderMain> selectAllOrderCompanyId(@Param("companyId") Long companyId);

    List<SzmCOrderMain> selectAllOrderCompanyIdAndUntreated(@Param("companyId") Long companyId);

    List<SzmCOrderMain> selectAllOrderCompanyIdAndFinish(@Param("companyId") Long companyId);

    Integer selectStoreOrderCoutn(@Param("storeId") Long storeId);

    Integer selectStoreDayOrderCoutn(@Param("storeId") Long storeId, @Param("stateTime") String stateTime, @Param("endTime") String endTime);

    List<SzmCOrderMain> selectOrderStatus(@Param("storeId") Long storeId, @Param("orderStatus") Integer orderStatus);

    List<SzmCOrderMain> selectOrderStatusPC(@Param("storeId") Long storeId, @Param("userName")String name,@Param("address") String address,@Param("orderStatus") Integer orderStatus,@Param("userType") String userType,
                                            @Param("deliveryName") String deliveryName, @Param("payMent") String payMent,
                                            @Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("selfStatus") Boolean selfStatus, @Param("ordersource") Integer ordersource);

    Integer selectOrderStatusPCCount(@Param("storeId") Long storeId, @Param("userName")String name,@Param("orderStatus") Integer orderStatus,@Param("userType") String userType,
                                            @Param("deliveryName") String deliveryName, @Param("payMent") String payMent,
                                            @Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("ordersource") Integer ordersource);

    Integer selectUserCount(@Param("userId") Long userId);


    Integer selectUserCountNew(@Param("userId") Long userId);

    List<SzmCOrderMain> selectNewStoreAll(@Param("storeId") Long storeId);

    Integer selectStoreIdCount(@Param("storeId") Long storeId);

    Integer selectUserIdCount(@Param("userId") Long userId);

    List<SzmCOrderMain> selectDeliveryAll(@Param("deliveryId") Long deliveryId, @Param("stateTime") Date stateTime, @Param("endTime") Date endTime);

    List<SzmCOrderMain> selectOneUser(@Param("userId") String userId, @Param("storeId") String storeId);

    SzmCOrderMain selectUserLastBuy(@Param("userId") Long userId, @Param("storeId") Long storeId);
    SzmCOrderMain selectUserLastBuyFinish(@Param("userId") Long userId, @Param("storeId") Long storeId);

    List<SzmCOrderMain> selectByPayTimeMax(@Param("userId") Long userId);

    List<SzmCOrderMain> selectByPaymentModeId(@Param("userId") Long userId, @Param("storeId") Long storeId, @Param("payMentId") Long payMentId);




    List<SzmCOrderMain> selectByPaymentModeIdNew(@Param("userId") Long userId, @Param("storeId") Long storeId, @Param("payMentId") Long payMentId);

    List<SzmCOrderMain> selectByPaymentModeIdDesc(@Param("userId") Long userId, @Param("storeId") Long storeId, @Param("payMentId") Long payMentId);

    Integer selectByPaymentModeIdCount(@Param("userId") Long userId, @Param("storeId") Long storeId, @Param("payMentId") Long payMentId);

    Integer selectByStoreIdCount(@Param("storeId") Long storeId);

    Integer selectDeliveryCount(@Param("storeId") Long storeId, @Param("userId") Long userId, @Param("orderState") String orderState, @Param("delivyState") String delivyState);

    Integer selectByPaymentCount(@Param("userId") Long userId, @Param("storeId") Long storeId, @Param("payMentId") Long payMentId);
    /**
     * 根据状态查看订单
     * @param storeId
     * @param orderStatus
     * @return
     */
    List<SzmCOrderMain> selectStateCount(@Param("storeId") Long storeId, @Param("orderStatus") Integer orderStatus);

    List<SzmCOrderMain> selectOrderListCount(@Param("storeId") Long storeId);


    List<SzmCOrderMain> selectByPayType(@Param("userId") Long userId, @Param("payType") Long payType);


    List<SzmCOrderMain> selectByPayTypeNew(@Param("userId") Long userId, @Param("payType") Long payType, @Param("stateTime") Date stateTime, @Param("endTime") Date endTime);

    Double selectByMontu(@Param("userId") Long userId, @Param("stateTime") Date stateTime, @Param("payId") Integer payMentId);

    Double yingshou(@Param("userId") Long userId, @Param("stateTime")
            Date stateTime, @Param("endTime") Date endTime, @Param("payMentId") Integer payMentId);


    Double selectTimeqichu(@Param("storeId") Long storeId, @Param("deliveryId") Long deliveryId, @Param("stateTime") Date stateTime);


    List<SzmCOrderMain> selectOrderListall();

    Double selectOrderSumByStoreId(@Param("storeId") Long storeId, @Param("payType") Integer payType, @Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("userType") Integer userType, @Param("nameOrPhone") String nameOrPhone);

    List<SzmCOrderMain> selectOrderSumByStoreIdList(@Param("storeId") Long storeId,
                                                    @Param("payType") Integer payType,
                                                    @Param("userType") Integer userType,
                                                    @Param("nameOrPhone") String nameOrPhone,
                                                    @Param("startTime") Date startTime,
                                                    @Param("endTime") Date endTime);


    /**
     * 商品数量
     * @param storeId
     * @param payType
     * @param userType
     * @param nameOrPhone
     * @param startTime
     * @param endTime
     * @return
     */
    Integer selectOrderSumByStoreIdListCountNewNew(@Param("storeId") Long storeId,
                                                    @Param("payType") Integer payType,
                                                    @Param("userType") Integer userType,
                                                    @Param("nameOrPhone") String nameOrPhone,
                                                    @Param("startTime") Date startTime,
                                                    @Param("endTime") Date endTime);


    /**
     * 查看单商品的数量
     * @param storeId
     * @param payType
     * @param userType
     * @param nameOrPhone
     * @param startTime
     * @param endTime
     * @return
     */
    Integer selectOrderDeatilNum(@Param("storeId") Long storeId,
                                                   @Param("payType") Integer payType,
                                                   @Param("userType") Integer userType,
                                                   @Param("nameOrPhone") String nameOrPhone,
                                                   @Param("startTime") Date startTime,
                                                   @Param("endTime") Date endTime);


    /**
     * 查看套餐的数量
     * @param storeId
     * @param payType
     * @param userType
     * @param nameOrPhone
     * @param startTime
     * @param endTime
     * @return
     */
    Integer selectOrderGroupNum(@Param("storeId") Long storeId,
                                 @Param("payType") Integer payType,
                                 @Param("userType") Integer userType,
                                 @Param("nameOrPhone") String nameOrPhone,
                                 @Param("startTime") Date startTime,
                                 @Param("endTime") Date endTime);


    /**
     * 订单金额
     * @param storeId
     * @param payType
     * @param userType
     * @param nameOrPhone
     * @param startTime
     * @param endTime
     * @return
     */
    Double selectOrderSumByStoreIdMoney(@Param("storeId") Long storeId,
                                                    @Param("payType") Integer payType,
                                                    @Param("userType") Integer userType,
                                                    @Param("nameOrPhone") String nameOrPhone,
                                                    @Param("startTime") Date startTime,
                                                    @Param("endTime") Date endTime);
    List<StoreDoubleValueVo> selectOrderSumByStoreIdMoneyByStoreIds(@Param("storeIds") List<Long> storeIds,
                                                    @Param("payType") Integer payType,
                                                    @Param("userType") Integer userType,
                                                    @Param("nameOrPhone") String nameOrPhone,
                                                    @Param("startTime") Date startTime,
                                                    @Param("endTime") Date endTime);

    List<PayInfo> selectOrderPayInfo(TurnoverVO tv);

    List<PayInfo> selectOrderPayAllInfo(TurnoverVO tv);

    List<PayInfo> selectShopOrderPayInfo(TurnoverVO tv);

    List<PayInfo> selectFillInPayInfo(TurnoverVO tv);

    List<PayInfo> selectServicePayInfo(TurnoverVO tv);

    List<PayInfo> selectTicketPayInfo(TurnoverVO tv);

    List<PayInfo> selectPledgeBuckAmountInfo(TurnoverVO tv);

    List<PayInfo> selectBackBuckAmountInfo(TurnoverVO tv);

    List<PayInfo> selectDeductionBuckMoneyInfo(TurnoverVO tv);

    List<PayInfo> selectDiffPriceInfo(TurnoverVO tv);

    List<PayInfo> selectDiffPricePayInfo(TurnoverVO tv);

    /**
     * 回桶补差价
     * @param storeId
     * @param payType
     * @param userType
     * @param nameOrPhone
     * @param startTime
     * @param endTime
     * @return
     */
    Double selectOrderRepayMoney(@Param("storeId") Long storeId,
                                        @Param("payType") Integer payType,
                                        @Param("userType") Integer userType,
                                        @Param("nameOrPhone") String nameOrPhone,
                                        @Param("startTime") Date startTime,
                                        @Param("endTime") Date endTime);


    /**
     * 退桶补差价
     * @param storeId
     * @param payType
     * @param userType
     * @param nameOrPhone
     * @param startTime
     * @param endTime
     * @return
     */
    Double selectOrderBucketMoney(@Param("storeId") Long storeId,
                                 @Param("payType") Integer payType,
                                 @Param("userType") Integer userType,
                                 @Param("nameOrPhone") String nameOrPhone,
                                 @Param("startTime") Date startTime,
                                 @Param("endTime") Date endTime);


    /**
     * 查看桶押金
     * @param storeId
     * @param payType
     * @param userType
     * @param nameOrPhone
     * @param startTime
     * @param endTime
     * @return
     */
    Double selectPledgMoney(@Param("storeId") Long storeId,
                                  @Param("payType") Integer payType,
                                  @Param("userType") Integer userType,
                                  @Param("nameOrPhone") String nameOrPhone,
                                  @Param("startTime") Date startTime,
                                  @Param("endTime") Date endTime);

    Integer selectOrderSumByStoreIdListCount(@Param("storeId") Long storeId, @Param("payType") Integer payType, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    Double selectOrderSumByStoreIdListSum(@Param("storeId") Long storeId, @Param("payType") Integer payType, @Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("userType") Integer userType, @Param("nameOrPhone") String nameOrPhone);

    List<String> selectOrderSumByStoreIdListOrderNum(@Param("storeId") Long storeId, @Param("payType") Integer payType, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    List<SzmCOrderMain> last(Long userId);


    Double selectStoreBusinessMoney(@Param("storeId") Long storeId, @Param("orderState") Integer orderState, @Param("stateTime") Date stateTime, @Param("endTime") Date endTime);


    Integer selectStoreTodayCount(@Param("storeId") Long storeId, @Param("stateTime") Date stateTime, @Param("endTime") Date endTime);


    Double selectStoreTodayMoneyCount(@Param("storeId") Long storeId, @Param("stateTime") Date stateTime, @Param("endTime") Date endTime);

    Integer selectDeliveryCountNew(@Param("storeId") Long storeId, @Param("stateTime") Date stateTime, @Param("endTime") Date endTime);


    Integer selectReturnOrderCount(@Param("storeId") Long storeId, @Param("stateTime") Date stateTime, @Param("endTime") Date endTime);

    Double selectcdMoneyCount(@Param("userId") Long userId, @Param("storeId") Long storeId);

    Double selectCountNew(@Param("userId") Long userId, @Param("storeId") Long storeId);


    Integer selectCountCount(@Param("storeId") Long storeId, @Param("stateTime") Date stateTime, @Param("endTime") Date endTime);


    SzmCOrderMain selectLastTimeOrder(@Param("userId") Long userId, @Param("storeId") Long storeId,@Param("orderId") Long orderId);

    List<SzmCOrderMain> selectOrderByStoreIdAndDate(@Param("storeId") Long storeId, @Param("stateTime") Date stateTime, @Param("endTime") Date endTime);

    List<SzmCOrderMain> selectTodayOrderByStoreIdAndDate(@Param("storeId") Long storeId, @Param("stateTime") Date stateTime, @Param("endTime") Date endTime);

    List<SzmCOrderMain> selectYiSongDaOrderByStoreId(@Param("storeId") Long storeId);

    List<SzmCOrderMain> selectBuckOrderByStoreId(@Param("storeId") Long storeId);

    SzmCOrderMain selectOneOrderNumber(@Param("userId") Long userId);

    List<String> selectRepayList(@Param("storeId") Long storeId,
                                 @Param("userId") Long userId,
                                 @Param("stateTime") Date stateTime,
                                 @Param("endTime") Date endTime,
                                 @Param("payState") Integer payState,
                                 @Param("deliveryId") Long deliveryId);


    List<Long> selectUserYfList(@Param("storeId") Long storeId,
                                @Param("name") String name,
                                @Param("stateTime") Date stateTime,
                                @Param("endTime") Date endTime,
                                @Param("state") Integer state,
                                @Param("userId") Long userId);



    Double selectUserYfListMoney(@Param("storeId") Long storeId,
                                @Param("name") String name,
                                @Param("stateTime") Date stateTime,
                                @Param("endTime") Date endTime,
                                @Param("state") Integer state,
    @Param("userId") Long userId);


    /**
     * 查看月结金额
     * @param storeId
     * @param name
     * @param stateTime
     * @param endTime
     * @param state
     * @param userId
     * @return
     */
    Double selectUserYfListMoneyNew(@Param("storeId") Long storeId,
                                 @Param("name") String name,
                                 @Param("stateTime") Date stateTime,
                                 @Param("endTime") Date endTime,
                                 @Param("state") Integer state,
                                 @Param("userId") Long userId);


    Double selectUserYfMoney(@Param("storeId") Long storeId,
                             @Param("userId") Long userId,
                             @Param("stateTime") Date stateTime,
                             @Param("endTime") Date endTime);

    List<Map> selectBankTransfer(@Param("userId") Long userId, @Param("isPay")Integer isPay,@Param("stateTime") Date stateTime, @Param("endTime") Date endTime);

    List<Map> selectBankTransferC(@Param("userId") Long userId, @Param("isPay")Integer isPay,@Param("stateTime") Date stateTime, @Param("endTime") Date endTime);

    Integer selectBankTransferNumberSum(@Param("userId") Long storeId, @Param("isPay")Integer isPay,@Param("stateTime") Date stateTime, @Param("endTime") Date endTime);

    Double selectBankTransferPriceSum(@Param("userId") Long storeId, @Param("isPay")Integer isPay,@Param("wire")Integer wire,@Param("stateTime") Date stateTime, @Param("endTime") Date endTime);

    Double selectBankTransferYetPriceSum(@Param("userId") Long storeId, @Param("isPay")Integer isPay,@Param("wire")Integer wire,@Param("stateTime") Date stateTime, @Param("endTime") Date endTime);

    Double selectBankTransferYetPriceHeadSum(@Param("userId") Long storeId, @Param("isPay")Integer isPay,@Param("wire")Integer wire,@Param("stateTime") Date stateTime, @Param("endTime") Date endTime);

    Double selectBankTransferPriceAllSum(@Param("userId") Long storeId);

    Double selectBankTransferYetPriceAllSum(@Param("userId") Long storeId);

    List<SzmCOrderMain> selectQuanOrder();

    Integer selectYiSongDaOrderByStoreIdCount(@Param("storeId") Long storeId,@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    Double selectYiSongDaOrderByStoreIdSum(@Param("storeId") Long storeId,@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    Double selectTodayOrderByStoreIdAndDateSum(@Param("storeId") Long storeId, @Param("stateTime") Date stateTime, @Param("endTime") Date endTime);

    Double selectBankTransferPriceAllSumC(@Param("userId") Long storeId);

    Double selectBankTransferYetPriceAllSumC(@Param("userId") Long storeId);


    Integer selectListUserTotal(@Param("storeId") Long storeId, @Param("userId") Long userId);





    Integer selectBankTransferNumberCount(@Param("storeId") Long storeId, @Param("isPay")Integer isPay,@Param("userId") Long userId,@Param("stateTime") Date stateTime, @Param("endTime") Date endTime);


    SzmCOrderMain selectOtherByOrderNum(@Param("orderNum") String orderNum);

    List<SzmCOrderMain> selectOtherByPaymentModeId(@Param("userId") Long userId, @Param("storeId") Long storeId, @Param("payMentId") Long payMentId);

    List<SzmCOrderMain> selectAllListYf();

    List<Long> selectUserIdByStoreIdAndDate(@Param("storeId") Long storeId, @Param("stateTime") Date stateTime, @Param("endTime") Date endTime);

    Integer selectstoreOrderCount(SzmCOrderMain szmCOrderMain);

    SzmCOrderMain selectOtherByOrderNum1(@Param("orderNum") String orderNum);

    String selectOtherR4ByOrderNum(@Param("orderNum") String orderNum);

    List<Map> selectThreeCollect(@Param("userId") Long userId);

    List<BankTransferApp> selectStoreUser(@Param("storeId") Long storeId);

    BankTransferApp selectBankTransferPriceSumApp(@Param("storeId") Long storeId,@Param("userId") Long userId);

    List<BankTransferApp> selectStoreUserList(@Param("storeId") Long storeId,@Param("concat") String concat,@Param("stateTime") Date stateTime, @Param("endTime") Date endTime,@Param("moneySort") Integer moneySort);

    BankTransferApp userUnBankTransferTotal(@Param("userId") Long userId,@Param("stateTime") Date stateTime, @Param("endTime") Date endTime);

    BankTransferApp userAcBankTransferTotal(@Param("userId") Long userId,@Param("stateTime") Date stateTime, @Param("endTime") Date endTime);

    BankTransferApp userReBankTransferTotal(@Param("userId") Long userId,@Param("stateTime") Date stateTime, @Param("endTime") Date endTime);

    BankTransferApp selectUser(@Param("userId") Long userId);

    List<BankTransferApp> selectBankTransferApp(@Param("userId") Long userId,@Param("affirm")Integer affirm);

    BankTransferApp singlePersonvalet(@Param("orderNum") String orderNum);

    BankTransferApp merchantValet(@Param("orderNum") String orderNum);

    BankTransferApp pledgeBucket(@Param("orderNum") String orderNum);

    List<BankTransferApp> selectPledgeBucket(@Param("orderNum") String orderNum);

    List<BankTransferApp> selectWaterTicket(@Param("orderNum") String orderNum);

    BankTransferApp selectSubscript(@Param("userId") Long userId);

    Integer selectSubscriptByStoreId(@Param("storeId") Long storeId);

    List<BankTransferApp> selectuserShopList(@Param("orderNum") String orderNum);

    BankTransferApp confirmCollection(@Param("orderNum") String orderNum);

    int updateIsBankAffirm(@Param("orderNum") String orderNum,@Param("isBankAffirm") Integer isBankAffirm);

    List<BankTransferApp> selecComboPackage(@Param("orderNum") String orderNum);

    Integer selectCustomerSubscript(@Param("storeId") Long storeId);

    List<SzmCOrderMain> selectOrderListByStoreId(Long storeId);

    List<SzmCOrderMain> selectNoDeal(@Param("storeId") Long storeId);
    List<SzmCOrderMain> selectYfNoDeal(@Param("storeId") Long storeId);

    List<BankTransferApp> selectStoreOrderDetails(@Param("storeId") Long storeId,@Param("userId") Long userId,@Param("affirm") Integer affirm,@Param("concat") String concat,@Param("stateTime") Date stateTime, @Param("endTime") Date endTime);

    List<SzmCOrderMain> selectByOrderNums(@Param("orderNum") List<String> orderNum);

    void updateUserId(@Param("userId") Long userId, @Param("newUserId") Long newUserId);

    Integer countByCuidan(@Param("storeId") Long storeId,@Param("cuidan") Integer cuidan);

    List<SzmCOrderMain> selectByCuidan(@Param("storeId") Long storeId,@Param("cuidan") Integer cuidan);
    SzmCOrderMain selectZitiByMobile(@Param("mobile") String mobile,@Param("orderId") Long orderId);

    List<RetuenOrderList> querysmcorderAll(Map<String,Object> map);

    List<SzmCOrderMain> selectByUserIdAndStoreIdAndPayTimeDikou(@Param("userId") Long userId,@Param("storeId") Long storeId,@Param("payTime") String payTime);

    List<SzmCOrderMain> findTransferOrderNoArrivedByTime(@Param("startTime") Date startTime);

    List<SzmCOrderMain> selectByStoreIdAndUserNameLikeAndUserContentLike(@Param("storeId") Long storeId, @Param("userName") String userName, @Param("userContent") String userContent);

    // ==================== 订单来源关联查询方法 ====================

    /**
     * 根据主键查询订单信息（包含订单来源名称和图标）
     */
    OrderWithSourceVO selectOrderWithSourceByPrimaryKey(@Param("orderMainId") Long orderMainId);

    /**
     * 根据订单号查询订单信息（包含订单来源名称和图标）
     */
    OrderWithSourceVO selectOrderWithSourceByOrderNum(@Param("orderNum") String orderNum);

    /**
     * 查询店铺订单列表（包含订单来源名称和图标）
     */
    List<OrderWithSourceVO> selectOrdersWithSourceByStoreId(@Param("storeId") Long storeId,
                                                           @Param("userId") Long userId,
                                                           @Param("addressId") Long addressId,
                                                           @Param("username") String username,
                                                           @Param("startTime") String startTime,
                                                           @Param("endTime") String endTime,
                                                           @Param("ordersource") Integer ordersource,
                                                           @Param("deliveryUserId") Long deliveryUserId,
                                                           @Param("startTimeFinish") String startTimeFinish,
                                                           @Param("endTimeFinish") String endTimeFinish,
                                                           @Param("orderStatus") Integer orderStatus);

    /**
     * 查询用户订单列表（包含订单来源名称和图标）
     */
    List<OrderWithSourceVO> selectOrdersWithSourceByUserId(@Param("userId") Long userId);

    /**
     * 分页查询订单列表（包含订单来源名称和图标）
     */
    List<OrderWithSourceVO> selectOrdersWithSourceByPage(@Param("offset") Integer offset,
                                                        @Param("limit") Integer limit,
                                                        @Param("storeId") Long storeId,
                                                        @Param("orderStatus") Integer orderStatus,
                                                        @Param("ordersource") Integer ordersource,
                                                        @Param("startTime") String startTime,
                                                        @Param("endTime") String endTime);

        int updatemark(@Param("orderMainId") Long orderMainId, @Param("mark") String mark);
}