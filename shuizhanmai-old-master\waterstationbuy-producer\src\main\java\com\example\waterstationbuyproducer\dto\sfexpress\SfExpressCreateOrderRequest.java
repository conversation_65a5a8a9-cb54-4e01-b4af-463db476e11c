package com.example.waterstationbuyproducer.dto.sfexpress;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * SF Express Create Order Request
 * 顺丰快递下订单请求对象
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
public class SfExpressCreateOrderRequest implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * Language
     * 语言
     */
    private String language = "zh-cn";
    
    /**
     * Order ID (Customer Order Number)
     * 订单ID（客户订单号）
     */
    @JsonProperty("orderId")
    private String orderId;
    
    /**
     * Express Type
     * 快件类型
     * 1:标准快递 2:顺丰特惠 3:电商特惠 5:顺丰次晨 6:顺丰即日 7:电商速配 15:生鲜速配
     */
    @JsonProperty("expressTypeId")
    private Integer expressTypeId = 1;

    
    @JsonProperty("parcelQty")
    private Integer parcelQty = 1;
    
    /**
     * Pay Method
     * 付款方式
     * 1:寄方付 2:收方付 3:第三方付
     */
    @JsonProperty("payMethod")
    private Integer payMethod = 1;
    @JsonProperty("monthlyCard")
    private String monthlyCard = "0211347247";
    
    /**
     * Is Document
     * 是否文件
     * 1:文件 0:非文件
     */
    @JsonProperty("isDocall")
    private Integer isDocall = 0;

    @JsonProperty("remark")
    private String remark = "电话号码有效期7天，超期异常联系门店客服电话";
    
    /**
     * Sender Info
     * 寄件人信息
     */
    @JsonProperty("contactInfoList")
    private List<ContactInfo> contactInfoList;
    
    /**
     * Cargo Details
     * 货物详情
     */
    @JsonProperty("cargoDetails")
    private List<CargoDetail> cargoDetails;
    
    /**
     * Contact Info
     * 联系人信息
     */
    @Data
    public static class ContactInfo implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        /**
         * Contact Type
         * 联系人类型
         * 1:寄件方 2:收件方 3:付款方
         */
        @JsonProperty("contactType")
        private Integer contactType;
        
        /**
         * Company
         * 公司名称
         */
        @JsonProperty("company")
        private String company;
        
        /**
         * Contact
         * 联系人
         */
        @JsonProperty("contact")
        private String contact;
        
        /**
         * Tel
         * 电话
         */
        @JsonProperty("tel")
        private String tel;
        
        /**
         * Mobile
         * 手机
         */
        @JsonProperty("mobile")
        private String mobile;
        
        /**
         * Province
         * 省份
         */
        @JsonProperty("province")
        private String province;
        
        /**
         * City
         * 城市
         */
        @JsonProperty("city")
        private String city;
        
        /**
         * County
         * 区/县
         */
        @JsonProperty("county")
        private String county;
        
        /**
         * Address
         * 详细地址
         */
        @JsonProperty("address")
        private String address;
    }
    
    /**
     * Cargo Detail
     * 货物详情
     */
    @Data
    public static class CargoDetail implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        /**
         * Cargo Name
         * 货物名称
         */
        @JsonProperty("name")
        private String name;
        
        /**
         * Count
         * 数量
         */
        @JsonProperty("count")
        private Integer count;
        
        /**
         * Unit
         * 单位
         */
        @JsonProperty("unit")
        private String unit;
        
        /**
         * Weight
         * 重量(kg)
         */
        @JsonProperty("weight")
        private Double weight;
        
        /**
         * Amount
         * 货物价值
         */
        @JsonProperty("amount")
        private Double amount;
        
        /**
         * Currency
         * 货币
         */
        @JsonProperty("currency")
        private String currency = "CNY";
    }
}
