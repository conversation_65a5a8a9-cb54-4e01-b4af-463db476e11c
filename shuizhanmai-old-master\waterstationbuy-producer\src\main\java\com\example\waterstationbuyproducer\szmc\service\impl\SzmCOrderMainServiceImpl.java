/**
 * @姓名 于瑞杰
 * @版本号 1.1.4
 * @日期 2019/6/25
 */
package com.example.waterstationbuyproducer.szmc.service.impl;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.Map;
import java.util.HashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.NumberUtil;
import net.coobird.thumbnailator.Thumbnails;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.example.waterstationbuyproducer.emun.FenxiaoUserTypeEnum;
import com.example.waterstationbuyproducer.entity.*;
import com.example.waterstationbuyproducer.enums.UserTypeEnum;
import com.example.waterstationbuyproducer.exception.ServiceBizException;
import com.example.waterstationbuyproducer.szmb.export.OrderMainExportService;
import com.example.waterstationbuyproducer.szmb.service.config.ConfigService;
import com.example.waterstationbuyproducer.szmb.service.hz.order.SzmBOrderService;
import com.example.waterstationbuyproducer.szmb.service.yq.SelectClassService;
import com.example.waterstationbuyproducer.szmb.service.yq.SzmbVipPriceService;
import com.example.waterstationbuyproducer.szmb.vo.yq.ShopGroupSkuAll;
import com.example.waterstationbuyproducer.szmc.controller.newwx.util.*;
import com.example.waterstationbuyproducer.szmc.service.*;
import com.example.waterstationbuyproducer.dao.*;
import com.example.waterstationbuyproducer.szmc.service.y2j.ordermain.OrderMainService;
import com.example.waterstationbuyproducer.szmc.service.y2j.vo.FrimOrderListVo;
import com.example.waterstationbuyproducer.util.*;
import com.example.waterstationbuyproducer.util.StoreIdUtil;
import com.example.waterstationbuyproducer.util.logger.LoggerUtil;
import com.example.waterstationbuyproducer.util.rabbit.RabbitMqUtil;
import com.example.waterstationbuyproducer.util.redis.RedisUtil;
import com.example.waterstationbuyproducer.util.sms.RemindSMS;
import com.example.waterstationbuyproducer.util.sms.UtilSMS;
import com.example.waterstationbuyproducer.util.wx.IpUtils;
import com.example.waterstationbuyproducer.util.wx.weixin.config.WxPayConfig;
import com.example.waterstationbuyproducer.vo.*;
import com.example.waterstationbuyproducer.vo.ordermain.BucketPriceVo;
import com.example.waterstationbuyproducer.vo.ordermain.SzmCOrderMainVo;
import com.example.waterstationbuyproducer.wx.WeiXinCommonService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.thoughtworks.xstream.XStream;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;
import org.xml.sax.SAXException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @版本号 1.1.4
 * @description 订单表业务层实现
 * @date 2019/3/8
 */
@Service
public class SzmCOrderMainServiceImpl implements SzmCOrderMainService {
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private SzmCOrderMainMapper szmCOrderMainMapper;
    @Autowired
    private SmzCOrderDetailsMapper smzCOrderDetailsMapper;
    @Autowired
    private SmzCGroupOrderMapper smzCGroupOrderMapper;
    @Autowired
    private RabbitTemplate rabbitTemplate;
    @Autowired
    private SzmCProductModelMapper szmCProductModelMapper;
    @Autowired
    private SzmCShopgroupMapper szmCShopgroupMapper;
    @Autowired
    private SmzCIntegralWaterMapper smzCIntegralWaterMapper;
    @Autowired
    private SzmCUserinfoMapper szmCUserinfoMapper;
    @Autowired
    private SzmCProductMapper szmCProductMapper;
    @Autowired
    private SzmCShopgroupListMapper szmCShopgroupListMapper;
    @Autowired
    private SzmCDistributionMapper szmCDistributionMapper;
    @Autowired
    private SmzCPaymentModeMapper smzCPaymentModeMapper;
    @Autowired
    private SzmCCartMainService szmCCartMainService;
    @Autowired
    private SzmCTimeToBuyMapper szmCTimeToBuyMapper;
    @Autowired
    private SmzCLimitToBuyToBuyMapper smzCLimitToBuyToBuyMapper;
    @Autowired
    private SzmCCartMainMapper szmCCartMainMapper;
    @Autowired
    private SmzCWcUseService smzCWcUseService;
    @Autowired
    private SmzCFundWatercourseService smzCFundWatercourseService;
    @Autowired
    private SzmCUserBillsMapper szmCUserBillsMapper;
    @Autowired
    private SmzCHistoryOrderMapper smzCHistoryOrderMapper;
    @Autowired
    private SzmCUserConsumeMapper szmCUserConsumeMapper;
    @Autowired
    private SzmCMyMsgService szmCMyMsgService;
    @Autowired
    private SzmCProductVipGradeMapper szmCProductVipGradeMapper;
    @Autowired
    private SzmbVipPriceService szmbVipPriceService;
    @Autowired
    private SzmCUserMapper szmCUserMapper;
    @Autowired
    private SzmCStoreMapper szmCStoreMapper;
    @Autowired
    private SzmBWalletDetailMapper szmBWalletDetailMapper;
    @Autowired
    private SmzCOrderReturnsMapper smzCOrderReturnsMapper;
    @Autowired
    private SmzCFundWatercourseMapper smzCFundWatercourseMapper;
    @Autowired
    private SzmCProductDiscussMapper szmCProductDiscussMapper;
    @Autowired
    private ExtractMapper extractMapper;
    @Autowired
    private StoreSmsInfoMapper storeSmsInfoMapper;
    @Autowired
    private SmsRelevanceMapper smsRelevanceMapper;
    @Autowired
    private SmsRecordMapper smsRecordMapper;
    @Autowired
    private SmsMasterMapper smsMasterMapper;
    @Autowired
    private SmzCDeliveryInfoMapper smzCDeliveryInfoMapper;
    @Autowired
    private SmzCDeliveryUserMapper smzCDeliveryUserMapper;
    @Autowired
    private DeductCashInfoMapper deductCashInfoMapper;
    @Autowired
    private DeliveryRelevanceMapper deliveryRelevanceMapper;
    @Autowired
    private DeliverybussinessMapper deliverybussinessMapper;
    @Autowired
    private DeliveryDeductDeatilMapper deliveryDeductDeatilMapper;
    @Autowired
    private WcRelevanceMapper wcRelevanceMapper;
    @Autowired
    private SmzCWaterCouponMapper smzCWaterCouponMapper;
    @Autowired
    private RabbitMqUtil rabbitMqUtil;
    @Autowired
    private WcUseRecordMapper wcUseRecordMapper;
    @Autowired
    private OrderMainService orderMainService;
    @Autowired
    private SzmCAddressMapper szmCAddressMapper;
    @Autowired
    private CDShareOrderMapper cdShareOrderMapper;
    @Autowired
    private CommercialDelegateMapper commercialDelegateMapper;
    @Autowired
    private StoreBusinessMapper storeBusinessMapper;
    @Autowired
    private InvoiceOrderMapper invoiceOrderMapper;
    @Autowired
    private PledgeBuckOrderMapper pledgeBuckOrderMapper;
    @Autowired
    private WeiXinCommonService weiXinCommonService;

    @Autowired
    private SzmCProductClassifyMapper szmCProductClassifyMapper;
    @Autowired
    private ClerkUserMapper clerkUserMapper;
    @Autowired
    private ClerkShareUserMapper clerkShareUserMapper;
    @Autowired
    private ClerkMoneyRecordMapper clerkMoneyRecordMapper;
    @Autowired
    private OrderYfMapper orderYfMapper;
    @Autowired
    private SzmCStoreApplyForMapper szmCStoreApplyForMapper;
    @Autowired
    private MarketActivityOrderMapper marketActivityOrderMapper;
    @Autowired
    private MarketActivityGoodsMapper marketActivityGoodsMapper;
    @Autowired
    private OrderMainExportService orderMainExportService;
    @Autowired
    private SzmCUserBalanceMapper szmCUserBalanceMapper;
    @Autowired
    private SzmCWalletDetailMapper szmCWalletDetailMapper;
    @Autowired
    private DrainageUserMapper drainageUserMapper;
    @Autowired
    private SzmBOrderService szmBOrderService;
    @Autowired
    private ConfigService configService;
    @Autowired
    private SelectClassService selectClassService;
    @Autowired
    private OrderSourceMapper orderSourceMapper;
    @Autowired
    private StoreMsgMapper storeMsgMapper;
    @Autowired
    private SzmCMyMsgMapper szmCMyMsgMapper;
    @Autowired
    private ProvincesMapper provincesMapper;
    @Autowired
    private CitiesMapper citiesMapper;
    @Autowired
    private AreasMapper areasMapper;
    @Autowired
    private StoreIdUtil storeIdUtil;

    /**
     * 订单数据准备结果类
     */
    public static class OrderDataPreparationResult {
        private Map<Long, SzmCStore> szmCStoresMap;
        private Map<String, List<SmzCGroupOrder>> groupOrderMap;
        private Map<String, List<SmzCOrderDetails>> orderDetailMap;
        private Map<Long, String> paymentModeMap;
        private Map<Long, SzmCUser> szmCuserMap;
        private Map<Long, LongIntegerVo> pledgeBuckOrderMap;
        private Map<Integer, OrderSource> orderSourceMap;
        private Map<Long, SmzCDeliveryUser> deliveryUserMap;
        private Map<String, SmzCOrderReturns> orderReturnsMap;
        private Map<Long, SmzCDeliveryInfo> deliveryInfoMap;

        // Getters and Setters
        public Map<Long, SzmCStore> getSzmCStoresMap() { return szmCStoresMap; }
        public void setSzmCStoresMap(Map<Long, SzmCStore> szmCStoresMap) { this.szmCStoresMap = szmCStoresMap; }

        public Map<String, List<SmzCGroupOrder>> getGroupOrderMap() { return groupOrderMap; }
        public void setGroupOrderMap(Map<String, List<SmzCGroupOrder>> groupOrderMap) { this.groupOrderMap = groupOrderMap; }

        public Map<String, List<SmzCOrderDetails>> getOrderDetailMap() { return orderDetailMap; }
        public void setOrderDetailMap(Map<String, List<SmzCOrderDetails>> orderDetailMap) { this.orderDetailMap = orderDetailMap; }

        public Map<Long, String> getPaymentModeMap() { return paymentModeMap; }
        public void setPaymentModeMap(Map<Long, String> paymentModeMap) { this.paymentModeMap = paymentModeMap; }

        public Map<Long, SzmCUser> getSzmCuserMap() { return szmCuserMap; }
        public void setSzmCuserMap(Map<Long, SzmCUser> szmCuserMap) { this.szmCuserMap = szmCuserMap; }

        public Map<Long, LongIntegerVo> getPledgeBuckOrderMap() { return pledgeBuckOrderMap; }
        public void setPledgeBuckOrderMap(Map<Long, LongIntegerVo> pledgeBuckOrderMap) { this.pledgeBuckOrderMap = pledgeBuckOrderMap; }

        public Map<Integer, OrderSource> getOrderSourceMap() { return orderSourceMap; }
        public void setOrderSourceMap(Map<Integer, OrderSource> orderSourceMap) { this.orderSourceMap = orderSourceMap; }

        public Map<Long, SmzCDeliveryUser> getDeliveryUserMap() { return deliveryUserMap; }
        public void setDeliveryUserMap(Map<Long, SmzCDeliveryUser> deliveryUserMap) { this.deliveryUserMap = deliveryUserMap; }

        public Map<String, SmzCOrderReturns> getOrderReturnsMap() { return orderReturnsMap; }
        public void setOrderReturnsMap(Map<String, SmzCOrderReturns> orderReturnsMap) { this.orderReturnsMap = orderReturnsMap; }

        public Map<Long, SmzCDeliveryInfo> getDeliveryInfoMap() { return deliveryInfoMap; }
        public void setDeliveryInfoMap(Map<Long, SmzCDeliveryInfo> deliveryInfoMap) { this.deliveryInfoMap = deliveryInfoMap; }
    }

    /**
     * 准备订单相关数据
     *
     * @param szmCOrderMains 订单主表列表
     * @return 准备好的数据映射
     */
    private OrderDataPreparationResult prepareOrderData(List<SzmCOrderMain> szmCOrderMains) {
        OrderDataPreparationResult result = new OrderDataPreparationResult();

        // 提取基础ID列表
        List<Long> storeIds = szmCOrderMains.stream().map(SzmCOrderMain::getStoreId).distinct()
                .collect(Collectors.toList());
        List<String> orderNums = szmCOrderMains.stream().map(SzmCOrderMain::getOrderNum).collect(Collectors.toList());
        List<Long> userIds = szmCOrderMains.stream().map(SzmCOrderMain::getUserId).distinct()
                .collect(Collectors.toList());
        List<Long> orderMainIds = szmCOrderMains.stream().map(SzmCOrderMain::getOrderMainId)
                .collect(Collectors.toList());
        List<Long> deliveryUserIds = szmCOrderMains.stream().map(SzmCOrderMain::getDeliveryInfoId).distinct()
                .collect(Collectors.toList());

        // 查询基础数据
        List<SmzCGroupOrder> smzCGroupOrders = CollectionUtils.isEmpty(orderNums) ? new ArrayList<>()
                : smzCGroupOrderMapper.selectByOrderNums(orderNums);
        List<SzmCStore> szmCStores = CollectionUtils.isEmpty(storeIds) ? new ArrayList<>()
                : szmCStoreMapper.findByIds(storeIds);
        List<SmzCOrderDetails> smzCOrderDetails = CollectionUtils.isEmpty(orderNums) ? new ArrayList<>()
                : smzCOrderDetailsMapper.selectByOrderNums(orderNums);
        List<SzmCUser> szmCUsers = CollectionUtils.isEmpty(userIds) ? new ArrayList<>()
                : szmCUserMapper.findByIds(userIds);
        List<LongIntegerVo> pledgeBuckOrderList = CollectionUtils.isEmpty(orderMainIds) ? new ArrayList<>()
                : pledgeBuckOrderMapper.selectPledgCountByOrderNumbers(orderMainIds);
        List<SmzCDeliveryUser> deliveryUsers = CollectionUtils.isEmpty(deliveryUserIds) ? new ArrayList<>()
                : smzCDeliveryUserMapper.selectByIds(deliveryUserIds);

        // 预先查询退货信息和配送信息，避免在lookOrderDeatil中进行数据库查询
        List<SmzCOrderReturns> orderReturnsList = CollectionUtils.isEmpty(orderNums) ? new ArrayList<>()
                : smzCOrderReturnsMapper.selectByOrderNums(orderNums);
        List<SmzCDeliveryInfo> deliveryInfoList = CollectionUtils.isEmpty(orderMainIds) ? new ArrayList<>()
                : smzCDeliveryInfoMapper.selectByOrderMainIds(orderMainIds);

        // 处理店铺地区信息
        processStoreAreaInfo(szmCStores);

        // 查询支付方式和订单来源
        List<SmzCPaymentMode> smzCPaymentModes = smzCPaymentModeMapper.selectAll();
        Map<Integer, OrderSource> orderSourceMap = orderSourceMapper.selectAll().stream()
                .collect(Collectors.toMap(OrderSource::getId, orderSource -> orderSource));

        // 构建映射关系
        result.setSzmCStoresMap(szmCStores.stream()
                .collect(Collectors.toMap(SzmCStore::getStoreId, b -> b)));
        result.setGroupOrderMap(smzCGroupOrders.stream()
                .collect(Collectors.groupingBy(SmzCGroupOrder::getOrderMainId, Collectors.toList())));
        result.setOrderDetailMap(smzCOrderDetails.stream()
                .collect(Collectors.groupingBy(SmzCOrderDetails::getOrderMainId, Collectors.toList())));
        result.setPaymentModeMap(smzCPaymentModes.stream()
                .collect(Collectors.toMap(SmzCPaymentMode::getPaymentModeId, SmzCPaymentMode::getPaymentModeName)));
        result.setSzmCuserMap(szmCUsers.stream().collect(Collectors.toMap(SzmCUser::getUserId, b -> b)));
        result.setPledgeBuckOrderMap(pledgeBuckOrderList.stream()
                .collect(Collectors.toMap(LongIntegerVo::getId, b -> b)));
        result.setOrderSourceMap(orderSourceMap);
        result.setDeliveryUserMap(deliveryUsers.stream()
                .collect(Collectors.toMap(SmzCDeliveryUser::getDeliveryUserId, b -> b)));
        result.setOrderReturnsMap(orderReturnsList.stream()
                .collect(Collectors.toMap(SmzCOrderReturns::getOrderDetailsId, b -> b)));
        result.setDeliveryInfoMap(deliveryInfoList.stream()
                .collect(Collectors.toMap(SmzCDeliveryInfo::getOrderMainId, b -> b)));

        return result;
    }

    /**
     * 处理店铺地区信息
     *
     * @param szmCStores 店铺列表
     */
    private void processStoreAreaInfo(List<SzmCStore> szmCStores) {
        if (CollectionUtils.isEmpty(szmCStores)) {
            return;
        }

        // 提取地区ID
        List<String> province = szmCStores.stream().map(SzmCStore::getStoreProvince).filter(Objects::nonNull).distinct()
                .collect(Collectors.toList());
        List<String> city = szmCStores.stream().map(SzmCStore::getStoreCity).filter(Objects::nonNull).distinct()
                .collect(Collectors.toList());
        List<String> area = szmCStores.stream().map(SzmCStore::getStoreArea).filter(Objects::nonNull).distinct()
                .collect(Collectors.toList());

        // 查询地区信息
        List<Provinces> provincesList = CollectionUtil.isEmpty(province) ? new ArrayList<>()
                : provincesMapper.selectprovinceids(province);
        List<Cities> citiesList = CollectionUtil.isEmpty(city) ? new ArrayList<>()
                : citiesMapper.selectByCityids(city);
        List<Areas> areasList = CollectionUtil.isEmpty(area) ? new ArrayList<>()
                : areasMapper.selectByareaids(area);

        // 设置地区名称
        szmCStores.forEach(store -> {
            provincesList.forEach(prov -> {
                if (prov.getProvinceid().equals(store.getStoreProvince())) {
                    store.setStoreProvinceName(prov.getProvince());
                }
            });
            citiesList.forEach(cityItem -> {
                if (cityItem.getCityid().equals(store.getStoreCity())) {
                    store.setStoreCityName(cityItem.getCity());
                }
            });
            areasList.forEach(areaItem -> {
                if (areaItem.getAreaid().equals(store.getStoreArea())) {
                    store.setStoreAreaName(areaItem.getArea());
                }
            });
        });
    }

    /**
     * 处理订单列表，构建返回结果
     *
     * @param szmCOrderMains 订单主表列表
     * @param allList 结果列表
     */
    private List<RetuenOrderList> processOrderList(List<SzmCOrderMain> szmCOrderMains) {
        
        List<RetuenOrderList> allList = new ArrayList<>();

        if (CollectionUtils.isEmpty(szmCOrderMains)) {
            return null;
        }

        // 准备所有相关数据
        OrderDataPreparationResult dataResult = prepareOrderData(szmCOrderMains);
        
        // 批量查询退货信息
        List<String> orderNums = szmCOrderMains.stream()
                .map(SzmCOrderMain::getOrderNum)
                .collect(Collectors.toList());
        
        List<SmzCOrderReturns> orderReturnsList = smzCOrderReturnsMapper.selectByOrderNums(orderNums);
        
        // 将退货信息按订单号分组
        Map<String, SmzCOrderReturns> orderReturnsMap = orderReturnsList.stream()
                .collect(Collectors.toMap(
                        SmzCOrderReturns::getOrderDetailsId,
                        Function.identity(),
                        (existing, replacement) -> replacement));

        // 处理每个订单
        for (SzmCOrderMain szmCOrderMain : szmCOrderMains) {
            // 获取相关数据
            List<SmzCGroupOrder> smzCGroupOrders1 = dataResult.getGroupOrderMap().get(szmCOrderMain.getOrderNum());
            List<SmzCOrderDetails> smzCOrderDetailsList = dataResult.getOrderDetailMap().get(szmCOrderMain.getOrderNum());

            // 构建订单详情 - 使用优化后的方法，避免数据库查询
            RetuenOrderList retuenOrderList = lookOrderDeatilOptimized(szmCOrderMain,
                    dataResult.getSzmCStoresMap().get(szmCOrderMain.getStoreId()),
                    smzCOrderDetailsList, smzCGroupOrders1,
                    dataResult.getPaymentModeMap(), dataResult.getOrderSourceMap(),
                    dataResult.getOrderReturnsMap(), dataResult.getDeliveryInfoMap());

            // 设置基本信息
            setBasicOrderInfo(szmCOrderMain, retuenOrderList);

            // 设置配送员信息
            setDeliveryInfo(szmCOrderMain, retuenOrderList, dataResult.getDeliveryUserMap());

            // 设置押金状态
            setPledgeStatus(szmCOrderMain, retuenOrderList, dataResult.getPledgeBuckOrderMap());

            // 设置用户手机号
            setUserMobile(szmCOrderMain, retuenOrderList, dataResult.getSzmCuserMap());

            // 设置订单状态相关信息
            setOrderStatusInfo(szmCOrderMain, retuenOrderList, orderReturnsMap);

            allList.add(retuenOrderList);
        }

        return allList;
    }

    /**
     * 设置订单基本信息
     */
    private void setBasicOrderInfo(SzmCOrderMain szmCOrderMain, RetuenOrderList retuenOrderList) {
        retuenOrderList.setUserName(szmCOrderMain.getUserName());
        retuenOrderList.setUserPhone(szmCOrderMain.getUserPhone());
        retuenOrderList.setUserAddress(szmCOrderMain.getUserAddress());
        retuenOrderList.setDikou(szmCOrderMain.getDikou());
        retuenOrderList.setZiti(szmCOrderMain.getZiti());
        retuenOrderList.setOrdersource(szmCOrderMain.getOrdersource());
        retuenOrderList.setUserContent(szmCOrderMain.getUserContent());
        retuenOrderList.setFinishTime(szmCOrderMain.getFinishTime());
        retuenOrderList.setDiscounPrice(szmCOrderMain.getOrderDiscounts());
    }

    /**
     * 设置配送员信息
     */
    private void setDeliveryInfo(SzmCOrderMain szmCOrderMain, RetuenOrderList retuenOrderList,
                                Map<Long, SmzCDeliveryUser> deliveryUserMap) {
        if (szmCOrderMain.getDeliveryInfoId() != null && szmCOrderMain.getDeliveryInfoId().equals(0L)) {
            retuenOrderList.setDeliveryName("水站自送");
            retuenOrderList.setDeliveryMobile("");
        } else {
            SmzCDeliveryUser smzCDeliveryUser = deliveryUserMap.get(szmCOrderMain.getDeliveryInfoId());
            if (null != smzCDeliveryUser) {
                retuenOrderList.setDeliveryName(smzCDeliveryUser.getDeliveryUserName());
                retuenOrderList.setDeliveryMobile(smzCDeliveryUser.getDeliveryUserPhone());
            }
        }
    }

    /**
     * 设置押金状态
     */
    private void setPledgeStatus(SzmCOrderMain szmCOrderMain, RetuenOrderList retuenOrderList,
                                Map<Long, LongIntegerVo> pledgeBuckOrderMap) {
        LongIntegerVo longIntegerVo = pledgeBuckOrderMap.get(szmCOrderMain.getOrderMainId());
        if (null != longIntegerVo) {
            retuenOrderList.setBuckState(longIntegerVo.getCount());
        }
    }

    /**
     * 设置用户手机号
     */
    private void setUserMobile(SzmCOrderMain szmCOrderMain, RetuenOrderList retuenOrderList,
                              Map<Long, SzmCUser> szmCuserMap) {
        SzmCUser szmCUser = szmCuserMap.get(szmCOrderMain.getUserId());
        if (null != szmCUser) {
            retuenOrderList.setMobile(szmCUser.getUserMobile());
        }
    }

    /**
     * 设置订单状态相关信息（批量处理版本）
     */
    private void setOrderStatusInfo(SzmCOrderMain szmCOrderMain, RetuenOrderList retuenOrderList, 
                                   Map<String, SmzCOrderReturns> orderReturnsMap) {
        // 设置发送状态
        if (szmCOrderMain.getOrderStatus() == 1 || szmCOrderMain.getOrderStatus() == 2
                || szmCOrderMain.getOrderStatus() == 3 || szmCOrderMain.getOrderStatus() == 4) {
            if (redisUtil.get("store" + szmCOrderMain.getOrderNum()) == null) {
                retuenOrderList.setIsSend(1);
            } else {
                retuenOrderList.setIsSend(2);
            }
        } else {
            retuenOrderList.setIsSend(0);
        }

        // 设置评价状态
        if (szmCOrderMain.getOrderStatus() == 5 || szmCOrderMain.getOrderStatus() == 9
                || szmCOrderMain.getOrderStatus() == 10) {
            // SzmCProductDiscuss szmCProductDiscuss = szmCProductDiscussMapper
            //         .selectByOrderNum(szmCOrderMain.getOrderNum());
            // if (szmCProductDiscuss == null) {
            //     retuenOrderList.setEvaluate(1);
            // } else {
            //     retuenOrderList.setEvaluate(2);
            // }
        } else {
            retuenOrderList.setEvaluate(0);
        }

        // 设置退货状态
        if (szmCOrderMain.getOrderStatus() == 8 || szmCOrderMain.getOrderStatus() == 10) {
            SmzCOrderReturns smzCOrderReturns = orderReturnsMap.get(szmCOrderMain.getOrderNum());
            if (null != smzCOrderReturns) {
                setReturnStatus(retuenOrderList, smzCOrderReturns);
            }
        }
    }

    private void setReturnStatus(RetuenOrderList retuenOrderList, SmzCOrderReturns smzCOrderReturns) {
        if (smzCOrderReturns.getProcessstate() == 0) {
            retuenOrderList.setOrderReturnStete("申请中");
        } else if (smzCOrderReturns.getProcessstate() == 1) {
            retuenOrderList.setOrderReturnStete("已同意");
        } else {
            if ("退货退款".equals(smzCOrderReturns.getReturnsType())) {
                retuenOrderList.setOrderReturnStete("已拒绝");
            }
        }
    }

    /**
     * 新增订单
     *
     * @param szmCOrderMain
     * @return
     */
    @Override
    @Transactional(rollbackFor = { Exception.class })
    // @MyLog(module = "订单表业务层实现", className = "SzmCOrderMainServiceImpl",
    // description = "新增订单")
    public ResultBean addOrder(SzmCOrderMainVo szmCOrderMain) {
        ResultBean resultBean = new ResultBean();
        if (null == szmCOrderMain) {
            return resultBean.nulls("szmCOrderMain");
        }
        List<SmzCOrderDetails> smzCOrderDetailsList = szmCOrderMain.getSmzCOrderDetailsList();
        List<SmzCGroupOrder> smzCGroupOrderList = szmCOrderMain.getSmzCGroupOrderList();
        if (szmCOrderMain.getCreateIden() == null) {
            return resultBean.nulls("createIden");
        } else if (szmCOrderMain.getIncomeAddrId() == null) {
            return resultBean.nulls("incomeAddrId");
        } else if (szmCOrderMain.getPaymentModeId() == null) {
            return resultBean.nulls("paymentModeId");
        } else if (smzCOrderDetailsList == null) {
            resultBean.nulls("smzCOrderDetailsList");
        } else if (szmCOrderMain.getUserName() == null) {
            return resultBean.nulls("userName");
        } else if (szmCOrderMain.getUserAddress() == null) {
            return resultBean.nulls("userAddress");
        } else if (szmCOrderMain.getUserPhone() == null) {
            return resultBean.nulls("userPhone");
        } else if (szmCOrderMain.getUserPhone().length() != 11) {
            return resultBean.error("userPhone参数不正确");
        } else if (szmCOrderMain.getFreightPayable() == null) {
            return resultBean.nulls("freightPayable");
        } else if (szmCOrderMain.getFreightPayable() < 0) {
            return resultBean.error("freightPayable参数不正确");
        } else if (szmCOrderMain.getOrderMoney() == null) {
            return resultBean.nulls("orderMoney");
        } else if (szmCOrderMain.getOrderMoney() < 0) {
            return resultBean.error("orderMoney参数不正确");
        } else if (szmCOrderMain.getOrderDiscounts() == null) {
            return resultBean.nulls("orderDiscounts");
        } else if (szmCOrderMain.getR2() == null) {
            return resultBean.nulls("r2");
        } else if (szmCOrderMain.getCartIds() == null) {
            return resultBean.nulls("cartIds");
        }

        szmCOrderMain.setZiti(szmCOrderMain.getZiti());
        szmCOrderMain.setTicketPrice(szmCOrderMain.getTicketPrice());
        szmCOrderMain.setTicketPrice(szmCOrderMain.getTicketPrice());
        szmCOrderMain.setDrainageuserid(szmCOrderMain.getDrainageuserid());
        szmCOrderMain.setDrainageid(szmCOrderMain.getDrainageid());
        szmCOrderMain.setTicketUserId(szmCOrderMain.getTicketUserId());
        szmCOrderMain.setYunfei(szmCOrderMain.getYunfei());
        szmCOrderMain.setBankRequestNum(szmCOrderMain.getBankRequestNum());
        szmCOrderMain.setBankOutTradeNum(szmCOrderMain.getBankOutTradeNum());
        szmCOrderMain.setUserId(Long.parseLong(szmCOrderMain.getCreateIden()));
        szmCOrderMain.setOrderNum(OrderNumUtil.getOrderIdByTime1(szmCOrderMain.getCreateIden()));// 订单号
        // szmCOrderMain.setIncludeDpt(0);//是否包含大平台商品
        // 订单详情 设置订单号
        // 提交订单 上下架判断 限时购 时间判断 交易关闭
        if (smzCOrderDetailsList != null && smzCOrderDetailsList.size() > 0) {
            resultBean = orderMainService.smzCOrderDetailsList(smzCOrderDetailsList, szmCOrderMain.getOrderNum());
            if (resultBean.getCode() != 1) {
                return resultBean;
            }
        }
        // 组合套餐 设置订单号
        if (smzCGroupOrderList != null && smzCGroupOrderList.size() > 0) {
            resultBean = orderMainService.smzCGroupOrderList(smzCGroupOrderList, szmCOrderMain.getOrderNum());
            if (resultBean.getCode() != 1) {
                return resultBean;
            }
        }
        Map<String, Object> stringObjectMap = new HashMap<>();
        try {
            szmCOrderMain.setCreateTime(DateUtil.SchangeD(DateUtil.DchangeS(new Date())));// 创建时间
            szmCOrderMain.setIsReturn(0);// 是否退货
            szmCOrderMain.setIsSms(0);// 是否短信
            szmCOrderMain.setIsReplenishment(0);// 是否补货
            szmCOrderMain.setIsForward(1);// 是否整单转单 0是 1否
            szmCOrderMain.setYfMoney(0D);
            if (Validator.isNull(szmCOrderMain.getBucketPrice())) {
                szmCOrderMain.setBucketPrice(0D);
            }
            if (!CollectionUtil.isEmpty(szmCOrderMain.getBucketPriceVos())) {
                szmCOrderMain.setBucketBeans(JSON.toJSONString(szmCOrderMain.getBucketPriceVos()));
            }
            SzmCAddress szmCAddress = szmCAddressMapper.selectAddressId(szmCOrderMain.getIncomeAddrId());
            if (!Validator.isNull(szmCAddress.getR4())) {
                szmCOrderMain.setCompanyName(szmCAddress.getR4());
            }
            if (!Validator.isNull(szmCAddress.getR5())) {
                szmCOrderMain.setZuobiao(szmCAddress.getR5());
                if (StringUtils.isNotEmpty(szmCAddress.getR5())) {
                    String[] split = szmCAddress.getR5().split(",");
                    if (split.length > 1) {
                        szmCOrderMain.setLat(new BigDecimal(split[0]));
                        szmCOrderMain.setLon(new BigDecimal(split[1]));

                    }
                }
            }
            if (szmCOrderMain.getPaymentModeId() == 2 || szmCOrderMain.getPaymentModeId() == 3
                    || szmCOrderMain.getPaymentModeId() == 6 || szmCOrderMain.getPaymentModeId() == 7) {
                // 货到付款 or 月付
                szmCOrderMain.setSmzCOrderDetailsList(smzCOrderDetailsList);
                szmCOrderMain.setSmzCGroupOrderList(smzCGroupOrderList);
                resultBean = orderMainService.cashOnArrivalOrPayMonthly(szmCOrderMain);
                if (resultBean.getCode() != 1) {
                    throw new ServiceBizException("500", "系统异常:提交订单异常");
                }
                // 入库
            } else {
                szmCOrderMain.setOrderStatus(0);// 待付款状态
            }
            szmCOrderMain.setOrderDelState(0);// 未删除
            // 获取缓存的待支付订单
            if (szmCOrderMain.getPaymentModeId() == 1 || szmCOrderMain.getPaymentModeId() == 4) {
                // 获取缓存的待支付订单
                orderMainService.unpaidOrder(szmCOrderMain);
            }
            // 总订单入库
            szmCOrderMain.setRemind(1);
            szmCOrderMain.setStoreId(
                    StringUtils.isEmpty(szmCOrderMain.getR2()) ? 2L : Long.parseLong(szmCOrderMain.getR2()));
            szmCOrderMainMapper.insertCancelOrder(szmCOrderMain);

            int result = 0;
            // 设置标识 orderDetail 标识商品使用水票
            if (szmCOrderMain.getSmzCOrderDetailsList() != null && szmCOrderMain.getSmzCOrderDetailsList().size() > 0) {
                // 设置标识 orderDetail 标识商品使用水票
                orderMainService.setOrderDetailWater(szmCOrderMain);
            }
            // 普通商品 订单详情入库
            List<Long> list = new ArrayList<>();
            if (szmCOrderMain.getSmzCOrderDetailsList() != null && szmCOrderMain.getSmzCOrderDetailsList().size() > 0) {
                // 普通商品 订单详情入库
                orderMainService.addOrderDetail(szmCOrderMain, list, result);
            }
            stringObjectMap.put("skuId", list);
            // 组合套餐订单入库
            list = new ArrayList<>();
            if (szmCOrderMain.getSmzCGroupOrderList() != null && szmCOrderMain.getSmzCGroupOrderList().size() > 0) {
                // 组合套餐订单入库
                orderMainService.addOrderGroup(szmCOrderMain, list, result);
            }
            stringObjectMap.put("groupId", list);
            // 新增订单后 删除购物车商品
            orderMainService.cleanCartMain(szmCOrderMain.getCartIds());
            // 查看优惠方式 消费水票
            if (szmCOrderMain.getR4() != null && !"[]".equals(szmCOrderMain.getR4())) {
                // 查看优惠方式 消费水票
                resultBean = orderMainService.consumeWaterCoupon(szmCOrderMain);
                if (resultBean.getCode() != 1) {
                    throw new ServiceBizException("500", resultBean.getMsg());
                }
            }
            // 线上订单，是否有押桶
            if (szmCOrderMain.getPaymentModeId() == 1) {
                resultBean = getBucketOrderWx(Long.parseLong(szmCOrderMain.getR2()),
                        Long.parseLong(szmCOrderMain.getCreateIden()), 1, szmCOrderMain.getOrderNum(),
                        szmCOrderMain.getBucketPriceVos());
                if (resultBean.getCode() == 1) {
                    List<PledgeBuckOrder> pledgeBuckOrders = (LinkedList<PledgeBuckOrder>) resultBean.getData();
                    for (PledgeBuckOrder pledgeBuckOrder : pledgeBuckOrders) {
                        pledgeBuckOrderMapper.insert(pledgeBuckOrder);
                    }
                }
            }
            // ----货到 | 月份 | 水票支付 0 元------------------------------
            if (szmCOrderMain.getPaymentModeId() == 3 || szmCOrderMain.getPaymentModeId() == 2
                    || szmCOrderMain.getPaymentModeId() == 6 || szmCOrderMain.getPaymentModeId() == 7) {

                // 开票
                orderMainService.invoiceOrder(szmCOrderMain);
                if (szmCOrderMain.getPaymentModeId() == 7) {
                    szmCOrderMain.setIsBankAffirm(0);
                }
                if (szmCOrderMain.getBucketPrice() > 0) {
                    if (szmCOrderMain.getBucketPayType() != 0) {
                        resultBean = getBucketOrder(Long.parseLong(szmCOrderMain.getR2()),
                                Long.parseLong(szmCOrderMain.getCreateIden()), 5, szmCOrderMain.getOrderNum(),
                                szmCOrderMain.getBucketPriceVos());
                        if (resultBean.getCode() == 1) {
                            List<PledgeBuckOrder> pledgeBuckOrders = (LinkedList<PledgeBuckOrder>) resultBean.getData();
                            for (PledgeBuckOrder pledgeBuckOrder : pledgeBuckOrders) {
                                if (szmCOrderMain.getPaymentModeId() == 2L) {
                                    pledgeBuckOrder.setR4("6");
                                }
                                pledgeBuckOrderMapper.insert(pledgeBuckOrder);
                            }
                        }
                    }
                }

                String outTradeNo = szmCOrderMain.getOrderNum();
                SzmCOrderMain main = new SzmCOrderMain();
                main.setOrderNum(outTradeNo);
                main.setPayNum(outTradeNo);
                result = szmCOrderMainMapper.updateByOrderNum(main);
                if (result != 1) {
                    throw new ServiceBizException("500", "系统异常:修改订单异常");
                }
                // 添加账单
                // 新增普通商品账单
                resultBean = orderMainService.addUserBillByDetail(outTradeNo);
                if (resultBean.getCode() != 1) {
                    throw new ServiceBizException("500", "系统异常:新增普通商品账单异常");
                }
                // 新增套餐商品账单
                resultBean = orderMainService.addUserBillByGroup(outTradeNo);
                if (resultBean.getCode() != 1) {
                    throw new ServiceBizException("500", "系统异常:新增套餐商品账单异常");
                }
                // 新增其他信息
                resultBean = orderMainService.addOthers(outTradeNo);
                if (resultBean.getCode() != 1) {
                    throw new ServiceBizException("500", "系统异常:新增其他信息异常");
                }
                // 根据订单总额增加积分
                resultBean = orderMainService.updateIntegral(outTradeNo);
                if (resultBean.getCode() != 1) {
                    throw new ServiceBizException("500", "系统异常:增加积分异常");
                }
                // 延迟队列 24小时检查是否发货 货到付款和月付 发送延迟队列
                LoggerUtil.info("延迟队列 24小时检查是否发货 线下付款和月付 发送延迟队列");
                rabbitMqUtil.convertAndSend("test_exchange2", "test_queue_2", outTradeNo, 3600000L);
                // 绑定用户和商户关系
                resultBean = orderMainService.bindUserByStore(outTradeNo);
                if (resultBean.getCode() != 1) {
                    throw new ServiceBizException("500", "系统异常:绑定用户和商户关系异常");
                }

                // 看看这用户最近一次下单
                SzmCOrderMain szmCOrderMain12 = szmCOrderMainMapper.selectUserLastBuy(szmCOrderMain.getUserId(),
                        szmCOrderMain.getStoreId());
                if (szmCOrderMain12 != null) {
                    // 看看送水员是谁
                    if (szmCOrderMain12.getDeliveryInfoId() != null
                            && !szmCOrderMain12.getDeliveryInfoId().equals(0L)) {
                        // 看看送水员是谁
                        SmzCDeliveryUser smzCDeliveryUser12 = smzCDeliveryUserMapper
                                .selectByPrimaryKey(szmCOrderMain12.getDeliveryInfoId());
                        if (smzCDeliveryUser12 != null && smzCDeliveryUser12.getIsauto() != null
                                && smzCDeliveryUser12.getIsauto() == 1) {
                            if (smzCDeliveryUser12.getDateline() == null
                                    || smzCDeliveryUser12.getDateline().before(szmCOrderMain12.getCreateTime())) {
                                // 自动接单
                                szmBOrderService.selectDeliveryId(szmCOrderMain.getOrderNum(),
                                        smzCDeliveryUser12.getDeliveryUserId(), 0D, 0D, 0D, 0D);
                            }
                        }
                    }
                }
            }

            // 活动
            for (SmzCOrderDetails d : smzCOrderDetailsList) {
                if (d.getR2().equals("5")) {
                    MarketActivityOrder activityOrder = new MarketActivityOrder();
                    activityOrder.setUserId(Long.valueOf(szmCOrderMain.getCreateIden()));
                    activityOrder.setBeginDate(new Date());
                    activityOrder.setEndDate(new Date());
                    activityOrder.setOrderStatus(2);// 完成，才能下单
                    activityOrder = marketActivityOrderMapper.selectByFiled(activityOrder);
                    if (activityOrder == null) {
                        return new ResultBean().error("活动未完成或已结束");
                    }
                    MarketActivityGoods queryGoods = new MarketActivityGoods();
                    queryGoods.setActivityId(activityOrder.getActivityId());
                    queryGoods.setScope(1);// 免费商品
                    List<MarketActivityGoods> goodsList = marketActivityGoodsMapper.selectByFiled(queryGoods);
                    SzmCProductModel model = szmCProductModelMapper.selectByPrimaryKey(d.getProductModelId());
                    if (!goodsList.stream().anyMatch(g -> g.getProductId().equals(model.getProductId()))) {
                        return resultBean.error("非活动商品");
                    }
                    activityOrder.setOrderStatus(3);
                    marketActivityOrderMapper.updateInvitedUser(activityOrder);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceBizException("500", e.getMessage());
        }
        // 延迟队列 自动删除缓存 修改 数据库订单状态
        LoggerUtil.info("延迟队列 自动删除缓存 修改 数据库订单状态");
        SzmCOrderMain szmCOrderMain1 = new SzmCOrderMain();
        Dto2EntityUtil.copyProperties(szmCOrderMain, szmCOrderMain1);
        rabbitMqUtil.convertAndSend("test_exchange", "test_queue_1", szmCOrderMain1, 3600000L);
        // 延迟队列 检测库存 下架或通知商户库存预警
        LoggerUtil.info("延迟队列 检测库存 下架或通知商户库存预警");
        rabbitMqUtil.convertAndSend("limit_exchange", "limit_queue", stringObjectMap, 3600000L);

        return resultBean.success(szmCOrderMain.getOrderNum());
    }

    /**
     * 取消订单
     *
     * @param orderNum
     * @return
     */
    @Override
    @Transactional(rollbackFor = { Exception.class })
    public ResultBean cancelOrder(String orderNum) {
        ResultBean resultBean = new ResultBean();
        if (orderNum == null) {
            return resultBean.nulls("orderNum");
        }
        String userId = szmCOrderMainMapper.selectByOrderNum(orderNum).getCreateIden();
        try {
            Object obj = redisUtil.get(userId + "orders");
            String json = null;
            if (obj != null)
                json = obj.toString();
            if (json != null && !"".equals(json)) {
                List<SzmCOrderMain> szmCOrderMains = JSON.parseArray(json, SzmCOrderMain.class);
                Iterator<SzmCOrderMain> it = szmCOrderMains.iterator();
                while (it.hasNext()) {
                    SzmCOrderMain x = it.next();
                    if (x.getOrderNum().equals(orderNum)) {
                        SzmCOrderMain szmCOrderMain = new SzmCOrderMain();
                        szmCOrderMain.setOrderNum(orderNum);
                        szmCOrderMain.setUpdateTime(DateUtil.SchangeD(DateUtil.DchangeS(new Date())));
                        szmCOrderMain.setOrderStatus(6);
                        szmCOrderMainMapper.updateByOrderNum(szmCOrderMain);
                        List<SmzCGroupOrder> smzCGroupOrderList = smzCGroupOrderMapper.selectByOrderNum(orderNum);
                        List<SmzCOrderDetails> smzCOrderDetailsList = smzCOrderDetailsMapper.selectByOrderNum(orderNum);
                        if (smzCGroupOrderList != null && smzCGroupOrderList.size() > 0) {
                            for (SmzCGroupOrder item : smzCGroupOrderList) {
                                SzmCShopgroup szmCShopgroup = szmCShopgroupMapper
                                        .selectByPrimaryKey(item.getShopgroupId());
                                szmCShopgroup.setShopgroupCount(
                                        szmCShopgroup.getShopgroupCount() + item.getOrderProductNum());
                                szmCShopgroupMapper.updateByPrimaryKey(szmCShopgroup);// 还库存
                                List<SzmCShopgroupList> szmCShopgroupLists = szmCShopgroupListMapper
                                        .selectAll(szmCShopgroup.getShopgroupId().intValue());
                                for (SzmCShopgroupList szmCShopgroupList : szmCShopgroupLists) {
                                    int i = Integer.parseInt(szmCShopgroupList.getR1()) * item.getOrderProductNum();
                                    SzmCProductModel szmCProductModel = szmCProductModelMapper
                                            .selectByPrimaryKey(szmCShopgroupList.getProductModelId());
                                    if (szmCProductModel != null) {
                                        szmCProductModel
                                                .setProductInventory(szmCProductModel.getProductInventory() + i);
                                        szmCProductModelMapper.updateByPrimaryKey(szmCProductModel);// 还库存
                                    }
                                }
                            }
                        }
                        if (smzCOrderDetailsList != null && smzCOrderDetailsList.size() > 0) {
                            for (SmzCOrderDetails item : smzCOrderDetailsList) {
                                SzmCProductModel szmCProductModel = szmCProductModelMapper
                                        .selectByPrimaryKey(item.getProductModelId());
                                if ("0".equals(szmCProductModel.getR5()) || "1".equals(szmCProductModel.getR5())) {
                                    // 普通
                                    szmCProductModel.setProductInventory(
                                            szmCProductModel.getProductInventory() + item.getOrderProductNum());
                                    szmCProductModelMapper.updateByPrimaryKey(szmCProductModel);// 还库存
                                } else if ("2".equals(szmCProductModel.getR5())) {
                                    // 限量
                                    SmzCLimitToBuyToBuy limitToBuyToBuy = smzCLimitToBuyToBuyMapper
                                            .selectProudModelId(szmCProductModel.getProductModelId());
                                    limitToBuyToBuy.setLimitToBuyInventory(
                                            limitToBuyToBuy.getLimitToBuyInventory() + item.getOrderProductNum());
                                    smzCLimitToBuyToBuyMapper.updateByPrimaryKey(limitToBuyToBuy);// 还库存
                                }
                            }
                        }
                        if (x.getR4() != null) {
                            List<CouponVo> list = JSON.parseArray(x.getR4(), CouponVo.class);
                            for (CouponVo couponVo : list) {
                                WcRelevance wcRelevance = new WcRelevance();
                                wcRelevance.setUserId(Long.parseLong(x.getCreateIden()));
                                wcRelevance.setWcRelevanceId(couponVo.getWaterId());
                                wcRelevance.setUse(couponVo.getNumber());
                                Integer i = couponVo.getDiscountsNum() + couponVo.getDiscountsNumWater();
                                if (couponVo.getDiscountsNum() == null) {
                                    couponVo.setDiscountsNum(0);
                                }
                                if (couponVo.getDiscountsNumWater() == null) {
                                    couponVo.setDiscountsNumWater(0);
                                }
                                wcRelevance.setR1(i.toString());
                                ResultBean insert = smzCWcUseService.newRepay(wcRelevance);
                                if (insert.getCode() != 1) {
                                    throw new Exception(insert.getData().toString());
                                }
                            }
                        }
                        it.remove();
                    }
                }
                if (szmCOrderMains.size() > 0) {
                    redisUtil.set(userId + "orders", JSON.toJSONString(szmCOrderMains));
                } else {
                    redisUtil.del(userId + "orders");
                }
                return resultBean.success("取消订单成功");
            } else {
                return resultBean.error("取消订单失败");
            }
        } catch (Exception e) {
            throw new ServiceBizException("500", e.getMessage());
        }
    }

    /**
     * 支付成功后订单
     *
     * @param szmCOrderMain
     * @return
     */
    @Override
    public ResultBean successOrder(SzmCOrderMain szmCOrderMain) {
        return null;
    }

    /**
     * 查看用户所有订单
     *
     * @param userId@return
     */
    @Override
    public ResultBean findAllOrder(Long userId, Integer index, Integer pageSize, Long orderZt, Long storeId,
            Long addressId, String startTime, String endTime, String username, Integer ordersource, Long deliveryUserId,
            String startTimeFinish, String endTimeFinish, Integer orderStatus) {
        ResultBean resultBean = new ResultBean();
        Map<String, Object> result = new HashMap<>();
        List<RetuenOrderList> allList = new ArrayList<>();
        String startDate = null;
        String endDate = null;
        String startDateFinish = null;
        String endDateFinish = null;
        if (StringUtils.isNotEmpty(startTime) && StringUtils.isNotEmpty(endTime)) {

            startDate = startTime + " 00:00:00";
            endDate = endTime + " 23:59:59";
        }
        if (StringUtils.isNotEmpty(startTimeFinish) && StringUtils.isNotEmpty(endTimeFinish)) {
            startDateFinish = startTimeFinish + " 00:00:00";
            endDateFinish = endTimeFinish + " 23:59:59";
        }
        SzmCStore szmCStore = szmCStoreMapper.selectLocationById(storeId);
        // 设置上省市区
        if (null != szmCStore) {
            if (StringUtils.isNotEmpty(szmCStore.getStoreProvince())) {
                Provinces province = provincesMapper.selectprovinceid(szmCStore.getStoreProvince());
                szmCStore.setStoreProvinceName(province == null ? "" : province.getProvince());
            }
            if (StringUtils.isNotEmpty(szmCStore.getStoreCity())) {
                Cities city = citiesMapper.selectByCityCode(szmCStore.getStoreCity());
                szmCStore.setStoreCityName(city == null ? "" : city.getCity());
            }
            if (StringUtils.isNotEmpty(szmCStore.getStoreArea())) {
                Areas area = areasMapper.selectByareaid(szmCStore.getStoreArea());
                szmCStore.setStoreAreaName(area == null ? "" : area.getArea());
            }
        }

        if (orderZt == -1) {
            PageHelper.startPage(index, pageSize);
            List<SzmCOrderMain> szmCOrderMains = szmCOrderMainMapper.selectStoreByUserAndStoreIdAllUserIdCanNo(userId,
                    storeId, addressId, username, startDate, endDate, ordersource, deliveryUserId, startDateFinish,
                    endDateFinish, orderStatus);
            List<String> orderNums = szmCOrderMains.stream().map(SzmCOrderMain::getOrderNum)
                    .collect(Collectors.toList());
            List<SmzCGroupOrder> smzCGroupOrders = CollectionUtils.isEmpty(orderNums) ? new ArrayList<>()
                    : smzCGroupOrderMapper.selectByOrderNums(orderNums);
            Map<String, List<SmzCGroupOrder>> groupOrderMap = smzCGroupOrders.stream()
                    .collect(Collectors.groupingBy(SmzCGroupOrder::getOrderMainId, Collectors.toList()));
            List<SmzCOrderDetails> smzCOrderDetails = CollectionUtils.isEmpty(orderNums) ? new ArrayList<>()
                    : smzCOrderDetailsMapper.selectByOrderNums(orderNums);
            Map<String, List<SmzCOrderDetails>> orderDetailMap = smzCOrderDetails.stream()
                    .collect(Collectors.groupingBy(SmzCOrderDetails::getOrderMainId, Collectors.toList()));
            List<Long> userIds = szmCOrderMains.stream().map(SzmCOrderMain::getUserId).distinct()
                    .collect(Collectors.toList());
            List<SzmCUser> szmCUsers = CollectionUtils.isEmpty(userIds) ? new ArrayList<>()
                    : szmCUserMapper.findByIds(userIds);
            Map<Long, SzmCUser> szmCuserMap = szmCUsers.stream().collect(Collectors.toMap(SzmCUser::getUserId, b -> b));
            List<Long> deliveryUserIds = szmCOrderMains.stream().map(SzmCOrderMain::getDeliveryInfoId)
                    .collect(Collectors.toList());
            List<SmzCDeliveryUser> deliveryUsers = CollectionUtils.isEmpty(deliveryUserIds) ? new ArrayList<>()
                    : smzCDeliveryUserMapper.selectByIds(deliveryUserIds);
            Map<Long, SmzCDeliveryUser> deliveryUserMap = deliveryUsers.stream()
                    .collect(Collectors.toMap(SmzCDeliveryUser::getDeliveryUserId, b -> b));
            List<SmzCPaymentMode> smzCPaymentModes = smzCPaymentModeMapper.selectAll();
            Map<Long, String> paymentModeMap = smzCPaymentModes.stream()
                    .collect(Collectors.toMap(SmzCPaymentMode::getPaymentModeId, SmzCPaymentMode::getPaymentModeName));
            List<Long> orderMainIds = szmCOrderMains.stream().map(SzmCOrderMain::getOrderMainId)
                    .collect(Collectors.toList());
            List<LongIntegerVo> pledgeBuckOrderList = CollectionUtils.isEmpty(orderMainIds) ? new ArrayList<>()
                    : pledgeBuckOrderMapper.selectPledgCountByOrderNumbers(orderMainIds);
            Map<Long, LongIntegerVo> pledgeBuckOrderMap = pledgeBuckOrderList.stream()
                    .collect(Collectors.toMap(LongIntegerVo::getId, b -> b));
            List<SzmCProductDiscuss> szmCProductDiscussList = CollectionUtils.isEmpty(orderNums) ? new ArrayList<>()
                    : szmCProductDiscussMapper.selectByOrderNums(orderNums);
            Map<String, SzmCProductDiscuss> szmCProductDiscussMap = szmCProductDiscussList.stream()
                    .collect(Collectors.toMap(SzmCProductDiscuss::getOrderMainId, b -> b));
            Map<Integer, OrderSource> orderSourceMap = orderSourceMapper.selectAll().stream()
                    .collect(Collectors.toMap(OrderSource::getId, orderSource -> orderSource));
            // 查询全部订单信息
            for (SzmCOrderMain szmCOrderMain : szmCOrderMains) {
                // 查看普通订单
                List<SmzCGroupOrder> smzCGroupOrders1 = groupOrderMap.get(szmCOrderMain.getOrderNum());
                List<SmzCOrderDetails> smzCOrderDetailsList = orderDetailMap.get(szmCOrderMain.getOrderNum());
                RetuenOrderList retuenOrderList = lookOrderDeatil(szmCOrderMain, szmCStore, smzCOrderDetailsList,
                        smzCGroupOrders1, paymentModeMap, orderSourceMap);
                retuenOrderList.setUserName(szmCOrderMain.getUserName());
                retuenOrderList.setUserPhone(szmCOrderMain.getUserPhone());
                retuenOrderList.setUserAddress(szmCOrderMain.getUserAddress());
                retuenOrderList.setDikou(szmCOrderMain.getDikou());
                retuenOrderList.setZiti(szmCOrderMain.getZiti());
                retuenOrderList.setOrdersource(szmCOrderMain.getOrdersource());
                LongIntegerVo longIntegerVo = pledgeBuckOrderMap.get(szmCOrderMain.getOrderMainId());
                if (null != longIntegerVo) {
                    retuenOrderList.setBuckState(longIntegerVo.getCount());
                }
                SzmCUser szmCUser = szmCuserMap.get(szmCOrderMain.getUserId());
                if (null != szmCUser) {
                    retuenOrderList.setMobile(szmCUser.getUserMobile());
                }
                SmzCDeliveryUser smzCDeliveryUser = deliveryUserMap.get(szmCOrderMain.getDeliveryInfoId());
                if (szmCOrderMain.getDeliveryInfoId() != null && szmCOrderMain.getDeliveryInfoId().equals(0L)) {
                    retuenOrderList.setDeliveryName("水站自送");
                } else if (null != smzCDeliveryUser) {
                    retuenOrderList.setDeliveryName(smzCDeliveryUser.getDeliveryUserName());
                }
                retuenOrderList.setDiscounPrice(szmCOrderMain.getOrderDiscounts());
                if (szmCOrderMain.getOrderStatus() == 1 || szmCOrderMain.getOrderStatus() == 2
                        || szmCOrderMain.getOrderStatus() == 3 || szmCOrderMain.getOrderStatus() == 4) {
                    if (redisUtil.get("store" + szmCOrderMain.getOrderNum()) == null) {
                        retuenOrderList.setIsSend(1);
                    } else {
                        retuenOrderList.setIsSend(2);
                    }
                } else {
                    retuenOrderList.setIsSend(0);
                }
                if (szmCOrderMain.getOrderStatus() == 5 || szmCOrderMain.getOrderStatus() == 9
                        || szmCOrderMain.getOrderStatus() == 10) {
                    SzmCProductDiscuss szmCProductDiscuss = szmCProductDiscussMap.get(szmCOrderMain.getOrderMainId());
                    if (szmCProductDiscuss == null) {
                        retuenOrderList.setEvaluate(1);
                    } else {
                        retuenOrderList.setEvaluate(2);
                    }
                } else {
                    retuenOrderList.setEvaluate(0);
                }
                if (szmCOrderMain.getOrderStatus() == 8 || szmCOrderMain.getOrderStatus() == 6) {
                    SmzCOrderReturns smzCOrderReturns = smzCOrderReturnsMapper
                            .selectOtherByOrderNum(szmCOrderMain.getOrderNum());
                    if (null != smzCOrderReturns) {
                        if (smzCOrderReturns.getProcessstate() == 0) {
                            retuenOrderList.setOrderReturnStete("申请中");
                        } else if (smzCOrderReturns.getProcessstate() == 1) {
                            retuenOrderList.setOrderReturnStete("已同意");
                        } else {
                            if (smzCOrderReturns.getReturnsType().equals("退货退款")) {
                                retuenOrderList.setOrderReturnStete("已拒绝");
                            }
                        }
                    }
                }
                allList.add(retuenOrderList);
            }
            PageInfo<SzmCOrderMain> productPageInfo = new PageInfo<>(szmCOrderMains);
            long count = productPageInfo.getTotal();
            result.put("count", count);
            result.put("list", allList);

            return resultBean.success(result);
        } else {
            PageHelper.startPage(index, 10);
            List<SzmCOrderMain> szmCOrderMains = szmCOrderMainMapper.selectByUserAndStoreId(userId, orderZt, storeId,
                    addressId, ordersource);
            List<String> orderNums = szmCOrderMains.stream().map(SzmCOrderMain::getOrderNum)
                    .collect(Collectors.toList());
            List<SmzCGroupOrder> smzCGroupOrders = CollectionUtils.isEmpty(orderNums) ? new ArrayList<>()
                    : smzCGroupOrderMapper.selectByOrderNums(orderNums);
            Map<String, List<SmzCGroupOrder>> groupOrderMap = smzCGroupOrders.stream()
                    .collect(Collectors.groupingBy(SmzCGroupOrder::getOrderMainId, Collectors.toList()));
            List<SmzCOrderDetails> smzCOrderDetails = CollectionUtils.isEmpty(orderNums) ? new ArrayList<>()
                    : smzCOrderDetailsMapper.selectByOrderNums(orderNums);
            Map<String, List<SmzCOrderDetails>> orderDetailMap = smzCOrderDetails.stream()
                    .collect(Collectors.groupingBy(SmzCOrderDetails::getOrderMainId, Collectors.toList()));

            List<SmzCPaymentMode> smzCPaymentModes = smzCPaymentModeMapper.selectAll();
            Map<Long, String> paymentModeMap = smzCPaymentModes.stream()
                    .collect(Collectors.toMap(SmzCPaymentMode::getPaymentModeId, SmzCPaymentMode::getPaymentModeName));
            List<Long> orderMainIds = szmCOrderMains.stream().map(SzmCOrderMain::getOrderMainId)
                    .collect(Collectors.toList());
            List<LongIntegerVo> pledgeBuckOrderList = CollectionUtils.isEmpty(orderMainIds) ? new ArrayList<>()
                    : pledgeBuckOrderMapper.selectPledgCountByOrderNumbers(orderMainIds);
            Map<Long, LongIntegerVo> pledgeBuckOrderMap = pledgeBuckOrderList.stream()
                    .collect(Collectors.toMap(LongIntegerVo::getId, b -> b));
            Map<Integer, OrderSource> orderSourceMap = orderSourceMapper.selectAll().stream()
                    .collect(Collectors.toMap(OrderSource::getId, orderSource -> orderSource));
            // 查询全部订单信息
            for (SzmCOrderMain szmCOrderMain : szmCOrderMains) {
                // 查看普通订单

                List<SmzCGroupOrder> smzCGroupOrders1 = groupOrderMap.get(szmCOrderMain.getOrderNum());
                List<SmzCOrderDetails> smzCOrderDetailsList = orderDetailMap.get(szmCOrderMain.getOrderNum());
                RetuenOrderList retuenOrderList = lookOrderDeatil(szmCOrderMain, szmCStore, smzCOrderDetailsList,
                        smzCGroupOrders1, paymentModeMap, orderSourceMap);
                LongIntegerVo longIntegerVo = pledgeBuckOrderMap.get(szmCOrderMain.getOrderMainId());
                if (null != longIntegerVo) {
                    retuenOrderList.setBuckState(longIntegerVo.getCount());
                }
                retuenOrderList.setUserName(szmCOrderMain.getUserName());
                retuenOrderList.setUserPhone(szmCOrderMain.getUserPhone());
                retuenOrderList.setUserAddress(szmCOrderMain.getUserAddress());
                retuenOrderList.setDiscounPrice(szmCOrderMain.getOrderDiscounts());
                retuenOrderList.setDikou(szmCOrderMain.getDikou());
                retuenOrderList.setZiti(szmCOrderMain.getZiti());
                retuenOrderList.setOrdersource(szmCOrderMain.getOrdersource());
                if (szmCOrderMain.getOrderStatus() == 5 || szmCOrderMain.getOrderStatus() == 9
                        || szmCOrderMain.getOrderStatus() == 10) {
                    Integer integer1 = szmCProductDiscussMapper.selectByOrderNumCount(szmCOrderMain.getOrderNum());
                    if (integer1 == 0) {
                        retuenOrderList.setEvaluate(1);
                    } else {
                        retuenOrderList.setEvaluate(2);
                    }
                } else {
                    retuenOrderList.setEvaluate(0);
                }
                if (szmCOrderMain.getOrderStatus() == 8 || szmCOrderMain.getOrderStatus() == 10) {
                    SmzCOrderReturns smzCOrderReturns = smzCOrderReturnsMapper
                            .selectOtherByOrderNum(szmCOrderMain.getOrderNum());
                    if (null != smzCOrderReturns) {
                        if (smzCOrderReturns.getProcessstate() == 0) {
                            retuenOrderList.setOrderReturnStete("申请中");
                        } else if (smzCOrderReturns.getProcessstate() == 1) {
                            retuenOrderList.setOrderReturnStete("已同意");
                        } else {
                            if (smzCOrderReturns.getReturnsType().equals("退货退款")) {
                                retuenOrderList.setOrderReturnStete("已拒绝");
                            }
                        }
                    }
                }
                allList.add(retuenOrderList);
            }
            PageInfo<SzmCOrderMain> productPageInfo = new PageInfo<>(szmCOrderMains);
            long count = productPageInfo.getTotal();
            result.put("count", count);
            result.put("list", allList);

            return resultBean.success(result);

        }
    }

    @Override
    public ResultBean findAllOrderfront(Long userId, Integer index, Integer pageSize, Long orderZt, Long storeId,
            Long addressId, String startTime, String endTime, String username, Integer ordersource, Long deliveryUserId,
            String startTimeFinish, String endTimeFinish) {
        ResultBean resultBean = new ResultBean();
        Map<String, Object> result = new HashMap<>();
        List<RetuenOrderList> allList = new ArrayList<>();
        String startDate = null;
        String endDate = null;
        String startDateFinish = null;
        String endDateFinish = null;
        if (StringUtils.isNotEmpty(startTime) && StringUtils.isNotEmpty(endTime)) {

            startDate = startTime + " 00:00:00";
            endDate = endTime + " 23:59:59";
        }
        if (StringUtils.isNotEmpty(startTimeFinish) && StringUtils.isNotEmpty(endTimeFinish)) {
            startDateFinish = startTimeFinish + " 00:00:00";
            endDateFinish = endTimeFinish + " 23:59:59";
        }
        if (orderZt == -1) {
            PageHelper.startPage(index, pageSize);
            List<SzmCOrderMain> szmCOrderMains = szmCOrderMainMapper.selectStoreByUserAndStoreIdAllStoreIdCanNo(userId,
                    storeId, addressId, username, startDate, endDate, ordersource, deliveryUserId, startDateFinish,
                    endDateFinish);
            List<String> orderNums = szmCOrderMains.stream().map(SzmCOrderMain::getOrderNum)
                    .collect(Collectors.toList());
            List<SmzCGroupOrder> smzCGroupOrders = CollectionUtils.isEmpty(orderNums) ? new ArrayList<>()
                    : smzCGroupOrderMapper.selectByOrderNums(orderNums);
            Map<String, List<SmzCGroupOrder>> groupOrderMap = smzCGroupOrders.stream()
                    .collect(Collectors.groupingBy(SmzCGroupOrder::getOrderMainId, Collectors.toList()));
            List<SmzCOrderDetails> smzCOrderDetails = CollectionUtils.isEmpty(orderNums) ? new ArrayList<>()
                    : smzCOrderDetailsMapper.selectByOrderNums(orderNums);
            Map<String, List<SmzCOrderDetails>> orderDetailMap = smzCOrderDetails.stream()
                    .collect(Collectors.groupingBy(SmzCOrderDetails::getOrderMainId, Collectors.toList()));
            List<Long> userIds = szmCOrderMains.stream().map(SzmCOrderMain::getUserId).distinct()
                    .collect(Collectors.toList());
            List<SzmCUser> szmCUsers = CollectionUtils.isEmpty(userIds) ? new ArrayList<>()
                    : szmCUserMapper.findByIds(userIds);
            Map<Long, SzmCUser> szmCuserMap = szmCUsers.stream().collect(Collectors.toMap(SzmCUser::getUserId, b -> b));
            List<Long> deliveryUserIds = szmCOrderMains.stream().map(SzmCOrderMain::getDeliveryInfoId)
                    .collect(Collectors.toList());
            List<SmzCDeliveryUser> deliveryUsers = CollectionUtils.isEmpty(deliveryUserIds) ? new ArrayList<>()
                    : smzCDeliveryUserMapper.selectByIds(deliveryUserIds);
            Map<Long, SmzCDeliveryUser> deliveryUserMap = deliveryUsers.stream()
                    .collect(Collectors.toMap(SmzCDeliveryUser::getDeliveryUserId, b -> b));
            List<SmzCPaymentMode> smzCPaymentModes = smzCPaymentModeMapper.selectAll();
            Map<Long, String> paymentModeMap = smzCPaymentModes.stream()
                    .collect(Collectors.toMap(SmzCPaymentMode::getPaymentModeId, SmzCPaymentMode::getPaymentModeName));
            List<Long> orderMainIds = szmCOrderMains.stream().map(SzmCOrderMain::getOrderMainId)
                    .collect(Collectors.toList());
            List<LongIntegerVo> pledgeBuckOrderList = CollectionUtils.isEmpty(orderMainIds) ? new ArrayList<>()
                    : pledgeBuckOrderMapper.selectPledgCountByOrderNumbers(orderMainIds);
            Map<Long, LongIntegerVo> pledgeBuckOrderMap = pledgeBuckOrderList.stream()
                    .collect(Collectors.toMap(LongIntegerVo::getId, b -> b));
            List<Long> storeIds = szmCOrderMains.stream().map(SzmCOrderMain::getStoreId).distinct()
                    .collect(Collectors.toList());
            List<SzmCStore> szmCStoreList = CollectionUtils.isEmpty(storeIds) ? new ArrayList<>()
                    : szmCStoreMapper.findByIds(storeIds);

                    
        List<String> province = szmCStoreList.stream().map(SzmCStore::getStoreProvince).filter(Objects::nonNull).distinct()
                .collect(Collectors.toList());
        List<String> city = szmCStoreList.stream().map(SzmCStore::getStoreCity).filter(Objects::nonNull).distinct()
                .collect(Collectors.toList());
        List<String> area = szmCStoreList.stream().map(SzmCStore::getStoreArea).filter(Objects::nonNull).distinct()
                .collect(Collectors.toList());
        List<Provinces> provincesList = CollectionUtil.isEmpty(province) ? new ArrayList<>()
                : provincesMapper.selectprovinceids(province);
        List<Cities> citiesList = CollectionUtil.isEmpty(city) ? new ArrayList<>() : citiesMapper.selectByCityids(city);
        List<Areas> areasList = CollectionUtil.isEmpty(area) ? new ArrayList<>() : areasMapper.selectByareaids(area);

        szmCStoreList.forEach(e -> {
            provincesList.forEach(f -> {
                if (f.getProvinceid().equals(e.getStoreProvince())) {
                    e.setStoreProvinceName(f.getProvince());
                }
            });
            citiesList.forEach(f -> {
                if (f.getCityid().equals(e.getStoreCity())) {
                    e.setStoreCityName(f.getCity());
                }
            });
            areasList.forEach(f -> {
                if (f.getAreaid().equals(e.getStoreArea())) {
                    e.setStoreAreaName(f.getArea());
                }
            });
        });
            Map<Long, SzmCStore> szmCStoreMap = szmCStoreList.stream()
                    .collect(Collectors.toMap(SzmCStore::getStoreId, b -> b));
            Map<Integer, OrderSource> orderSourceMap = orderSourceMapper.selectAll().stream()
                    .collect(Collectors.toMap(OrderSource::getId, orderSource -> orderSource));
            // 查询全部订单信息
            for (SzmCOrderMain szmCOrderMain : szmCOrderMains) {
                // 查看普通订单
                List<SmzCGroupOrder> smzCGroupOrders1 = groupOrderMap.get(szmCOrderMain.getOrderNum());
                List<SmzCOrderDetails> smzCOrderDetailsList = orderDetailMap.get(szmCOrderMain.getOrderNum());
                RetuenOrderList retuenOrderList = lookOrderDeatil(szmCOrderMain,
                        szmCStoreMap.get(szmCOrderMain.getStoreId()), smzCOrderDetailsList,
                        smzCGroupOrders1, paymentModeMap, orderSourceMap);
                retuenOrderList.setUserName(szmCOrderMain.getUserName());
                retuenOrderList.setUserPhone(szmCOrderMain.getUserPhone());
                retuenOrderList.setUserAddress(szmCOrderMain.getUserAddress());
                retuenOrderList.setDikou(szmCOrderMain.getDikou());
                retuenOrderList.setZiti(szmCOrderMain.getZiti());
                retuenOrderList.setOrdersource(szmCOrderMain.getOrdersource());
                LongIntegerVo longIntegerVo = pledgeBuckOrderMap.get(szmCOrderMain.getOrderMainId());
                if (null != longIntegerVo) {
                    retuenOrderList.setBuckState(longIntegerVo.getCount());
                }
                SzmCUser szmCUser = szmCuserMap.get(szmCOrderMain.getUserId());
                if (null != szmCUser) {
                    retuenOrderList.setMobile(szmCUser.getUserMobile());
                }
                SmzCDeliveryUser smzCDeliveryUser = deliveryUserMap.get(szmCOrderMain.getDeliveryInfoId());
                if (szmCOrderMain.getDeliveryInfoId() != null && szmCOrderMain.getDeliveryInfoId().equals(0)) {
                    retuenOrderList.setDeliveryName("水站自送");
                } else if (null != smzCDeliveryUser) {
                    retuenOrderList.setDeliveryName(smzCDeliveryUser.getDeliveryUserName());
                }
                retuenOrderList.setDiscounPrice(szmCOrderMain.getOrderDiscounts());
                if (szmCOrderMain.getOrderStatus() == 1 || szmCOrderMain.getOrderStatus() == 2
                        || szmCOrderMain.getOrderStatus() == 3 || szmCOrderMain.getOrderStatus() == 4) {
                    if (redisUtil.get("store" + szmCOrderMain.getOrderNum()) == null) {
                        retuenOrderList.setIsSend(1);
                    } else {
                        retuenOrderList.setIsSend(2);
                    }
                } else {
                    retuenOrderList.setIsSend(0);
                }
                if (szmCOrderMain.getOrderStatus() == 5 || szmCOrderMain.getOrderStatus() == 9
                        || szmCOrderMain.getOrderStatus() == 10) {
                    SzmCProductDiscuss szmCProductDiscuss = szmCProductDiscussMapper
                            .selectByOrderNum(szmCOrderMain.getOrderNum());
                    if (szmCProductDiscuss == null) {
                        retuenOrderList.setEvaluate(1);
                    } else {
                        retuenOrderList.setEvaluate(2);
                    }
                } else {
                    retuenOrderList.setEvaluate(0);
                }
                if (szmCOrderMain.getOrderStatus() == 8 || szmCOrderMain.getOrderStatus() == 10) {
                    SmzCOrderReturns smzCOrderReturns = smzCOrderReturnsMapper
                            .selectOtherByOrderNum(szmCOrderMain.getOrderNum());
                    if (null != smzCOrderReturns) {
                        if (smzCOrderReturns.getProcessstate() == 0) {
                            retuenOrderList.setOrderReturnStete("申请中");
                        } else if (smzCOrderReturns.getProcessstate() == 1) {
                            retuenOrderList.setOrderReturnStete("已同意");
                        } else {
                            if (smzCOrderReturns.getReturnsType().equals("退货退款")) {
                                retuenOrderList.setOrderReturnStete("已拒绝");
                            }
                        }
                    }
                }
                allList.add(retuenOrderList);
            }
            PageInfo<SzmCOrderMain> productPageInfo = new PageInfo<>(szmCOrderMains);
            long count = productPageInfo.getTotal();
            result.put("count", count);
            result.put("list", allList);

            return resultBean.success(result);
        } else {
            PageHelper.startPage(index, 10);
            List<SzmCOrderMain> szmCOrderMains = szmCOrderMainMapper.selectByUserAndStoreId(userId, orderZt, storeId,
                    addressId, ordersource);
            List<String> orderNums = szmCOrderMains.stream().map(SzmCOrderMain::getOrderNum)
                    .collect(Collectors.toList());
            List<SmzCGroupOrder> smzCGroupOrders = CollectionUtils.isEmpty(orderNums) ? new ArrayList<>()
                    : smzCGroupOrderMapper.selectByOrderNums(orderNums);
            Map<String, List<SmzCGroupOrder>> groupOrderMap = smzCGroupOrders.stream()
                    .collect(Collectors.groupingBy(SmzCGroupOrder::getOrderMainId, Collectors.toList()));
            List<SmzCOrderDetails> smzCOrderDetails = CollectionUtils.isEmpty(orderNums) ? new ArrayList<>()
                    : smzCOrderDetailsMapper.selectByOrderNums(orderNums);
            Map<String, List<SmzCOrderDetails>> orderDetailMap = smzCOrderDetails.stream()
                    .collect(Collectors.groupingBy(SmzCOrderDetails::getOrderMainId, Collectors.toList()));
            List<Long> storeIds = szmCOrderMains.stream().map(SzmCOrderMain::getStoreId).distinct()
                    .collect(Collectors.toList());
            List<SzmCStore> szmCStoreList = CollectionUtils.isEmpty(storeIds) ? new ArrayList<>()
                    : szmCStoreMapper.findByIds(storeIds);
                    
        List<String> province = szmCStoreList.stream().map(SzmCStore::getStoreProvince).filter(Objects::nonNull).distinct()
                .collect(Collectors.toList());
        List<String> city = szmCStoreList.stream().map(SzmCStore::getStoreCity).filter(Objects::nonNull).distinct()
                .collect(Collectors.toList());
        List<String> area = szmCStoreList.stream().map(SzmCStore::getStoreArea).filter(Objects::nonNull).distinct()
                .collect(Collectors.toList());
        List<Provinces> provincesList = CollectionUtil.isEmpty(province) ? new ArrayList<>()
                : provincesMapper.selectprovinceids(province);
        List<Cities> citiesList = CollectionUtil.isEmpty(city) ? new ArrayList<>() : citiesMapper.selectByCityids(city);
        List<Areas> areasList = CollectionUtil.isEmpty(area) ? new ArrayList<>() : areasMapper.selectByareaids(area);

        szmCStoreList.forEach(e -> {
            provincesList.forEach(f -> {
                if (f.getProvinceid().equals(e.getStoreProvince())) {
                    e.setStoreProvinceName(f.getProvince());
                }
            });
            citiesList.forEach(f -> {
                if (f.getCityid().equals(e.getStoreCity())) {
                    e.setStoreCityName(f.getCity());
                }
            });
            areasList.forEach(f -> {
                if (f.getAreaid().equals(e.getStoreArea())) {
                    e.setStoreAreaName(f.getArea());
                }
            });
        });
            Map<Long, SzmCStore> szmCStoreMap = szmCStoreList.stream()
                    .collect(Collectors.toMap(SzmCStore::getStoreId, b -> b));

            List<SmzCPaymentMode> smzCPaymentModes = smzCPaymentModeMapper.selectAll();
            Map<Long, String> paymentModeMap = smzCPaymentModes.stream()
                    .collect(Collectors.toMap(SmzCPaymentMode::getPaymentModeId, SmzCPaymentMode::getPaymentModeName));
            List<Long> orderMainIds = szmCOrderMains.stream().map(SzmCOrderMain::getOrderMainId)
                    .collect(Collectors.toList());
            List<LongIntegerVo> pledgeBuckOrderList = CollectionUtils.isEmpty(orderMainIds) ? new ArrayList<>()
                    : pledgeBuckOrderMapper.selectPledgCountByOrderNumbers(orderMainIds);
            Map<Long, LongIntegerVo> pledgeBuckOrderMap = pledgeBuckOrderList.stream()
                    .collect(Collectors.toMap(LongIntegerVo::getId, b -> b));
            Map<Integer, OrderSource> orderSourceMap = orderSourceMapper.selectAll().stream()
                    .collect(Collectors.toMap(OrderSource::getId, orderSource -> orderSource));
            // 查询全部订单信息
            for (SzmCOrderMain szmCOrderMain : szmCOrderMains) {
                // 查看普通订单

                List<SmzCGroupOrder> smzCGroupOrders1 = groupOrderMap.get(szmCOrderMain.getOrderNum());
                List<SmzCOrderDetails> smzCOrderDetailsList = orderDetailMap.get(szmCOrderMain.getOrderNum());
                RetuenOrderList retuenOrderList = lookOrderDeatil(szmCOrderMain,
                        szmCStoreMap.get(szmCOrderMain.getStoreId()), smzCOrderDetailsList,
                        smzCGroupOrders1, paymentModeMap, orderSourceMap);
                LongIntegerVo longIntegerVo = pledgeBuckOrderMap.get(szmCOrderMain.getOrderMainId());
                if (null != longIntegerVo) {
                    retuenOrderList.setBuckState(longIntegerVo.getCount());
                }
                retuenOrderList.setUserName(szmCOrderMain.getUserName());
                retuenOrderList.setUserPhone(szmCOrderMain.getUserPhone());
                retuenOrderList.setUserAddress(szmCOrderMain.getUserAddress());
                retuenOrderList.setDiscounPrice(szmCOrderMain.getOrderDiscounts());
                retuenOrderList.setDikou(szmCOrderMain.getDikou());
                retuenOrderList.setZiti(szmCOrderMain.getZiti());
                retuenOrderList.setOrdersource(szmCOrderMain.getOrdersource());
                if (szmCOrderMain.getOrderStatus() == 5 || szmCOrderMain.getOrderStatus() == 9
                        || szmCOrderMain.getOrderStatus() == 10) {
                    Integer integer1 = szmCProductDiscussMapper.selectByOrderNumCount(szmCOrderMain.getOrderNum());
                    if (integer1 == 0) {
                        retuenOrderList.setEvaluate(1);
                    } else {
                        retuenOrderList.setEvaluate(2);
                    }
                } else {
                    retuenOrderList.setEvaluate(0);
                }
                if (szmCOrderMain.getOrderStatus() == 8 || szmCOrderMain.getOrderStatus() == 10) {
                    SmzCOrderReturns smzCOrderReturns = smzCOrderReturnsMapper
                            .selectOtherByOrderNum(szmCOrderMain.getOrderNum());
                    if (null != smzCOrderReturns) {
                        if (smzCOrderReturns.getProcessstate() == 0) {
                            retuenOrderList.setOrderReturnStete("申请中");
                        } else if (smzCOrderReturns.getProcessstate() == 1) {
                            retuenOrderList.setOrderReturnStete("已同意");
                        } else {
                            if (smzCOrderReturns.getReturnsType().equals("退货退款")) {
                                retuenOrderList.setOrderReturnStete("已拒绝");
                            }
                        }
                    }
                }
                allList.add(retuenOrderList);
            }
            PageInfo<SzmCOrderMain> productPageInfo = new PageInfo<>(szmCOrderMains);
            long count = productPageInfo.getTotal();
            result.put("count", count);
            result.put("list", allList);

            return resultBean.success(result);

        }
    }

    @Override
    public ResultBean findplatformorder(Map<String, Object> params) {
        ResultBean resultBean = new ResultBean();
        Map<String, Object> result = new HashMap<>();
        Integer index = Integer.parseInt((String) params.get("index"));
        Integer pageSize = Integer.parseInt((String) params.get("pageSize"));

        // 处理超时筛选参数
        String timeoutFilter = (String) params.get("timeoutFilter");
        if (timeoutFilter != null && !timeoutFilter.isEmpty()) {
            params.put("timeoutFilter", timeoutFilter);
        }

        // 处理退款订单筛选参数
        String back = (String) params.get("back");
        if (back != null && !back.isEmpty()) {
            params.put("back", back);
        }

        // 先查询总数
        Integer count = szmCOrderMainMapper.selectPingtaiCountByParams(params);

        // 添加分页参数 (MySQL OFFSET从0开始，所以需要减1)
        params.put("pageNo", (index - 1) * pageSize);
        params.put("pageSize", pageSize);

        // 查询分页数据
        List<SzmCOrderMain> szmCOrderMains = szmCOrderMainMapper.selectPingtaiByParams(params);

        // 使用重构后的方法处理订单列表
        List<RetuenOrderList> allList = processOrderList(szmCOrderMains);
        result.put("count", count);
        result.put("list", allList);

        return resultBean.success(result);

    }

    @Override
    public ResultBean findallorder(Map<String, Object> params) {
        ResultBean resultBean = new ResultBean();
        Map<String, Object> result = new HashMap<>();
        Integer index = Integer.parseInt((String) params.get("index"));
        Integer pageSize = Integer.parseInt((String) params.get("pageSize"));

        // 处理超时筛选参数
        String timeoutFilter = (String) params.get("timeoutFilter");
        if (timeoutFilter != null && !timeoutFilter.isEmpty()) {
            params.put("timeoutFilter", timeoutFilter);
        }

        // 处理退款订单筛选参数
        String back = (String) params.get("back");
        if (back != null && !back.isEmpty()) {
            params.put("back", back);
        }

        // PageHelper.startPage(index, pageSize);
        Integer pageNo = (index) * pageSize;
        params.put("pageNo", pageNo);
        params.put("pageSize", pageSize);
        List<SzmCOrderMain> szmCOrderMains = szmCOrderMainMapper.selectAllByParams(params);
        Integer count = szmCOrderMainMapper.selectAllCountByParams(params);

        // 使用重构后的方法处理订单列表
        List<RetuenOrderList> allList = processOrderList(szmCOrderMains);
        result.put("count", count);
        result.put("list", allList);

        return resultBean.success(result);

    }

    @Override
    public ResultBean findtransferorder(Map<String, Object> params) {
        ResultBean resultBean = new ResultBean();
        Map<String, Object> result = new HashMap<>();
        List<RetuenOrderList> allList = new ArrayList<>();
        Integer index = Integer.parseInt((String) params.get("index"));
        Integer pageSize = Integer.parseInt((String) params.get("pageSize"));

        PageHelper.startPage(index, pageSize);
        List<SzmCOrderMain> szmCOrderMains = szmCOrderMainMapper.selectTransferByParams(params);
        List<Long> storeIds = szmCOrderMains.stream().map(SzmCOrderMain::getStoreId).distinct()
                .collect(Collectors.toList());
        List<Long> oldStoreIds = szmCOrderMains.stream().map(SzmCOrderMain::getOldstoreid).distinct()
                .collect(Collectors.toList());
        List<String> orderNums = szmCOrderMains.stream().map(SzmCOrderMain::getOrderNum).collect(Collectors.toList());
        List<SmzCGroupOrder> smzCGroupOrders = CollectionUtils.isEmpty(orderNums) ? new ArrayList<>()
                : smzCGroupOrderMapper.selectByOrderNums(orderNums);
        List<SzmCStore> szmCStores = CollectionUtils.isEmpty(storeIds) ? new ArrayList<>()
                : szmCStoreMapper.findByIds(storeIds);
        List<SzmCStore> oldSzmCStores = CollectionUtils.isEmpty(oldStoreIds) ? new ArrayList<>()
                : szmCStoreMapper.findByIds(oldStoreIds);
        Map<Long, SzmCStore> szmCStoresMap = szmCStores.stream()
                .collect(Collectors.toMap(SzmCStore::getStoreId, b -> b));
        Map<Long, SzmCStore> oldSzmCStoresMap = oldSzmCStores.stream()
                .collect(Collectors.toMap(SzmCStore::getStoreId, b -> b));
        Map<String, List<SmzCGroupOrder>> groupOrderMap = smzCGroupOrders.stream()
                .collect(Collectors.groupingBy(SmzCGroupOrder::getOrderMainId, Collectors.toList()));
        List<SmzCOrderDetails> smzCOrderDetails = CollectionUtils.isEmpty(orderNums) ? new ArrayList<>()
                : smzCOrderDetailsMapper.selectByOrderNums(orderNums);
        Map<String, List<SmzCOrderDetails>> orderDetailMap = smzCOrderDetails.stream()
                .collect(Collectors.groupingBy(SmzCOrderDetails::getOrderMainId, Collectors.toList()));
        List<Long> userIds = szmCOrderMains.stream().map(SzmCOrderMain::getUserId).distinct()
                .collect(Collectors.toList());
        List<SzmCUser> szmCUsers = CollectionUtils.isEmpty(userIds) ? new ArrayList<>()
                : szmCUserMapper.findByIds(userIds);
        List<SmzCPaymentMode> smzCPaymentModes = smzCPaymentModeMapper.selectAll();
        Map<Long, String> paymentModeMap = smzCPaymentModes.stream()
                .collect(Collectors.toMap(SmzCPaymentMode::getPaymentModeId, SmzCPaymentMode::getPaymentModeName));
        Map<Long, SzmCUser> szmCuserMap = szmCUsers.stream().collect(Collectors.toMap(SzmCUser::getUserId, b -> b));
        List<Long> orderMainIds = szmCOrderMains.stream().map(SzmCOrderMain::getOrderMainId)
                .collect(Collectors.toList());
        List<LongIntegerVo> pledgeBuckOrderList = CollectionUtils.isEmpty(orderMainIds) ? new ArrayList<>()
                : pledgeBuckOrderMapper.selectPledgCountByOrderNumbers(orderMainIds);
        Map<Long, LongIntegerVo> pledgeBuckOrderMap = pledgeBuckOrderList.stream()
                .collect(Collectors.toMap(LongIntegerVo::getId, b -> b));
        Map<Integer, OrderSource> orderSourceMap = orderSourceMapper.selectAll().stream()
                .collect(Collectors.toMap(OrderSource::getId, orderSource -> orderSource));
        // 查询全部订单信息
        for (SzmCOrderMain szmCOrderMain : szmCOrderMains) {
            // 查看普通订单
            List<SmzCGroupOrder> smzCGroupOrders1 = groupOrderMap.get(szmCOrderMain.getOrderNum());
            List<SmzCOrderDetails> smzCOrderDetailsList = orderDetailMap.get(szmCOrderMain.getOrderNum());
            RetuenOrderList retuenOrderList = lookOrderDeatil(szmCOrderMain,
                    szmCStoresMap.get(szmCOrderMain.getStoreId()), smzCOrderDetailsList, smzCGroupOrders1,
                    paymentModeMap, orderSourceMap);
            retuenOrderList.setUserName(szmCOrderMain.getUserName());
            retuenOrderList.setUserPhone(szmCOrderMain.getUserPhone());
            retuenOrderList.setUserAddress(szmCOrderMain.getUserAddress());
            retuenOrderList.setDikou(szmCOrderMain.getDikou());
            retuenOrderList.setZiti(szmCOrderMain.getZiti());
            retuenOrderList.setOrdersource(szmCOrderMain.getOrdersource());
            retuenOrderList.setUserContent(szmCOrderMain.getUserContent());
            retuenOrderList.setFinishTime(szmCOrderMain.getFinishTime());
            LongIntegerVo longIntegerVo = pledgeBuckOrderMap.get(szmCOrderMain.getOrderMainId());
            if (null != longIntegerVo) {
                retuenOrderList.setBuckState(longIntegerVo.getCount());
            }
            SzmCUser szmCUser = szmCuserMap.get(szmCOrderMain.getUserId());
            if (null != szmCUser) {
                retuenOrderList.setMobile(szmCUser.getUserMobile());
            }
            SzmCStore oldSzmCStore = oldSzmCStoresMap.get(szmCOrderMain.getOldstoreid());
            if (null != oldSzmCStore) {
                retuenOrderList.setOldStoreName(oldSzmCStore.getStoreAme());
            }
            retuenOrderList.setDiscounPrice(szmCOrderMain.getOrderDiscounts());
            if (szmCOrderMain.getOrderStatus() == 1 || szmCOrderMain.getOrderStatus() == 2
                    || szmCOrderMain.getOrderStatus() == 3 || szmCOrderMain.getOrderStatus() == 4) {
                if (redisUtil.get("store" + szmCOrderMain.getOrderNum()) == null) {
                    retuenOrderList.setIsSend(1);
                } else {
                    retuenOrderList.setIsSend(2);
                }
            } else {
                retuenOrderList.setIsSend(0);
            }
            if (szmCOrderMain.getOrderStatus() == 5 || szmCOrderMain.getOrderStatus() == 9
                    || szmCOrderMain.getOrderStatus() == 10) {
                SzmCProductDiscuss szmCProductDiscuss = szmCProductDiscussMapper
                        .selectByOrderNum(szmCOrderMain.getOrderNum());
                if (szmCProductDiscuss == null) {
                    retuenOrderList.setEvaluate(1);
                } else {
                    retuenOrderList.setEvaluate(2);
                }
            } else {
                retuenOrderList.setEvaluate(0);
            }
            if (szmCOrderMain.getOrderStatus() == 8 || szmCOrderMain.getOrderStatus() == 10) {
                SmzCOrderReturns smzCOrderReturns = smzCOrderReturnsMapper
                        .selectOtherByOrderNum(szmCOrderMain.getOrderNum());
                if (null != smzCOrderReturns) {
                    if (smzCOrderReturns.getProcessstate() == 0) {
                        retuenOrderList.setOrderReturnStete("申请中");
                    } else if (smzCOrderReturns.getProcessstate() == 1) {
                        retuenOrderList.setOrderReturnStete("已同意");
                    } else {
                        if (smzCOrderReturns.getReturnsType().equals("退货退款")) {
                            retuenOrderList.setOrderReturnStete("已拒绝");
                        }
                    }
                }
            }
            allList.add(retuenOrderList);
        }
        PageInfo<SzmCOrderMain> productPageInfo = new PageInfo<>(szmCOrderMains);
        long count = productPageInfo.getTotal();
        result.put("count", count);
        result.put("list", allList);

        return resultBean.success(result);

    }

    @Override
    public ResultBean findtransferorderfront(Map<String, Object> params) {
        ResultBean resultBean = new ResultBean();
        Map<String, Object> result = new HashMap<>();
        List<RetuenOrderList> allList = new ArrayList<>();
        String type = (String) params.get("type");
        if (StringUtils.isEmpty(type)) {
            return resultBean.error("type不能为空");
        }
        if ((type.equals("3") || type.equals("4"))) {
            Object storeId = params.get("storeId");
            if (storeId != null && !storeId.toString().equals("2")) {
                return resultBean.error("必须是管理员才有调用接口权限");
            }
        }
        Integer index = Integer.parseInt((String) params.get("index"));
        // Integer pageSize = Integer.parseInt((String) params.get("pageSize"));
        Integer pageSize = 10000;

        PageHelper.startPage(index, pageSize);
        List<SzmCOrderMain> szmCOrderMains = szmCOrderMainMapper.selectTransferFrontByParams(params);
        List<Long> storeIds = szmCOrderMains.stream().map(SzmCOrderMain::getStoreId).distinct()
                .collect(Collectors.toList());
        List<Long> oldStoreIds = szmCOrderMains.stream().map(SzmCOrderMain::getOldstoreid).distinct()
                .collect(Collectors.toList());
        List<String> orderNums = szmCOrderMains.stream().map(SzmCOrderMain::getOrderNum).collect(Collectors.toList());
        List<SmzCGroupOrder> smzCGroupOrders = CollectionUtils.isEmpty(orderNums) ? new ArrayList<>()
                : smzCGroupOrderMapper.selectByOrderNums(orderNums);
        List<SzmCStore> szmCStores = CollectionUtils.isEmpty(storeIds) ? new ArrayList<>()
                : szmCStoreMapper.findByIds(storeIds);
        List<SzmCStore> oldSzmCStores = CollectionUtils.isEmpty(oldStoreIds) ? new ArrayList<>()
                : szmCStoreMapper.findByIds(oldStoreIds);
        Map<Long, SzmCStore> szmCStoresMap = szmCStores.stream()
                .collect(Collectors.toMap(SzmCStore::getStoreId, b -> b));
        Map<Long, SzmCStore> oldSzmCStoresMap = oldSzmCStores.stream()
                .collect(Collectors.toMap(SzmCStore::getStoreId, b -> b));
        Map<String, List<SmzCGroupOrder>> groupOrderMap = smzCGroupOrders.stream()
                .collect(Collectors.groupingBy(SmzCGroupOrder::getOrderMainId, Collectors.toList()));
        List<SmzCOrderDetails> smzCOrderDetails = CollectionUtils.isEmpty(orderNums) ? new ArrayList<>()
                : smzCOrderDetailsMapper.selectByOrderNums(orderNums);
        Map<String, List<SmzCOrderDetails>> orderDetailMap = smzCOrderDetails.stream()
                .collect(Collectors.groupingBy(SmzCOrderDetails::getOrderMainId, Collectors.toList()));
        List<Long> userIds = szmCOrderMains.stream().map(SzmCOrderMain::getUserId).distinct()
                .collect(Collectors.toList());
        List<SzmCUser> szmCUsers = CollectionUtils.isEmpty(userIds) ? new ArrayList<>()
                : szmCUserMapper.findByIds(userIds);
        List<SmzCPaymentMode> smzCPaymentModes = smzCPaymentModeMapper.selectAll();
        Map<Long, String> paymentModeMap = smzCPaymentModes.stream()
                .collect(Collectors.toMap(SmzCPaymentMode::getPaymentModeId, SmzCPaymentMode::getPaymentModeName));
        Map<Long, SzmCUser> szmCuserMap = szmCUsers.stream().collect(Collectors.toMap(SzmCUser::getUserId, b -> b));
        List<Long> orderMainIds = szmCOrderMains.stream().map(SzmCOrderMain::getOrderMainId)
                .collect(Collectors.toList());
        List<LongIntegerVo> pledgeBuckOrderList = CollectionUtils.isEmpty(orderMainIds) ? new ArrayList<>()
                : pledgeBuckOrderMapper.selectPledgCountByOrderNumbers(orderMainIds);
        Map<Long, LongIntegerVo> pledgeBuckOrderMap = pledgeBuckOrderList.stream()
                .collect(Collectors.toMap(LongIntegerVo::getId, b -> b));
        Map<Integer, OrderSource> orderSourceMap = orderSourceMapper.selectAll().stream()
                .collect(Collectors.toMap(OrderSource::getId, orderSource -> orderSource));
        // 查询全部订单信息
        for (SzmCOrderMain szmCOrderMain : szmCOrderMains) {
            // 查看普通订单
            List<SmzCGroupOrder> smzCGroupOrders1 = groupOrderMap.get(szmCOrderMain.getOrderNum());
            List<SmzCOrderDetails> smzCOrderDetailsList = orderDetailMap.get(szmCOrderMain.getOrderNum());
            RetuenOrderList retuenOrderList = lookOrderDeatil(szmCOrderMain,
                    szmCStoresMap.get(szmCOrderMain.getStoreId()), smzCOrderDetailsList, smzCGroupOrders1,
                    paymentModeMap, orderSourceMap);
            retuenOrderList.setUserName(szmCOrderMain.getUserName());
            retuenOrderList.setUserPhone(szmCOrderMain.getUserPhone());
            retuenOrderList.setUserAddress(szmCOrderMain.getUserAddress());
            retuenOrderList.setDikou(szmCOrderMain.getDikou());
            retuenOrderList.setZiti(szmCOrderMain.getZiti());
            retuenOrderList.setOrdersource(szmCOrderMain.getOrdersource());
            retuenOrderList.setUserContent(szmCOrderMain.getUserContent());
            retuenOrderList.setFinishTime(szmCOrderMain.getFinishTime());
            LongIntegerVo longIntegerVo = pledgeBuckOrderMap.get(szmCOrderMain.getOrderMainId());
            if (null != longIntegerVo) {
                retuenOrderList.setBuckState(longIntegerVo.getCount());
            }
            SzmCUser szmCUser = szmCuserMap.get(szmCOrderMain.getUserId());
            if (null != szmCUser) {
                retuenOrderList.setMobile(szmCUser.getUserMobile());
            }
            SzmCStore oldSzmCStore = oldSzmCStoresMap.get(szmCOrderMain.getOldstoreid());
            if (null != oldSzmCStore) {
                retuenOrderList.setOldStoreName(oldSzmCStore.getStoreAme());
            }
            retuenOrderList.setDiscounPrice(szmCOrderMain.getOrderDiscounts());
            if (szmCOrderMain.getOrderStatus() == 1 || szmCOrderMain.getOrderStatus() == 2
                    || szmCOrderMain.getOrderStatus() == 3 || szmCOrderMain.getOrderStatus() == 4) {
                if (redisUtil.get("store" + szmCOrderMain.getOrderNum()) == null) {
                    retuenOrderList.setIsSend(1);
                } else {
                    retuenOrderList.setIsSend(2);
                }
            } else {
                retuenOrderList.setIsSend(0);
            }
            if (szmCOrderMain.getOrderStatus() == 5 || szmCOrderMain.getOrderStatus() == 9
                    || szmCOrderMain.getOrderStatus() == 10) {
                SzmCProductDiscuss szmCProductDiscuss = szmCProductDiscussMapper
                        .selectByOrderNum(szmCOrderMain.getOrderNum());
                if (szmCProductDiscuss == null) {
                    retuenOrderList.setEvaluate(1);
                } else {
                    retuenOrderList.setEvaluate(2);
                }
            } else {
                retuenOrderList.setEvaluate(0);
            }
            if (szmCOrderMain.getOrderStatus() == 8 || szmCOrderMain.getOrderStatus() == 10) {
                SmzCOrderReturns smzCOrderReturns = smzCOrderReturnsMapper
                        .selectOtherByOrderNum(szmCOrderMain.getOrderNum());
                if (null != smzCOrderReturns) {
                    if (smzCOrderReturns.getProcessstate() == 0) {
                        retuenOrderList.setOrderReturnStete("申请中");
                    } else if (smzCOrderReturns.getProcessstate() == 1) {
                        retuenOrderList.setOrderReturnStete("已同意");
                    } else {
                        if (smzCOrderReturns.getReturnsType().equals("退货退款")) {
                            retuenOrderList.setOrderReturnStete("已拒绝");
                        }
                    }
                }
            }
            allList.add(retuenOrderList);
        }
        PageInfo<SzmCOrderMain> productPageInfo = new PageInfo<>(szmCOrderMains);
        long count = productPageInfo.getTotal();
        result.put("count", count);
        result.put("list", allList);

        return resultBean.success(result);

    }

    @Override
    public ResultBean findtransferorderfrontcount(Long storeId) {
        ResultBean resultBean = new ResultBean();
        Map<String, Object> result = new HashMap<>();
        Map<String, Object> map = new HashMap<>();
        // 可抢单
        map.put("type", 0);
        List<SzmCOrderMain> szmCOrderMains = szmCOrderMainMapper.selectTransferFrontCountByParams(map);
        Map<String, Object> map1 = new HashMap<>();
        // 我的抢单
        map1.put("type", 1);
        map1.put("storeId", storeId);
        List<SzmCOrderMain> szmCOrderMains1 = szmCOrderMainMapper.selectTransferFrontCountByParams(map1);
        Map<String, Object> map2 = new HashMap<>();
        // 我的抢单
        map2.put("type", 2);
        map2.put("storeId", storeId);
        List<SzmCOrderMain> szmCOrderMains2 = szmCOrderMainMapper.selectTransferFrontCountByParams(map2);
        result.put("count", CollectionUtils.isEmpty(szmCOrderMains) ? 0 : szmCOrderMains.size());
        result.put("count1", CollectionUtils.isEmpty(szmCOrderMains1) ? 0 : szmCOrderMains1.size());
        result.put("count2", CollectionUtils.isEmpty(szmCOrderMains2) ? 0 : szmCOrderMains2.size());
        return resultBean.success(result);

    }

    @Override
    public ResultBean findtransferorderverifycount(Long storeId) {
        ResultBean resultBean = new ResultBean();
        Map<String, Object> result = new HashMap<>();
        Map<String, Object> map = new HashMap<>();
        if (storeId == null || !storeId.equals(2L)) {
            return resultBean.error("必须是管理员才有调用接口权限");
        }
        // 可抢单
        map.put("type", 3);
        List<SzmCOrderMain> szmCOrderMains = szmCOrderMainMapper.selectTransferFrontCountByParams(map);
        Map<String, Object> map1 = new HashMap<>();
        // 我的抢单
        map1.put("type", 4);
        map1.put("storeId", storeId);
        List<SzmCOrderMain> szmCOrderMains1 = szmCOrderMainMapper.selectTransferFrontCountByParams(map1);
        result.put("count", CollectionUtils.isEmpty(szmCOrderMains) ? 0 : szmCOrderMains.size());
        result.put("count1", CollectionUtils.isEmpty(szmCOrderMains1) ? 0 : szmCOrderMains1.size());
        return resultBean.success(result);

    }

    @Override
    public void exportplatformorder(Map<String, Object> params, HttpServletResponse response) throws IOException {
        List<SzmCOrderMain> szmCOrderMains = szmCOrderMainMapper.selectPingtaiByParams(params);

        // 使用重构后的方法处理订单列表
        List<RetuenOrderList> allList = processOrderList(szmCOrderMains);

        orderMainExportService.write(allList, response);
    }

    @Override
    public void exporttransferorder(Map<String, Object> params, HttpServletResponse response) throws IOException {

        List<SzmCOrderMain> szmCOrderMains = szmCOrderMainMapper.selectTransferByParams(params);
        
        // 使用重构后的方法处理订单列表
        List<RetuenOrderList> allList = processOrderList(szmCOrderMains);

        orderMainExportService.write(allList, response);
    }

    @Override
    public void export(Long userId, Long orderZt, Long storeId, Long addressId, String startTime, String endTime,
            String username, Integer ordersource, Long deliveryUserId, String startTimeFinish, String endTimeFinish,
            Integer orderStatus,
            HttpServletResponse response) throws IOException {
        String startDate = "";
        String endDate = "";
        String startDateFinish = "";
        String endDateFinish = "";
        if (StringUtils.isNotEmpty(startTime) && StringUtils.isNotEmpty(endTime)) {
            startDate = startTime + " 00:00:00";
            endDate = endTime + " 23:59:59";
        }
        if (StringUtils.isNotEmpty(startTimeFinish) && StringUtils.isNotEmpty(endTimeFinish)) {
            startDateFinish = startTimeFinish + " 00:00:00";
            endDateFinish = endTimeFinish + " 23:59:59";
        }
        List<SzmCOrderMain> szmCOrderMains = szmCOrderMainMapper.selectStoreByUserAndStoreIdAllUserIdCanNo(userId,
                storeId, addressId, username, startDate, endDate, ordersource, deliveryUserId, startDateFinish,
                endDateFinish, orderStatus);
                
        // 使用重构后的方法处理订单列表
        List<RetuenOrderList> allList = processOrderList(szmCOrderMains);

        orderMainExportService.write(allList, response);
    }

    /**
     * 获取一个订单的信息
     *
     * @return
     */
    @Override
    public RetuenOrderList lookOrderDeatil(SzmCOrderMain szmCOrderMain, SzmCStore szmCStore,
            List<SmzCOrderDetails> smzCOrderDetails, List<SmzCGroupOrder> smzCGroupOrders,
            Map<Long, String> paymentModeMap, Map<Integer, OrderSource> orderSourceMap) {

        // List<SmzCOrderDetails> smzCOrderDetails =
        // smzCOrderDetailsMapper.selectByOrderNum(szmCOrderMain.getOrderNum());
        // //查看套餐订单
        // List<SmzCGroupOrder> smzCGroupOrders =
        // smzCGroupOrderMapper.selectByOrderNum(szmCOrderMain.getOrderNum());
        smzCGroupOrders = smzCGroupOrders == null ? new ArrayList<>() : smzCGroupOrders;
        smzCOrderDetails = smzCOrderDetails == null ? new ArrayList<>() : smzCOrderDetails;
        // 判断这次支付是不是有普通订单和套餐订单
        RetuenOrderList retuenOrderList = new RetuenOrderList();
        if (szmCOrderMain.getOrderStatus() == 0) {
            long longDate = szmCOrderMain.getCreateTime().getTime();
            long currentTime = longDate + 60 * 60 * 1000;
            Date date = new Date(currentTime);
            DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String nowTime = df.format(date);
            retuenOrderList.setEndTime(DateUtil.SchangeD(nowTime));
        }
        retuenOrderList.setDiscounPrice(szmCOrderMain.getOrderDiscounts());
        retuenOrderList.setDeliverId(szmCOrderMain.getDeliveryInfoId());
        if (null != szmCStore) {
            retuenOrderList.setStoreName(szmCStore.getStoreAme());
            retuenOrderList.setStoreMobile(szmCStore.getStorePhone());
            retuenOrderList.setStoreProvince(szmCStore.getStoreProvinceName());
            retuenOrderList.setStoreCity(szmCStore.getStoreCityName());
            retuenOrderList.setStoreArea(szmCStore.getStoreAreaName());
        }
        String paymentModeName = paymentModeMap.get(szmCOrderMain.getPaymentModeId());
        if (paymentModeName != null) {
            retuenOrderList.setPayMentId(szmCOrderMain.getPaymentModeId());
            retuenOrderList.setNewOrderState(paymentModeName);
        }
        OrderSource orderSource = szmCOrderMain.getOrdersource() == null || szmCOrderMain.getOrdersource().equals(0)
                ? orderSourceMap.get(0)
                : orderSourceMap.get(szmCOrderMain.getOrdersource());
        if (orderSource != null) {
            retuenOrderList.setOrderSourceName(orderSource.getName());
            retuenOrderList.setOrderSourceImage(orderSource.getImage());
        }
        switch (szmCOrderMain.getOrderStatus()) {
            case 0:
                if (paymentModeName != null) {
                    if (szmCOrderMain.getPaymentModeId() == 1) {
                        retuenOrderList.setNewOrderState("微信待付款");
                    } else if (szmCOrderMain.getPaymentModeId() == 4) {
                        retuenOrderList.setNewOrderState("钱包待付款");
                    }
                }
                break;
            case 1:
                if (paymentModeName != null) {
                    retuenOrderList.setNewOrderState(paymentModeName);
                }
                break;
            case 2:
                retuenOrderList.setNewOrderState(
                        szmCOrderMain.getZiti() != null && szmCOrderMain.getZiti().equals(0) ? "待自提" : "待发货");
                break;
            case 3:
                retuenOrderList.setNewOrderState("已发货");
                break;
            case 4:
                retuenOrderList.setNewOrderState("待签收");
                break;
            case 5:
                retuenOrderList.setNewOrderState("已签收");
                break;
            case 6:
                retuenOrderList.setNewOrderState("已取消");
                break;
            case 7:
                retuenOrderList.setNewOrderState("拒单");
                break;
            case 8:
                SmzCOrderReturns smzCOrderReturns = smzCOrderReturnsMapper
                        .selectOtherByOrderNum(szmCOrderMain.getOrderNum());
                if (smzCOrderReturns != null && smzCOrderReturns.getReturnsType().equals("退款")) {
                    retuenOrderList.setNewOrderState("退款");
                } else {
                    retuenOrderList.setNewOrderState("退货");
                }
                break;
            case 9:
                retuenOrderList.setNewOrderState("已评价");
                break;
            case 10:
                retuenOrderList.setNewOrderState("已完成");
                break;
        }
        // 订单的状态
        retuenOrderList.setDistance(
                szmCOrderMain.getDistance() != null ? szmCOrderMain.getDistance().setScale(3, BigDecimal.ROUND_HALF_UP)
                        : null);
        retuenOrderList.setShipsn(szmCOrderMain.getShipsn());
        retuenOrderList.setShipchannel(szmCOrderMain.getShipchannel()); 
        retuenOrderList.setStoreId(szmCOrderMain.getStoreId());
        retuenOrderList.setOrderState(szmCOrderMain.getOrderStatus());
        retuenOrderList.setOrderDate(szmCOrderMain.getCreateTime());
        retuenOrderList.setFinishTime(szmCOrderMain.getFinishTime());
        retuenOrderList.setOrderTotalPrice(Double.parseDouble(szmCOrderMain.getR1()));
        retuenOrderList.setOrderNumber(szmCOrderMain.getOrderNum());
        retuenOrderList.setOrderId(szmCOrderMain.getOrderMainId());
        retuenOrderList.setOrderTotalNumber(Integer.parseInt(szmCOrderMain.getR5()));
        retuenOrderList.setOrderPrice(szmCOrderMain.getOrderMoney());
        retuenOrderList.setTranstatus(szmCOrderMain.getTranstatus());
        retuenOrderList.setTranprice(szmCOrderMain.getTranprice());
        retuenOrderList.setOldprice(szmCOrderMain.getOldprice());
        retuenOrderList.setPlatformprice(szmCOrderMain.getPlatformprice());
        retuenOrderList.setOldstoreid(szmCOrderMain.getOldstoreid());
        retuenOrderList.setAppkey(szmCOrderMain.getAppkey());
        retuenOrderList.setApptoken(szmCOrderMain.getApptoken());
        retuenOrderList.setPicurl(szmCOrderMain.getPicurl());
        retuenOrderList.setLon(szmCOrderMain.getLon());
        retuenOrderList.setLat(szmCOrderMain.getLat());
        retuenOrderList.setYuyuetime(szmCOrderMain.getYuyuetime());
        retuenOrderList.setException(szmCOrderMain.getException());
        retuenOrderList.setIsprint(szmCOrderMain.getIsprint());
        retuenOrderList.setMark(szmCOrderMain.getMark());
        if (szmCOrderMain.getOrderStatus() == 1 || szmCOrderMain.getOrderStatus() == 2) {
            if (szmCOrderMain.getIsReturn() == 1) {
                SmzCOrderReturns smzCOrderReturns = smzCOrderReturnsMapper
                        .selectOtherByOrderNum(szmCOrderMain.getOrderNum());
                if (2 != smzCOrderReturns.getProcessstate()) {
                    retuenOrderList.setIsReturnMoney(1);
                    retuenOrderList.setIsReturnGoods(0);
                }
            }
        } else if (szmCOrderMain.getOrderStatus() == 5) {
            if (szmCOrderMain.getIsReturn() == 1) {
                retuenOrderList.setIsReturnMoney(0);
                retuenOrderList.setIsReturnGoods(1);
            }
        }

        if (!CollectionUtils.isEmpty(smzCOrderDetails) && !CollectionUtils.isEmpty(smzCGroupOrders)) {
            OrderDeatil orderDeatil = new OrderDeatil();
            // 订单的状态
            orderDeatil.setOrderNumber(szmCOrderMain.getOrderNum());
            // 返回的订单的详细信息
            List<OrderShopDeatil> orderShopDeatils = resultOrderMain(smzCOrderDetails);
            orderDeatil.setOrderShopDeatilList(orderShopDeatils);
            List<OrderDeatil> orderDeatils = new ArrayList<>();
            orderDeatils.add(orderDeatil);
            retuenOrderList.setList(orderDeatils);
            // 添加套餐订单
            List<GroupShop> groupShops1 = resultGroupShop(smzCGroupOrders);
            retuenOrderList.setGroupShopList(groupShops1);

        } else if (!CollectionUtils.isEmpty(smzCOrderDetails) && CollectionUtils.isEmpty(smzCGroupOrders)) {
            OrderDeatil orderDeatil = new OrderDeatil();
            // 订单号
            orderDeatil.setOrderNumber(szmCOrderMain.getOrderNum());
            // 订单详细信息
            // 返回的订单的详细信息
            List<OrderShopDeatil> orderShopDeatils = resultOrderMain(smzCOrderDetails);
            orderDeatil.setOrderShopDeatilList(orderShopDeatils);
            List<OrderDeatil> orderDeatils = new ArrayList<>();
            orderDeatils.add(orderDeatil);
            retuenOrderList.setList(orderDeatils);
        } else {
            // 添加套餐订单
            List<GroupShop> groupShops1 = resultGroupShop(smzCGroupOrders);
            retuenOrderList.setGroupShopList(groupShops1);
        }
        Long brandId = null;
        // for (SmzCOrderDetails smzCOrderDetails1 : smzCOrderDetails) {
        // Long productId =
        // szmCProductModelMapper.selectProductId(smzCOrderDetails1.getProductModelId());
        // SzmCProduct szmCProduct = szmCProductMapper.selectProductById(productId);
        // if (Validator.isEmpty(szmCProduct)) {
        // continue;
        // }
        // SzmCProductClassify szmCProductClassify = szmCProductClassifyMapper
        // .selectR4ById(szmCProduct.getProductClassifyId());
        // if (szmCProductClassify != null &&
        // StringUtils.isNotEmpty(szmCProductClassify.getR4()) &&
        // "1".equals(szmCProductClassify.getR4())) {
        // brandId = Long.parseLong(szmCProduct.getR3());
        // break;
        // }
        // }
        // for (SmzCGroupOrder smzCGroupOrder : smzCGroupOrders) {
        // List<Long> szmCShopgroupLists = szmCShopgroupListMapper
        // .selectModelId(smzCGroupOrder.getShopgroupId().intValue());
        // for (Long szmCShopgroupList : szmCShopgroupLists) {
        // Long productId = szmCProductModelMapper.selectProductId(szmCShopgroupList);
        // SzmCProduct szmCProduct = szmCProductMapper.selectByPrimaryKey(productId);
        // SzmCProductClassify szmCProductClassify = szmCProductClassifyMapper
        // .selectR4ById(szmCProduct.getProductClassifyId());
        // if ("1".equals(szmCProductClassify.getR4())) {
        // brandId = Long.parseLong(szmCProduct.getR3());
        // break;
        // }
        // }
        // }
        if (szmCOrderMain.getOrderStatus() != 0 && szmCOrderMain.getOrderStatus() != 6
                && szmCOrderMain.getOrderStatus() != 8
                && (szmCOrderMain.getOrdersource() == null || szmCOrderMain.getOrdersource().equals(0))) {
            // SmzCOrderReturns smzCOrderReturns = smzCOrderReturnsMapper
            // .selectOtherByOrderNum(szmCOrderMain.getOrderNum());
            // if ((szmCOrderMain.getIsReturn() == 0
            // || (null != smzCOrderReturns && 2 == smzCOrderReturns.getProcessstate()))) {
            SmzCDeliveryInfo smzCDeliveryInfo = smzCDeliveryInfoMapper
                    .selectR3AndStateByOrderId(szmCOrderMain.getOrderMainId());
            if (null != smzCDeliveryInfo) {
                if (null != smzCDeliveryInfo.getR3() && !"".equals(smzCDeliveryInfo.getR3())) {
                    // 1表示申请押桶中
                    retuenOrderList.setApplyBuck(Integer.parseInt(smzCDeliveryInfo.getR3()));
                }
                // else {
                // List<PledgeBuckOrder> pledgeBuckOrders = pledgeBuckOrderMapper
                // .selectOrderAndPayTypeDelState(szmCOrderMain.getOrderNum(), "0", "1", null);
                // if (pledgeBuckOrders.size() == 0) {
                // retuenOrderList.setApplyBuck(0);
                // } else {
                // if (pledgeBuckOrders.get(0).getDelState() == 0) {
                // retuenOrderList.setApplyBuck(1);
                // } else {
                // retuenOrderList.setApplyBuck(2);
                // }
                // }
                // }
            }
            // else {
            // List<PledgeBuckOrder> pledgeBuckOrders = pledgeBuckOrderMapper
            // .selectOrderAndPayTypeDelState(szmCOrderMain.getOrderNum(), "0", "1", null);
            // if (pledgeBuckOrders.size() == 0) {
            // retuenOrderList.setApplyBuck(0);
            // } else {
            // if (pledgeBuckOrders.get(0).getDelState() == 0) {
            // retuenOrderList.setApplyBuck(1);
            // } else {
            // retuenOrderList.setApplyBuck(2);
            // }
            // }
            // }
            if (null == smzCDeliveryInfo) {
                retuenOrderList.setApplyBuck(null);
            } else {
                if (0 == smzCDeliveryInfo.getDeliveryInfoState()) {
                    retuenOrderList.setApplyBuck(null);
                }
            }
            // }
        }
        return retuenOrderList;
    }

    /**
     * 优化版本的lookOrderDeatil方法，避免在方法内部进行数据库查询
     *
     * @param szmCOrderMain 订单主表
     * @param szmCStore 店铺信息
     * @param smzCOrderDetails 订单详情列表
     * @param smzCGroupOrders 套餐订单列表
     * @param paymentModeMap 支付方式映射
     * @param orderSourceMap 订单来源映射
     * @param orderReturnsMap 退货信息映射
     * @param deliveryInfoMap 配送信息映射
     * @return 订单详情
     */
    public RetuenOrderList lookOrderDeatilOptimized(SzmCOrderMain szmCOrderMain, SzmCStore szmCStore,
            List<SmzCOrderDetails> smzCOrderDetails, List<SmzCGroupOrder> smzCGroupOrders,
            Map<Long, String> paymentModeMap, Map<Integer, OrderSource> orderSourceMap,
            Map<String, SmzCOrderReturns> orderReturnsMap, Map<Long, SmzCDeliveryInfo> deliveryInfoMap) {

        smzCGroupOrders = smzCGroupOrders == null ? new ArrayList<>() : smzCGroupOrders;
        smzCOrderDetails = smzCOrderDetails == null ? new ArrayList<>() : smzCOrderDetails;

        // 判断这次支付是不是有普通订单和套餐订单
        RetuenOrderList retuenOrderList = new RetuenOrderList();
        if (szmCOrderMain.getOrderStatus() == 0) {
            long longDate = szmCOrderMain.getCreateTime().getTime();
            long currentTime = longDate + 60 * 60 * 1000;
            Date date = new Date(currentTime);
            DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String nowTime = df.format(date);
            retuenOrderList.setEndTime(DateUtil.SchangeD(nowTime));
        }
        retuenOrderList.setDiscounPrice(szmCOrderMain.getOrderDiscounts());
        retuenOrderList.setDeliverId(szmCOrderMain.getDeliveryInfoId());
        if (null != szmCStore) {
            retuenOrderList.setStoreName(szmCStore.getStoreAme());
            retuenOrderList.setStoreMobile(szmCStore.getStorePhone());
            retuenOrderList.setStoreProvince(szmCStore.getStoreProvinceName());
            retuenOrderList.setStoreCity(szmCStore.getStoreCityName());
            retuenOrderList.setStoreArea(szmCStore.getStoreAreaName());
        }
        String paymentModeName = paymentModeMap.get(szmCOrderMain.getPaymentModeId());
        if (paymentModeName != null) {
            retuenOrderList.setPayMentId(szmCOrderMain.getPaymentModeId());
            retuenOrderList.setNewOrderState(paymentModeName);
        }
        OrderSource orderSource = szmCOrderMain.getOrdersource() == null || szmCOrderMain.getOrdersource().equals(0)
                ? orderSourceMap.get(0)
                : orderSourceMap.get(szmCOrderMain.getOrdersource());
        if (orderSource != null) {
            retuenOrderList.setOrderSourceName(orderSource.getName());
            retuenOrderList.setOrderSourceImage(orderSource.getImage());
        }
        switch (szmCOrderMain.getOrderStatus()) {
            case 0:
                if (paymentModeName != null) {
                    if (szmCOrderMain.getPaymentModeId() == 1) {
                        retuenOrderList.setNewOrderState("微信待付款");
                    } else if (szmCOrderMain.getPaymentModeId() == 4) {
                        retuenOrderList.setNewOrderState("钱包待付款");
                    }
                }
                break;
            case 1:
                if (paymentModeName != null) {
                    retuenOrderList.setNewOrderState(paymentModeName);
                }
                break;
            case 2:
                retuenOrderList.setNewOrderState(
                        szmCOrderMain.getZiti() != null && szmCOrderMain.getZiti().equals(0) ? "待自提" : "待发货");
                break;
            case 3:
                retuenOrderList.setNewOrderState("已发货");
                break;
            case 4:
                retuenOrderList.setNewOrderState("待签收");
                break;
            case 5:
                retuenOrderList.setNewOrderState("已签收");
                break;
            case 6:
                retuenOrderList.setNewOrderState("已取消");
                break;
            case 7:
                retuenOrderList.setNewOrderState("拒单");
                break;
            case 8:
                // 使用预查询的退货信息，避免数据库查询
                SmzCOrderReturns smzCOrderReturns = orderReturnsMap.get(szmCOrderMain.getOrderNum());
                if (smzCOrderReturns != null && smzCOrderReturns.getReturnsType().equals("退款")) {
                    retuenOrderList.setNewOrderState("退款");
                } else {
                    retuenOrderList.setNewOrderState("退货");
                }
                break;
            case 9:
                retuenOrderList.setNewOrderState("已评价");
                break;
            case 10:
                retuenOrderList.setNewOrderState("已完成");
                break;
        }

        // 设置订单的其他基本信息
        retuenOrderList.setDistance(
                szmCOrderMain.getDistance() != null ? szmCOrderMain.getDistance().setScale(3, BigDecimal.ROUND_HALF_UP)
                        : null);
        retuenOrderList.setShipsn(szmCOrderMain.getShipsn());
        retuenOrderList.setShipchannel(szmCOrderMain.getShipchannel());
        retuenOrderList.setStoreId(szmCOrderMain.getStoreId());
        retuenOrderList.setOrderState(szmCOrderMain.getOrderStatus());
        retuenOrderList.setOrderDate(szmCOrderMain.getCreateTime());
        retuenOrderList.setFinishTime(szmCOrderMain.getFinishTime());
        retuenOrderList.setOrderTotalPrice(Double.parseDouble(szmCOrderMain.getR1()));
        retuenOrderList.setOrderNumber(szmCOrderMain.getOrderNum());
        retuenOrderList.setOrderId(szmCOrderMain.getOrderMainId());
        retuenOrderList.setOrderTotalNumber(Integer.parseInt(szmCOrderMain.getR5()));
        retuenOrderList.setOrderPrice(szmCOrderMain.getOrderMoney());
        retuenOrderList.setTranstatus(szmCOrderMain.getTranstatus());
        retuenOrderList.setTranprice(szmCOrderMain.getTranprice());
        retuenOrderList.setOldprice(szmCOrderMain.getOldprice());
        retuenOrderList.setPlatformprice(szmCOrderMain.getPlatformprice());
        retuenOrderList.setOldstoreid(szmCOrderMain.getOldstoreid());
        retuenOrderList.setAppkey(szmCOrderMain.getAppkey());
        retuenOrderList.setApptoken(szmCOrderMain.getApptoken());
        retuenOrderList.setPicurl(szmCOrderMain.getPicurl());
        retuenOrderList.setLon(szmCOrderMain.getLon());
        retuenOrderList.setLat(szmCOrderMain.getLat());
        retuenOrderList.setYuyuetime(szmCOrderMain.getYuyuetime());
        retuenOrderList.setException(szmCOrderMain.getException());
        retuenOrderList.setIsprint(szmCOrderMain.getIsprint());
        retuenOrderList.setMark(szmCOrderMain.getMark());

        // 处理退货相关逻辑，使用预查询的数据
        if (szmCOrderMain.getOrderStatus() == 1 || szmCOrderMain.getOrderStatus() == 2) {
            if (szmCOrderMain.getIsReturn() == 1) {
                SmzCOrderReturns smzCOrderReturns = orderReturnsMap.get(szmCOrderMain.getOrderNum());
                if (smzCOrderReturns != null && 2 != smzCOrderReturns.getProcessstate()) {
                    retuenOrderList.setIsReturnMoney(1);
                    retuenOrderList.setIsReturnGoods(0);
                }
            }
        } else if (szmCOrderMain.getOrderStatus() == 5) {
            if (szmCOrderMain.getIsReturn() == 1) {
                retuenOrderList.setIsReturnMoney(0);
                retuenOrderList.setIsReturnGoods(1);
            }
        }

        // 处理订单详情和套餐信息
        if (!CollectionUtils.isEmpty(smzCOrderDetails) && !CollectionUtils.isEmpty(smzCGroupOrders)) {
            OrderDeatil orderDeatil = new OrderDeatil();
            orderDeatil.setOrderNumber(szmCOrderMain.getOrderNum());
            List<OrderShopDeatil> orderShopDeatils = resultOrderMain(smzCOrderDetails);
            orderDeatil.setOrderShopDeatilList(orderShopDeatils);
            List<OrderDeatil> orderDeatils = new ArrayList<>();
            orderDeatils.add(orderDeatil);
            retuenOrderList.setList(orderDeatils);
            List<GroupShop> groupShops1 = resultGroupShop(smzCGroupOrders);
            retuenOrderList.setGroupShopList(groupShops1);
        } else if (!CollectionUtils.isEmpty(smzCOrderDetails) && CollectionUtils.isEmpty(smzCGroupOrders)) {
            OrderDeatil orderDeatil = new OrderDeatil();
            orderDeatil.setOrderNumber(szmCOrderMain.getOrderNum());
            List<OrderShopDeatil> orderShopDeatils = resultOrderMain(smzCOrderDetails);
            orderDeatil.setOrderShopDeatilList(orderShopDeatils);
            List<OrderDeatil> orderDeatils = new ArrayList<>();
            orderDeatils.add(orderDeatil);
            retuenOrderList.setList(orderDeatils);
        } else {
            List<GroupShop> groupShops1 = resultGroupShop(smzCGroupOrders);
            retuenOrderList.setGroupShopList(groupShops1);
        }

        // 处理押桶申请状态，使用预查询的配送信息
        if (szmCOrderMain.getOrderStatus() != 0 && szmCOrderMain.getOrderStatus() != 6
                && szmCOrderMain.getOrderStatus() != 8
                && (szmCOrderMain.getOrdersource() == null || szmCOrderMain.getOrdersource().equals(0))) {
            SmzCDeliveryInfo smzCDeliveryInfo = deliveryInfoMap.get(szmCOrderMain.getOrderMainId());
            if (null != smzCDeliveryInfo) {
                if (null != smzCDeliveryInfo.getR3() && !"".equals(smzCDeliveryInfo.getR3())) {
                    // 1表示申请押桶中
                    retuenOrderList.setApplyBuck(Integer.parseInt(smzCDeliveryInfo.getR3()));
                }
            }
            if (null == smzCDeliveryInfo) {
                retuenOrderList.setApplyBuck(null);
            } else {
                if (0 == smzCDeliveryInfo.getDeliveryInfoState()) {
                    retuenOrderList.setApplyBuck(null);
                }
            }
        }

        return retuenOrderList;
    }

    @Override
    public ResultBean info(Long id) {
        return new ResultBean().success(szmCOrderMainMapper.selectByPrimaryKey(id));
    }

    @Override
    public ResultBean updatestore(SzmCOrderMain szmCOrderMain12) {
        SzmCOrderMain szmCOrderMain = szmCOrderMainMapper.selectByPrimaryKey(szmCOrderMain12.getOrderMainId());
        if (null == szmCOrderMain) {
            return new ResultBean().error("订单不存在");
        }
        if (szmCOrderMain12.getStoreId().equals(szmCOrderMain.getStoreId())) {
            return new ResultBean().error("修改水站不能跟旧水站一致");
        }
        // 一定要在未派送前才能换
        // if (!szmCOrderMain.getOrderStatus().equals(2)) {
        // return new ResultBean().error("订单状态不对(未派单才能换)，不能换水站");
        // }
        SzmCUser szmCUser = szmCUserMapper.selectByUserId(Long.parseLong(szmCOrderMain.getCreateIden()));
        szmCUser.setR2(szmCOrderMain12.getStoreId().toString());
        szmCUser.setStoreId(szmCOrderMain12.getStoreId());
        szmCUserMapper.updateByPrimaryKey(szmCUser);
        szmCOrderMain.setStoreId(szmCOrderMain12.getStoreId());
        szmCOrderMain.setR2(szmCOrderMain12.getStoreId().toString());
        szmCOrderMainMapper.updateByPrimaryKey(szmCOrderMain);
        // 找到新旧商家
        SzmCStore szmCStore1 = szmCStoreMapper.selectByPrimaryKey(szmCOrderMain12.getStoreId());
        SzmCStore szmCStore2 = szmCStoreMapper.selectByPrimaryKey(szmCOrderMain.getStoreId());
        // 新增水站订单通知
        StoreMsg storeMsg = new StoreMsg();
        storeMsg.setStoreMsgModel("订单更换商家");// 模块名称
        storeMsg.setStoreId(szmCOrderMain12.getStoreId());// 商户id
        storeMsg.setModelUrl("orderAdmin");// 模块地址
        storeMsg.setUserId(szmCOrderMain.getUserId());// 用户id
        StringBuilder stringBuffer = new StringBuilder();
        stringBuffer.append(
                "订单：" + szmCOrderMain.getOrderNum() + " 更换了商家：" + (szmCStore1 == null ? "-" : szmCStore1.getStoreAme())
                        + "，原商家为：" + (szmCStore2 == null ? "-" : szmCStore2.getStoreAme()));
        storeMsg.setContent(stringBuffer.toString());// 内容
        storeMsg.setReadState(0);// 已读 1 未读 0
        storeMsg.setSource(21);// 来源 1 待处理订单，2退款，3 购买水票，4退桶，5借物资，6 还物资，7 使用水票
        storeMsg.setDelState(0);// 删除状态
        storeMsg.setR1("pagesMore/mine/exchangeOrder/exchangeOrder");// 小程序路径
        storeMsg.setR2(szmCOrderMain.getOrderNum());// id
        storeMsg.setR3(szmCOrderMain.getOrderMainId().toString());// id
        storeMsgMapper.insert(storeMsg);
        return new ResultBean().success();
    }

    @Override
    public ResultBean updateother(SzmCOrderMain szmCOrderMain12) {
        SzmCOrderMain szmCOrderMain = szmCOrderMainMapper.selectByPrimaryKey(szmCOrderMain12.getOrderMainId());
        if (null == szmCOrderMain) {
            return new ResultBean().error("订单不存在");
        }
        if (szmCOrderMain12.getIsprint() != null) {
            szmCOrderMain.setIsprint(szmCOrderMain12.getIsprint());
        }
        if (szmCOrderMain12.getException() != null) {
            szmCOrderMain.setException(szmCOrderMain12.getException());
        }
        if (szmCOrderMain12.getMark() != null) {
            szmCOrderMain.setMark(szmCOrderMain12.getMark());
        }
        szmCOrderMainMapper.updateByPrimaryKey(szmCOrderMain);
        return new ResultBean().success();
    }

    @Override
    public ResultBean updatebatchmark(String orderIds, String mark) {
        List<Long> orderMainIds = Arrays.stream(orderIds.split(",")).map(Long::parseLong)
                .collect(Collectors.toList());
        for (Long mainId : orderMainIds) {
            szmCOrderMainMapper.updatemark(mainId, mark);
        }
        return new ResultBean().success();
    }

    @Override
    public ResultBean updatebatchstore(String orderIds, Long newStoreId, String reason) {
        try {
            List<Long> orderMainIds = Arrays.stream(orderIds.split(",")).map(Long::parseLong)
                    .collect(Collectors.toList());

            int successCount = 0;
            int failCount = 0;
            StringBuilder errorMessages = new StringBuilder();
            SzmCStore szmCStore1 = szmCStoreMapper.selectByPrimaryKey(newStoreId);

            for (Long mainId : orderMainIds) {
                try {
                    SzmCOrderMain szmCOrderMain = szmCOrderMainMapper.selectByPrimaryKey(mainId);
                    if (null == szmCOrderMain) {
                        failCount++;
                        errorMessages.append("订单ID ").append(mainId).append(" 不存在; ");
                        continue;
                    }

                    if (newStoreId.equals(szmCOrderMain.getStoreId())) {
                        failCount++;
                        errorMessages.append("订单ID ").append(mainId).append(" 已属于该商家; ");
                        continue;
                    }

                    // 更新订单的商家信息
                    szmCOrderMain.setStoreId(newStoreId);
                    szmCOrderMain.setR2(newStoreId.toString());
                    szmCOrderMainMapper.updateByPrimaryKey(szmCOrderMain);

                    // 更新用户的默认商家信息
                    if (szmCOrderMain.getCreateIden() != null) {
                        SzmCUser szmCUser = szmCUserMapper
                                .selectByUserId(Long.parseLong(szmCOrderMain.getCreateIden()));
                        if (szmCUser != null) {
                            szmCUser.setR2(newStoreId.toString());
                            szmCUser.setStoreId(newStoreId);
                            szmCUserMapper.updateByPrimaryKey(szmCUser);
                        }
                    }

                    // 找到新旧商家
                    SzmCStore szmCStore2 = szmCStoreMapper.selectByPrimaryKey(szmCOrderMain.getStoreId());
                    // 新增水站订单通知
                    StoreMsg storeMsg = new StoreMsg();
                    storeMsg.setStoreMsgModel("订单更换商家");// 模块名称
                    storeMsg.setStoreId(newStoreId);// 商户id
                    storeMsg.setModelUrl("orderAdmin");// 模块地址
                    storeMsg.setUserId(szmCOrderMain.getUserId());// 用户id
                    StringBuilder stringBuffer = new StringBuilder();
                    stringBuffer.append("订单：" + szmCOrderMain.getOrderNum() + " 更换了商家："
                            + (szmCStore1 == null ? "-" : szmCStore1.getStoreAme()) + "，原商家为："
                            + (szmCStore2 == null ? "-" : szmCStore2.getStoreAme()));
                    storeMsg.setContent(stringBuffer.toString());// 内容
                    storeMsg.setReadState(0);// 已读 1 未读 0
                    storeMsg.setSource(21);// 来源 1 待处理订单，2退款，3 购买水票，4退桶，5借物资，6 还物资，7 使用水票
                    storeMsg.setDelState(0);// 删除状态
                    storeMsg.setR1("pagesMore/mine/exchangeOrder/exchangeOrder");// 小程序路径
                    storeMsg.setR2(szmCOrderMain.getOrderNum());// id
                    storeMsg.setR3(szmCOrderMain.getOrderMainId().toString());// id
                    storeMsgMapper.insert(storeMsg);

                    successCount++;
                } catch (Exception e) {
                    failCount++;
                    errorMessages.append("订单ID ").append(mainId).append(" 更新失败: ").append(e.getMessage()).append("; ");
                }
            }

            if (successCount > 0 && failCount == 0) {
                return new ResultBean().success("成功更换 " + successCount + " 个订单的商家");
            } else if (successCount > 0 && failCount > 0) {
                return new ResultBean().success(
                        "成功更换 " + successCount + " 个订单的商家，失败 " + failCount + " 个。失败原因: " + errorMessages.toString());
            } else {
                return new ResultBean().error("批量更换商家失败: " + errorMessages.toString());
            }
        } catch (Exception e) {
            return new ResultBean().error("批量更换商家失败: " + e.getMessage());
        }
    }

    @Override
    public ResultBean updatebatchstorebygeofence(String orderIds, String reason) {
        try {
            List<Long> orderMainIds = Arrays.stream(orderIds.split(",")).map(Long::parseLong)
                    .collect(Collectors.toList());

            int successCount = 0;
            int failCount = 0;
            int noChangeCount = 0;
            StringBuilder errorMessages = new StringBuilder();

            for (Long mainId : orderMainIds) {
                try {
                    SzmCOrderMain szmCOrderMain = szmCOrderMainMapper.selectByPrimaryKey(mainId);
                    if (szmCOrderMain == null) {
                        failCount++;
                        errorMessages.append("订单ID ").append(mainId).append(" 不存在; ");
                        continue;
                    }

                    // 获取订单的坐标和地址信息
                    BigDecimal lat = szmCOrderMain.getLat();
                    BigDecimal lon = szmCOrderMain.getLon();

                    // 解析地址信息
                    String userAddress = szmCOrderMain.getUserAddress();
                    String province = "";
                    String city = "";
                    String town = "";
                    String street = "";
                    String encrypt_detail = "";

                    if (StringUtils.isNotEmpty(userAddress)) {
                        // 简单的地址解析，可以根据实际情况调整
                        String[] addressParts = userAddress.split("省|市|区|县|镇|街道");
                        if (addressParts.length > 0) province = addressParts[0] + (userAddress.contains("省") ? "省" : "");
                        if (addressParts.length > 1) city = addressParts[1] + (userAddress.contains("市") ? "市" : "");
                        if (addressParts.length > 2) town = addressParts[2] + (userAddress.contains("区") || userAddress.contains("县") ? (userAddress.contains("区") ? "区" : "县") : "");
                        if (addressParts.length > 3) street = addressParts[3];
                        encrypt_detail = userAddress;
                    }

                    // 调用围栏判断方法
                    Long newStoreId = storeIdUtil.determineByWeiLan(
                        lat, lon, province, city, town, street, (town + encrypt_detail), 0
                    );

                    // 检查是否需要更换商家
                    if (newStoreId != null && !newStoreId.equals(szmCOrderMain.getStoreId())) {
                        // 验证新商家是否存在
                        SzmCStore newStore = szmCStoreMapper.selectByPrimaryKey(newStoreId);
                        if (newStore == null) {
                            failCount++;
                            errorMessages.append("订单ID ").append(mainId).append(" 围栏判断的新商家不存在; ");
                            continue;
                        }

                        // 更新订单商家
                        szmCOrderMain.setStoreId(newStoreId);
                        szmCOrderMain.setR2(newStoreId.toString());
                        szmCOrderMainMapper.updateByPrimaryKey(szmCOrderMain);

                        // 更新用户的默认商家信息
                        if (szmCOrderMain.getCreateIden() != null) {
                            SzmCUser szmCUser = szmCUserMapper
                                    .selectByUserId(Long.parseLong(szmCOrderMain.getCreateIden()));
                            if (szmCUser != null) {
                                szmCUser.setR2(newStoreId.toString());
                                szmCUser.setStoreId(newStoreId);
                                szmCUserMapper.updateByPrimaryKey(szmCUser);
                            }
                        }

                        // 找到新旧商家
                        SzmCStore oldStore = szmCStoreMapper.selectByPrimaryKey(szmCOrderMain.getStoreId());
                        // 新增水站订单通知
                        StoreMsg storeMsg = new StoreMsg();
                        storeMsg.setStoreMsgModel("根据围栏自动更换商家");// 模块名称
                        storeMsg.setStoreId(newStoreId);// 商户id
                        storeMsg.setModelUrl("orderAdmin");// 模块地址
                        storeMsg.setUserId(szmCOrderMain.getUserId());// 用户id
                        StringBuilder stringBuffer = new StringBuilder();
                        stringBuffer.append("订单：" + szmCOrderMain.getOrderNum() + " 根据围栏自动更换了商家："
                                + (newStore == null ? "-" : newStore.getStoreAme()) + "，原商家为："
                                + (oldStore == null ? "-" : oldStore.getStoreAme()));
                        if (StringUtils.isNotEmpty(reason)) {
                            stringBuffer.append("，原因：").append(reason);
                        }
                        storeMsg.setContent(stringBuffer.toString());// 内容
                        storeMsg.setReadState(0);// 已读 1 未读 0
                        storeMsg.setSource(21);// 来源 1 待处理订单，2退款，3 购买水票，4退桶，5借物资，6 还物资，7 使用水票
                        storeMsg.setDelState(0);// 删除状态
                        storeMsg.setR1("pagesMore/mine/exchangeOrder/exchangeOrder");// 小程序路径
                        storeMsg.setR2(szmCOrderMain.getOrderNum());// id
                        storeMsg.setR3(szmCOrderMain.getOrderMainId().toString());// id
                        storeMsgMapper.insert(storeMsg);

                        successCount++;
                    } else {
                        // 商家无需更换
                        noChangeCount++;
                    }
                } catch (Exception e) {
                    failCount++;
                    errorMessages.append("订单ID ").append(mainId).append(" 处理失败: ").append(e.getMessage()).append("; ");
                }
            }

            String resultMessage = "处理完成：成功更换 " + successCount + " 个订单的商家，" +
                                 "无需更换 " + noChangeCount + " 个订单";
            if (failCount > 0) {
                resultMessage += "，失败 " + failCount + " 个。失败原因: " + errorMessages.toString();
            }

            if (successCount > 0 || noChangeCount > 0) {
                return new ResultBean().success(resultMessage);
            } else {
                return new ResultBean().error("批量根据围栏更换商家失败: " + errorMessages.toString());
            }
        } catch (Exception e) {
            return new ResultBean().error("批量根据围栏更换商家失败: " + e.getMessage());
        }
    }

    /**
     * 获取一个订单的信息
     *
     * @return
     */
    public RetuenOrderList lookOrderDeatil1(SzmCOrderMain szmCOrderMain) {

        List<SmzCOrderDetails> smzCOrderDetails = smzCOrderDetailsMapper.selectByOrderNum1(szmCOrderMain.getOrderNum());
        // 查看套餐订单
        List<SmzCGroupOrder> smzCGroupOrders = smzCGroupOrderMapper.selectByOrderNum1(szmCOrderMain.getOrderNum());
        // 判断这次支付是不是有普通订单和套餐订单
        RetuenOrderList retuenOrderList = new RetuenOrderList();
        if (szmCOrderMain.getOrderStatus() == 0) {
            Date nextDay = DateUtil.getNextDay(szmCOrderMain.getCreateTime());
            retuenOrderList.setEndTime(nextDay);
        }
        retuenOrderList.setDiscounPrice(szmCOrderMain.getOrderDiscounts());
        retuenOrderList.setDeliverId(szmCOrderMain.getDeliveryInfoId());
        SzmCStore szmCStore = szmCStoreMapper.selectByPrimaryKey(Long.parseLong(szmCOrderMain.getR2()));
        retuenOrderList.setStoreName(szmCStore.getStoreAme());
        SmzCPaymentMode smzCPaymentMode = smzCPaymentModeMapper.selectByPrimaryKey(szmCOrderMain.getPaymentModeId());
        retuenOrderList.setPayMentId(smzCPaymentMode.getPaymentModeId());
        switch (szmCOrderMain.getOrderStatus()) {
            case 0:
                if (smzCPaymentMode.getPaymentModeId() == 1) {
                    retuenOrderList.setNewOrderState("微信待付款");
                } else if (smzCPaymentMode.getPaymentModeId() == 4) {
                    retuenOrderList.setNewOrderState("钱包待付款");
                }
                break;
            case 1:

                retuenOrderList.setNewOrderState(smzCPaymentMode.getPaymentModeName());
                break;
            case 2:
                retuenOrderList.setNewOrderState("待发货");
                break;
            case 3:
                retuenOrderList.setNewOrderState("已发货");
                break;
            case 4:
                retuenOrderList.setNewOrderState("待签收");
                break;
            case 5:
                retuenOrderList.setNewOrderState("已签收");
                break;
            case 6:
                retuenOrderList.setNewOrderState("已取消");
                break;
            case 7:
                retuenOrderList.setNewOrderState("拒单");
                break;
            case 8:
                SmzCOrderReturns smzCOrderReturns = smzCOrderReturnsMapper
                        .selectByOrderNum(szmCOrderMain.getOrderNum());
                if (smzCOrderReturns.getReturnsType().equals("退款")) {
                    retuenOrderList.setNewOrderState("退款");
                } else {
                    retuenOrderList.setNewOrderState("退货");
                }
                break;
            case 9:
                retuenOrderList.setNewOrderState("已评价");
                break;
            case 10:
                retuenOrderList.setNewOrderState("已完成");
                break;
        }
        // 订单的状态
        retuenOrderList.setOrderState(szmCOrderMain.getOrderStatus());
        retuenOrderList.setOrderDate(szmCOrderMain.getCreateTime());
        retuenOrderList.setFinishTime(szmCOrderMain.getFinishTime());
        retuenOrderList.setOrderTotalPrice(Double.parseDouble(szmCOrderMain.getR1()));
        retuenOrderList.setOrderNumber(szmCOrderMain.getOrderNum());
        retuenOrderList.setOrderTotalNumber(Integer.parseInt(szmCOrderMain.getR5()));
        retuenOrderList.setOrderPrice(szmCOrderMain.getOrderMoney());
        //
        if (szmCOrderMain.getOrderStatus() == 1 || szmCOrderMain.getOrderStatus() == 2) {
            if (szmCOrderMain.getIsReturn() == 1) {
                retuenOrderList.setIsReturnMoney(1);
                retuenOrderList.setIsReturnGoods(0);
            }
        } else if (szmCOrderMain.getOrderStatus() == 5) {
            if (szmCOrderMain.getIsReturn() == 1) {
                retuenOrderList.setIsReturnMoney(0);
                retuenOrderList.setIsReturnGoods(1);

            }
        }
        if (smzCOrderDetails.size() > 0 && smzCGroupOrders.size() > 0) {
            OrderDeatil orderDeatil = new OrderDeatil();
            // 订单的状态
            orderDeatil.setOrderNumber(szmCOrderMain.getOrderNum());
            // 返回的订单的详细信息
            List<OrderShopDeatil> orderShopDeatils = resultOrderMain(smzCOrderDetails);
            orderDeatil.setOrderShopDeatilList(orderShopDeatils);
            List<OrderDeatil> orderDeatils = new ArrayList<>();
            orderDeatils.add(orderDeatil);
            retuenOrderList.setList(orderDeatils);
            // 添加套餐订单
            List<GroupShop> groupShops1 = resultGroupShop(smzCGroupOrders);
            retuenOrderList.setGroupShopList(groupShops1);

        } else if (smzCOrderDetails.size() > 0 && smzCGroupOrders.size() <= 0) {
            OrderDeatil orderDeatil = new OrderDeatil();
            // 订单号
            orderDeatil.setOrderNumber(szmCOrderMain.getOrderNum());
            // 订单详细信息
            // 返回的订单的详细信息
            List<OrderShopDeatil> orderShopDeatils = resultOrderMain(smzCOrderDetails);
            orderDeatil.setOrderShopDeatilList(orderShopDeatils);
            List<OrderDeatil> orderDeatils = new ArrayList<>();
            orderDeatils.add(orderDeatil);
            retuenOrderList.setList(orderDeatils);
        } else {
            // 添加套餐订单
            List<GroupShop> groupShops1 = resultGroupShop(smzCGroupOrders);
            retuenOrderList.setGroupShopList(groupShops1);
        }
        return retuenOrderList;
    }

    /**
     * 待付款订单展示
     * 待收货订单展示
     *
     * @param userId
     * @return
     */
    @Override
    public ResultBean findAllByState(Long userId, Long state, String storeId) {
        ResultBean resultBean = new ResultBean();
        if (userId == null) {
            return resultBean.nulls("userId");
        } else if (userId < 0) {
            return resultBean.error("userId参数不正确");
        }
        if (state == null) {
            return resultBean.nulls("state");
        } else if (state < 0) {
            return resultBean.error("state参数不正确");
        }
        if (state == 0) {
            Object obj = redisUtil.get(userId + "orders");
            String json = null;
            if (obj != null)
                json = obj.toString();
            else
                return resultBean.error("暂无数据");
            List<SzmCOrderMain> szmCOrderMains1 = JSONArray.parseArray(json, SzmCOrderMain.class);
            return resultBean.success(szmCOrderMains1);
        }
        List<SzmCOrderMain> szmCOrderMains = szmCOrderMainMapper.selectAllByState(userId, state);
        if (szmCOrderMains.size() < 1) {
            return resultBean.error("暂无数据");
        }
        return resultBean.success(szmCOrderMains);
    }

    /**
     * 根据名称查询订单
     *
     * @param name
     * @return
     */
    @Override
    public ResultBean findAllByName(String name) {
        ResultBean resultBean = new ResultBean();
        if (name == null) {
            resultBean.nulls("name");
        }
        List<SzmCOrderMain> szmCOrderMains = szmCOrderMainMapper.selectAllByName(name);
        if (szmCOrderMains.size() == 0) {
            return resultBean.error("暂无数据");
        }
        return resultBean.success(szmCOrderMains);
    }

    /**
     * 根据订单编号查询订单
     *
     * @param orderNum
     * @return
     */
    @Override
    public ResultBean findOneOrderByNum(String orderNum) {
        ResultBean resultBean = new ResultBean();
        if (null == orderNum) {
            return resultBean.nulls("orderNum");
        }
        SzmCOrderMain szmCOrderMain = szmCOrderMainMapper.selectByOrderNum(orderNum);
        if (szmCOrderMain == null) {
            return resultBean.error("暂无数据");
        } else {
            ServiceOrderDeatil serviceOrderDeatil = new ServiceOrderDeatil();
            serviceOrderDeatil.setUserId(Long.parseLong(szmCOrderMain.getCreateIden()));
            serviceOrderDeatil.setUserName(szmCOrderMain.getUserName());
            serviceOrderDeatil.setPhoneNumber(szmCOrderMain.getUserPhone());
            serviceOrderDeatil.setAddress(szmCOrderMain.getUserAddress());
            serviceOrderDeatil.setAddressId(szmCOrderMain.getIncomeAddrId());
            serviceOrderDeatil.setUpPrice(szmCOrderMain.getUpPrice());
            serviceOrderDeatil.setShipsn(szmCOrderMain.getShipsn());
            serviceOrderDeatil.setShipchannel(szmCOrderMain.getShipchannel());
            serviceOrderDeatil.setConnectordernum(szmCOrderMain.getConnectordernum());
            serviceOrderDeatil.setConnectstoreid(szmCOrderMain.getConnectstoreid());
            serviceOrderDeatil.setZiti(szmCOrderMain.getZiti());
            serviceOrderDeatil.setDikou(szmCOrderMain.getDikou());
            serviceOrderDeatil.setPicurl(szmCOrderMain.getPicurl());
            serviceOrderDeatil.setTicketPrice(szmCOrderMain.getTicketPrice() == null ? "0.00"
                    : String.format("%.2f", szmCOrderMain.getTicketPrice()));
            serviceOrderDeatil.setYunfei(
                    szmCOrderMain.getYunfei() == null ? "0.00" : String.format("%.2f", szmCOrderMain.getYunfei()));
            if (szmCOrderMain.getDrainageuserid() != null) {
                DrainageUser drainageUser = drainageUserMapper.selectByPrimaryKey(szmCOrderMain.getDrainageuserid());
                if (null != drainageUser) {
                    SzmCUser szmCUser = szmCUserMapper.selectByUserId(drainageUser.getUserId());
                    drainageUser.setUsername(szmCUser.getUserNickname());
                    drainageUser.setMobile(szmCUser.getUserMobile());
                    drainageUser.setAddress(szmCUser.getTuanzhangdizhi());
                }
                serviceOrderDeatil.setDrainageUser(drainageUser);
            }

            SzmCStore szmCStore = szmCStoreMapper.selectStoreId(szmCOrderMain.getStoreId());
            List<SmzCGroupOrder> groupShopList = smzCGroupOrderMapper.selectByOrderNum(szmCOrderMain.getOrderNum());
            List<SmzCOrderDetails> smzCOrderDetails = smzCOrderDetailsMapper
                    .selectByOrderNum(szmCOrderMain.getOrderNum());
            List<SmzCPaymentMode> smzCPaymentModes = smzCPaymentModeMapper.selectAll();
            Map<Long, String> paymentModeMap = smzCPaymentModes.stream()
                    .collect(Collectors.toMap(SmzCPaymentMode::getPaymentModeId, SmzCPaymentMode::getPaymentModeName));
            Map<Integer, OrderSource> orderSourceMap = orderSourceMapper.selectAll().stream()
                    .collect(Collectors.toMap(OrderSource::getId, orderSource -> orderSource));
            RetuenOrderList retuenOrderList = lookOrderDeatil(szmCOrderMain, szmCStore, smzCOrderDetails,
                    groupShopList, paymentModeMap, orderSourceMap);
            serviceOrderDeatil.setRetuenOrderList(retuenOrderList);

            double productRuling = 0d;// 商品现价
            double groupRuling = 0d;// 套餐现价
            double marketPrice = 0d;// 划线价 商品合计
            // List<SmzCOrderDetails> smzCOrderDetails =
            // smzCOrderDetailsMapper.selectByOrderNum(szmCOrderMain.getOrderNum());
            for (SmzCOrderDetails smzCOrderDetail : smzCOrderDetails) {
                if (smzCOrderDetail.getMarketPrice() == null) {
                    marketPrice += Double.parseDouble(smzCOrderDetail.getR4())
                            * Double.parseDouble(smzCOrderDetail.getOrderProductNum().toString());
                } else {
                    marketPrice += smzCOrderDetail.getMarketPrice() * smzCOrderDetail.getOrderProductNum();
                }
                productRuling += smzCOrderDetail.getOrderDetailsProductPrice();
            }

            // 查看套餐订单
            // List<SmzCGroupOrder> groupShopList =
            // smzCGroupOrderMapper.selectByOrderNum(szmCOrderMain.getOrderNum());
            if (groupShopList.size() > 0) {
                for (int i = 0; i < groupShopList.size(); i++) {
                    List<Object> ts = JSONArray.parseArray(groupShopList.get(i).getR4(), Object.class);// 商品信息
                    double practicalPrice = 0d;
                    for (int k = 0; k < ts.size(); k++) {
                        Map<String, Object> maps = (Map<String, Object>) ts.get(k);
                        String price = maps.get("price").toString();
                        String num = maps.get("num").toString();
                        double mul = MoneyUtil.mul(Double.parseDouble(price), Integer.parseInt(num));
                        practicalPrice = MoneyUtil.sum(practicalPrice, mul);
                    }
                    groupRuling += practicalPrice * groupShopList.get(i).getOrderProductNum();
                    marketPrice += groupRuling;
                }
            }
            // 商品合计
            serviceOrderDeatil.setProductTotalPrice(marketPrice);
            // 商品优惠
            serviceOrderDeatil.setProductDiscounts(marketPrice - productRuling - groupRuling);
            // 上楼费总额
            serviceOrderDeatil.setTotalUpPrice(szmCOrderMain.getUpPrice() * Integer.parseInt(szmCOrderMain.getR5()));
            // 支付方式金额
            serviceOrderDeatil
                    .setPayMentPrice(Double.parseDouble(szmCOrderMain.getR1()) - szmCOrderMain.getBucketPrice());
            // 订单总额
            serviceOrderDeatil.setOrderPrice(szmCOrderMain.getOrderMoney());
            // 配送价格
            serviceOrderDeatil.setBuyermessage(szmCOrderMain.getUserContent());
            serviceOrderDeatil.setShopDoller(Double.parseDouble(szmCOrderMain.getR1()));
            serviceOrderDeatil.setPostageDoller(szmCOrderMain.getFreightPayable());
            serviceOrderDeatil.setDiscountsDoller(szmCOrderMain.getOrderDiscounts());
            // 划线价
            // serviceOrderDeatil.setMarketPrice(Double.parseDouble(szmCOrderMain.getR1()));

            // 配送方式
            if (szmCOrderMain.getR1() != null)
                serviceOrderDeatil.setPracticalPayDoller(Double.parseDouble(szmCOrderMain.getR1()));
            else
                serviceOrderDeatil.setPracticalPayDoller(null);
            String r2 = szmCOrderMain.getR2();
            SzmCDistribution szmCDistribution = szmCDistributionMapper.selectByPrimaryKey(Long.parseLong(r2));
            if (null != szmCDistribution) {
                serviceOrderDeatil.setModeOfDistribution(szmCDistribution.getR1());
            }
            if ("[]".equals(szmCOrderMain.getR4())) {
                serviceOrderDeatil.setPreferentialWay("无");
            } else {
                serviceOrderDeatil.setPreferentialWay("水票");
            }

            serviceOrderDeatil.setMerchantServiceId(Long.parseLong(szmCOrderMain.getR2()));

            // 订单号
            serviceOrderDeatil.setOrderNumber(orderNum);
            SmzCPaymentMode smzCPaymentMode = smzCPaymentModeMapper
                    .selectByPrimaryKey(szmCOrderMain.getPaymentModeId());
            if (null != smzCPaymentMode) {
                serviceOrderDeatil.setPayType(smzCPaymentMode.getPaymentModeName());
            }
            serviceOrderDeatil.setOrderDate(szmCOrderMain.getCreateTime());

            // 添加押桶信息
            // 押桶信息
            List<Map<String, Object>> pledgList = new ArrayList<>();
            List<PledgeBuckOrder> pledgeBuckOrders = pledgeBuckOrderMapper
                    .selectOrderAndPayType(szmCOrderMain.getOrderNum(), "0", "0", null);

            Double pledgMoney = 0.00;
            for (PledgeBuckOrder pledgeBuckOrder : pledgeBuckOrders) {
                Map<String, Object> pledg = new HashMap<>();
                pledg.put("name", pledgeBuckOrder.getBrandName());
                pledg.put("num", pledgeBuckOrder.getBuckNumber());
                pledg.put("money", pledgeBuckOrder.getBuckMoney());
                pledgMoney += pledgeBuckOrder.getBuckMoney() * pledgeBuckOrder.getBuckNumber();
                pledgList.add(pledg);
            }
            serviceOrderDeatil.setPledgMoney(pledgMoney);// 押桶金
            serviceOrderDeatil.setPledgList(JSON.toJSONString(pledgList));
            String buckPayType = "";
            if (pledgeBuckOrders.size() > 0) {
                switch (pledgeBuckOrders.get(0).getR4()) {
                    case "1":
                        buckPayType = "微信支付";
                        break;
                    case "2":
                        buckPayType = "余额支付";
                        break;
                    case "3":
                        buckPayType = "支付宝";

                        break;
                    case "4":
                        buckPayType = "现金支付";

                        break;
                    case "5":
                        buckPayType = "银行转账";

                        break;
                    case "6":
                        buckPayType = "线下付款";

                        break;
                }
                serviceOrderDeatil.setBuckPayType(buckPayType);
                serviceOrderDeatil.setBuckState(pledgeBuckOrders.get(0).getDelState().toString());
            }
            serviceOrderDeatil
                    .setShopTotalMoney(NumberUtil.sub(szmCOrderMain.getOrderMoney(), szmCOrderMain.getBucketPrice()));

            Double groupMoney = 0.00;

            List<SmzCGroupOrder> smzCGroupOrders = smzCGroupOrderMapper.selectByOrderNum(szmCOrderMain.getOrderNum());
            for (SmzCGroupOrder smzCGroupOrder : smzCGroupOrders) {
                SzmCShopgroup szmCShopgroup = szmCShopgroupMapper.selectByPrimaryKey(smzCGroupOrder.getShopgroupId());
                groupMoney += (szmCShopgroup.getShopgroupPrices() - szmCShopgroup.getShopgroupPrice())
                        * smzCGroupOrder.getOrderProductNum();
            }
            serviceOrderDeatil.setGroupMoney(groupMoney);// 套餐优惠
            // 送水员信息
            if (szmCOrderMain.getDeliveryInfoId() != null) {
                // 找到送水员信息
                SmzCDeliveryUser smzCDeliveryUser = smzCDeliveryUserMapper
                        .selectByPrimaryKey(szmCOrderMain.getDeliveryInfoId());
                if (null != smzCDeliveryUser) {
                    serviceOrderDeatil.setDeliveryName(smzCDeliveryUser.getDeliveryUserName());
                    serviceOrderDeatil.setDeliveryMobile(smzCDeliveryUser.getDeliveryUserPhone());
                }
            }
            // 水站信息
            if (StringUtils.isNotEmpty(szmCOrderMain.getR2())) {
                // 找到送水员信息
                // SzmCStore szmCStore =
                // szmCStoreMapper.selectByPrimaryKey(Long.parseLong(szmCOrderMain.getR2()));
                if (null != szmCStore) {
                    serviceOrderDeatil.setStoreAddress(szmCStore.getStoreDetailed());
                    serviceOrderDeatil.setStoreLogo(szmCStore.getStoreLogo());
                    serviceOrderDeatil.setStorePhone(szmCStore.getStorePhone());
                    serviceOrderDeatil.setStoreName(szmCStore.getStoreAme());
                }
            }
            return resultBean.success(serviceOrderDeatil);
        }

    }

    /**
     * 根据订单编号查询订单
     *
     * @param orderNum
     * @return
     */
    @Override
    public ResultBean findOneOrderById(String orderNum) {
        ResultBean resultBean = new ResultBean();
        if (null == orderNum) {
            return resultBean.nulls("orderNum");
        }
        SzmCOrderMain szmCOrderMain = szmCOrderMainMapper.selectByOrderNum(orderNum);
        if (szmCOrderMain == null) {
            return resultBean.error("暂无数据");
        }
        szmCOrderMain.setSmzCGroupOrderList(smzCGroupOrderMapper.selectByOrderNum(orderNum));
        szmCOrderMain.setSmzCOrderDetailsList(smzCOrderDetailsMapper.selectByOrderNum(orderNum));
        return resultBean.success(szmCOrderMain);
    }

    /**
     * 修改订单
     *
     * @param szmCOrderMain
     * @return
     */
    @Override
    @Transactional(rollbackFor = { Exception.class })
    public ResultBean updateByOrderNum(SzmCOrderMain szmCOrderMain) {
        ResultBean resultBean = new ResultBean();
        if (szmCOrderMain == null) {
            return resultBean.nulls("szmCOrderMain");
        } else if (szmCOrderMain.getOrderNum() == null) {
            return resultBean.nulls("orderNum");
        }
        SzmCOrderMain szmCOrderMain2 = szmCOrderMainMapper.selectByOrderNum(szmCOrderMain.getOrderNum());

        SzmCUser szmCuser = szmCUserMapper.selectByUserId(Long.parseLong(szmCOrderMain2.getCreateIden()));
        if (Validator.isNull(szmCOrderMain2)) {
            return resultBean.error("暂无订单信息");
        }
        String lock = "updateByOrderNum:" + szmCOrderMain.getOrderNum();
        // 获取订单锁
        Object obj = redisUtil.get(lock);
        if (null != obj) {
            if (obj.toString().equals("0")) {
                redisUtil.set(lock, "1", 30);
            } else {
                return resultBean.error("重复提交,请稍后再试");
            }
        } else {
            redisUtil.set(lock, "1", 30);
        }
        if (szmCOrderMain.getOrderStatus() != null) {
            if (szmCOrderMain.getOrderStatus() == 5) {
                if (szmCOrderMain2.getOrderStatus() > 5) {
                    return resultBean.success("已确认收货");
                }
                List<SzmBWalletDetail> szmBWalletDetai3 = szmBWalletDetailMapper
                        .selectByOrderNum(szmCOrderMain.getOrderNum());
                // 不是分账订单的已经完成签收的情况
                if ((!CollectionUtils.isEmpty(szmBWalletDetai3)
                        && StringUtils.isEmpty(szmCOrderMain2.getBankOutTradeNum())
                        && (szmCOrderMain2.getTranstatus() == null || szmCOrderMain2.getTranstatus() < 3))
                        || (!CollectionUtils.isEmpty(szmBWalletDetai3) && szmBWalletDetai3.size() > 1
                                && szmCOrderMain2.getTranstatus() != null && szmCOrderMain2.getTranstatus() == 3)) {
                    return resultBean.success("已确认收货");
                }
                // 确认收货
                // 积分流水
                try {
                    // pledgeBuckOrderMapper
                    List<PledgeBuckOrder> pledgeBuckOrders = pledgeBuckOrderMapper
                            .selectOrderAndPayType(szmCOrderMain.getOrderNum(), "0", "0", null);
                    if (1 == szmCOrderMain2.getPaymentModeId()) {
                        if (!CollectionUtil.isEmpty(pledgeBuckOrders)) {
                            for (PledgeBuckOrder pledgeBuckOrder : pledgeBuckOrders) {
                                if (pledgeBuckOrder.getR1().equals("0")) {
                                    pledgeBuckOrder.setDelState(1);
                                    pledgeBuckOrderMapper.updateByPrimaryKey(pledgeBuckOrder);
                                }
                            }
                        }
                    }
                    // 积分流水
                    SzmCOrderMain szmCOrderMain1 = szmCOrderMain2;
                    if (StringUtils.isNotEmpty(szmCOrderMain1.getR3()) && isPositiveInteger(szmCOrderMain1.getR3())
                            && (szmCOrderMain1.getLianying() == null || szmCOrderMain1.getLianying().equals(0))
                            && (szmCOrderMain1.getDikou() == null
                                    || szmCOrderMain1.getDikou().compareTo(BigDecimal.ZERO) == 0)) {
                        int count = Integer.parseInt(szmCOrderMain1.getR3());
                        if (count != 0) {
                            SmzCIntegralWater smzCIntegralWater = smzCIntegralWaterMapper
                                    .selectByOrderNum(szmCOrderMain.getOrderNum());
                            if (Validator.isNull(smzCIntegralWater)) {
                                smzCIntegralWater = new SmzCIntegralWater();
                                smzCIntegralWater.setUserId(Long.parseLong(szmCOrderMain1.getCreateIden()));
                                smzCIntegralWater.setIntegralSourceId(szmCOrderMain1.getOrderNum());
                                smzCIntegralWater.setIntegralSourceState(1);
                                smzCIntegralWater.setIntegralConsumeCount(count);// 增加积分
                                smzCIntegralWater.setIntegralState(0);// 积分状态 0 到账 1未到账 2 失效
                                smzCIntegralWaterMapper.insert(smzCIntegralWater);

                                SzmCUserinfo szmCUserinfo = szmCUserinfoMapper
                                        .selectBrUserId(szmCOrderMain2.getUserId());
                                if (null != szmCUserinfo) {
                                    szmCUserinfo.setUserinfoIntegral((szmCUserinfo.getUserinfoIntegral() == null ? 0
                                            : szmCUserinfo.getUserinfoIntegral()) + count);
                                    szmCUserinfoMapper.updateByPrimaryKey(szmCUserinfo);
                                }
                            }
                        }
                    }
                    SzmCStore szmCStore = szmCStoreMapper.selectStoreId(Long.parseLong(szmCOrderMain1.getR2()));
                    double price = Double.parseDouble(szmCOrderMain1.getR1());
                    if (!Validator.isEmpty(szmCOrderMain1.getBucketPayType())
                            && szmCOrderMain1.getBucketPayType() != 0) {
                        price = price - szmCOrderMain1.getBucketPrice();
                    }
                    if (szmCOrderMain1.getLianyingprice() != null && szmCOrderMain1.getLianyingprice() != 0) {
                        price = price - szmCOrderMain1.getLianyingprice();
                    }
                    int i = 0;
                    // 发送业务员提成和普通用户提成
                    DeliveryDeductDeatil deliveryDeductDeatil = deliveryDeductDeatilMapper
                            .selectByOrderNum(szmCOrderMain2.getOrderNum());
                    if (deliveryDeductDeatil.getYewu1Money() != null && deliveryDeductDeatil.getYewu1Money() > 0D) {
                        // 有一级业务提成
                        if (szmCuser.getOneId() != null) {
                            if (szmCuser.getOneType() != null
                                    && szmCuser.getOneType().equals(FenxiaoUserTypeEnum.USER.getCode())) {
                                SzmCUserBalance szmCUserBalance = szmCUserBalanceMapper
                                        .selectByUserId(szmCuser.getOneId());
                                if (!Validator.isNull(szmCUserBalance)) {
                                    szmCUserBalance.setBalanceNum(
                                            szmCUserBalance.getBalanceNum() + deliveryDeductDeatil.getYewu1Money());// 钱包还钱
                                    szmCUserBalanceMapper.updateByPrimaryKey(szmCUserBalance);
                                    // 用户分销
                                    SzmCWalletDetail szmCWalletDetail = new SzmCWalletDetail();
                                    szmCWalletDetail.setUserId(szmCuser.getOneId());
                                    szmCWalletDetail.setWalletDetailSource("0");
                                    szmCWalletDetail.setWalletDetailPrice(deliveryDeductDeatil.getYewu1Money());
                                    szmCWalletDetail.setState(0);
                                    szmCWalletDetail.setR1("5");// 0 订单 1 服务 2 水票 3压桶 4 退桶 5 分销收入
                                    szmCWalletDetail.setR2(szmCOrderMain2.getOrderNum());
                                    szmCWalletDetail.setR3(szmCOrderMain2.getR2());
                                    szmCWalletDetailMapper.insert(szmCWalletDetail);
                                }
                            }
                            if (szmCuser.getOneType() != null
                                    && szmCuser.getOneType().equals(FenxiaoUserTypeEnum.SALESMAN.getCode())) {
                                // 业务员分销
                                CommercialDelegate commercialDelegate = commercialDelegateMapper
                                        .selectByPrimaryUserId(szmCuser.getOneId());
                                if (!Validator.isNull(commercialDelegate)) {
                                    StoreBusiness storeBusiness = new StoreBusiness();
                                    storeBusiness.setBusinessId(commercialDelegate.getUserId());
                                    storeBusiness.setUserId(Long.parseLong(szmCOrderMain2.getCreateIden()));
                                    storeBusiness.setStoreId(Long.parseLong(szmCOrderMain2.getR2()));
                                    storeBusiness.setDelState(0);
                                    storeBusiness.setBuState(0);
                                    storeBusiness.setOrderNumber(szmCOrderMain2.getOrderNum());
                                    storeBusiness.setBuType(1);
                                    // storeBusiness.setBuRatio(szmCOrderMain2.getCdTypeMoney());
                                    storeBusiness.setBuMoney(deliveryDeductDeatil.getYewu1Money());
                                    storeBusinessMapper.insert(storeBusiness);
                                    commercialDelegate.setMoney(NumberUtil.add(commercialDelegate.getMoney(),
                                            deliveryDeductDeatil.getYewu1Money()));
                                    commercialDelegateMapper.updateByPrimaryKey(commercialDelegate);
                                }
                            }
                        }
                    }
                    if (deliveryDeductDeatil.getYewu2Money() != null && deliveryDeductDeatil.getYewu2Money() > 0D) {
                        // 有二级业务提成
                        if (szmCuser.getTwoId() != null) {
                            if (szmCuser.getTwoType() != null
                                    && szmCuser.getTwoType().equals(FenxiaoUserTypeEnum.USER.getCode())) {
                                SzmCUserBalance szmCUserBalance = szmCUserBalanceMapper
                                        .selectByUserId(szmCuser.getTwoId());
                                if (!Validator.isNull(szmCUserBalance)) {
                                    szmCUserBalance.setBalanceNum(
                                            szmCUserBalance.getBalanceNum() + deliveryDeductDeatil.getYewu2Money());// 钱包还钱
                                    szmCUserBalanceMapper.updateByPrimaryKey(szmCUserBalance);
                                    // 用户分销
                                    SzmCWalletDetail szmCWalletDetail = new SzmCWalletDetail();
                                    szmCWalletDetail.setUserId(szmCuser.getTwoId());
                                    szmCWalletDetail.setWalletDetailSource("0");
                                    szmCWalletDetail.setWalletDetailPrice(deliveryDeductDeatil.getYewu2Money());
                                    szmCWalletDetail.setState(0);
                                    szmCWalletDetail.setR1("5");// 0 订单 1 服务 2 水票 3压桶 4 退桶 5 分销收入
                                    szmCWalletDetail.setR2(szmCOrderMain2.getOrderNum());
                                    szmCWalletDetail.setR3(szmCOrderMain2.getR2());
                                    szmCWalletDetailMapper.insert(szmCWalletDetail);
                                }
                            }
                            if (szmCuser.getTwoType() != null
                                    && szmCuser.getTwoType().equals(FenxiaoUserTypeEnum.SALESMAN.getCode())) {
                                // 业务员分销
                                CommercialDelegate commercialDelegate = commercialDelegateMapper
                                        .selectByPrimaryUserId(szmCuser.getTwoId());
                                if (!Validator.isNull(commercialDelegate)) {
                                    StoreBusiness storeBusiness = new StoreBusiness();
                                    storeBusiness.setBusinessId(commercialDelegate.getUserId());
                                    storeBusiness.setUserId(Long.parseLong(szmCOrderMain2.getCreateIden()));
                                    storeBusiness.setStoreId(Long.parseLong(szmCOrderMain2.getR2()));
                                    storeBusiness.setDelState(0);
                                    storeBusiness.setBuState(0);
                                    storeBusiness.setOrderNumber(szmCOrderMain2.getOrderNum());
                                    storeBusiness.setBuType(1);
                                    // storeBusiness.setBuRatio(szmCOrderMain2.getCdTypeMoney());
                                    storeBusiness.setBuMoney(deliveryDeductDeatil.getYewu2Money());
                                    storeBusinessMapper.insert(storeBusiness);
                                    commercialDelegate.setMoney(NumberUtil.add(commercialDelegate.getMoney(),
                                            deliveryDeductDeatil.getYewu2Money()));
                                    commercialDelegateMapper.updateByPrimaryKey(commercialDelegate);
                                }
                            }
                        }
                    }
                    if (szmCOrderMain1.getPaymentModeId() == 1 || szmCOrderMain1.getPaymentModeId() == 4
                            || szmCOrderMain1.getPaymentModeId() == 6) {
                        Double royalty = 0d; // 提成
                        if (szmCOrderMain1.getDeliveryInfoId() != null && szmCOrderMain1.getDeliveryInfoId() != 0) {
                            SmzCDeliveryInfo smzCDeliveryInfo = smzCDeliveryInfoMapper
                                    .selectAllByOrderId(szmCOrderMain1.getOrderMainId());
                            if (smzCDeliveryInfo != null) {
                                if (smzCDeliveryInfo.getR2() != null) {
                                    SmzCDeliveryUser smzCDeliveryUser = smzCDeliveryUserMapper
                                            .selectByPrimaryKey(smzCDeliveryInfo.getDeliveryUserId());
                                    if (smzCDeliveryInfo.getDeliveryUserId() != null) {
                                        if (smzCDeliveryUser.getDeliveryUserStatus() == 0) {
                                            if (szmCOrderMain1.getRoyalty() != null) {
                                                royalty = szmCOrderMain1.getRoyalty();
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        // 是否是转单
                        if (szmCOrderMain2.getTranstatus() != null && szmCOrderMain2.getTranstatus() == 3) {
                            // 商户钱包明细
                            // 找到冻结时间
                            Integer freezeDate = 1;
                            Config freezeDateConfig = configService.findByLabel("freeze_date");
                            if (freezeDateConfig != null) {
                                freezeDate = Integer.parseInt(freezeDateConfig.getValue());
                            }
                            double tranprice = Double.parseDouble(szmCOrderMain1.getR1());
                            // 给当前商家在途金额
                            szmCStore.setMoney(MoneyUtil.sum(szmCStore.getMoney(), tranprice));
                            szmCStoreMapper.updateByPrimaryKey(szmCStore);
                            // 当前商家佣金明细
                            SzmBWalletDetail szmBWalletDetail = new SzmBWalletDetail();
                            szmBWalletDetail.setOrderNum(szmCOrderMain1.getOrderNum());
                            szmBWalletDetail.setPrice(tranprice);// 减提成
                            szmBWalletDetail.setStoreId(szmCStore.getStoreId());
                            szmBWalletDetail.setOrderState(2);// 1，提现，2在途。3正常订单，4失效订单
                            szmBWalletDetail.setName("转单收入");
                            szmBWalletDetail.setR2("1");
                            SzmCUser szmCUser = szmCUserMapper
                                    .selectByUserId(Long.parseLong(szmCOrderMain1.getCreateIden()));
                            if (null != szmCUser) {
                                szmBWalletDetail.setUsername(szmCUser.getUserNickname());
                                szmBWalletDetail.setMobile(szmCUser.getUserMobile());
                            }
                            szmBWalletDetailMapper.insert(szmBWalletDetail);

                            // 发送消息
                            // 时间 freezeDate 天
                            // long time = freezeDate * 24 * 60 * 60 * 1000;
                            long time = 2000L;
                            rabbitMqUtil.convertAndSend("test_exchange102", "test_queue_102",
                                    szmBWalletDetail.getWalletId(),
                                    time);

                            // 旧商户佣金
                            SzmCStore szmCStore2 = szmCStoreMapper.selectStoreId(szmCOrderMain1.getOldstoreid());
                            if (szmCStore2 != null) {
                                szmCStore2.setMoney(MoneyUtil.sum(szmCStore2.getMoney(),
                                        szmCOrderMain1.getOldprice().doubleValue()));
                                szmCStoreMapper.updateByPrimaryKey(szmCStore2);
                                // 旧商户佣金明细

                                SzmBWalletDetail szmBWalletDetail1 = new SzmBWalletDetail();
                                szmBWalletDetail1.setOrderNum(szmCOrderMain1.getOrderNum());
                                szmBWalletDetail1.setPrice(szmCOrderMain1.getOldprice().doubleValue());// 减提成
                                szmBWalletDetail1.setStoreId(szmCStore2.getStoreId());
                                szmBWalletDetail1.setOrderState(2);// 1，提现，2在途。3正常订单，4失效订单
                                szmBWalletDetail1.setR2("1");
                                szmBWalletDetail1.setName("佣金收入");

                                if (null != szmCUser) {
                                    szmBWalletDetail1.setUsername(szmCUser.getUserNickname());
                                    szmBWalletDetail1.setMobile(szmCUser.getUserMobile());
                                }
                                szmBWalletDetailMapper.insert(szmBWalletDetail1);

                                rabbitMqUtil.convertAndSend("test_exchange102", "test_queue_102",
                                        szmBWalletDetail1.getWalletId(),
                                        time);

                            }
                        } else {
                            if (StringUtils.isEmpty(szmCOrderMain2.getBankOutTradeNum())) {
                                // 商户钱包明细
                                double sub = MoneyUtil.sub(price, royalty);

                                szmCStore.setMoney(MoneyUtil.sub(szmCStore.getMoney(), sub));
                                // 加余额
                                szmCStore.setWallet(MoneyUtil.sum(szmCStore.getWallet(), sub));
                                szmCStoreMapper.updateByPrimaryKey(szmCStore);
                                // if (price > 0) {
                                List<SzmBWalletDetail> szmBWalletDetails = szmBWalletDetailMapper
                                        .selectByOrderNum(szmCOrderMain1.getOrderNum());
                                if (CollectionUtils.isEmpty(szmBWalletDetails)) {
                                    SzmBWalletDetail szmBWalletDetail = new SzmBWalletDetail();
                                    szmBWalletDetail.setOrderNum(szmCOrderMain1.getOrderNum());
                                    szmBWalletDetail.setPrice(sub);// 减提成
                                    szmBWalletDetail.setStoreId(szmCStore.getStoreId());
                                    szmBWalletDetail.setOrderState(3);// 1，提现，2在途。3正常订单，4失效订单
                                    if (szmCOrderMain1.getPaymentModeId() == 4) {
                                        szmBWalletDetail.setR2("0");
                                    } else {
                                        szmBWalletDetail.setR2("1");
                                    }
                                    SzmCUser szmCUser = szmCUserMapper
                                            .selectByUserId(Long.parseLong(szmCOrderMain1.getCreateIden()));
                                    if (null != szmCUser) {
                                        szmBWalletDetail.setUsername(szmCUser.getUserNickname());
                                        szmBWalletDetail.setMobile(szmCUser.getUserMobile());
                                    }
                                    szmBWalletDetailMapper.insert(szmBWalletDetail);
                                }
                            }
                        }
                    }
                    // 3天后自动完成订单
                    rabbitMqUtil.convertAndSend("test_exchange3", "test_queue_3", szmCOrderMain.getOrderNum(),
                            3600000L);
                } catch (Exception e) {
                    throw new ServiceBizException("500", e.getMessage());
                }
            }
        }
        int i = szmCOrderMainMapper.updateByOrderNum(szmCOrderMain);

        // 月付时候添加订单月付表
        if (3 == szmCOrderMain2.getPaymentModeId()) {
            OrderYf orderYf = new OrderYf();
            orderYf.setDelState(0);
            orderYf.setOrderMainId(szmCOrderMain2.getOrderMainId());
            orderYf.setOrderNumber(szmCOrderMain.getOrderNum());
            orderYf.setStoreId(Long.parseLong(szmCOrderMain2.getR2()));
            orderYf.setUserId(Long.parseLong(szmCOrderMain2.getCreateIden()));
            orderYf.setIsReturn(0);
            orderYf.setR1("0");
            orderYf.setTotalMoney(Double.parseDouble(szmCOrderMain2.getR1()) - szmCOrderMain2.getBucketPrice());
            orderYf.setYfMoney(0.00);
            orderYf.setRedisMoney(orderYf.getTotalMoney());
            orderYf.setR1("0");
            if (orderYf.getTotalMoney() > 0) {
                orderYfMapper.insert(orderYf);
            }
        }
        StoreSmsInfo storeSmsInfo = storeSmsInfoMapper.selectByStoreId(Long.parseLong(szmCOrderMain2.getR2()));
        if (storeSmsInfo != null) {
            SzmCStoreApplyFor szmCStoreApplyFor = szmCStoreApplyForMapper
                    .selectStoreId(Long.parseLong(szmCOrderMain2.getR2()));
            if (storeSmsInfo.getResidueNum() > 0) {
                SmsRelevance smsRelevance = smsRelevanceMapper
                        .selectByStoreAndMaster(Long.parseLong(szmCOrderMain2.getR2()), 11l);// 订单完结
                if (smsRelevance != null && smsRelevance.getState() == 1) {
                    String template = "【水站买】：您有一条已完结的订单，请到订单管理中查看。";
                    UtilSMS.sendSMS(szmCStoreApplyFor.getStoreTel(), template);
                    SmsRecord smsRecord = new SmsRecord();
                    smsRecord.setStoreId(Long.parseLong(szmCOrderMain2.getR2()));
                    SmsMaster smsMaster = smsMasterMapper.selectByPrimaryKey(11l);
                    smsRecord.setContent(smsMaster.getName());
                    smsRecordMapper.insert(smsRecord);
                    storeSmsInfo.setResidueNum(storeSmsInfo.getResidueNum() - 1);
                    storeSmsInfo.setPastNum(storeSmsInfo.getPastNum() + 1);
                    storeSmsInfoMapper.updateByPrimaryKey(storeSmsInfo);
                }
            } else {
                RemindSMS.remindSMS(szmCStoreApplyFor.getStoreTel());
            }
        }
        if (i > 0) {
            redisUtil.del(lock);
            return resultBean.success("修改成功");
        }
        redisUtil.del(lock);
        return resultBean.error("修改失败");
    }

    public static boolean isPositiveInteger(String str) {
        // 正则表达式，匹配以1到9开头的数字序列
        String regex = "^[1-9]\\d*$";

        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(str);

        return matcher.matches();
    }

    /**
     * 再来一单
     *
     * @param orderNumber
     * @return
     */
    @Override
    public ResultBean lookOrderNumber(String orderNumber) {
        ResultBean resultBean = new ResultBean();
        Integer updat = 1;
        if (null == orderNumber) {
            return resultBean.error("系统错误请重新下单");
        } else {
            List<OrderDeatil> list = null;
            SzmCOrderMain szmCOrderMain = szmCOrderMainMapper.selectByOrderNum(orderNumber);
            // 查看普通订单
            List<SmzCOrderDetails> smzCOrderDetails = smzCOrderDetailsMapper
                    .selectByOrderNum(szmCOrderMain.getOrderNum());
            // 查看套餐订单
            List<SmzCGroupOrder> smzCGroupOrders = smzCGroupOrderMapper.selectByOrderNum(szmCOrderMain.getOrderNum());
            // 判断这次支付是不是有普通订单和套餐订单
            RetuenOrderList retuenOrderList = new RetuenOrderList();
            retuenOrderList.setOrderNumber(szmCOrderMain.getOrderNum());
            if (smzCOrderDetails.size() > 0 && smzCGroupOrders.size() > 0) {
                for (SmzCOrderDetails smzCOrderDetails1 : smzCOrderDetails) {
                    SzmCCartMain szmCCartMain = new SzmCCartMain();
                    szmCCartMain.setUserId(Long.parseLong(szmCOrderMain.getCreateIden()));
                    szmCCartMain.setProductNum(smzCOrderDetails1.getOrderProductNum());
                    szmCCartMain.setR1(smzCOrderDetails1.getR2());
                    SzmCProductModel szmCProductModel = szmCProductModelMapper
                            .selectByPrimaryKey(smzCOrderDetails1.getProductModelId());
                    if (null != szmCProductModel) {
                        szmCCartMain.setProductPrice(szmCProductModel.getProductOriginalPrice());
                    }
                    szmCCartMain.setProductModelId(smzCOrderDetails1.getProductModelId());
                    szmCCartMain.setR2("true");
                    szmCCartMain.setR3(szmCOrderMain.getR2());
                    szmCCartMain.setCartDelState(0);
                    List<SzmCCartMain> szmCCartMainList = szmCCartMainMapper
                            .selectAllUserShop(Long.parseLong(szmCOrderMain.getCreateIden()), szmCCartMain.getR3());
                    if (null == szmCCartMainList || szmCCartMainList.size() == 0) {
                        int insert = szmCCartMainMapper.insert(szmCCartMain);
                        if (insert != 1) {
                            return resultBean.error("添加失败");
                        } else {
                            updat = 2;
                        }
                    } else {
                        ResultBean resultBean1 = szmCCartMainService.checkCartAddCart(szmCCartMainList, szmCCartMain);
                        Integer result = (Integer) resultBean1.getData();
                        if (result == 3) {
                            return resultBean.error("添加失败");
                        } else if (result == 1) {
                            int insert = szmCCartMainMapper.insert(szmCCartMain);
                            if (insert != 1) {
                                return resultBean.error("添加失败");
                            } else {
                                updat = 2;
                            }
                        }
                    }
                }
                for (SmzCGroupOrder smzCGroupOrder : smzCGroupOrders) {
                    SzmCCartMain szmCCartMain = new SzmCCartMain();
                    szmCCartMain.setUserId(Long.parseLong(szmCOrderMain.getCreateIden()));
                    szmCCartMain.setProductNum(smzCGroupOrder.getOrderProductNum());
                    szmCCartMain.setR1("1");
                    SmzCGroupOrder smzCGroupOrder1 = smzCGroupOrderMapper
                            .selectByPrimaryKey(smzCGroupOrder.getShopgroupId());
                    if (null != smzCGroupOrder1) {
                        szmCCartMain.setProductPrice(smzCGroupOrder1.getGroupOrderProductPrice());
                    }
                    szmCCartMain.setProductModelId(smzCGroupOrder.getShopgroupId());
                    szmCCartMain.setR2("true");
                    szmCCartMain.setR3(szmCOrderMain.getR2());
                    szmCCartMain.setCartDelState(0);
                    List<SzmCCartMain> szmCCartMainList = szmCCartMainMapper
                            .selectAllUserShop(Long.parseLong(szmCOrderMain.getCreateIden()), szmCCartMain.getR3());
                    if (null == szmCCartMainList || szmCCartMainList.size() == 0) {
                        int insert = szmCCartMainMapper.insert(szmCCartMain);
                        if (insert != 1) {
                            return resultBean.error("添加失败");
                        } else {
                            updat = 2;
                        }
                    } else {
                        ResultBean resultBean1 = szmCCartMainService.checkCartAddCart(szmCCartMainList, szmCCartMain);
                        Integer result = (Integer) resultBean1.getData();
                        if (result == 3) {
                            return resultBean.error("添加失败");
                        } else if (result == 1) {
                            int insert = szmCCartMainMapper.insert(szmCCartMain);
                            if (insert != 1) {
                                return resultBean.error("添加失败");
                            } else {
                                updat = 2;
                            }
                        }
                    }
                }
            } else if (smzCOrderDetails.size() > 0 && smzCGroupOrders.size() <= 0) {
                for (SmzCOrderDetails smzCOrderDetails1 : smzCOrderDetails) {
                    SzmCCartMain szmCCartMain = new SzmCCartMain();
                    szmCCartMain.setUserId(Long.parseLong(szmCOrderMain.getCreateIden()));
                    szmCCartMain.setProductNum(smzCOrderDetails1.getOrderProductNum());
                    szmCCartMain.setR1(smzCOrderDetails1.getR2());
                    SzmCProductModel szmCProductModel = szmCProductModelMapper
                            .selectByPrimaryKey(smzCOrderDetails1.getProductModelId());
                    if (null != szmCProductModel) {
                        szmCCartMain.setProductPrice(szmCProductModel.getProductOriginalPrice());
                    }
                    szmCCartMain.setProductModelId(smzCOrderDetails1.getProductModelId());
                    szmCCartMain.setR2("true");
                    szmCCartMain.setR3(szmCOrderMain.getR2());

                    szmCCartMain.setCartDelState(0);
                    List<SzmCCartMain> szmCCartMainList = szmCCartMainMapper
                            .selectAllUserShop(Long.parseLong(szmCOrderMain.getCreateIden()), szmCCartMain.getR3());
                    if (null == szmCCartMainList || szmCCartMainList.size() == 0) {
                        int insert = szmCCartMainMapper.insert(szmCCartMain);
                        if (insert != 1) {
                            return resultBean.error("添加失败");
                        } else {
                            updat = 2;
                        }
                    } else {
                        ResultBean resultBean1 = szmCCartMainService.checkCartAddCart(szmCCartMainList, szmCCartMain);
                        Integer result = (Integer) resultBean1.getData();
                        if (result == 3) {
                            return resultBean.error("添加失败");
                        } else if (result == 1) {
                            int insert = szmCCartMainMapper.insert(szmCCartMain);
                            if (insert != 1) {
                                return resultBean.error("添加失败");
                            } else {
                                updat = 2;
                            }
                        }
                    }
                }
            } else {
                for (SmzCGroupOrder smzCGroupOrder : smzCGroupOrders) {
                    SzmCCartMain szmCCartMain = new SzmCCartMain();
                    szmCCartMain.setUserId(Long.parseLong(szmCOrderMain.getCreateIden()));
                    szmCCartMain.setProductNum(smzCGroupOrder.getOrderProductNum());
                    szmCCartMain.setR1("1");
                    SmzCGroupOrder smzCGroupOrder1 = smzCGroupOrderMapper
                            .selectByPrimaryKey(smzCGroupOrder.getShopgroupId());
                    if (null != smzCGroupOrder1) {
                        szmCCartMain.setProductPrice(smzCGroupOrder1.getGroupOrderProductPrice());
                    }
                    szmCCartMain.setProductModelId(smzCGroupOrder.getShopgroupId());
                    szmCCartMain.setR2("true");
                    szmCCartMain.setR3(szmCOrderMain.getR2());
                    szmCCartMain.setCartDelState(0);
                    List<SzmCCartMain> szmCCartMainList = szmCCartMainMapper
                            .selectAllUserShop(Long.parseLong(szmCOrderMain.getCreateIden()), szmCCartMain.getR3());
                    if (null == szmCCartMainList || szmCCartMainList.size() == 0) {
                        int insert = szmCCartMainMapper.insert(szmCCartMain);
                        if (insert != 1) {
                            return resultBean.error("添加失败");
                        } else {
                            updat = 2;
                        }
                    } else {
                        ResultBean resultBean1 = szmCCartMainService.checkCartAddCart(szmCCartMainList, szmCCartMain);
                        Integer result = (Integer) resultBean1.getData();
                        if (result == 3) {
                            return resultBean.error("添加失败");
                        } else if (result == 1) {
                            int insert = szmCCartMainMapper.insert(szmCCartMain);
                            if (insert != 1) {
                                return resultBean.error("添加失败");
                            } else {
                                updat = 2;
                            }
                        }
                    }
                }
            }
            if (updat == 2) {
                long userId = Long.parseLong(szmCOrderMain.getCreateIden());
                List<SzmCCartMain> szmCCartMainList = szmCCartMainMapper.selectAll(userId);
                boolean set = redisUtil.set(userId + "cartsList", JSON.toJSONString(szmCCartMainList));
                if (set == true) {
                    return resultBean.success("添加成功");
                } else {
                    return resultBean.error("添加失败");
                }
            }
            return resultBean.success("添加成功");
        }
    }

    /**
     * 查看用户最近的订单
     *
     * @param userId
     * @return
     */
    @Override
    public ResultBean selectOneUser(Long userId, Long storeId) {
        ResultBean resultBean = new ResultBean();
        if (null == userId || 0 == userId) {
            return resultBean.error("用户id不能为空");
        }
        List<SzmCOrderMain> szmCOrderMains = szmCOrderMainMapper.selectOneUser(userId.toString(), storeId.toString());
        String img = "";
        if (!CollectionUtils.isEmpty(szmCOrderMains)) {
            List<SmzCPaymentMode> smzCPaymentModes = smzCPaymentModeMapper.selectAll();
            Map<Long, String> paymentModeMap = smzCPaymentModes.stream()
                    .collect(Collectors.toMap(SmzCPaymentMode::getPaymentModeId, SmzCPaymentMode::getPaymentModeName));
            Map<String, Object> returnMap = new HashMap<>();
            SzmCOrderMain szmCOrderMain = szmCOrderMains.get(0);
            SzmCStore szmCStore = szmCStoreMapper.selectStoreId(storeId);
            SzmCOrderMain szmCOrderMain1 = szmCOrderMainMapper.selectByOrderNum(szmCOrderMain.getOrderNum());
            List<SmzCGroupOrder> smzCGroupOrders = smzCGroupOrderMapper.selectByOrderNum(szmCOrderMain1.getOrderNum());
            List<SmzCOrderDetails> smzCOrderDetails = smzCOrderDetailsMapper
                    .selectByOrderNum(szmCOrderMain1.getOrderNum());
            Map<Integer, OrderSource> orderSourceMap = orderSourceMapper.selectAll().stream()
                    .collect(Collectors.toMap(OrderSource::getId, orderSource -> orderSource));
            returnMap.put("orderNumber", szmCOrderMain1.getOrderNum());
            String format = String.format("%.2f", Double.parseDouble(szmCOrderMain1.getR1()));
            returnMap.put("money", format);
            RetuenOrderList retuenOrderList = lookOrderDeatil(szmCOrderMain1, szmCStore, smzCOrderDetails,
                    smzCGroupOrders, paymentModeMap, orderSourceMap);
            List<OrderDeatil> list = retuenOrderList.getList();
            List<Map<String, Object>> list1 = new ArrayList<>();
            for (OrderDeatil orderDeatil : list) {
                for (OrderShopDeatil orderShopDeatil : orderDeatil.getOrderShopDeatilList()) {
                    Map<String, Object> map1 = new HashMap<>();
                    map1.put("name", orderShopDeatil.getTitle() + orderShopDeatil.getSkuName());
                    map1.put("number", orderShopDeatil.getShopNumber());
                    map1.put("img", orderShopDeatil.getImage());
                    map1.put("price", orderShopDeatil.getOrderMoney());
                    map1.put("onePrice",
                            new BigDecimal(
                                    orderShopDeatil.getOrderMoney() == null ? 0 : orderShopDeatil.getOrderMoney())
                                    .divide(
                                            orderShopDeatil.getShopNumber() == null ? BigDecimal.ONE
                                                    : new BigDecimal(orderShopDeatil.getShopNumber())));
                    map1.put("title", orderShopDeatil.getTitle());
                    map1.put("skuName", orderShopDeatil.getSkuName());
                    map1.put("skuId", orderShopDeatil.getSkuId());
                    if ("".equals(img)) {
                        SzmCProductModel szmCProductModel = szmCProductModelMapper
                                .selectByPrimaryKey(orderShopDeatil.getSkuId());
                        img = szmCProductModel.getR1();
                    }
                    list1.add(map1);
                }
            }
            for (GroupShop groupShop : retuenOrderList.getGroupShopList()) {
                Map<String, Object> map = new HashMap<>();
                map.put("name", groupShop.getGroupTitle());
                map.put("number", groupShop.getGroupNumber());
                map.put("img", groupShop.getGroupPhoto());
                if ("".equals(img)) {
                    SzmCShopgroup szmCShopgroup = szmCShopgroupMapper.selectByPrimaryKey(groupShop.getGrouoId());
                    img = szmCShopgroup.getR1();
                }
                list1.add(map);
            }
            returnMap.put("list", list1);
            returnMap.put("img", img);
            return resultBean.success(returnMap);
        }
        return resultBean.error("暂无数据");
    }

    @Override
    public ResultBean selectOneUserreturnAdmin(Long userId, Long storeId) {
        ResultBean resultBean = new ResultBean();
        if (null == userId || 0 == userId) {
            return resultBean.error("用户id不能为空");
        }

        List<ShopGroupSkuAll> list = new ArrayList<>();
        SzmCOrderMain szmCOrderMain = szmCOrderMainMapper.selectUserLastBuy(userId, storeId);
        String img = "";
        if (szmCOrderMain != null) {
            List<SmzCOrderDetails> smzCOrderDetails = smzCOrderDetailsMapper
                    .selectByOrderNum(szmCOrderMain.getOrderNum());
            List<Long> orderModelIds = smzCOrderDetails.stream().map(SmzCOrderDetails::getProductModelId)
                    .collect(Collectors.toList());
            List<SzmCProductModel> szmCProductModels = CollectionUtil.isEmpty(orderModelIds) ? new ArrayList<>()
                    : szmCProductModelMapper.selectByIds(orderModelIds);
            List<SzmCProduct> szmCProducts = CollectionUtil.isEmpty(szmCProductModels) ? new ArrayList<>()
                    : szmCProductMapper.selectByIds(szmCProductModels.stream().map(SzmCProductModel::getProductId)
                            .collect(Collectors.toList()));
            Map<Long, SzmCProduct> productMap = szmCProducts.stream()
                    .collect(Collectors.toMap(SzmCProduct::getProductId, e -> e));
            Map<Long, SzmCProductModel> modelMap = szmCProductModels.stream()
                    .collect(Collectors.toMap(SzmCProductModel::getProductModelId, e -> e));

            for (SmzCOrderDetails smzCOrderDetail : smzCOrderDetails) {
                SzmCProductModel szmCProductModel = modelMap.get(smzCOrderDetail.getProductModelId());

                SzmCProduct szmCProduct1 = productMap.get(szmCProductModel.getProductId());
                String s = selectClassService.selectStoreBrand(szmCProductModel.getR3());
                // if ("0".equals(szmCProductModel.getR5())) {
                ShopGroupSkuAll shopGroupSkuAll = new ShopGroupSkuAll();
                shopGroupSkuAll.setSkuId(szmCProductModel == null ? null : szmCProductModel.getProductModelId());
                Map map = szmCProductModel == null ? new HashMap<>()
                        : JSON.parseObject(szmCProductModel.getSpecificationsDescribe(), Map.class);
                StringBuffer stringBuffer = new StringBuffer();
                for (Object obj : map.keySet()) {
                    stringBuffer.append(map.get(obj) + "");
                }
                shopGroupSkuAll.setSpuName(szmCProduct1.getProductTitle());
                shopGroupSkuAll.setBrandName(s);
                shopGroupSkuAll.setSkuName(stringBuffer.toString());
                shopGroupSkuAll
                        .setPrice(StringUtils.isNotEmpty(szmCProductModel == null ? "" : szmCProductModel.getR2())
                                ? Double.parseDouble(szmCProductModel == null ? "" : szmCProductModel.getR2())
                                : szmCProductModel == null ? 0 : szmCProductModel.getProductOriginalPrice());
                shopGroupSkuAll
                        .setContent(StringUtils.isNotEmpty(szmCProductModel == null ? "" : szmCProductModel.getR1())
                                ? szmCProductModel == null ? "" : szmCProductModel.getR1()
                                : szmCProduct1 == null ? "" : szmCProduct1.getR1());
                shopGroupSkuAll
                        .setNewPrice(szmCProductModel == null ? 0 : szmCProductModel.getProductOriginalPrice());
                shopGroupSkuAll.setInventory(szmCProductModel == null ? 0 : szmCProductModel.getProductInventory());
                shopGroupSkuAll.setNum(smzCOrderDetail.getOrderProductNum());
                shopGroupSkuAll.setDeliveryUserId(szmCOrderMain.getDeliveryInfoId());
                list.add(shopGroupSkuAll);
                // }
            }
            return resultBean.success(list);
        }
        return resultBean.error("暂无数据");
    }

    /**
     * 查看水票列表
     *
     * @param watereReId
     * @return
     */
    @Override
    public ResultBean selectOrderWaterList(Long watereReId, Integer index) {
        ResultBean resultBean = new ResultBean();
        if (null == watereReId || 0 == watereReId) {
            return resultBean.error("水票id不能为空");
        }
        if (null == index || 0 == index) {
            return resultBean.error("下标不能为空");
        }
        PageHelper.startPage(index, 10);
        ResultBean resultBean1 = smzCWcUseService.selOrderNumByReId(watereReId, index);
        if (resultBean1.getCode() != 1) {
            return resultBean1;
        }
        List<WcUseRecord> wcUseRecord = (List<WcUseRecord>) resultBean1.getData();
        if (wcUseRecord.size() <= 0) {
            return resultBean.error("暂无数据");
        }
        List<RetuenOrderList> lists = new ArrayList<>();
        List<SmzCPaymentMode> smzCPaymentModes = smzCPaymentModeMapper.selectAll();
        Map<Integer, OrderSource> orderSourceMap = orderSourceMapper.selectAll().stream()
                .collect(Collectors.toMap(OrderSource::getId, orderSource -> orderSource));
        Map<Long, String> paymentModeMap = smzCPaymentModes.stream()
                .collect(Collectors.toMap(SmzCPaymentMode::getPaymentModeId, SmzCPaymentMode::getPaymentModeName));
        for (WcUseRecord wcUseRecord1 : wcUseRecord) {
            SzmCOrderMain szmCOrderMain = szmCOrderMainMapper.selectByOrderNum(wcUseRecord1.getOrderNum());
            SzmCStore szmCStore = szmCStoreMapper.selectLocationById(szmCOrderMain.getStoreId());
            List<SmzCGroupOrder> smzCGroupOrders = smzCGroupOrderMapper.selectByOrderNum(szmCOrderMain.getOrderNum());
            List<SmzCOrderDetails> smzCOrderDetails = smzCOrderDetailsMapper
                    .selectByOrderNum(szmCOrderMain.getOrderNum());

            RetuenOrderList retuenOrderList = lookOrderDeatil(szmCOrderMain, szmCStore, smzCOrderDetails,
                    smzCGroupOrders, paymentModeMap, orderSourceMap);
            retuenOrderList.setDiscounPrice(szmCOrderMain.getOrderDiscounts());
            if (szmCOrderMain.getOrderStatus() == 1 || szmCOrderMain.getOrderStatus() == 2) {
                if (redisUtil.get("store" + szmCOrderMain.getOrderNum()) == null) {
                    retuenOrderList.setIsSend(1);
                } else {
                    retuenOrderList.setIsSend(2);
                }
            } else {
                retuenOrderList.setIsSend(0);
            }

            if (szmCOrderMain.getOrderStatus() == 5 || szmCOrderMain.getOrderStatus() == 9
                    || szmCOrderMain.getOrderStatus() == 10) {
                SzmCProductDiscuss szmCProductDiscuss = szmCProductDiscussMapper
                        .selectByOrderNum(szmCOrderMain.getOrderNum());
                if (szmCProductDiscuss == null) {
                    retuenOrderList.setEvaluate(1);
                } else {
                    retuenOrderList.setEvaluate(2);
                }
            } else {
                retuenOrderList.setEvaluate(0);
            }
            if (szmCOrderMain.getOrderStatus() == 8 || szmCOrderMain.getOrderStatus() == 10) {
                SmzCOrderReturns smzCOrderReturns = smzCOrderReturnsMapper
                        .selectByOrderNum(szmCOrderMain.getOrderNum());
                if (null != smzCOrderReturns) {
                    if (smzCOrderReturns.getProcessstate() == 0) {
                        retuenOrderList.setOrderReturnStete("申请中");
                    } else if (smzCOrderReturns.getProcessstate() == 1) {
                        retuenOrderList.setOrderReturnStete("已同意");
                    } else {
                        retuenOrderList.setOrderReturnStete("已拒绝");
                    }
                }
            }
            lists.add(retuenOrderList);
        }
        Collections.sort(lists, new Comparator<RetuenOrderList>() {
            @Override
            public int compare(RetuenOrderList o1, RetuenOrderList o2) {
                try {
                    if (o1.getOrderDate().getTime() > o2.getOrderDate().getTime()) {
                        return 1;
                    } else if (o1.getOrderDate().getTime() < o2.getOrderDate().getTime()) {
                        return -1;
                    } else {
                        return 0;
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                return 0;
            }
        });
        Collections.reverse(lists);
        return resultBean.success(lists);
    }

    /**
     * 生成线上押桶对象准备支付
     *
     * @param storeId
     * @param userId
     * @param bucketPriceVo
     * @return
     */
    @Override
    public ResultBean getBucketOrder(Long storeId, Long userId, Integer payType, String orderNum,
            List<BucketPriceVo> bucketPriceVo) {
        ResultBean resultBean = new ResultBean();
        String storeIdByTime = OrderNumUtil.getStoreIdByTime("1");
        SzmCOrderMain szmCOrderMain = szmCOrderMainMapper.selectByOrderNum(orderNum);
        if (!CollectionUtil.isEmpty(bucketPriceVo)) {
            LinkedList<PledgeBuckOrder> pledgeBuckOrders = new LinkedList<>();
            for (BucketPriceVo priceVo : bucketPriceVo) {
                PledgeBuckOrder pledgeBuckOrder = new PledgeBuckOrder();
                pledgeBuckOrder.setDelState(0);
                pledgeBuckOrder.setUserId(userId);
                pledgeBuckOrder.setStoreId(storeId);
                pledgeBuckOrder.setBrandId(priceVo.getBrandId());
                pledgeBuckOrder.setBrandName(priceVo.getBrandName());
                pledgeBuckOrder.setBuckNumber(priceVo.getNum());
                pledgeBuckOrder.setBuckMoney(priceVo.getPrice());
                pledgeBuckOrder.setOrderNumber(orderNum);
                pledgeBuckOrder.setR1("0");
                pledgeBuckOrder.setR2(storeIdByTime);
                pledgeBuckOrder.setR3(priceVo.getSkuId().toString());
                pledgeBuckOrder.setR4(payType.toString());
                pledgeBuckOrder.setR5("0");
                if (null != szmCOrderMain) {
                    pledgeBuckOrder.setOrderMainId(szmCOrderMain.getOrderMainId());
                }
                pledgeBuckOrders.add(pledgeBuckOrder);
            }
            return resultBean.success(pledgeBuckOrders);
        }
        return resultBean.error("暂无押桶信息");
    }

    /**
     * 生成线上押桶对象准备支付(微信)
     *
     * @param storeId
     * @param userId
     * @param bucketPriceVo
     * @return
     */
    @Override
    public ResultBean getBucketOrderWx(Long storeId, Long userId, Integer payType, String orderNum,
            List<BucketPriceVo> bucketPriceVo) {
        ResultBean resultBean = new ResultBean();
        String storeIdByTime = OrderNumUtil.getStoreIdByTime("1");
        SzmCOrderMain szmCOrderMain = szmCOrderMainMapper.selectByOrderNum(orderNum);
        if (!CollectionUtil.isEmpty(bucketPriceVo)) {
            LinkedList<PledgeBuckOrder> pledgeBuckOrders = new LinkedList<>();
            for (BucketPriceVo priceVo : bucketPriceVo) {
                PledgeBuckOrder pledgeBuckOrder = new PledgeBuckOrder();
                pledgeBuckOrder.setDelState(2);
                pledgeBuckOrder.setUserId(userId);
                pledgeBuckOrder.setStoreId(storeId);
                pledgeBuckOrder.setBrandId(priceVo.getBrandId());
                pledgeBuckOrder.setBrandName(priceVo.getBrandName());
                pledgeBuckOrder.setBuckNumber(priceVo.getNum());
                pledgeBuckOrder.setBuckMoney(priceVo.getPrice());
                pledgeBuckOrder.setOrderNumber(orderNum);
                pledgeBuckOrder.setR1("0");
                pledgeBuckOrder.setR2(storeIdByTime);
                pledgeBuckOrder.setR3(priceVo.getSkuId().toString());
                pledgeBuckOrder.setR4(payType.toString());
                pledgeBuckOrder.setR5("0");
                if (null != szmCOrderMain) {
                    pledgeBuckOrder.setOrderMainId(szmCOrderMain.getOrderMainId());
                }
                pledgeBuckOrders.add(pledgeBuckOrder);
            }
            return resultBean.success(pledgeBuckOrders);
        }
        return resultBean.error("暂无押桶信息");
    }

    /**
     * 发起线上押桶对象支付
     *
     * @param orderNum
     * @param userId
     * @return
     */
    @Override
    public ResultBean payBucketOrder(String orderNum, Long userId, Double money) {
        ResultBean resultBean = new ResultBean();
        SzmCUser szmCUser = szmCUserMapper.selectByUserId(userId);
        if (Validator.isNull(szmCUser)) {
            return resultBean.error("系统异常,请稍等再试");
        }
        String openid = StringUtils.isNotBlank(szmCUser.getUserOpenid()) ? szmCUser.getUserOpenid() : szmCUser.getR5();
        SzmCStore szmCStore = szmCStoreMapper.selectStoreId(Long.parseLong(szmCUser.getR2()));
        if (szmCStore != null && szmCStore.getFenzhang() != null
                && szmCStore.getFenzhang().equals(1) && StringUtils.isNotEmpty(szmCStore.getFenzhangnumber())) {
            return weiXinCommonService.commonFenzhangPay(WxPayConfig.appid_c, WxPayConfig.mch_id, openid,
                    "线上押桶支付-" + orderNum, orderNum, money, WxPayConfig.payBucketOrder_urlfenzhang, szmCStore);
        } else {
            return weiXinCommonService.commonWxPay(WxPayConfig.appid_c, WxPayConfig.mch_id, openid,
                    "线上押桶支付-" + orderNum, orderNum, money, WxPayConfig.payBucketOrder_url, null);
        }
        // resultBean = weiXinCommonService.commonWxPay(WxPayConfig.appid_c,
        // WxPayConfig.mch_id, openid,
        // "线上押桶支付-" + orderNum, orderNum, money, WxPayConfig.payBucketOrder_url, null);
        // return resultBean;
    }

    /**
     * 根据购物车id查看商品是否下架
     *
     * @param list
     * @return
     */
    @Override
    public ResultBean isSoldout(String list) {
        ResultBean resultBean = new ResultBean();
        List<Long> longList = new ArrayList<>();
        List<Long> list1 = JSONArray.parseArray(list, Long.class);
        for (Long aLong : list1) {
            Integer integer = 0;
            SzmCCartMain szmCCartMain = szmCCartMainMapper.selectByPrimaryKey(aLong);
            if (szmCCartMain.getR1().equals("0")) {
                integer = szmCProductModelMapper.selectByState(szmCCartMain.getProductModelId());
            } else {
                integer = szmCShopgroupMapper.selectByState(szmCCartMain.getProductModelId());
            }
            if (integer > 0) {
                longList.add(aLong);
            }
        }
        return resultBean.success(longList);
    }

    /**
     * 返回一个普通订单的集合
     *
     * @param szmlist
     * @return
     */
    public List<OrderShopDeatil> resultOrderMain(List<SmzCOrderDetails> szmlist) {
        // 返回的订单的详细信息
        List<OrderShopDeatil> orderShopDeatilList = new ArrayList<OrderShopDeatil>();
        // 订单详细信息
        for (SmzCOrderDetails smzCOrderDetails1 : szmlist) {
            OrderShopDeatil orderShopDeatil = new OrderShopDeatil();
            // 商品的sku
            orderShopDeatil.setSkuId(smzCOrderDetails1.getProductModelId());
            orderShopDeatil.setOrderDetailsId(smzCOrderDetails1.getOrderDetailsId());
            // 商品的图片
            orderShopDeatil.setPrice(
                    smzCOrderDetails1.getMarketPrice() == null ? "0" : smzCOrderDetails1.getMarketPrice().toString());
            orderShopDeatil.setImage(smzCOrderDetails1.getProductSkuimg());
            // 订单的价格
            orderShopDeatil.setOrderMoney(smzCOrderDetails1.getOrderDetailsProductPrice());
            // 商品的数量
            orderShopDeatil.setShopNumber(smzCOrderDetails1.getOrderProductNum());
            orderShopDeatil.setDiscounts(smzCOrderDetails1.getR4());
            // }
            if ((smzCOrderDetails1.getOrderDetailsProductPrice() / smzCOrderDetails1.getOrderProductNum() != Double
                    .parseDouble(smzCOrderDetails1.getR1()))) {
                orderShopDeatil.setVipDoller(
                        smzCOrderDetails1.getOrderDetailsProductPrice() / smzCOrderDetails1.getOrderProductNum());
            }
            if (smzCOrderDetails1.getProductModelId() != null) {

                SzmCProductModel szmCProductModel = szmCProductModelMapper
                        .selectProductIdAndRule(smzCOrderDetails1.getProductModelId());
                // 商品的sku具体的值
                if (null != szmCProductModel) {
                    Map map = JSON.parseObject(szmCProductModel.getSpecificationsDescribe(), Map.class);
                    String name = szmCProductMapper.selectProductNameById(szmCProductModel.getProductId());
                    orderShopDeatil.setTitle(name);
                    if (null == smzCOrderDetails1.getR1()) {
                        smzCOrderDetails1.setR1("0.00");
                    }
                    orderShopDeatil.setShopDoller(Double.parseDouble(smzCOrderDetails1.getR1()));
                    orderShopDeatil.setMarketPrice(smzCOrderDetails1.getMarketPrice());
                    StringBuffer stringBuffer = new StringBuffer();
                    for (Object obj : map.keySet()) {
                        stringBuffer.append(map.get(obj));
                    }
                    // sku具体的名称
                    orderShopDeatil.setSkuName(stringBuffer.toString());
                    // orderShopDeatil.setTitle(stringBuffer.toString());
                } else {
                    if (null == smzCOrderDetails1.getR1()) {
                        smzCOrderDetails1.setR1("0.00");
                    }
                    orderShopDeatil.setShopDoller(Double.parseDouble(smzCOrderDetails1.getR1()));
                    orderShopDeatil.setMarketPrice(smzCOrderDetails1.getMarketPrice());
                    orderShopDeatil.setSkuName(smzCOrderDetails1.getProductSkuname());
                    orderShopDeatil.setTitle(smzCOrderDetails1.getProductSkuname());
                }
            } else {
                if (null == smzCOrderDetails1.getR1()) {
                    smzCOrderDetails1.setR1("0.00");
                }
                orderShopDeatil.setShopDoller(Double.parseDouble(smzCOrderDetails1.getR1()));
                orderShopDeatil.setMarketPrice(smzCOrderDetails1.getMarketPrice());
                orderShopDeatil.setSkuName(smzCOrderDetails1.getProductSkuname());
                orderShopDeatil.setTitle(smzCOrderDetails1.getProductSkuname());

            }
            orderShopDeatilList.add(orderShopDeatil);
        }
        return orderShopDeatilList;
    }

    /**
     * 处理套餐集合
     *
     * @param smzCGroupOrders
     * @return
     */
    public List<GroupShop> resultGroupShop(List<SmzCGroupOrder> smzCGroupOrders) {
        List<GroupShop> groupShops = new ArrayList<>();
        if (CollectionUtil.isEmpty(smzCGroupOrders)) {
            return groupShops;
        }
        for (SmzCGroupOrder smzCGroupOrder : smzCGroupOrders) {
            GroupShop groupShop = new GroupShop();
            groupShop.setGroupOrderId(smzCGroupOrder.getGroupOrderNum());
            // 组合套餐的订单号
            groupShop.setGroupOrderNumber(smzCGroupOrder.getOrderMainId());
            // 查看套餐商品
            // SzmCShopgroup szmCShopgroup =
            // szmCShopgroupMapper.selectByPrimaryKey(smzCGroupOrder.getShopgroupId());
            // 组合套餐的名称
            groupShop.setGroupName(smzCGroupOrder.getR1());
            // 组合套餐的标题
            groupShop.setGroupTitle(smzCGroupOrder.getR1());
            // 组合套餐的数量
            groupShop.setGroupNumber(smzCGroupOrder.getOrderProductNum());
            // 组合套餐的图片
            groupShop.setGroupPhoto(smzCGroupOrder.getR2());
            double original = 0d;
            // 组合套餐的id
            groupShop.setGrouoId(smzCGroupOrder.getShopgroupId());
            // Long shopgroupId = szmCShopgroup.getShopgroupId();
            // 商品数据
            List<GroupShopList> groupShopLists = new ArrayList<>();
            if (null != smzCGroupOrder.getR4()) {
                List<Map<String, Object>> listObjectFir = (List<Map<String, Object>>) JSONArray
                        .parse(smzCGroupOrder.getR4());
                for (Map<String, Object> mapList : listObjectFir) {
                    GroupShopList groupShopList = new GroupShopList();
                    groupShopList.setRule(mapList.get("name").toString());
                    groupShopList.setTitle(mapList.get("rule").toString());
                    groupShopList.setShopNumber(Integer.parseInt(mapList.get("num").toString()));
                    original += Double.parseDouble(mapList.get("price").toString())
                            * Integer.parseInt(mapList.get("num").toString());
                    groupShopList.setShopDoller(Double.parseDouble(mapList.get("price").toString()));
                    groupShopLists.add(groupShopList);
                }
            }
            // 组合套餐的原价
            groupShop.setGrooupDoller(original);
            // 组合套餐的价钱
            groupShop.setGroupPrice(
                    MoneyUtil.div(smzCGroupOrder.getGroupOrderProductPrice(), smzCGroupOrder.getOrderProductNum()));
            groupShop.setList(groupShopLists);
            groupShops.add(groupShop);
        }
        return groupShops;
    }

    /**
     * 企业付款
     *
     * @param openid
     * @param prices
     * @param ordernum
     * @param sppId
     * @param request
     * @return
     */
    public ResultBean wxSendPay(String openid, Double prices, String ordernum, String sppId,
            HttpServletRequest request) {
        ResultBean resultBean = new ResultBean();
        // 客户的openid
        String openId = openid;
        // 订单编号
        String orderNum = ordernum;
        Integer price = (int) (MoneyUtil.mul(prices, 100));
        String spbill_create_ip = IpUtils.getIpAddr(request);
        try {
            TransfersInfo order = new TransfersInfo();
            order.setMch_appid(sppId);
            order.setMchid(WxPayConfig.mch_id);
            order.setNonce_str(RandomStringGenerator.getRandomStringByLength(32));
            order.setDesc("付款-" + orderNum);
            order.setPartner_trade_no(orderNum);
            order.setAmount(price); // 该金钱其实10 是 0.1元
            order.setSpbill_create_ip(spbill_create_ip);
            order.setOpenid(openId);
            order.setCheck_name("NO_CHECK");
            // 生成签名
            String sign = Signature.getSign(order);
            order.setSign(sign);
            String result = HttpRequest.sendPostCert(WxPayConfig.transfers_url, order);
            System.out.println(result);
            XStream xStream = new XStream();
            xStream.alias("xml", TransfersReturnInfo.class);
            TransfersReturnInfo returnInfo = (TransfersReturnInfo) xStream.fromXML(result);
            SerializeUtil.Reflect(returnInfo);
            if ("SUCCESS".equals(returnInfo.getReturn_code()) && "SUCCESS".equals(returnInfo.getResult_code())) {
                LoggerUtil.info("企业付款成功");
                return resultBean.success("成功");
            }
            LoggerUtil.info("企业付款失败");
            return resultBean.error("企业付款失败" + returnInfo.getReturn_msg());
        } catch (Exception e) {
            LoggerUtil.info("企业付款失败");
            e.printStackTrace();
            return new ResultBean(e);
        }
    }

    /**
     * 获取超时订单统计 - 按时间区间分组
     * 
     * @param params 查询参数
     * @return 超时统计数据
     */
    @Override
    public ResultBean getTimeoutStats(Map<String, Object> params) {
        ResultBean resultBean = new ResultBean();
        String searchType = (String) params.get("searchType");
        params.put("orderStatus", 2);
        try {
            // 查询所有订单
            List<SzmCOrderMain> allOrders = (StringUtils.isNotEmpty(searchType)
                    && (searchType.equals("planform") || searchType.equals("pdd")))
                            ? szmCOrderMainMapper.selectPingtaiByParams(params)
                            : szmCOrderMainMapper.selectAllByParams(params);

            // 初始化时间区间统计 - 前端期望的数据结构
            Map<String, Integer> timeoutStats = new HashMap<>();
            timeoutStats.put("1h", 0); // 1小时内
            timeoutStats.put("2h", 0); // 1-2小时
            timeoutStats.put("4h", 0); // 2-4小时
            timeoutStats.put("8h", 0); // 4-8小时
            timeoutStats.put("16h", 0); // 8-16小时
            timeoutStats.put("24h", 0); // 16-24小时
            timeoutStats.put("48h", 0); // 24-48小时
            timeoutStats.put("72h", 0); // 48-72小时
            timeoutStats.put("72h+", 0); // 72小时以上

            Date currentTime = new Date();

            // 遍历订单计算超时统计
            for (SzmCOrderMain order : allOrders) {
                // 只统计未配送的订单（状态为0-2）
                if (order.getCreateTime() != null) {
                    double timeoutHours = calculateTimeoutHours(order, currentTime);
                    categorizeTimeout(timeoutHours, timeoutStats);
                }
            }

            // 直接返回前端期望的数据结构
            return resultBean.success(timeoutStats);

        } catch (Exception e) {
            return resultBean.error("获取超时统计失败: " + e.getMessage());
        }
    }

    @Override
    public ResultBean getUndeliveredTimeoutStats(Map<String, Object> params) {
        ResultBean resultBean = new ResultBean();
        String searchType = (String) params.get("searchType");
        params.put("orderStatus", 3);
        try {
            // 查询所有订单
            // 查询所有订单
            List<SzmCOrderMain> allOrders = (StringUtils.isNotEmpty(searchType)
                    && (searchType.equals("planform") || searchType.equals("pdd")))
                            ? szmCOrderMainMapper.selectPingtaiByParams(params)
                            : szmCOrderMainMapper.selectAllByParams(params);

            // 初始化时间区间统计 - 前端期望的数据结构
            Map<String, Integer> timeoutStats = new HashMap<>();
            timeoutStats.put("1h", 0); // 1小时内
            timeoutStats.put("2h", 0); // 1-2小时
            timeoutStats.put("4h", 0); // 2-4小时
            timeoutStats.put("8h", 0); // 4-8小时
            timeoutStats.put("16h", 0); // 8-16小时
            timeoutStats.put("24h", 0); // 16-24小时
            timeoutStats.put("48h", 0); // 24-48小时
            timeoutStats.put("72h", 0); // 48-72小时
            timeoutStats.put("72h+", 0); // 72小时以上

            Date currentTime = new Date();

            // 遍历订单计算超时统计
            for (SzmCOrderMain order : allOrders) {
                // 只统计未配送的订单（状态为0-2）
                if (order.getCreateTime() != null) {
                    double timeoutHours = calculateTimeoutHours(order, currentTime);
                    categorizeTimeout(timeoutHours, timeoutStats);
                }
            }

            // 直接返回前端期望的数据结构
            return resultBean.success(timeoutStats);

        } catch (Exception e) {
            return resultBean.error("获取超时统计失败: " + e.getMessage());
        }
    }

    /**
     * 判断是否为未配送订单
     */
    private boolean isUndeliveredOrder(SzmCOrderMain order) {
        // 根据业务逻辑，状态为0-2为未配送
        return order.getOrderStatus() != null &&
                (order.getOrderStatus() == 0 || order.getOrderStatus() == 1 || order.getOrderStatus() == 2);
    }

    /**
     * 计算订单超时小时数
     */
    private double calculateTimeoutHours(SzmCOrderMain order, Date currentTime) {
        if (order.getCreateTime() == null) {
            return 0;
        }

        long diffMs = currentTime.getTime() - order.getCreateTime().getTime();
        double diffHours = diffMs / (1000.0 * 60 * 60);
        return Math.max(0, diffHours);
    }

    /**
     * 将超时小时数归类到对应的统计区间
     */
    private void categorizeTimeout(double hours, Map<String, Integer> timeoutStats) {
        if (hours <= 1) {
            timeoutStats.put("1h", timeoutStats.get("1h") + 1);
        } else if (hours <= 2) {
            timeoutStats.put("2h", timeoutStats.get("2h") + 1);
        } else if (hours <= 4) {
            timeoutStats.put("4h", timeoutStats.get("4h") + 1);
        } else if (hours <= 8) {
            timeoutStats.put("8h", timeoutStats.get("8h") + 1);
        } else if (hours <= 16) {
            timeoutStats.put("16h", timeoutStats.get("16h") + 1);
        } else if (hours <= 24) {
            timeoutStats.put("24h", timeoutStats.get("24h") + 1);
        } else if (hours <= 48) {
            timeoutStats.put("48h", timeoutStats.get("48h") + 1);
        } else if (hours <= 72) {
            timeoutStats.put("72h", timeoutStats.get("72h") + 1);
        } else {
            timeoutStats.put("72h+", timeoutStats.get("72h+") + 1);
        }
    }

    @Override
    public ResultBean uploadPic(MultipartFile file, Long orderMainId) {

        ResultBean resultBean = new ResultBean();
        if (file.isEmpty()) {
            return resultBean.error("上传失败，请选择文件");
        }
        SzmCOrderMain szmCOrderMain = szmCOrderMainMapper.selectByPrimaryKey(orderMainId);
        if (szmCOrderMain == null) {
            return resultBean.error("订单不存在");
        }
        String name = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf(".") + 1);
        String fileName = OrderNumUtil.getOrderIdByTime(null) + "." + name;
        Integer state = 0;
        if (fileName.contains(".png")) {
            state = 1;
        }
        String filePath = "/opt/static/upload/";
        file.getName();
        File dest = new File(filePath + fileName);
        try {
            file.transferTo(dest);
            String[] format = { "AVI", "mov", "rmvb", "rm", "FLV", "mp4", "3GP" };
            boolean flag = false;
            for (int i = 0; i < format.length; i++) {
                if (format[i].equalsIgnoreCase(name)) {
                    flag = true;
                    break;
                }
            }
            if (!flag) {
                if (state == 0) {
                    Thumbnails.of(dest).scale(1f).outputQuality(0.25f).toFile(dest);
                }
            }
            String url = "https://waterstation.com.cn/szm/upload/" + fileName;
            if (StringUtils.isNotEmpty(szmCOrderMain.getPicurl())) {

                szmCOrderMain.setPicurl(szmCOrderMain.getPicurl() + "," + url);
            } else {
                szmCOrderMain.setPicurl(url);
            }
            szmCOrderMainMapper.updateByOrderNum(szmCOrderMain);
            return resultBean.success("https://waterstation.com.cn/szm/upload/" + fileName);
        } catch (IOException e) {
            return new ResultBean(e);
        }
    }

    /**
     * 获取按水站分组的超时统计数据
     */
    @Override
    public ResultBean getStoreTimeoutStats(Map<String, Object> params) {
        ResultBean resultBean = new ResultBean();
        try {
            // 获取查询参数
            String storeId = (String) params.get("storeId");
            String timeoutFilter = (String) params.get("timeoutFilter");
            String timeoutType = (String) params.get("timeoutType");
            String startTime = (String) params.get("startTime");
            String endTime = (String) params.get("endTime");
            String appkey = (String) params.get("appkey");
            String ordersource = (String) params.get("ordersource");

            // 构建查询条件
            Map<String, Object> queryParams = new HashMap<>();
            queryParams.put("storeId", storeId);
            queryParams.put("startTime", startTime);
            queryParams.put("endTime", endTime);
            queryParams.put("appkey", appkey);
            queryParams.put("ordersource", ordersource);

            // 根据超时类型设置订单状态
            if ("undelivered".equals(timeoutType)) {
                queryParams.put("orderStatus", "2"); // 未配送状态
            } else if ("undelivered_timeout".equals(timeoutType)) {
                queryParams.put("orderStatus", "3"); // 未送达状态
            }

            // 获取所有符合条件的订单
            List<SzmCOrderMain> allOrders = szmCOrderMainMapper.selectPingtaiByParams(queryParams);

            // 按水站分组统计超时订单
            Map<Long, Map<String, Object>> storeStatsMap = new HashMap<>();
            Date currentTime = new Date();

            for (SzmCOrderMain order : allOrders) {
                if (order.getCreateTime() != null && order.getStoreId() != null) {
                    double timeoutHours = calculateTimeoutHours(order, currentTime);

                    // 判断是否符合指定的超时区间
                    if (isInTimeoutRange(timeoutHours, timeoutFilter)) {
                        Long orderStoreId = order.getStoreId();

                        if (!storeStatsMap.containsKey(orderStoreId)) {
                            // 查询水站信息
                            SzmCStore store = szmCStoreMapper.selectByPrimaryKey(orderStoreId);
                            String storeName = store != null ? store.getStoreAme() : "未知水站";
                            String storePhone = store != null ? store.getStorePhone() : null;

                            Map<String, Object> storeInfo = new HashMap<>();
                            storeInfo.put("storeId", orderStoreId);
                            storeInfo.put("storeName", storeName);
                            storeInfo.put("storePhone", storePhone);
                            storeInfo.put("timeoutCount", 0);
                            storeStatsMap.put(orderStoreId, storeInfo);
                        }

                        Map<String, Object> storeInfo = storeStatsMap.get(orderStoreId);
                        int currentCount = (Integer) storeInfo.get("timeoutCount");
                        storeInfo.put("timeoutCount", currentCount + 1);
                    }
                }
            }

            // 转换为列表并按超时订单数量降序排序
            List<Map<String, Object>> storeStatsList = new ArrayList<>(storeStatsMap.values());
            storeStatsList.sort((a, b) -> {
                Integer countA = (Integer) a.get("timeoutCount");
                Integer countB = (Integer) b.get("timeoutCount");
                return countB.compareTo(countA);
            });

            return resultBean.success(storeStatsList);

        } catch (Exception e) {
            return resultBean.error("获取水站超时统计失败: " + e.getMessage());
        }
    }

    /**
     * 判断超时时长是否在指定区间内
     */
    private boolean isInTimeoutRange(double timeoutHours, String timeoutFilter) {
        if (timeoutFilter == null || timeoutFilter.isEmpty()) {
            return true;
        }

        switch (timeoutFilter) {
            case "1h":
                return timeoutHours <= 1;
            case "2h":
                return timeoutHours > 1 && timeoutHours <= 2;
            case "4h":
                return timeoutHours > 2 && timeoutHours <= 4;
            case "8h":
                return timeoutHours > 4 && timeoutHours <= 8;
            case "16h":
                return timeoutHours > 8 && timeoutHours <= 16;
            case "24h":
                return timeoutHours > 16 && timeoutHours <= 24;
            case "48h":
                return timeoutHours > 24 && timeoutHours <= 48;
            case "72h":
                return timeoutHours > 48 && timeoutHours <= 72;
            case "72h+":
                return timeoutHours > 72;
            default:
                return true;
        }
    }

}
