(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6f81ffda"],{"1fab8":function(n,t,e){"use strict";e("96d7")},"26dcc":function(n,t,e){"use strict";e.r(t);var i=function(){var n=this,t=n._self._c;return t("div",{staticClass:"tinymce-editor"},[t("editor",{attrs:{init:n.init},on:{onExecCommand:n.onExecCommand},model:{value:n.myValue,callback:function(t){n.myValue=t},expression:"myValue"}})],1)},o=[],r=["onActivate","onAddUndo","onBeforeAddUndo","onBeforeExecCommand","onBeforeGetContent","onBeforeRenderUI","onBeforeSetContent","onBeforePaste","onBlur","onChange","onClearUndos","onClick","onContextMenu","onCopy","onCut","onDblclick","onDeactivate","onDirty","onDrag","onDragDrop","onDragEnd","onDragGesture","onDragOver","onDrop","onExecCommand","onFocus","onFocusIn","onFocusOut","onGetContent","onHide","onInit","onKeyDown","onKeyPress","onKeyUp","onLoadContent","onMouseDown","onMouseEnter","onMouseLeave","onMouseMove","onMouseOut","onMouseOver","onMouseUp","onNodeChange","onObjectResizeStart","onObjectResized","onObjectSelected","onPaste","onPostProcess","onPostRender","onPreProcess","onProgressState","onRedo","onRemove","onReset","onSaveContent","onSelectionChange","onSetAttrib","onSetContent","onShow","onSubmit","onUndo","onVisualAid"],a=function(n){return-1!==r.map((function(n){return n.toLowerCase()})).indexOf(n.toLowerCase())},s=function(n,t,e){Object.keys(t).filter(a).forEach((function(i){var o=t[i];"function"===typeof o&&("onInit"===i?o(n,e):e.on(i.substring(2),(function(n){return o(n,e)})))}))},c=function(n,t){var e=n.$props.modelEvents?n.$props.modelEvents:null,i=Array.isArray(e)?e.join(" "):e;t.on(i||"change input undo redo",(function(){n.$emit("input",t.getContent({format:n.$props.outputFormat}))}))},u=function(n,t,e){var i=t.$props.value?t.$props.value:"",o=t.$props.initialValue?t.$props.initialValue:"";e.setContent(i||(t.initialized?t.cache:o)),t.$watch("value",(function(n,i){e&&"string"===typeof n&&n!==i&&n!==e.getContent({format:t.$props.outputFormat})&&e.setContent(n)})),t.$listeners.input&&c(t,e),s(n,t.$listeners,e),t.initialized=!0},l=0,d=function(n){var t=Date.now(),e=Math.floor(1e9*Math.random());return l++,n+"_"+e+l+String(t)},p=function(n){return null!==n&&"textarea"===n.tagName.toLowerCase()},f=function(n){return"undefined"===typeof n||""===n?[]:Array.isArray(n)?n:n.split(" ")},h=function(n,t){return f(n).concat(f(t))},m=function(n){return null===n||void 0===n},g=function(){return{listeners:[],scriptId:d("tiny-script"),scriptLoaded:!1}},y=function(){var n=g(),t=function(n,t,e,i){var o=t.createElement("script");o.referrerPolicy="origin",o.type="application/javascript",o.id=n,o.src=e;var r=function(){o.removeEventListener("load",r),i()};o.addEventListener("load",r),t.head&&t.head.appendChild(o)},e=function(e,i,o){n.scriptLoaded?o():(n.listeners.push(o),e.getElementById(n.scriptId)||t(n.scriptId,e,i,(function(){n.listeners.forEach((function(n){return n()})),n.scriptLoaded=!0})))},i=function(){n=g()};return{load:e,reinitialize:i}},v=y(),b=e("c4a9"),$={apiKey:String,cloudChannel:String,id:String,init:Object,initialValue:String,inline:Boolean,modelEvents:[String,Array],plugins:[String,Array],tagName:String,toolbar:[String,Array],value:String,disabled:Boolean,tinymceScriptSrc:String,outputFormat:{type:String,validator:function(n){return"html"===n||"text"===n}}},C=function(){return C=Object.assign||function(n){for(var t,e=1,i=arguments.length;e<i;e++)for(var o in t=arguments[e],t)Object.prototype.hasOwnProperty.call(t,o)&&(n[o]=t[o]);return n},C.apply(this,arguments)},S=function(n,t,e){return n(e||"div",{attrs:{id:t}})},w=function(n,t){return n("textarea",{attrs:{id:t},style:{visibility:"hidden"}})},E=function(n){return function(){var t=C(C({},n.$props.init),{readonly:n.$props.disabled,selector:"#"+n.elementId,plugins:h(n.$props.init&&n.$props.init.plugins,n.$props.plugins),toolbar:n.$props.toolbar||n.$props.init&&n.$props.init.toolbar,inline:n.inlineEditor,setup:function(t){n.editor=t,t.on("init",(function(e){return u(e,n,t)})),n.$props.init&&"function"===typeof n.$props.init.setup&&n.$props.init.setup(t)}});p(n.element)&&(n.element.style.visibility="",n.element.style.display=""),Object(b["a"])().init(t)}},j={props:$,created:function(){this.elementId=this.$props.id||d("tiny-vue"),this.inlineEditor=this.$props.init&&this.$props.init.inline||this.$props.inline,this.initialized=!1},watch:{disabled:function(){this.editor.setMode(this.disabled?"readonly":"design")}},mounted:function(){if(this.element=this.$el,null!==Object(b["a"])())E(this)();else if(this.element&&this.element.ownerDocument){var n=this.$props.cloudChannel?this.$props.cloudChannel:"5",t=this.$props.apiKey?this.$props.apiKey:"no-api-key",e=m(this.$props.tinymceScriptSrc)?"https://cdn.tiny.cloud/1/"+t+"/tinymce/"+n+"/tinymce.min.js":this.$props.tinymceScriptSrc;v.load(this.element.ownerDocument,e,E(this))}},beforeDestroy:function(){null!==Object(b["a"])()&&Object(b["a"])().remove(this.editor)},deactivated:function(){var n;this.inlineEditor||(this.cache=this.editor.getContent(),null===(n=Object(b["a"])())||void 0===n||n.remove(this.editor))},activated:function(){!this.inlineEditor&&this.initialized&&E(this)()},render:function(n){return this.inlineEditor?S(n,this.elementId,this.$props.tagName):w(n,this.elementId)}},O=j,D={name:"tinymce-editor",components:{Editor:O},props:{value:{type:String,default:""}},data:function(){var n=this;return{init:{language_url:"./tinymce/zh_CN.js",language:"zh_CN",height:500,plugins:"lists image media table paste link searchreplace anchor code preview pagebreak importcss",toolbar:"undo redo searchreplace |  formatselect pagebreak | bold italic forecolor backcolor | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | lists link anchor image media table | removeformat code preview",toolbar_drawer:!1,image_advtab:!0,object_resizing:!1,paste_data_images:!0,content_css:"./tinymce/article.css",images_upload_handler:function(t,e,i){n.uploadFile(t.blob()).then((function(n){return e(n)})).catch((function(n){return i(n)}))}},myValue:this.value,uploading:!1,cosConfig:[]}},mounted:function(){tinymce.init({}),this.cosInit()},methods:{cosInit:function(){var n=this;this.$http({url:this.$http.adornUrl("/sys/oss/config"),method:"get",params:this.$http.adornParams()}).then((function(t){var e=t.data;e&&200===e.code?n.cosConfig=e.config:n.$message.error("请先配置云存储相关信息！")}))},onExecCommand:function(n){},uploadFile:function(n){var t=this;return this.uploading=!0,new Promise((function(e,i){var o=new FormData;o.append("file",n),t.$post("uploadcontroller/upload",{file:o,header:'enctype="multipart/form-data" '}).then((function(n){1===n.code?(t.$emit("uploaded",n.data),e(n.data)):(t.$message.error("文件上传失败："+n.data),i(n.data))})).catch((function(n){return i(n)}))}))}},watch:{value:function(n){this.myValue=n},myValue:function(n){this.$emit("input",n)}}},I=D,_=(e("1fab8"),e("0c7c")),k=Object(_["a"])(I,i,o,!1,null,null,null);t["default"]=k.exports},"96d7":function(n,t,e){},c4a9:function(n,t,e){"use strict";(function(n){e.d(t,"a",(function(){return o}));var i=function(){return"undefined"!==typeof window?window:n},o=function(){var n=i();return n&&n.tinymce?n.tinymce:null}}).call(this,e("c8ba"))}}]);