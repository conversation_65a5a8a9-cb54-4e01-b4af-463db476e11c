(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["retister"],{"0008":function(e,t,s){},"66e8":function(e,t,s){},"6de8":function(e,t,s){e.exports=s.p+"img/registerSubmit.76c731e8.png"},"78b4":function(e,t,s){"use strict";s("66e8")},"7ba8":function(e,t,s){"use strict";var o=function(){var e=this,t=e._self._c;return t("div",[t("el-dialog",{attrs:{visible:e.dialogVisible,"show-close":!1,"close-on-click-modal":!1,"close-on-press-escape":!1,title:"视频内容查看"},on:{"update:visible":function(t){e.dialogVisible=t}}},[t("div",{staticClass:"video-box"},[t("video-player",{ref:"videoPlayer",staticClass:"video-player vjs-custom-skin",attrs:{playsinline:!0,options:e.playerOptions},on:{play:function(t){return e.onPlayerPlay(t)},pause:function(t){return e.onPlayerPause(t)}}})],1),t("div",{staticStyle:{"text-align":"right","margin-top":"20px"}},[t("el-button",{attrs:{type:"danger"},on:{click:e.submitRole}},[e._v("关闭")])],1)])],1)},a=[],r={props:{videoUrl:{type:String,default:"https://www.runoob.com/try/demo_source/movie.mp4"},dialogVisible:{type:Boolean,default:!1}},data:function(){return{playerOptions:{playbackRates:[.5,1,1.5,2],autoplay:!1,muted:!1,loop:!1,preload:"auto",language:"zh-CN",aspectRatio:"16:9",fluid:!0,sources:[{type:"video/mp4",src:""}],poster:"",notSupportedMessage:"此视频暂无法播放，请稍后再试",controlBar:{timeDivider:!0,durationDisplay:!0,remainingTimeDisplay:!1,fullscreenToggle:!0}}}},computed:{},created:function(){},mounted:function(){this.$nextTick((function(){this.playerOptions={playbackRates:[.5,1,1.5,2],autoplay:!1,muted:!1,loop:!1,preload:"auto",language:"zh-CN",aspectRatio:"16:9",fluid:!0,sources:[{type:"video/mp4",src:this.videoUrl}],poster:"",notSupportedMessage:"此视频暂无法播放，请稍后再试",controlBar:{timeDivider:!0,durationDisplay:!0,remainingTimeDisplay:!1,fullscreenToggle:!0}}}))},watch:{dialogVisible:function(e){e?this.$nextTick((function(){this.playerOptions={playbackRates:[.5,1,1.5,2],autoplay:!1,muted:!1,loop:!1,preload:"auto",language:"zh-CN",aspectRatio:"16:9",fluid:!0,sources:[{type:"video/mp4",src:this.videoUrl}],poster:"",notSupportedMessage:"此视频暂无法播放，请稍后再试",controlBar:{timeDivider:!0,durationDisplay:!0,remainingTimeDisplay:!1,fullscreenToggle:!0}}})):this.$refs.videoPlayer.player.pause()}},methods:{onPlayerPlay:function(e){},onPlayerPause:function(e){},submitRole:function(){this.$emit("close-video-dialog")}},components:{}},i=r,l=(s("e392"),s("0c7c")),n=Object(l["a"])(i,o,a,!1,null,null,null);t["a"]=n.exports},"8af7":function(e,t,s){"use strict";s.r(t);var o=function(){var e=this,t=e._self._c;return t("div",{staticClass:"login-box"},[t("div",{staticClass:"login-form"},[t("p",{staticClass:"login-title"},[e._v("水站注册")]),t("p",{staticClass:"login-en"},[e._v("USER REGISTER")]),t("el-form",{ref:"saveFormElement",staticStyle:{"margin-top":"30px"},attrs:{rules:e.saveRules,model:e.saveForm,"label-width":"100px"}},[t("el-form-item",{attrs:{label:"手机号",prop:"phone"}},[t("el-input",{attrs:{placeholder:"请输入手机号码"},model:{value:e.saveForm.phone,callback:function(t){e.$set(e.saveForm,"phone",t)},expression:"saveForm.phone"}})],1),t("el-form-item",{staticClass:"img-code",attrs:{label:"图片验证码"}},[t("el-input",{model:{value:e.inputImgcode,callback:function(t){e.inputImgcode=t},expression:"inputImgcode"}},[t("template",{slot:"append"},[t("img",{staticStyle:{width:"100px",height:"30px",display:"block"},attrs:{src:e.imgcode,alt:""},on:{click:function(t){return t.stopPropagation(),e.requestImgCode.apply(null,arguments)}}})])],2),t("div",{staticStyle:{"text-align":"right",color:"#656565","font-size":"12px"}},[e._v("看不清？点击图片换一张")])],1),t("el-form-item",{staticClass:"sen-msg",attrs:{label:"验证码",prop:"code"}},[t("el-input",{attrs:{oninput:"value=value.replace(/[^\\d]/g,'')"},model:{value:e.saveForm.code,callback:function(t){e.$set(e.saveForm,"code",t)},expression:"saveForm.code"}},[t("template",{slot:"append"},[t("el-button",{staticClass:"codeClass",attrs:{type:"primary",loading:"发送验证码"!=e.saveForm.codeText},on:{click:e.sendCode}},[e._v(e._s(e.saveForm.codeText))])],1)],2)],1),t("el-form-item",{attrs:{label:"密码",prop:"password"}},[t("el-input",{attrs:{value:e.password1},on:{input:e.getPassword1}})],1),t("el-form-item",{attrs:{label:"确认密码",prop:"password1"}},[t("el-input",{attrs:{value:e.password2},on:{input:e.getPassword2}})],1),t("el-form-item",[t("el-button",{staticClass:"float-right",attrs:{type:"text"},on:{click:e.toLogin}},[e._v("已有账号，我要登录")])],1),t("el-form-item",[t("el-button",{attrs:{type:"primary",loading:e.saveForm.isSubmit},on:{click:e.saveSetSubmit}},[e._v("确 定")])],1)],1),t("div",{staticClass:"reg-agreement"},[e._v("注册代表同意"),t("el-button",{attrs:{type:"text"},on:{click:function(t){e.serve=!0}}},[e._v("水站买平台服务协议")]),e._v("、"),t("el-button",{attrs:{type:"text"},on:{click:function(t){e.privacy=!0}}},[e._v("水站买隐私政策")]),e._v("，并"),t("br"),e._v("授权使用您的账号信息（如昵称、头像、地址）以便您管理。")],1)],1),t("el-dialog",{attrs:{width:"70%",visible:e.serve},on:{"update:visible":function(t){e.serve=t}}},[t("serveHtml",{attrs:{id:"serveHtml"}})],1),t("el-dialog",{attrs:{width:"70%",visible:e.privacy},on:{"update:visible":function(t){e.privacy=t}}},[t("privacyHtml",{attrs:{id:"privacyHtml"}})],1)],1)},a=[],r=s("2355"),i=s("62f5"),l={components:{serveHtml:r["a"],privacyHtml:i["a"]},data:function(){var e=this,t=this,s=function(t,s,o){s?s!==e.saveForm.password?o(new Error("两次输入密码不一致!")):o():o(new Error("请再次输入密码!"))},o=function(e,s,o){if(s){var a=t.$util.checkPhone(s);a?o():o(new Error("请输入有效的手机号码"))}else o(new Error("请输入手机号"))};return{saveForm:{phone:"",code:"",codeText:"发送验证码",password:"",password1:"",isSubmit:!1},imgcode:"",inputImgcode:"",imgCodeKey:"",password1:"",password2:"",saveRules:{phone:[{validator:o,trigger:"change"}],code:[{required:!0,message:"验证码不能为空",trigger:"blur"}],password:[{required:!0,message:"密码不能为空",trigger:"blur"},{min:6,max:20,message:"密码长度在 6 到 20 个字符",trigger:"blur"}],password1:[{validator:s,trigger:"blur"}]},serve:!1,privacy:!1}},methods:{requestImgCode:function(){var e=this;this.$get("/szmb/code/getImgCode").then((function(t){1===t.code?(e.imgcode=t.data.data,e.imgCodeKey=t.data.imgCodeKey):e.$message.error(t.data)})).catch((function(e){console.log(e)}))},toLogin:function(){var e=this;e.$router.push({name:"login",params:{}})},sendCode:function(){var e=this;if(""!=e.inputImgcode){var t=60;e.saveForm.codeText=t+"s";var s="/szmcuercontroller/sendcode",o={mobilPhone:e.saveForm.phone,imgCode:e.inputImgcode,imgCodeKey:e.imgCodeKey};e.$post(s,o).then((function(s){console.log(s),1==s.code?(e.$message({message:"发送成功",type:"success"}),e.saveForm.dTime=setInterval((function(){t--,t<0?(e.saveForm.codeText="发送验证码",clearInterval(e.saveForm.dTime)):e.saveForm.codeText=t+"s"}),1e3)):(e.saveForm.codeText="发送验证码",e.$message.error(s.data))}))}else e.$message({message:"请输入图片验证码",type:"warning"})},getPassword1:function(e){var t=this;if(e.length>t.password1.length){e=e.replace(/[\u4E00-\u9FA5]/g,"");var s=e.substring(e.length-1);t.saveForm.password=t.saveForm.password+s,t.password1="●".repeat(e.length)}else t.saveForm.password="",t.password1="";console.log(t.saveForm.password,"ppp111")},getPassword2:function(e){var t=this;if(e.length>t.password2.length){e=e.replace(/[\u4E00-\u9FA5]/g,"");var s=e.substring(e.length-1);t.saveForm.password1=t.saveForm.password1+s,console.log(t.saveForm.password1,"ppp222"),t.password2="●".repeat(e.length)}else t.saveForm.password1="",t.password2=""},saveSetSubmit:function(){var e=this;console.log(e.saveForm),e.$refs["saveFormElement"].validate((function(t){if(!t)return!1;if(console.log("bbb"),""!=e.inputImgcode){e.saveForm.isSubmit=!0;var s="/szmb/szmbstorecontroller/register",o={phone:e.saveForm.phone,code:e.saveForm.code,password:e.saveForm.password};e.$post(s,o).then((function(t){console.log(t),e.saveForm.isSubmit=!1,1==t.code?(e.$message({message:"注册成功!",type:"success"}),e.Cookies.set("storeId",t.data,{expires:e.$expires}),e.Cookies.set("storeStatus","0",{expires:e.$expires}),e.$refs.saveFormElement.resetFields(),e.password1="",e.password2="",e.$router.push({name:"registerStore",params:{edit:0}})):e.$message.error(t.data)}))}else e.$message({message:"请输入图片验证码",type:"warning"})}))}},created:function(){},mounted:function(){this.requestImgCode()}},n=l,c=(s("da31"),s("0c7c")),d=Object(c["a"])(n,o,a,!1,null,"b5d28cb0",null);t["default"]=d.exports},b99d:function(e,t,s){"use strict";s.r(t);var o=function(){var e=this,t=e._self._c;return t("div",{staticClass:"DIV1"},[t("div",[e._m(0),t("div",{staticClass:"margin-top-30 flex"},[t("el-card",{staticClass:"box-card",staticStyle:{"min-width":"365px"}},[t("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[t("span",{staticClass:"bold"},[e._v("基础信息")])]),t("div",[t("el-form",{ref:"storeFormElement1",attrs:{rules:e.rules,"label-width":"100px","label-position":"left",model:e.storeForm}},[t("el-form-item",{attrs:{label:"水站名称",prop:"storeName"}},[t("el-input",{model:{value:e.storeForm.storeName,callback:function(t){e.$set(e.storeForm,"storeName",t)},expression:"storeForm.storeName"}})],1),t("el-form-item",{attrs:{label:"水站地址",prop:"address"}},[t("el-input",{ref:"mapInput",attrs:{placeholder:"请选择地址",focus:e.addressFocus,value:e.storeForm.address}},[t("template",{slot:"append"},[t("div",{on:{click:function(t){e.mapDialog=!0}}},[e._v("地图选址 "),t("i",{staticClass:"el-icon-arrow-right"})])])],2)],1),t("el-form-item",{attrs:{label:"经营人",prop:"userName"}},[t("el-input",{model:{value:e.storeForm.userName,callback:function(t){e.$set(e.storeForm,"userName",t)},expression:"storeForm.userName"}})],1),t("el-form-item",{attrs:{label:"手机号",prop:"userPhone"}},[t("el-input",{model:{value:e.storeForm.userPhone,callback:function(t){e.$set(e.storeForm,"userPhone",t)},expression:"storeForm.userPhone"}})],1),t("el-form-item",{attrs:{label:"固定电话",prop:"fixedPhone"}},[t("el-input",{attrs:{placeholder:"固定电话格式：区号-固定电话号码"},model:{value:e.storeForm.fixedPhone,callback:function(t){e.$set(e.storeForm,"fixedPhone",t)},expression:"storeForm.fixedPhone"}})],1),t("el-form-item",{staticClass:"line-heig",attrs:{prop:"jobRange"}},[t("span",{attrs:{slot:"label"},slot:"label"},[e._v("经营范围 "),t("br"),t("span",{staticClass:"color-red"},[e._v("(可自行修改)")])]),t("el-input",{attrs:{type:"textarea",maxlength:"40","show-word-limit":""},model:{value:e.storeForm.jobRange,callback:function(t){e.$set(e.storeForm,"jobRange",t)},expression:"storeForm.jobRange"}})],1),t("el-form-item",{attrs:{label:"配送范围",prop:"sendRange"}},[t("el-input",{on:{input:e.getSendRange},model:{value:e.storeForm.sendRange,callback:function(t){e.$set(e.storeForm,"sendRange",e._n(t))},expression:"storeForm.sendRange"}},[t("template",{slot:"append"},[e._v("公里")])],2)],1),t("el-form-item",{attrs:{label:"营业时间",prop:"time"}},[t("el-time-picker",{attrs:{"is-range":"","value-format":"HH:mm",format:"HH:mm","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",placeholder:"选择时间范围"},model:{value:e.storeForm.time,callback:function(t){e.$set(e.storeForm,"time",t)},expression:"storeForm.time"}})],1),t("el-form-item",{staticClass:"line-heig",attrs:{prop:"bankAdmin"}},[t("span",{attrs:{slot:"label"},slot:"label"},[e._v("银行账号 "),t("br"),t("span",{staticClass:"color-red"},[e._v("(可后期完善)")])]),t("el-input",{nativeOn:{keyup:function(t){return e.handleClick.apply(null,arguments)}},model:{value:e.storeForm.bankAdmin,callback:function(t){e.$set(e.storeForm,"bankAdmin",t)},expression:"storeForm.bankAdmin"}})],1),t("el-form-item",{staticClass:"line-heig",attrs:{prop:"bankName"}},[t("span",{attrs:{slot:"label"},slot:"label"},[e._v("开户行 "),t("br"),t("span",{staticClass:"color-red"},[e._v("(可后期完善)")])]),t("el-input",{model:{value:e.storeForm.bankName,callback:function(t){e.$set(e.storeForm,"bankName",t)},expression:"storeForm.bankName"}})],1)],1)],1)]),t("el-card",{staticClass:"box-card",staticStyle:{"min-width":"300px"}},[t("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[t("span",{staticClass:"bold"},[e._v("图片信息"),t("span",{staticClass:"font-size-14 color-red"},[e._v("（点击图片可修改）")])])]),t("div",[t("el-form",{ref:"storeFormElement2",attrs:{"label-position":"left","label-width":"100px",model:e.storeForm}},[t("el-form-item",{attrs:{label:"水站招牌"}},[t("el-upload",{attrs:{action:"https://szmsh.waterstation.com.cn/szm/uploadcontroller/upload",accept:".png,.jpg","list-type":"picture-card","show-file-list":!1,"on-success":e.uploadLogo}},[e.storeForm.logo?t("img",{staticClass:"avatar",attrs:{src:e.storeForm.logo}}):t("i",{staticClass:"el-icon-plus"})])],1),t("el-form-item",{staticClass:"line-heig"},[t("span",{attrs:{slot:"label"},slot:"label"},[e._v("营业执照 "),t("br"),t("span",{staticClass:"color-red"},[e._v("(可填可不填)")])]),t("el-upload",{attrs:{action:"https://szmsh.waterstation.com.cn/szm/uploadcontroller/upload",accept:".png,.jpg","list-type":"picture-card","show-file-list":!1,"on-success":e.uploadbusinessPic}},[e.storeForm.businessPic?t("img",{staticClass:"avatar",attrs:{src:e.storeForm.businessPic}}):t("i",{staticClass:"el-icon-plus"})])],1)],1)],1)]),t("el-card",{staticClass:"box-card",staticStyle:{"min-width":"365px"}},[t("div",{staticClass:"flex align-items-center justify-content-between",attrs:{slot:"header"},slot:"header"},[t("div",{staticClass:"bold"},[e._v("宣传资料")])]),t("div",[t("el-form",{ref:"storeFormElement3",attrs:{"label-position":"top",model:e.storeForm}},[t("el-form-item",[t("span",{attrs:{slot:"label"},slot:"label"},[e._v("水站宣传语 "),t("span",{staticClass:"color-red"},[e._v("(可自行修改)")])]),t("el-input",{attrs:{type:"textarea",maxlength:"20","show-word-limit":""},model:{value:e.storeForm.slogan,callback:function(t){e.$set(e.storeForm,"slogan",t)},expression:"storeForm.slogan"}})],1),t("el-form-item",[t("span",{attrs:{slot:"label"},slot:"label"},[e._v("水站宣传图 "),t("span",{staticClass:"color-red"},[e._v("(可填可不填)")])]),t("el-upload",{attrs:{action:"https://szmsh.waterstation.com.cn/szm/uploadcontroller/upload",accept:".jpg,.png","list-type":"picture-card","on-success":e.uploadDetailPic,"on-remove":e.removeDetailPic,multiple:!0,"on-exceed":e.uploadMore,limit:5}},[t("i",{staticClass:"el-icon-plus"})])],1),t("el-form-item",[t("span",{attrs:{slot:"label"},slot:"label"},[e._v("宣传视频 "),t("span",{staticClass:"color-red"},[e._v("(可填可不填)")])]),t("div",{staticStyle:{width:"100%",height:"160px"}},[t("div",{staticClass:"flex align-items-center"},[e.storeForm.video.length?t("el-button",{attrs:{size:"small",type:"primary",disabled:!0}},[e._v("点击上传视频")]):e._e(),e.storeForm.video.length?t("el-button",{attrs:{size:"small",type:"primary"},on:{click:function(t){e.videoPreview=!0}}},[e._v("查看已上传视频")]):e._e()],1),t("el-upload",{staticClass:"upload-demo",attrs:{accept:".mp4,.avi,.mov,.rmvb,.rm,.flv,.3gp","on-success":e.uploadVideo,"on-remove":e.removeVideo,"before-upload":e.checkVideoSize,action:"https://szmsh.waterstation.com.cn/szm/uploadcontroller/upload"}},[""==e.storeForm.video?t("el-button",{attrs:{size:"small",type:"primary"}},[e._v("点击上传视频")]):e._e(),t("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[e._v("视频大小不能超过10M")])],1)],1)])],1)],1),t("div",{staticClass:"flex align-items-center justify-content-center margin-top-10"},[t("img",{staticStyle:{width:"120px",height:"40px",cursor:"pointer"},attrs:{src:s("6de8"),alt:""},on:{click:e.formSubmit}})])])],1),t("el-dialog",{attrs:{width:"500px",title:"地图",visible:e.mapDialog},on:{"update:visible":function(t){e.mapDialog=t}}},[t("div",[t("iframe",{attrs:{id:"mapPage",width:"100%",height:"600px",frameborder:"0",src:"https://apis.map.qq.com/tools/locpicker?search=1&type=1&key=NM5BZ-UTYHP-PZ2DF-VESJ4-T6AQ3-7KFOH&referer=web&mapdraggable=0"}})]),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{attrs:{type:"primary"},on:{click:e.mapSure}},[e._v("确 定")])],1)]),t("videoDialog",{attrs:{dialogVisible:e.videoPreview,videoUrl:e.storeForm.video},on:{"close-video-dialog":function(t){e.videoPreview=!1}}})],1)])},a=[function(){var e=this,t=e._self._c;return t("div",{staticClass:"flex align-items-center"},[t("div",[t("p",{staticClass:"login-title",staticStyle:{color:"#fff"}},[e._v("注册水站")]),t("p",{staticClass:"login-en"},[e._v("REGISTER STORE")])])])}],r=s("7ba8"),i={props:{},data:function(){var e=this,t=function(t,s,o){if(s){var a=e.$util.checkPhone(s);a?o():o(new Error("请输入有效的手机号码"))}else o(new Error("请输入手机号码"))},s=function(e,t,s){var o=/^([0-9]{3,4}-)?[0-9]{7,8}$/;return""!=t?o.test(t)?s():s(new Error("请正确的输入固定电话")):s()};return{storeForm:{logo:"https://waterstation.com.cn/szm/upload/storeLogo.png",storeName:"",longitude:"",latitude:"",address:"",userName:"",userPhone:"",fixedPhone:"",jobRange:"桶装水、箱装水、饮料、饮水机、手压泵等",sendRange:"3",slogan:"及时配送、服务周到，欢迎购买~",time:["08:00:00","22:00:00"],bankAdmin:"",bankName:"",businessPic:"",storePic:"",detailPic:[],video:"",code:""},addressFocus:!1,rules:{logo:[{required:!0,message:"请上传 LOGO 图",trigger:"blur"}],storeName:[{required:!0,message:"请输入水站名称",trigger:"blur"}],address:[{required:!0,message:"请选择水站地址",trigger:"blur"}],userName:[{required:!0,message:"请填写经营人姓名",trigger:"blur"}],userPhone:[{required:!0,message:"请填写经营人手机号",trigger:"blur"},{validator:t,trigger:"change"}],fixedPhone:[{validator:s,trigger:"change"}],jobRange:[{required:!0,message:"请填写经营范围",trigger:"blur"}],sendRange:[{required:!0,message:"请填写配送距离",trigger:"blur"}],time:[{required:!0,message:"请选择营业时间",trigger:"blur"}]},mapDialog:!1,isSubmit:0,videoPreview:!1}},computed:{},created:function(){},mounted:function(){var e=this;window.addEventListener("message",(function(t){var s=t.data;s&&"locationPicker"==s.module&&(console.log("location",s),e.storeForm.latitude=s.latlng.lat,e.storeForm.longitude=s.latlng.lng,e.storeForm.address=s.poiaddress)}),!1)},watch:{},methods:{handleClick:function(){this.storeForm.bankAdmin=this.storeForm.bankAdmin.replace(/[^\w]/g,"")},uploadLogo:function(e,t,s){var o=this;console.log(e,"response"),console.log(t,"file"),console.log(s,"fileList"),o.storeForm.logo="https://waterstation.com.cn/szm"+t.response.data,console.log(o.storeForm.logo,"logo")},uploadbusinessPic:function(e,t,s){var o=this;o.storeForm.businessPic="https://waterstation.com.cn/szm"+t.response.data},uploadStorePic:function(e,t,s){var o=this;o.storeForm.storePic="https://waterstation.com.cn/szm"+t.response.data},uploadDetailPic:function(e,t,s){var o=this,a=s.map((function(e){return"https://waterstation.com.cn/szm"+e.response.data}));o.storeForm.detailPic=a,console.log(a,"arr")},removeDetailPic:function(e,t){var s=this,o=t.map((function(e){return"https://waterstation.com.cn/szm"+e.response.data}));s.storeForm.detailPic=o,console.log(o,"arr")},checkVideoSize:function(e){var t=this;if(console.log(e,"file,file"),e.size>********)return t.$message.error("视频大小不能超过10M!"),!1},uploadVideo:function(e,t,s){console.log(e,"response"),console.log(t,"file"),console.log(s,"fileList");var o=this;o.storeForm.video="https://waterstation.com.cn/szm"+t.response.data},removeVideo:function(e,t){var s=this;s.storeForm.video=""},getSendRange:function(e){console.log(e);var t=this;e=e.replace(/[^\d]/g,""),t.storeForm.sendRange=e,e>=10&&t.$confirm("您输入的配送范围过大，继续使用请点击确定按钮！?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){})).catch((function(){t.storeForm.sendRange=""}))},mapSure:function(){console.log("mapmap");var e=this;e.mapDialog=!1,e.$nextTick((function(){this.$refs.mapInput.$el.querySelector("input").focus()}))},formSubmit:function(){var e=this,t=this,s=t.storeForm;t.$refs.storeFormElement1.validate((function(o){if(!o)return!1;if(""!=s.businessPic||""!=s.logo){var a="/szmb/storeapplyfor/update",r={storeId:t.Cookies.get("storeId"),logoUrl:s.logo,name:s.storeName,address:s.address,latitude:s.latitude,longitude:s.longitude,user:s.userName,phone:s.userPhone,fixationphone:s.fixedPhone,tagline:s.slogan,storeInfo:"",code:s.code,jobRange:s.jobRange,sendRange:s.sendRange,time:s.time[0]+"-"+s.time[1],bankCard:s.bankAdmin,openingBank:s.bankName,businessPic:s.businessPic,shopPic:s.logo,videoUrl:s.video,picsUrl:s.detailPic,sign:"pc",header:"json"};t.$post(a,r).then((function(s){if(console.log(s),1==s.code){var o="/szmb/szmbstorecontroller/selectstoreid",a={storeId:t.Cookies.get("storeId")};t.$post(o,a).then((function(o){if(console.log(o),1==o.code){e.Cookies.set("userToken",s.data,{expires:t.$expires}),t.Cookies.set("storeLocal",o.data.storeLocal,{expires:t.$expires}),t.Cookies.set("storeStatus",o.data.storeStatus,{expires:t.$expires}),t.Cookies.set("storeInfo",o.data.apply,{expires:t.$expires});var a=o.data.apply.jurisdiction,r={};a.forEach((function(e){"设置优惠价格"==e.moduleName&&(r.yhjg=e.state),"组合套餐"==e.moduleName&&(r.zhtc=e.state),"派单管理"==e.moduleName&&(r.pdgl=e.state),"库存盘点"==e.moduleName&&(r.kcpd=e.state),"消费提醒"==e.moduleName&&(r.xftx=e.state),"收支简表"==e.moduleName&&(r.szjb=e.state),"月付"==e.moduleName&&(r.yf=e.state)})),t.Cookies.set("moduleList",r,{expires:t.$expires}),t.$message({message:"水站信息保存成功!",type:"success"}),t.storeForm={logo:"",storeName:"",longitude:"",latitude:"",address:"",detailAddress:"",userName:"",userPhone:"",jobRange:"",sendRange:"",time:["08:00:00","22:00:00"],businessPic:"",storePic:"",detailPic:[],video:"",code:""},e.$router.push({name:"home",params:{from:"register"}})}else t.$message.error(s.data)}))}else t.$message.error(s.data)}))}else t.$message.error("营业执照和水站招牌至少传其一")}))},uploadMore:function(e,t){var s=this;s.$message.error("水站宣传图最多只能上传5张")}},components:{videoDialog:r["a"]}},l=i,n=(s("78b4"),s("0c7c")),c=Object(n["a"])(l,o,a,!1,null,"3edf2232",null);t["default"]=c.exports},da31:function(e,t,s){"use strict";s("0008")},e234:function(e,t,s){},e392:function(e,t,s){"use strict";s("e234")}}]);