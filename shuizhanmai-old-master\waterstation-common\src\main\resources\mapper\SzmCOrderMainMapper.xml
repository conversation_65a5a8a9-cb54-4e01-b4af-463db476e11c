<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.waterstationbuyproducer.dao.SzmCOrderMainMapper">
    <resultMap id="BaseResultMap" type="com.example.waterstationbuyproducer.entity.SzmCOrderMain">
        <id column="order_main_id" jdbcType="BIGINT" property="orderMainId"/>
        <result column="order_num" jdbcType="VARCHAR" property="orderNum"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_iden" jdbcType="VARCHAR" property="createIden"/>
        <result column="pay_time" jdbcType="TIMESTAMP" property="payTime"/>
        <result column="user_name" jdbcType="VARCHAR" property="userName"/>
        <result column="user_address" jdbcType="VARCHAR" property="userAddress"/>
        <result column="user_phone" jdbcType="VARCHAR" property="userPhone"/>
        <result column="delivery_info_id" jdbcType="BIGINT" property="deliveryInfoId"/>
        <result column="payment_mode_id" jdbcType="BIGINT" property="paymentModeId"/>
        <result column="income_addr_id" jdbcType="BIGINT" property="incomeAddrId"/>
        <result column="freight_payable" jdbcType="DOUBLE" property="freightPayable"/>
        <result column="order_money" jdbcType="DOUBLE" property="orderMoney"/>
        <result column="order_discounts" jdbcType="DOUBLE" property="orderDiscounts"/>
        <result column="is_return" jdbcType="INTEGER" property="isReturn"/>
        <result column="is_sms" jdbcType="INTEGER" property="isSms"/>
        <result column="distribution_date" jdbcType="TIMESTAMP" property="distributionDate"/>
        <result column="distribution_time" jdbcType="TIMESTAMP" property="distributionTime"/>
        <result column="is_replenishment" jdbcType="INTEGER" property="isReplenishment"/>
        <result column="pay_num" jdbcType="VARCHAR" property="payNum"/>
        <result column="order_status" jdbcType="INTEGER" property="orderStatus"/>
        <result column="user_content" jdbcType="VARCHAR" property="userContent"/>
        <result column="order_del_state" jdbcType="INTEGER" property="orderDelState"/>
        <result column="r_1" jdbcType="VARCHAR" property="r1"/>
        <result column="r_2" jdbcType="VARCHAR" property="r2"/>
        <result column="r_3" jdbcType="VARCHAR" property="r3"/>
        <result column="r_4" jdbcType="VARCHAR" property="r4"/>
        <result column="r_5" jdbcType="VARCHAR" property="r5"/>
        <result column="remind" jdbcType="INTEGER" property="remind"/>
        <result column="royalty" jdbcType="DOUBLE" property="royalty"/>
        <result column="up_price" jdbcType="DOUBLE" property="upPrice"/>
        <result column="send_state" jdbcType="INTEGER" property="sendState"/>
        <result column="include_dpt" jdbcType="INTEGER" property="includeDpt"/>
        <result column="is_forward" jdbcType="INTEGER" property="isForward"/>
        <result column="forward_company_id" jdbcType="BIGINT" property="forwardCompanyId"/>
        <result column="company_name" jdbcType="VARCHAR" property="companyName"/>
        <result column="c_d_money" jdbcType="DOUBLE" property="cdMoney"/>
        <result column="c_d_money_type" jdbcType="INTEGER" property="cdMoneyType"/>
        <result column="c_d_type_money" jdbcType="DOUBLE" property="cdTypeMoney"/>
        <result column="order_returns_id" jdbcType="BIGINT" property="orderReturnsId"/>
        <result column="is_invoice" jdbcType="INTEGER" property="isInvoice"/>
        <result column="bucket_price" jdbcType="DOUBLE" property="bucketPrice"/>
        <result column="bucket_beans" jdbcType="LONGVARCHAR" property="bucketBeans"/>
        <result column="bucket_pay_type" jdbcType="INTEGER" property="bucketPayType"/>
        <result column="is_bank_affirm" jdbcType="INTEGER" property="isBankAffirm"/>
        <result column="yf_money" jdbcType="DOUBLE" property="yfMoney"/>
        <result column="group" jdbcType="INTEGER" property="group"/>
        <result column="daike" jdbcType="INTEGER" property="daike"/>
        <result column="back" jdbcType="INTEGER" property="back"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="finish_time" jdbcType="TIMESTAMP" property="finishTime"/>
        <result column="cuidan" jdbcType="INTEGER" property="cuidan"/>
        <result column="store_id" jdbcType="BIGINT" property="storeId"/>
        <result column="ticket_user_id" jdbcType="BIGINT" property="ticketUserId"/>
        <result column="ticket_price" jdbcType="DECIMAL" property="ticketPrice"/>
        <result column="yunfei" jdbcType="DECIMAL" property="yunfei"/>
        <result column="bank_request_num" jdbcType="VARCHAR" property="bankRequestNum"/>
        <result column="bank_out_trade_num" jdbcType="VARCHAR" property="bankOutTradeNum"/>
        <result column="connectstoreid" jdbcType="BIGINT" property="connectstoreid"/>
        <result column="connectordernum" jdbcType="VARCHAR" property="connectordernum"/>
        <result column="shipsn" jdbcType="VARCHAR" property="shipsn"/>
        <result column="shipchannel" jdbcType="VARCHAR" property="shipchannel"/>
        <result column="lianying" jdbcType="INTEGER" property="lianying"/>
        <result column="lianyingprice" jdbcType="DOUBLE" property="lianyingprice"/>
        <result column="drainageuserid" jdbcType="BIGINT" property="drainageuserid"/>
        <result column="drainageid" jdbcType="BIGINT" property="drainageid"/>
        <result column="ziti" jdbcType="INTEGER" property="ziti"/>
        <result column="dikou" jdbcType="DECIMAL" property="dikou"/>
        <result column="ordersource" jdbcType="INTEGER" property="ordersource"/>
        <result column="appkey" jdbcType="VARCHAR" property="appkey"/>
        <result column="apptoken" jdbcType="VARCHAR" property="apptoken"/>
        <result column="picurl" jdbcType="VARCHAR" property="picurl"/>
        <result column="lon" jdbcType="DECIMAL" property="lon"/>
        <result column="lat" jdbcType="DECIMAL" property="lat"/>
        <result column="yuyuetime" jdbcType="TIMESTAMP" property="yuyuetime"/>
        <result column="exception" jdbcType="VARCHAR" property="exception"/>
        <result column="isprint" jdbcType="INTEGER" property="isprint"/>
        <result column="transtatus" jdbcType="INTEGER" property="transtatus"/>
        <result column="oldstoreid" jdbcType="BIGINT" property="oldstoreid"/>
        <result column="tranprice" jdbcType="DECIMAL" property="tranprice"/>
        <result column="oldprice" jdbcType="DECIMAL" property="oldprice"/>
        <result column="platformprice" jdbcType="DECIMAL" property="platformprice"/>
        <result column="distance" jdbcType="DECIMAL" property="distance"/>
        <result column="mark" jdbcType="VARCHAR" property="mark"/>
    </resultMap>
    <sql id="SqlList">
        order_main_id, order_num,create_time,
      update_time, create_iden, pay_time,
      user_name, user_address, user_phone,
      delivery_info_id, payment_mode_id, income_addr_id,
      freight_payable, order_money, order_discounts,
      is_return, is_sms, distribution_date,
      distribution_time, is_replenishment, pay_num,
      order_status, user_content, order_del_state,
      r_1, r_2, r_3, r_4,
      r_5,remind,royalty,up_price,send_state,include_dpt,is_forward,forward_company_id,company_name,c_d_money,
      c_d_money_type,c_d_type_money,is_invoice,bucket_price,bucket_beans,bucket_pay_type,is_bank_affirm,yf_money,zuobiao,
    `group`,back,daike,user_id,finish_time,cuidan,store_id,ticket_user_id
                     ,ticket_price
                     ,yunfei
                     ,bank_request_num
                     ,bank_out_trade_num
                     ,connectstoreid
                     ,connectordernum
                     ,shipchannel
                     ,shipsn
                     ,lianying
                     ,lianyingprice
                     ,drainageuserid
                     ,drainageid
                     ,ziti
                     ,dikou
                     ,ordersource
                     ,appkey
                     ,apptoken
                     ,picurl
                     ,lon
                     ,lat
                     ,yuyuetime
                     ,exception
                     ,isprint
                     ,transtatus
                     ,oldstoreid
                     ,tranprice
                     ,oldprice
                     ,platformprice
                     ,mark
    </sql>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from szm_c_order_main
    where order_main_id = #{orderMainId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByOrderNum" parameterType="java.lang.String">
    delete from szm_c_order_main
    where order_num = #{orderNum}
  </delete>
    <insert id="insert" parameterType="com.example.waterstationbuyproducer.entity.SzmCOrderMain">
    insert into szm_c_order_main (order_main_id, order_num,create_time,
      update_time, create_iden, pay_time,
      user_name, user_address, user_phone,
      delivery_info_id, payment_mode_id, income_addr_id,
      freight_payable, order_money, order_discounts,
      is_return, is_sms, distribution_date,
      distribution_time, is_replenishment, pay_num,
      order_status, user_content, order_del_state,
      r_1, r_2, r_3, r_4,
      r_5,remind,royalty,up_price,send_state,include_dpt,is_forward,forward_company_id,company_name,c_d_money,
      c_d_money_type,c_d_type_money,is_invoice,bucket_price,bucket_beans,bucket_pay_type,is_bank_affirm,yf_money,back,daike,user_id,
                                  finish_time,cuidan,store_id,ticket_user_id
                                  ,ticket_price
                                  ,yunfei
                                  ,bank_request_num
                                  ,bank_out_trade_num
                                  ,connectstoreid
                                  ,connectordernum
                                  ,shipchannel
                                  ,shipsn
                                  ,lianying
                                  ,lianyingprice
                                  ,drainageuserid
                                  ,drainageid
                                  ,ziti
                                  ,dikou
                                  ,ordersource
                                  ,appkey
                                  ,apptoken
                                  ,picurl
                                  ,lon
                                  ,lat
                                  ,yuyuetime
                                  ,exception
                                  ,isprint
                                  ,transtatus
                                  ,oldstoreid
                                  ,tranprice
                                  ,oldprice
                                  ,platformprice
                                  ,mark
                                  )
    values (#{orderMainId,jdbcType=BIGINT}, #{orderNum,jdbcType=VARCHAR},
      #{updateTime,jdbcType=TIMESTAMP}, #{createIden,jdbcType=VARCHAR}, #{payTime,jdbcType=TIMESTAMP},
      #{userName,jdbcType=VARCHAR}, #{userAddress,jdbcType=VARCHAR}, #{userPhone,jdbcType=VARCHAR},
      #{deliveryInfoId,jdbcType=BIGINT}, #{paymentModeId,jdbcType=BIGINT}, #{incomeAddrId,jdbcType=BIGINT},
      #{freightPayable,jdbcType=DOUBLE}, #{orderMoney,jdbcType=DOUBLE}, #{orderDiscounts,jdbcType=DOUBLE},
      #{isReturn,jdbcType=INTEGER}, #{isSms,jdbcType=INTEGER}, #{distributionDate,jdbcType=TIMESTAMP},
      #{distributionTime,jdbcType=TIMESTAMP}, #{isReplenishment,jdbcType=INTEGER}, #{payNum,jdbcType=VARCHAR},
      #{orderStatus,jdbcType=INTEGER}, #{userContent,jdbcType=VARCHAR}, #{orderDelState,jdbcType=INTEGER},
      #{r1,jdbcType=VARCHAR}, #{r2,jdbcType=VARCHAR}, #{r3,jdbcType=VARCHAR}, #{r4,jdbcType=VARCHAR},
      #{r5,jdbcType=VARCHAR},#{remind,jdbcType=INTEGER},#{royalty,jdbcType=DOUBLE},#{upPrice,jdbcType=DOUBLE},#{sendState,jdbcType=INTEGER}
      ,#{includeDpt,jdbcType=INTEGER},#{isForward,jdbcType=INTEGER},#{forwardCompanyId,jdbcType=BIGINT},#{companyName,jdbcType=VARCHAR}
      ,#{cdMoney,jdbcType=DOUBLE},#{cdMoneyType,jdbcType=INTEGER},#{cdTypeMoney,jdbcType=DOUBLE},#{isInvoice,jdbcType=INTEGER},
      #{bucketPrice,jdbcType=DOUBLE},#{bucketBeans,jdbcType=LONGVARCHAR},#{bucketPayType,jdbcType=INTEGER},#{isBankAffirm,jdbcType=INTEGER},
      #{yfMoney,jdbcType=DOUBLE},#{back},#{daike},#{userId},#{finishTime},#{cuidan},#{storeId},#{ticketUserId}
      ,#{ticketPrice}
      ,#{yunfei}
      ,#{bankRequestNum}
      ,#{bankOutTradeNum}
      ,#{connectstoreid}
      ,#{connectordernum}
      ,#{shipchannel}
      ,#{shipsn}
      ,#{lianying}
      ,#{lianyingprice}
      ,#{drainageuserid}
      ,#{drainageid}
      ,#{ziti}
      ,#{dikou}
      ,#{ordersource}
      ,#{appkey}
      ,#{apptoken}
      ,#{picurl}
      ,#{lon}
      ,#{lat}
      ,#{yuyuetime}
      ,#{exception}
      ,#{isprint}
      ,#{transtatus}
      ,#{oldstoreid}
      ,#{tranprice}
      ,#{oldprice}
      ,#{platformprice}
      ,#{mark}
      )
  </insert>
    <insert id="insertCancelOrder" useGeneratedKeys="true" keyProperty="orderMainId" parameterType="com.example.waterstationbuyproducer.entity.SzmCOrderMain">
        <selectKey keyProperty="orderMainId" order="AFTER" resultType="java.lang.Long">
            SELECT LAST_INSERT_ID()
        </selectKey>
     insert into szm_c_order_main (order_main_id, order_num,create_time,
      update_time, create_iden, pay_time,
      user_name, user_address, user_phone,
      delivery_info_id, payment_mode_id, income_addr_id,
      freight_payable, order_money, order_discounts,
      is_return, is_sms, distribution_date,
      distribution_time, is_replenishment, pay_num,
      order_status, user_content, order_del_state,
      r_1, r_2, r_3, r_4,
      r_5,remind,royalty,up_price,send_state,include_dpt,is_forward,forward_company_id,company_name,c_d_money,
      c_d_money_type,c_d_type_money,is_invoice,bucket_price,bucket_beans,bucket_pay_type,is_bank_affirm,yf_money,zuobiao,back,daike,user_id
      ,finish_time
      ,cuidan
      ,store_id,ticket_user_id
      ,ticket_price
      ,yunfei
         ,bank_request_num
         ,bank_out_trade_num
         ,connectstoreid
         ,connectordernum
         ,shipsn
         ,shipchannel
         ,lianying
         ,lianyingprice
         ,drainageuserid
         ,drainageid
         ,ziti
         ,dikou
         ,ordersource
         ,apptoken
         ,picurl
         ,appkey
         ,lon
         ,lat
         ,yuyuetime
         ,exception
         ,isprint
         ,transtatus
         ,oldstoreid
         ,tranprice
         ,oldprice
         ,platformprice
         ,mark
      )
    values (#{orderMainId,jdbcType=BIGINT}, #{orderNum,jdbcType=VARCHAR},#{createTime,jdbcType=TIMESTAMP},
      #{updateTime,jdbcType=TIMESTAMP}, #{createIden,jdbcType=VARCHAR}, #{payTime,jdbcType=TIMESTAMP},
      #{userName,jdbcType=VARCHAR}, #{userAddress,jdbcType=VARCHAR}, #{userPhone,jdbcType=VARCHAR},
      #{deliveryInfoId,jdbcType=BIGINT}, #{paymentModeId,jdbcType=BIGINT}, #{incomeAddrId,jdbcType=BIGINT},
      #{freightPayable,jdbcType=DOUBLE}, #{orderMoney,jdbcType=DOUBLE}, #{orderDiscounts,jdbcType=DOUBLE},
      #{isReturn,jdbcType=INTEGER}, #{isSms,jdbcType=INTEGER}, #{distributionDate,jdbcType=TIMESTAMP},
      #{distributionTime,jdbcType=TIMESTAMP}, #{isReplenishment,jdbcType=INTEGER}, #{payNum,jdbcType=VARCHAR},
      #{orderStatus,jdbcType=INTEGER}, #{userContent,jdbcType=VARCHAR}, #{orderDelState,jdbcType=INTEGER},
      #{r1,jdbcType=VARCHAR}, #{r2,jdbcType=VARCHAR}, #{r3,jdbcType=VARCHAR}, #{r4,jdbcType=VARCHAR},
      #{r5,jdbcType=VARCHAR}, #{remind,jdbcType=INTEGER}, #{royalty,jdbcType=DOUBLE},#{upPrice,jdbcType=DOUBLE},#{sendState,jdbcType=INTEGER},
      #{includeDpt,jdbcType=INTEGER},#{isForward,jdbcType=INTEGER},#{forwardCompanyId,jdbcType=BIGINT},#{companyName,jdbcType=VARCHAR}
      ,#{cdMoney,jdbcType=DOUBLE},#{cdMoneyType,jdbcType=INTEGER},#{cdTypeMoney,jdbcType=DOUBLE},#{isInvoice,jdbcType=INTEGER},
      #{bucketPrice,jdbcType=DOUBLE},#{bucketBeans,jdbcType=LONGVARCHAR},#{bucketPayType,jdbcType=INTEGER},#{isBankAffirm,jdbcType=INTEGER},
      #{yfMoney,jdbcType=DOUBLE},#{zuobiao},#{back},#{daike},#{userId}
      ,#{finishTime}
      ,#{cuidan}
      ,#{storeId}
      ,#{ticketUserId}
      ,#{ticketPrice}
      ,#{yunfei}
       ,#{bankRequestNum}
       ,#{bankOutTradeNum}
       ,#{connectstoreid}
       ,#{connectordernum}
       ,#{shipsn}
       ,#{shipchannel}
       ,#{lianying}
       ,#{lianyingprice}
       ,#{drainageuserid}
       ,#{drainageid}
       ,#{ziti}
       ,#{dikou}
       ,#{ordersource}
       ,#{apptoken}
       ,#{picurl}
       ,#{appkey}
       ,#{lon}
       ,#{lat}
       ,#{yuyuetime}
       ,#{exception}
       ,#{isprint}
       ,#{transtatus}
       ,#{oldstoreid}
       ,#{tranprice}
       ,#{oldprice}
       ,#{platformprice}
       ,#{mark}
      )
  </insert>
    <update id="updateByPrimaryKey" parameterType="com.example.waterstationbuyproducer.entity.SzmCOrderMain">
    update szm_c_order_main
    set order_num = #{orderNum,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_iden = #{createIden,jdbcType=VARCHAR},
      pay_time = #{payTime,jdbcType=TIMESTAMP},
      user_name = #{userName,jdbcType=VARCHAR},
      user_address = #{userAddress,jdbcType=VARCHAR},
      user_phone = #{userPhone,jdbcType=VARCHAR},
      delivery_info_id = #{deliveryInfoId,jdbcType=BIGINT},
      payment_mode_id = #{paymentModeId,jdbcType=BIGINT},
      income_addr_id = #{incomeAddrId,jdbcType=BIGINT},
      freight_payable = #{freightPayable,jdbcType=DOUBLE},
      order_money = #{orderMoney,jdbcType=DOUBLE},
      order_discounts = #{orderDiscounts,jdbcType=DOUBLE},
      is_return = #{isReturn,jdbcType=INTEGER},
      is_sms = #{isSms,jdbcType=INTEGER},
      distribution_date = #{distributionDate,jdbcType=TIMESTAMP},
      distribution_time = #{distributionTime,jdbcType=TIMESTAMP},
      is_replenishment = #{isReplenishment,jdbcType=INTEGER},
      pay_num = #{payNum,jdbcType=VARCHAR},
      order_status = #{orderStatus,jdbcType=INTEGER},
      user_content = #{userContent,jdbcType=VARCHAR},
      order_del_state = #{orderDelState,jdbcType=INTEGER},
      r_1 = #{r1,jdbcType=VARCHAR},
      r_2 = #{r2,jdbcType=VARCHAR},
      r_3 = #{r3,jdbcType=VARCHAR},
      r_4 = #{r4,jdbcType=VARCHAR},
      r_5 = #{r5,jdbcType=VARCHAR},
      royalty = #{royalty,jdbcType=VARCHAR},
      up_price = #{upPrice,jdbcType=DOUBLE},
      send_state = #{sendState,jdbcType=INTEGER},
      include_dpt = #{includeDpt,jdbcType=INTEGER},
      is_forward = #{isForward,jdbcType=INTEGER},
      forward_company_id = #{forwardCompanyId,jdbcType=BIGINT},
      company_name = #{companyName,jdbcType=VARCHAR},
      c_d_money=#{cdMoney,jdbcType=DOUBLE},
      c_d_money_type=#{cdMoneyType,jdbcType=INTEGER},
      c_d_type_money=#{cdTypeMoney,jdbcType=DOUBLE},
      is_invoice = #{isInvoice,jdbcType=INTEGER},
      bucket_price = #{bucketPrice,jdbcType=DOUBLE},
      bucket_beans = #{bucketBeans,jdbcType=LONGVARCHAR},
      bucket_pay_type = #{bucketPayType,jdbcType=INTEGER},
      is_bank_affirm = #{isBankAffirm,jdbcType=INTEGER},
      back = #{back},
      daike = #{daike},
        user_id = #{userId},
        finish_time = #{finishTime},
        cuidan = #{cuidan},
        store_id = #{storeId},
        ticket_user_id = #{ticketUserId},
        ticket_price = #{ticketPrice},
        yunfei = #{yunfei},
        bank_request_num = #{bankRequestNum},
        bank_out_trade_num = #{bankOutTradeNum},
        connectstoreid = #{connectstoreid},
        connectordernum = #{connectordernum},
        shipchannel = #{shipchannel},
        shipsn = #{shipsn},
        lianying = #{lianying},
        lianyingprice = #{lianyingprice},
        drainageuserid = #{drainageuserid},
        drainageid = #{drainageid},
        ziti = #{ziti},
        dikou = #{dikou},
        ordersource = #{ordersource},
        appkey = #{appkey},
        apptoken = #{apptoken},
        picurl = #{picurl},
        lon = #{lon},
        lat = #{lat},
        yuyuetime = #{yuyuetime},
        exception = #{exception},
        isprint = #{isprint},
        transtatus = #{transtatus},
        oldstoreid = #{oldstoreid},
        tranprice = #{tranprice},
        oldprice = #{oldprice},
        platformprice = #{platformprice},
        mark = #{mark},
      yf_money = #{yfMoney,jdbcType=DOUBLE}
    where order_main_id = #{orderMainId,jdbcType=INTEGER}
  </update>
    <update id="updateUserinfoByPrimaryKey" parameterType="com.example.waterstationbuyproducer.entity.SzmCOrderMain">
    update szm_c_order_main
    set
      user_name = #{userName,jdbcType=VARCHAR},
      user_address = #{userAddress,jdbcType=VARCHAR},
      user_phone = #{userPhone,jdbcType=VARCHAR}
    where order_main_id = #{orderMainId,jdbcType=INTEGER}
  </update>
    <update id="updateByOrderNum" parameterType="com.example.waterstationbuyproducer.entity.SzmCOrderMain">
        update szm_c_order_main
        <trim prefix="set" suffixOverrides=",">
            <if test="createTime!=null">create_time = #{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime!=null">update_time = #{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="createIden!=null">create_iden = #{createIden,jdbcType=VARCHAR},</if>
            <if test="payTime!=null">pay_time = #{payTime,jdbcType=TIMESTAMP},</if>
            <if test="userName!=null">user_name = #{userName,jdbcType=VARCHAR},</if>
            <if test="userAddress!=null">user_address = #{userAddress,jdbcType=VARCHAR},</if>
            <if test="userPhone!=null">user_phone = #{userPhone,jdbcType=VARCHAR},</if>
            <if test="deliveryInfoId!=null">delivery_info_id = #{deliveryInfoId,jdbcType=BIGINT},</if>
            <if test="paymentModeId!=null">payment_mode_id = #{paymentModeId,jdbcType=BIGINT},</if>
            <if test="incomeAddrId!=null">income_addr_id = #{incomeAddrId,jdbcType=BIGINT},</if>
            <if test="freightPayable!=null">freight_payable = #{freightPayable,jdbcType=DOUBLE},</if>
            <if test="orderMoney!=null">order_money = #{orderMoney,jdbcType=DOUBLE},</if>
            <if test="orderDiscounts!=null">order_discounts = #{orderDiscounts,jdbcType=DOUBLE},</if>
            <if test="isReturn!=null">is_return = #{isReturn,jdbcType=INTEGER},</if>
            <if test="isSms!=null">is_sms = #{isSms,jdbcType=INTEGER},</if>
            <if test="distributionDate!=null">distribution_date = #{distributionDate,jdbcType=TIMESTAMP},</if>
            <if test="distributionTime!=null">distribution_time = #{distributionTime,jdbcType=TIMESTAMP},</if>
            <if test="isReplenishment!=null">is_replenishment = #{isReplenishment,jdbcType=INTEGER},</if>
            <if test="payNum!=null">pay_num = #{payNum,jdbcType=VARCHAR},</if>
            <if test="orderStatus!=null">order_status = #{orderStatus,jdbcType=INTEGER},</if>
            <if test="userContent!=null">user_content = #{userContent,jdbcType=VARCHAR},</if>
            <if test="orderDelState!=null">order_del_state = #{orderDelState,jdbcType=INTEGER},</if>
            <if test="r1!=null">r_1 = #{r1,jdbcType=VARCHAR},</if>
            <if test="r2!=null">r_2 = #{r2,jdbcType=VARCHAR},</if>
            <if test="r3!=null">r_3 = #{r3,jdbcType=VARCHAR},</if>
            <if test="r4!=null">r_4 = #{r4,jdbcType=VARCHAR},</if>
            <if test="r5!=null">r_5 = #{r5,jdbcType=VARCHAR},</if>
            <if test="royalty!=null">royalty = #{royalty,jdbcType=DOUBLE},</if>
            <if test="upPrice!=null">up_price = #{upPrice,jdbcType=DOUBLE},</if>
            <if test="sendState!=null">send_state = #{sendState,jdbcType=INTEGER},</if>
            <if test="includeDpt!=null">include_dpt = #{includeDpt,jdbcType=INTEGER},</if>
            <if test="isForward!=null">is_forward = #{isForward,jdbcType=INTEGER},</if>
            <if test="forwardCompanyId!=null">forward_company_id = #{forwardCompanyId,jdbcType=BIGINT},</if>
            <if test="companyName!=null">company_name = #{companyName,jdbcType=VARCHAR},</if>
            <if test="cdMoney!=null">c_d_money=#{cdMoney,jdbcType=DOUBLE},</if>
            <if test="cdMoneyType!=null">c_d_money_type=#{cdMoneyType,jdbcType=INTEGER},</if>
            <if test="cdTypeMoney!=null">c_d_type_money=#{cdTypeMoney,jdbcType=DOUBLE},</if>
            <if test="isInvoice!=null">is_invoice= #{isInvoice,jdbcType=INTEGER},</if>
            <if test="bucketPrice!=null">bucket_price = #{bucketPrice,jdbcType=DOUBLE},</if>
            <if test="bucketBeans!=null">bucket_beans = #{bucketBeans,jdbcType=LONGVARCHAR},</if>
            <if test="bucketPayType!=null">bucket_pay_type = #{bucketPayType,jdbcType=INTEGER},</if>
            <if test="isBankAffirm!=null">is_bank_affirm = #{isBankAffirm,jdbcType=INTEGER},</if>
            <if test="yfMoney!=null">yf_money = #{yfMoney,jdbcType=DOUBLE},</if>
            <if test="group!=null">`group` = #{group},</if>
            <if test="zuobiao!=null">zuobiao = #{zuobiao},</if>
            <if test="back!=null">back = #{back},</if>
            <if test="daike!=null">daike = #{daike},</if>
            <if test="userId!=null">user_id = #{userId},</if>
            <if test="finishTime!=null">finish_time = #{finishTime},</if>
            <if test="cuidan!=null">cuidan = #{cuidan},</if>
            <if test="storeId!=null">store_id = #{storeId},</if>
            <if test="ticketUserId!=null">ticket_user_id = #{ticketUserId},</if>
            <if test="ticketPrice!=null">ticket_price = #{ticketPrice},</if>
            <if test="yunfei!=null">yunfei = #{yunfei},</if>
            <if test="bankRequestNum!=null">bank_request_num = #{bankRequestNum},</if>
            <if test="bankOutTradeNum!=null">bank_out_trade_num = #{bankOutTradeNum},</if>
            <if test="connectstoreid!=null">connectstoreid = #{connectstoreid},</if>
            <if test="connectordernum!=null">connectordernum = #{connectordernum},</if>
            <if test="shipsn!=null">shipsn = #{shipsn},</if>
            <if test="shipchannel!=null">shipchannel = #{shipchannel},</if>
            <if test="lianying!=null">lianying = #{lianying},</if>
            <if test="lianyingprice!=null">lianyingprice = #{lianyingprice},</if>
            <if test="drainageuserid!=null">drainageuserid = #{drainageuserid},</if>
            <if test="drainageid!=null">drainageid = #{drainageid},</if>
            <if test="dikou!=null">dikou = #{dikou},</if>
            <if test="ordersource!=null">ordersource = #{ordersource},</if>
            <if test="appkey!=null">appkey = #{appkey},</if>
            <if test="apptoken!=null">apptoken = #{apptoken},</if>
            <if test="picurl!=null">picurl = #{picurl},</if>
            <if test="lon!=null">lon = #{lon},</if>
            <if test="lat!=null">lat = #{lat},</if>
            <if test="yuyuetime!=null">yuyuetime = #{yuyuetime},</if>
            <if test="exception!=null">exception = #{exception},</if>
            <if test="isprint!=null">isprint = #{isprint},</if>
            <if test="transtatus!=null">transtatus = #{transtatus},</if>
            <if test="oldstoreid!=null">oldstoreid = #{oldstoreid},</if>
            <if test="tranprice!=null">tranprice = #{tranprice},</if>
            <if test="oldprice!=null">oldprice = #{oldprice},</if>
            <if test="platformprice!=null">platformprice = #{platformprice},</if>
            <if test="mark!=null">mark = #{mark},</if>
            <if test="ziti!=null">ziti = #{ziti},</if>
        </trim>
        where order_num = #{orderNum}
    </update>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="SqlList"/>
        from szm_c_order_main
        where order_main_id = #{orderMainId,jdbcType=BIGINT}
    </select>

    <select id="selectByIds" resultMap="BaseResultMap">
        select
        <include refid="SqlList"/>
        from szm_c_order_main
        where order_main_id in
        <foreach item="id" collection="orderMainId" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectAllOrders" resultMap="BaseResultMap">
        select
        <include refid="SqlList"/>
        from szm_c_order_main
    </select>
    <select id="selectAll" resultMap="BaseResultMap">
        select
        <include refid="SqlList"/>
        from szm_c_order_main where 1=1
        <if test="userId != null">
            and create_iden = #{userId}
        </if>
        and order_del_state = 0 order by create_time DESC
    </select>
    <select id="selectAllByState" resultMap="BaseResultMap">
        select
        <include refid="SqlList"/>
        from szm_c_order_main where create_iden = #{userId} and order_del_state = 0
        <if test="state == 0">
            and order_status = 0
        </if>
        <if test="state == 1">
            and order_status >=1 and order_status &lt;= 4
        </if>
        <if test="state == 2">
            and order_status = 5
        </if>
        <if test="state == 3">
            and order_status = 8
        </if>
    </select>
    <select id="selectAllByName" resultMap="BaseResultMap">
      SELECT *
    FROM	szm_c_order_main
    INNER JOIN smz_c_group_order ON szm_c_order_main.order_num = smz_c_group_order.order_main_id
    INNER JOIN smz_c_order_details ON smz_c_order_details.order_main_id = szm_c_order_main.order_num
    INNER JOIN szm_c_shopgroup ON smz_c_group_order.shopgroup_id = szm_c_shopgroup.shopgroup_id
    WHERE	szm_c_shopgroup.shopgroup_name LIKE CONCAT('%',#{name},'%') AND smz_c_order_details.product_skuname LIKE CONCAT('%',#{name},'%')
    </select>
    <select id="selectByOrderNum" resultMap="BaseResultMap">
        select
        <include refid="SqlList"/>
        from szm_c_order_main
        where order_num = #{orderNum,jdbcType=VARCHAR}
    </select>
    <select id="selectByOrderNums" resultMap="BaseResultMap">
        select
        <include refid="SqlList"/>
        from szm_c_order_main
        where order_num in
        <foreach item="id" collection="orderNum" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="selectR5ByOrderNum" resultMap="BaseResultMap">
        select
          order_main_id, user_address,order_status,r_5
        from szm_c_order_main
        where order_num = #{orderNum,jdbcType=VARCHAR}
    </select>


    <select id="selectByOrderNum1" resultMap="BaseResultMap">
        select
        <include refid="SqlList"/>
        from szm_c_order_main
        where order_num = #{orderNum,jdbcType=VARCHAR}
    </select>

    <select id="selectMemByOrderNum" resultMap="BaseResultMap">
        select
          order_main_id, user_address,order_status,r_5
        from szm_c_order_main
        where order_num = #{orderNum,jdbcType=VARCHAR}
    </select>

    <select id="selectConut" resultType="java.lang.Long">
        select count(1) from szm_c_order_main where create_iden = #{userId} and order_del_state = 0
    </select>
    <select id="selectByStoreId" resultType="java.lang.Integer">
         select count(1) from szm_c_order_main where store_id = #{storeId} and order_status = 5
    </select>

    <select id="selectUntreated" resultMap="BaseResultMap">
        select
        <include refid="SqlList"/>
        from szm_c_order_main
        where store_id = #{storeId,jdbcType=VARCHAR} and (order_status = 1 or order_status = 2 or order_status = 5 or
        order_status = 9)
        order by create_time desc
    </select>

    <select id="selectUntreatedPC" resultMap="BaseResultMap">
        select order_main_id, order_num,ordermain.create_time,
        ordermain.update_time, create_iden, pay_time,
        ordermain.user_name, user_address, user_phone,
        delivery_info_id, payment_mode_id, income_addr_id,
        freight_payable, order_money, order_discounts,
        is_return, is_sms, distribution_date,
        distribution_time, is_replenishment, pay_num,
        order_status, user_content, order_del_state,
        ordermain.r_1, ordermain.r_2, ordermain.r_3, ordermain.r_4,
        ordermain.r_5,ordermain.bucket_price,remind,royalty,up_price,send_state,include_dpt,
        is_forward,forward_company_id,company_name,ordermain.bucket_beans,ordermain.bucket_pay_type,ordermain.is_bank_affirm,
        ordermain.yf_money,ordermain.cuidan,ordermain.store_id,ordermain.ticket_user_id,
        ordermain.ticket_price,
        ordermain.yunfei,
        ordermain.dikou,
        ordermain.ordersource,
        ordermain.apptoken,
        ordermain.picurl,
        ordermain.yuyuetime,
        ordermain.exception,
        ordermain.isprint,
        ordermain.transtatus,
        ordermain.oldstoreid,
        ordermain.tranprice,
        ordermain.oldprice,
        ordermain.platformprice,
        ordermain.mark,
        ordermain.lon,
        ordermain.lat,
        ordermain.appkey,
        ordermain.ziti,
        delivery.delivery_user_name,delivery.delivery_user_id,delivery.delivery_user_state
        from szm_c_order_main as ordermain
        LEFT JOIN smz_c_delivery_user as delivery ON ordermain.delivery_info_id = delivery.delivery_user_id
        where ordermain.store_id = #{storeId} and (order_status = 1 or order_status = 2 or order_status = 5 or
        order_status = 9)
        <if test="payMent!=''">
            and ordermain.payment_mode_id = #{payMent}
        </if>
        <if test="userType!=''">
            <if test="userType==1">
                and company_name is not null and company_name != ''
            </if>
            <if test="userType==0">
                and (company_name is null or company_name = '')
            </if>
        </if>
        <if test="deliveryName!=''">
            and delivery.delivery_user_name = #{deliveryName}
        </if>
        <if test="null!= startTime">
            and ordermain.create_time >= #{startTime}
        </if>
        <if test="null!= endTime">
            and ordermain.create_time &lt;= #{endTime}
        </if>
        order by ordermain.create_time desc
    </select>


    <select id="selectUntreatedCount" resultType="java.lang.Long">
        select
        count(1)
        from szm_c_order_main
        where store_id = #{storeId,jdbcType=VARCHAR} and order_status = 2
    </select>


    <select id="selectUntreated1" resultMap="BaseResultMap">
        select
        <include refid="SqlList"/>
        from szm_c_order_main
        where store_id = #{storeId,jdbcType=VARCHAR} and (order_status = 1 or order_status = 2 or order_status = 5 or
        order_status = 9) and is_forward = 1 and include_dpt = 1
        order by create_time desc
    </select>
    <select id="selectstoreOrder" resultMap="BaseResultMap" parameterType="com.example.waterstationbuyproducer.entity.SzmCOrderMain">
        select
           *
        from
          szm_c_order_main
        where 1 = 1
        <if test="orderStatus!=null">
            and order_status = #{orderStatus}
        </if>
        <if test="isReplenishment!=null">
            and is_replenishment = #{isReplenishment}
        </if>
        <if test="deliveryInfoId!=null">
            and delivery_info_id = #{deliveryInfoId}
        </if>
        <if test="orderDelState!=null">
            and order_del_state = #{orderDelState}
        </if>
        <if test="r2!=null">
            and store_id = #{r2}
        </if>
        <if test="group!=null">
            and `group` = #{group}
        </if>
        ORDER BY  user_address,create_time
    </select>

    <select id="selectState" resultMap="BaseResultMap">
        select
        <include refid="SqlList"/>
        from szm_c_order_main
        where store_id = #{storeId,jdbcType=VARCHAR} and order_status = #{orderStatus}
    </select>

    <update id="updateByOrderId" parameterType="com.example.waterstationbuyproducer.entity.SzmCOrderMain">
    update szm_c_order_main
    set order_status = #{orderStatus,jdbcType=INTEGER}
    where order_main_id = #{orderMainId,jdbcType=BIGINT}
  </update>

    <update id="updateGroupByOrderId" >
    update szm_c_order_main
    set `group` = #{group,jdbcType=INTEGER}
    where order_main_id = #{orderId,jdbcType=BIGINT}
  </update>

    <update id="updatePirurlByOrderId" >
    update szm_c_order_main
    set `picurl` = #{picurl}
    where order_main_id = #{orderId,jdbcType=BIGINT}
  </update>

    <update id="backByOrderId" >
    update szm_c_order_main
    set `delivery_info_id` = null,`order_status`= 2,back = 1 ,exception = #{reason}
    where order_main_id = #{orderId,jdbcType=BIGINT}
  </update>
    <update id="backAdminByOrderId" >
    update szm_c_order_main
    set `delivery_info_id` = null,`order_status`= 2,back = 1,exception = #{reason},r_2 = #{storeId},store_id = #{storeId}
    where order_main_id = #{orderId,jdbcType=BIGINT}
  </update>

    <update id="updateState" parameterType="com.example.waterstationbuyproducer.entity.SzmCOrderMain">
        update szm_c_order_main
        <trim prefix="set" suffixOverrides=",">
            <if test="orderStatus!=null">
                order_status = #{orderStatus,jdbcType=INTEGER},
            </if>
            <if test="state != null">
                is_replenishment = #{state,jdbcType=INTEGER},
            </if>
            <if test="total != null">
                royalty = #{total},
            </if>
        </trim>
        where order_main_id = #{orderId,jdbcType=BIGINT}
    </update>

    <select id="selectFinish" resultMap="BaseResultMap">
        select
        <include refid="SqlList"/>
        from szm_c_order_main
        where store_id = #{storeId,jdbcType=VARCHAR} and order_status = 10
        order by pay_time desc
    </select>

    <select id="selectFinishPC" resultMap="BaseResultMap">
        select order_main_id, order_num,ordermain.create_time,
        ordermain.update_time, create_iden, pay_time,
        ordermain.user_name, user_address, user_phone,
        delivery_info_id, payment_mode_id, income_addr_id,
        freight_payable, order_money, order_discounts,
        is_return, is_sms, distribution_date,
        distribution_time, is_replenishment, pay_num,
        order_status, user_content, order_del_state,
        ordermain.r_1, ordermain.r_2, ordermain.r_3, ordermain.r_4,
        ordermain.r_5,remind,royalty,up_price,send_state,include_dpt,
        is_forward,forward_company_id,company_name,ordermian.bucket_price,ordermain.bucket_beans,ordermain.bucket_pay_type,
        ordermain.is_bank_affirm,ordermain.yf_money,ordermain.cuidan,ordermain.store_id,ordermain.ticket_user_id,
        ordermain.ticket_price,
        ordermain.yunfei,
        ordermain.dikou,
        ordermain.ordersource,
        ordermain.apptoken,
        ordermain.picurl,
        ordermain.lon,
        ordermain.lat,
        ordermain.yuyuetime,
        ordermain.transtatus,
        ordermain.oldstoreid,
        ordermain.tranprice,
        ordermain.oldprice,
        ordermain.platformprice,
        ordermain.mark,
        ordermain.exception,
        ordermain.isprint,
        ordermain.appkey,
        ordermain.ziti,
        delivery.delivery_user_name,delivery.delivery_user_id,delivery.delivery_user_state
        from szm_c_order_main as ordermain
        LEFT JOIN smz_c_delivery_user as delivery ON ordermain.delivery_info_id = delivery.delivery_user_id
        where ordermain.store_id = #{storeId,jdbcType=VARCHAR} and order_status = 10
        <if test="payMent!=''">
            and ordermain.payment_mode_id = #{payMent}
        </if>
        <if test="userType!=''">
            <if test="userType==1">
                and company_name is not null and company_name != ''
            </if>
            <if test="userType==0">
                and (company_name is null or company_name = '')
            </if>
        </if>
        <if test="deliveryName!=''">
            and delivery.delivery_user_name = #{deliveryName}
        </if>
        <if test="null!= startTime">
            and ordermain.create_time >= #{startTime}
        </if>
        <if test="null!= endTime">
            and ordermain.create_time &lt;= #{endTime}
        </if>
        order by ordermain.create_time desc
    </select>
    <select id="selectFinish1" resultMap="BaseResultMap">
        select
        <include refid="SqlList"/>
        from szm_c_order_main
        where store_id = #{storeId,jdbcType=VARCHAR} and order_status = 10 and is_forward = 1
        order by create_time desc
    </select>

    <select id="selectOrderOne" resultMap="BaseResultMap">
        select
        <include refid="SqlList"/>
        from szm_c_order_main
        where order_main_id = #{orderId}
    </select>
    <select id="selectStoreBillAll" resultMap="BaseResultMap">
        select
        <include refid="SqlList"/>
        from szm_c_order_main
        where store_id = #{storeId} and is_return = 0
        and order_status = 10
        <if test="null != statrTime">
            and create_time >= #{statrTime}
        </if>
        <if test="null != endTime">
            and create_time &lt;= #{endTime}
        </if>
    </select>

    <select id="selectStoreBillCount" resultType="java.lang.Double">
        select sum(order_money)
        from szm_c_order_main
        where store_id = #{storeId} and is_return = 0 and (order_status != 0 or order_status != 6 or order_status = 7 or order_status = 8)
    </select>


    <select id="selectByOrderNumAndState" resultMap="BaseResultMap">
        select
        <include refid="SqlList"/>
        from szm_c_order_main
        where order_num = #{orderNum,jdbcType=VARCHAR}
    </select>
    <select id="selectStoreBillList" resultMap="BaseResultMap">
        select
        <include refid="SqlList"/>
        from szm_c_order_main
        where store_id = #{storeId} and is_return = 0 and (order_status == 5 or order_status == 9 or order_status = 10)
    </select>
    <select id="selectByStoreIdAndSengMessage" resultMap="BaseResultMap">
        select
        <include refid="SqlList"/>
        from szm_c_order_main
        where store_id = #{storeId} and delivery_info_id != 0
    </select>
    <select id="selectByStoreIdOrderTotal" resultType="java.lang.Integer">
        select count(1)
        from szm_c_order_main
        where store_id = #{storeId} and is_return = 0 and order_status != 0
    </select>

    <select id="selectStateAndIs" resultMap="BaseResultMap">
        select
        <include refid="SqlList"/>
        from szm_c_order_main
        where order_status = #{orderStatus,jdbcType=VARCHAR} and is_replenishment = #{state} and store_id = #{storeId}
    </select>

    <select id="selectDelivery" resultMap="BaseResultMap">
        select
        <include refid="SqlList"/>
        from szm_c_order_main
        where order_status = #{orderStatus,jdbcType=VARCHAR} and store_id = #{storeId} and (delivery_info_id is not null or
        is_replenishment != 0)
    </select>

    <select id="selectDeliveryPC" resultMap="BaseResultMap">
        select order_main_id, order_num,ordermain.create_time,
        ordermain.update_time, create_iden, pay_time,
        ordermain.user_name, user_address, user_phone,
        delivery_info_id, payment_mode_id, income_addr_id,
        freight_payable, order_money, order_discounts,
        is_return, is_sms, distribution_date,
        distribution_time, is_replenishment, pay_num,
        order_status, user_content, order_del_state,
        ordermain.r_1, ordermain.r_2, ordermain.r_3, ordermain.r_4,
        ordermain.r_5,remind,royalty,up_price,send_state,include_dpt,
        is_forward,forward_company_id,company_name,ordermain.bucket_price,ordermain.bucket_beans,ordermain.bucket_pay_type,
        ordermain.is_bank_affirm,ordermain.yf_money,ordermain.back,ordermain.daike,ordermain.cuidan,ordermain.user_id as user_id,
        ordermain.store_id,ordermain.ticket_user_id,
        ordermain.ticket_price,
        ordermain.yunfei,
        ordermain.dikou,
        ordermain.ordersource,
        ordermain.apptoken,
        ordermain.picurl,
        ordermain.lon,
        ordermain.lat,
        ordermain.yuyuetime,
        ordermain.transtatus,
        ordermain.oldstoreid,
        ordermain.tranprice,
        ordermain.oldprice,
        ordermain.platformprice,
        ordermain.mark,
        ordermain.exception,
        ordermain.isprint,
        ordermain.appkey,
        ordermain.ziti,
        delivery.delivery_user_name,delivery.delivery_user_id,delivery.delivery_user_state
        from szm_c_order_main as ordermain

        LEFT JOIN smz_c_delivery_user as delivery ON ordermain.delivery_info_id = delivery.delivery_user_id
        where ordermain.store_id = #{storeId} and
        ordermain.delivery_info_id is not null and ordermain.user_name is not null
        and  ordermain.order_status != 6 and ordermain.order_status != 7 and ordermain.order_status != 8
        <if test="orderStatus!=null">
            <choose>
                <when test="orderStatus==4">
                    and ordermain.order_status in (4,11)
                </when>
                <otherwise>
                    and ordermain.order_status = #{orderStatus,jdbcType=VARCHAR}
                </otherwise>
            </choose>
        </if>

        <if test="userName!=null">
            and ( ordermain.user_name like concat(concat("%",#{userName}),"%")
            or ordermain.user_phone like concat(concat("%",#{userName}),"%")
                    or ordermain.order_num like CONCAT("%",#{userName},"%"))
        </if>
        <if test="address!=null">
            and ordermain.user_address like concat(concat("%",#{address}),"%")
        </if>
        <if test="payMent!=''">
            and ordermain.payment_mode_id = #{payMent}
        </if>
        <if test="userType!=''">
            <if test="userType==1">
                and company_name is not null and company_name != ''
            </if>
            <if test="userType==0">
                and (company_name is null or company_name = '')
            </if>
        </if>
        <if test="deliveryName!=''">
            and delivery.delivery_user_name = #{deliveryName}
        </if>
        <if test="null!= startTime">
            and ordermain.create_time >= #{startTime}
        </if>
        <if test="null!= endTime">
            and ordermain.create_time &lt;= #{endTime}
        </if>
        <if test="null!= ordersource and ordersource != 100">
            and ordermain.ordersource = #{ordersource}
        </if>
        <if test="null!= ordersource and ordersource == 100">
            and (ordermain.ordersource is null or ordermain.ordersource = 0)
        </if>
        <if test="true == selfStatus">
            and ordermain.delivery_info_id = 0 and ordermain.order_status!=10
        </if>
        order by ordermain.order_main_id desc
    </select>
    <select id="selectDeliveryPCSelf" resultMap="BaseResultMap">
        select ordermain.order_main_id, ordermain.order_num,ordermain.create_time,
        ordermain.update_time, ordermain.create_iden, ordermain.pay_time,
        ordermain.user_name, ordermain.user_address, ordermain.user_phone,
        ordermain.delivery_info_id, ordermain.payment_mode_id, ordermain.income_addr_id,
        ordermain.freight_payable, ordermain.order_money, ordermain.order_discounts,
        ordermain.is_return, ordermain.is_sms, ordermain.distribution_date,
        ordermain.distribution_time, ordermain.is_replenishment, ordermain.pay_num,
        ordermain.order_status, ordermain.user_content, ordermain.order_del_state,
        ordermain.r_1, ordermain.r_2, ordermain.r_3, ordermain.r_4,
        ordermain.r_5,ordermain.remind,ordermain.royalty,ordermain.up_price,ordermain.send_state,ordermain.include_dpt,
        ordermain.is_forward,ordermain.forward_company_id,ordermain.company_name,ordermain.bucket_price,ordermain.bucket_beans,ordermain.bucket_pay_type,
        ordermain.is_bank_affirm,ordermain.yf_money,ordermain.back,ordermain.daike,ordermain.cuidan,ordermain.user_id as user_id,
        ordermain.store_id,ordermain.ticket_user_id,
        ordermain.ticket_price,
        ordermain.yunfei,
        ordermain.dikou,
        ordermain.ordersource,
        ordermain.apptoken,
        ordermain.picurl,
        ordermain.lon,
        ordermain.lat,
        ordermain.yuyuetime,
        ordermain.transtatus,
        ordermain.oldstoreid,
        ordermain.tranprice,
        ordermain.oldprice,
        ordermain.platformprice,
        ordermain.mark,
        ordermain.exception,
        ordermain.isprint,
        ordermain.appkey,
        ordermain.ziti,
        delivery.delivery_user_name,delivery.delivery_user_id,delivery.delivery_user_state
        from szm_c_order_main as ordermain

        LEFT JOIN smz_c_delivery_user as delivery ON ordermain.delivery_info_id = delivery.delivery_user_id
        inner JOIN smz_c_delivery_info as dd ON ordermain.order_main_id = dd.order_main_id
        where ordermain.store_id = #{storeId} and
        ordermain.delivery_info_id is not null and ordermain.user_name is not null
        and  ordermain.order_status != 6 and ordermain.order_status != 7 and ordermain.order_status != 8 and dd.delivery_info_state &lt; 3
        <if test="orderStatus!=null">
            <choose>
                <when test="orderStatus==4">
                    and ordermain.order_status in (4,11)
                </when>
                <otherwise>
                    and ordermain.order_status = #{orderStatus,jdbcType=VARCHAR}
                </otherwise>
            </choose>
        </if>

        <if test="userName!=null">
            and ( ordermain.user_name like concat(concat("%",#{userName}),"%")
            or ordermain.user_phone like concat(concat("%",#{userName}),"%")
                    or ordermain.user_address like concat(concat("%",#{userName}),"%")  or ordermain.order_num like CONCAT("%",#{userName},"%"))
        </if>
        <if test="payMent!=''">
            and ordermain.payment_mode_id = #{payMent}
        </if>
        <if test="userType!=''">
            <if test="userType==1">
                and ordermain.company_name is not null and ordermain.company_name != ''
            </if>
            <if test="userType==0">
                and (ordermain.company_name is null or ordermain.company_name = '')
            </if>
        </if>
        <if test="deliveryName!=''">
            and delivery.delivery_user_name = #{deliveryName}
        </if>
        <if test="null!= startTime">
            and ordermain.create_time >= #{startTime}
        </if>
        <if test="null!= endTime">
            and ordermain.create_time &lt;= #{endTime}
        </if>
        <if test="null!= ordersource and ordersource != 100">
            and ordermain.ordersource = #{ordersource}
        </if>
        <if test="null!= ordersource and ordersource == 100">
            and (ordermain.ordersource is null or ordermain.ordersource = 0)
        </if>
        <if test="true == selfStatus">
            and ordermain.delivery_info_id = 0 and ordermain.order_status!=10
        </if>
        order by ordermain.order_main_id desc
    </select>
    <select id="selectDeliveryPCSelfCount" resultType="java.lang.Integer">
        select 
        count(1)
        from szm_c_order_main as ordermain

        LEFT JOIN smz_c_delivery_user as delivery ON ordermain.delivery_info_id = delivery.delivery_user_id
        inner JOIN smz_c_delivery_info as dd ON ordermain.order_main_id = dd.order_main_id
        where ordermain.store_id = #{storeId} and
        ordermain.delivery_info_id is not null and ordermain.user_name is not null
        and  ordermain.order_status != 6 and ordermain.order_status != 7 and ordermain.order_status != 8 and dd.delivery_info_state &lt; 3
        <if test="orderStatus!=null">
            <choose>
                <when test="orderStatus==4">
                    and ordermain.order_status in (4,11)
                </when>
                <otherwise>
                    and ordermain.order_status = #{orderStatus,jdbcType=VARCHAR}
                </otherwise>
            </choose>
        </if>

        <if test="userName!=null">
            and ( ordermain.user_name like concat(concat("%",#{userName}),"%")
            or ordermain.user_phone like concat(concat("%",#{userName}),"%")
                    or ordermain.user_address like concat(concat("%",#{userName}),"%")  or ordermain.order_num like CONCAT("%",#{userName},"%"))
        </if>
        <if test="payMent!=''">
            and ordermain.payment_mode_id = #{payMent}
        </if>
        <if test="userType!=''">
            <if test="userType==1">
                and ordermain.company_name is not null and ordermain.company_name != ''
            </if>
            <if test="userType==0">
                and (ordermain.company_name is null or ordermain.company_name = '')
            </if>
        </if>
        <if test="deliveryName!=''">
            and delivery.delivery_user_name = #{deliveryName}
        </if>
        <if test="null!= startTime">
            and ordermain.create_time >= #{startTime}
        </if>
        <if test="null!= endTime">
            and ordermain.create_time &lt;= #{endTime}
        </if>
        <if test="null!= ordersource and ordersource != 100">
            and ordermain.ordersource = #{ordersource}
        </if>
        <if test="null!= ordersource and ordersource == 100">
            and (ordermain.ordersource is null or ordermain.ordersource = 0)
        </if>
        <if test="true == selfStatus">
            and ordermain.delivery_info_id = 0 and ordermain.order_status!=10
        </if>
        order by ordermain.order_main_id desc
    </select>

    <select id="selectDeliveryPCCount" resultType="java.lang.Integer">
        select count(1)
        from szm_c_order_main as ordermain
        LEFT JOIN smz_c_delivery_user as delivery ON ordermain.delivery_info_id = delivery.delivery_user_id
        where ordermain.order_status = #{orderStatus,jdbcType=VARCHAR} and ordermain.store_id = #{storeId} and
        ordermain.delivery_info_id is not null
        <if test="userName!=null">
            and ( ordermain.user_name like concat(concat("%",#{userName}),"%")
            or ordermain.user_phone like concat(concat("%",#{userName}),"%")
                    or ordermain.user_address like concat(concat("%",#{userName}),"%") or ordermain.order_num like CONCAT("%",#{userName},"%"))
        </if>
        <if test="payMent!=''">
            and ordermain.payment_mode_id = #{payMent}
        </if>
        <if test="userType!=''">
            <if test="userType==1">
                and company_name is not null and company_name != ''
            </if>
            <if test="userType==0">
                and (company_name is null or company_name = '')
            </if>
        </if>
        <if test="deliveryName!=''">
            and delivery.delivery_user_name = #{deliveryName}
        </if>
        <if test="null!= startTime">
            and ordermain.create_time >= #{startTime}
        </if>
        <if test="null!= endTime">
            and ordermain.create_time &lt;= #{endTime}
        </if>
        <if test="null!= ordersource and ordersource != 100">
            and ordermain.ordersource = #{ordersource}
        </if>
        <if test="null!= ordersource and ordersource == 100">
            and (ordermain.ordersource is null or ordermain.ordersource = 0)
        </if>
    </select>
    <select id="selectByStoreIdOrderDate" resultType="java.lang.Integer">
        select count(1)
        from szm_c_order_main
        where store_id = #{storeId} and order_status != 0 and pay_time BETWEEN #{staterTime} and #{endTime}
    </select>
    <select id="selectByStoreDateMoney" resultType="java.lang.Double">
        select sum(order_money)
        from szm_c_order_main
        where create_time >= #{staterTime} and create_time &lt; #{endTime} and store_id = #{storeId} and is_return = 0 and order_status = 10
    </select>
    <select id="selectByStoreOrderState" resultType="java.lang.Integer">
        select  DISTINCT order_status
        from szm_c_order_main
      where  create_time >= #{staterTime} and create_time &lt; #{endTime} and store_id = #{storeId}
    </select>
    <select id="selectByStoreOrderStateTotal" resultType="java.lang.Integer">
        select count(1)
        from szm_c_order_main
        where create_time >= #{staterTime} and create_time &lt; #{endTime} and store_id = #{storeId} and order_status = #{state}
    </select>
    <select id="selectByStoreOrderStateDome" resultType="java.lang.Integer">
        select count(1)
        from szm_c_order_main
        where create_time >= #{stateTime} and create_time &lt; #{endTime} and store_id = #{storeId} and (order_status = 10 or order_status = 5)
    </select>
    <select id="selectStoreByUserAndStoreIdAll" resultMap="BaseResultMap">
        select
        <include refid="SqlList"/>
        from szm_c_order_main where
        store_id = #{storeId}
        and user_id = #{userId}
        <if test="username != null and username != ''">
            and (user_name like CONCAT("%",#{username},"%") or user_phone like CONCAT("%",#{username},"%") or user_address like CONCAT("%",#{username},"%") or order_num like CONCAT("%",#{username},"%") )
        </if>
        <if test="addressId != null and addressId != ''">
            and income_addr_id = #{addressId}
        </if>
        <if test="startTime != null and startTime != ''">
            and create_time >= #{startTime} and create_time &lt; #{endTime}
        </if>
        order by
        order_main_id DESC

    </select>
    <select id="selectStoreByUserAndStoreIdAllUserIdCanNo" resultMap="BaseResultMap">
        
        select ordermain.order_main_id, ordermain.order_num,ordermain.create_time as create_time,
        ordermain.update_time, ordermain.create_iden, ordermain.pay_time,
        ordermain.user_name, ordermain.user_address, ordermain.user_phone,
        ordermain.delivery_info_id, ordermain.payment_mode_id, ordermain.income_addr_id,
        ordermain.freight_payable, ordermain.order_money, ordermain.order_discounts,
        ordermain.is_return, ordermain.is_sms, ordermain.distribution_date,
        ordermain.distribution_time, ordermain.is_replenishment, ordermain.pay_num,
        ordermain.order_status, ordermain.user_content, ordermain.order_del_state,
        ordermain.r_1, ordermain.r_2, ordermain.r_3, ordermain.r_4,ordermain.cuidan,
        ordermain.r_5,ordermain.remind,ordermain.royalty,ordermain.up_price,ordermain.send_state,ordermain.include_dpt,ordermain.back,ordermain.daike,
        ordermain.is_forward,ordermain.forward_company_id,ordermain.company_name,ordermain.bucket_price,ordermain.bucket_beans,
        ordermain.bucket_pay_type,ordermain.is_bank_affirm,ordermain.yf_money,
        ordermain.store_id,ordermain.ticket_user_id,
        ordermain.ticket_price,
        ordermain.yunfei,
        ordermain.drainageuserid,
        ordermain.drainageid,
        ordermain.dikou,
        ordermain.ordersource,
        ordermain.apptoken,
        ordermain.picurl,
        ordermain.lon,
        ordermain.lat,
        ordermain.yuyuetime,
        ordermain.transtatus,
        ordermain.oldstoreid,
        ordermain.tranprice,
        ordermain.oldprice,
        ordermain.platformprice,
        ordermain.mark,
        ordermain.exception,
        ordermain.isprint,
        ordermain.appkey,
        ordermain.user_id,
        ordermain.finish_time as finish_time,
        ordermain.ziti
        from szm_c_order_main ordermain 
        left join smz_c_delivery_info bb on bb.order_main_id = ordermain.order_main_id
        LEFT JOIN smz_c_order_details cc ON cc.orderid = ordermain.order_main_id 
         where
        ordermain.store_id = #{storeId}
        <if test="userId != null and userId != ''">
        and ordermain.user_id = #{userId}
        </if>
        <if test="orderStatus != null and orderStatus != ''">
            <if test="orderStatus == 10">
                and ordermain.order_status in (5,10)
            </if>
            <if test="orderStatus == 8">
                and ordermain.order_status in (6,7,8)
            </if>
            <if test="orderStatus == 3">
                and ordermain.order_status in (3,4) and bb.delivery_info_state = 0
            </if>
            <if test="orderStatus == 4">
                and ordermain.order_status in (3,4) and bb.delivery_info_state in (2,3)
            </if>
            <if test="orderStatus != 10 and orderStatus != 8 and orderStatus != 3 and orderStatus != 4">
            and ordermain.order_status = #{orderStatus}
            </if>
        </if>
        <if test="ordersource != null and ordersource != '' and ordersource != 100">
            and ordermain.ordersource = #{ordersource}
        </if>
        <if test="ordersource != null and ordersource != '' and ordersource == 100">
            and ordermain.ordersource is null
        </if>
        <if test="deliveryUserId != null">
            and ordermain.delivery_info_id = #{deliveryUserId}
        </if>
        <if test="username != null and username != ''">
            and (ordermain.user_name like CONCAT("%",#{username},"%") or 
            ordermain.user_phone like CONCAT("%",#{username},"%") or 
            ordermain.user_address like CONCAT("%",#{username},"%")  or
            cc.product_skuname LIKE CONCAT("%",#{username},"%")  or
             ordermain.order_num like CONCAT("%",#{username},"%"))
        </if>
        <if test="addressId != null and addressId != ''">
            and ordermain.income_addr_id = #{addressId}
        </if>
        <if test="startTime != null and startTime != ''">
            and ordermain.create_time >= #{startTime} and ordermain.create_time &lt; #{endTime}
        </if>
        <if test="startTimeFinish != null and startTimeFinish != ''">
            and ordermain.finish_time >= #{startTimeFinish} and ordermain.finish_time &lt; #{endTimeFinish}
        </if>
        GROUP BY
        ordermain.order_main_id 
        order by
        ordermain.order_main_id DESC

    </select>
    <select id="selectStoreByUserAndStoreIdAllStoreIdCanNo" resultMap="BaseResultMap">
        select
        <include refid="SqlList"/>
        from szm_c_order_main where
        
         user_id = #{userId}
        <if test="storeId != null and storeId != ''">
        and store_id = #{storeId}
        </if>
        <if test="ordersource != null and ordersource != '' and ordersource != 100">
        and ordersource = #{ordersource}
        </if>
        <if test="ordersource != null and ordersource != '' and ordersource == 100">
        and ordersource is null
        </if>
        <if test="deliveryUserId != null and deliveryUserId != ''">
            and delivery_info_id = #{deliveryUserId}
        </if>
        <if test="username != null and username != ''">
            and (user_name like CONCAT("%",#{username},"%") or user_phone like CONCAT("%",#{username},"%") or user_address like CONCAT("%",#{username},"%")  or order_num like CONCAT("%",#{username},"%"))
        </if>
        <if test="addressId != null and addressId != ''">
            and income_addr_id = #{addressId}
        </if>
        <if test="startTime != null and startTime != ''">
            and create_time >= #{startTime} and create_time &lt; #{endTime}
        </if>
        <if test="startTimeFinish != null and startTimeFinish != ''">
            and finish_time >= #{startTimeFinish} and finish_time &lt; #{endTimeFinish}
        </if>
        order by
        order_main_id DESC

    </select>
    <select id="selectByParams" parameterType="java.util.Map" resultMap="BaseResultMap">
        select
        <include refid="SqlList"/>
        from szm_c_order_main where
        1= 1
        <if test="ordersource != null and ordersource != ''">
        and ordersource = #{ordersource}
        </if>
        <if test="storeId != null and storeId != ''">
        and store_id = #{storeId}
        </if>

        <if test="userContent != null and userContent != ''">
        and user_content like CONCAT("%",#{userContent},"%")
        </if>

        <if test="userId != null and userId != ''">
        and user_id = #{userId}
        </if>
        <if test="appkey != null and appkey != ''">
        and appkey = #{appkey}
        </if>
        <if test="username != null and username != ''">
            and (user_name like CONCAT("%",#{username},"%") or user_phone like CONCAT("%",#{username},"%") or user_address like CONCAT("%",#{username},"%")  or order_num like CONCAT("%",#{username},"%"))
        </if>
        <if test="addressId != null and addressId != ''">
            and income_addr_id = #{addressId}
        </if>
        <if test="startTime != null and startTime != ''">
            and create_time >= #{startTime} and create_time &lt; #{endTime}
        </if>
        <if test="startTimeFinish != null and startTimeFinish != ''">
            and finish_time >= #{startTimeFinish} and finish_time &lt; #{endTimeFinish}
        </if>
        order by
        order_main_id DESC

    </select>
    <select id="selectPingtaiByParams" parameterType="java.util.Map" resultMap="BaseResultMap">
        select ordermain.order_main_id, ordermain.order_num,ordermain.create_time,
        ordermain.update_time, ordermain.create_iden, ordermain.pay_time,
        ordermain.user_name, ordermain.user_address, ordermain.user_phone,
        ordermain.delivery_info_id, ordermain.payment_mode_id, ordermain.income_addr_id,
        ordermain.freight_payable, ordermain.order_money, ordermain.order_discounts,
        ordermain.is_return, ordermain.is_sms, ordermain.distribution_date,
        ordermain.distribution_time, ordermain.is_replenishment, ordermain.pay_num,
        ordermain.order_status, ordermain.user_content, ordermain.order_del_state,
        ordermain.r_1, ordermain.r_2, ordermain.r_3, ordermain.r_4,ordermain.cuidan,
        ordermain.r_5,ordermain.remind,ordermain.royalty,ordermain.up_price,ordermain.send_state,ordermain.include_dpt,ordermain.back,ordermain.daike,
        ordermain.is_forward,ordermain.forward_company_id,ordermain.company_name,ordermain.bucket_price,ordermain.bucket_beans,
        ordermain.bucket_pay_type,ordermain.is_bank_affirm,ordermain.yf_money,
        ordermain.store_id,ordermain.ticket_user_id,
        ordermain.ticket_price,
        ordermain.yunfei,
        ordermain.drainageuserid,
        ordermain.drainageid,
        ordermain.dikou,
        ordermain.ordersource,
        ordermain.apptoken,
        ordermain.picurl,
        ordermain.lon,
        ordermain.lat,
        ordermain.yuyuetime,
        ordermain.transtatus,
        ordermain.oldstoreid,
        ordermain.tranprice,
        ordermain.oldprice,
        ordermain.platformprice,
        ordermain.mark,
        ordermain.exception,
        ordermain.isprint,
        ordermain.appkey,
        ordermain.finish_time,
        ordermain.shipsn,
        ordermain.shipchannel,
        ordermain.ziti
        from szm_c_order_main ordermain left join smz_c_delivery_info bb on bb.order_main_id = ordermain.order_main_id
        LEFT JOIN smz_c_order_details cc ON cc.orderid = ordermain.order_main_id 
        LEFT JOIN smz_c_order_returns dd ON dd.order_main_id = ordermain.order_main_id 
         where
        1= 1
        <if test="orderStatus != null and orderStatus != ''">
            <if test="orderStatus == 10">
                and ordermain.order_status in (5,10)
            </if>
            <if test="orderStatus == 8">
                and ordermain.order_status in (6,7,8) and dd.processstate = 0
            </if>
            <if test="orderStatus == 11">
                and ordermain.order_status in (6,7,8) and dd.processstate in (1,2)
            </if>
            <if test="orderStatus == 3">
                and ordermain.order_status in (3,4) and bb.delivery_info_state = 0
            </if>
            <if test="orderStatus == 4">
                and ordermain.order_status in (3,4) and bb.delivery_info_state in (2,3)
            </if>
            <if test="orderStatus != 10 and orderStatus != 8 and orderStatus != 3 and orderStatus != 4 and orderStatus != 11">
            and ordermain.order_status = #{orderStatus}
            </if>
        </if>
        <if test="ordersource != null and ordersource != ''">
        and ordermain.ordersource = #{ordersource}
        </if>
        <if test="ordersource == null or ordersource == ''">
        and ordermain.ordersource is not null and ordersource != 0
        </if>
        <if test="storeId != null and storeId != ''">
        and ordermain.store_id = #{storeId}
        </if>

        <if test="userContent != null and userContent != ''">
        and ordermain.user_content like CONCAT("%",#{userContent},"%")
        </if>

        <if test="userId != null and userId != ''">
        and ordermain.user_id = #{userId}
        </if>
        <if test="appkey != null and appkey != ''">
        and ordermain.appkey = #{appkey}
        </if>
        <if test="username != null and username != ''">
            and (ordermain.user_name like CONCAT("%",#{username},"%") or
             ordermain.user_phone like CONCAT("%",#{username},"%") or 
             ordermain.user_address like CONCAT("%",#{username},"%")  or 
             ordermain.order_num like CONCAT("%",#{username},"%") or
             cc.product_skuname LIKE CONCAT("%",#{username},"%"))
        </if>
        <if test="addressId != null and addressId != ''">
            and ordermain.income_addr_id = #{addressId}
        </if>
        <if test="startTime != null and startTime != ''">
            and ordermain.create_time >= #{startTime} and ordermain.create_time &lt; #{endTime}
        </if>
        <if test="startTimeFinish != null and startTimeFinish != ''">
            and ordermain.finish_time >= #{startTimeFinish} and ordermain.finish_time &lt; #{endTimeFinish}
        </if>

        <!-- 退款订单筛选 -->
        <if test="back != null and back != ''">
            and ordermain.back = #{back}
        </if>

        <if test="timeoutFilter != null and timeoutFilter != '' and timeoutType != null and timeoutType == 'undelivered'">
            <!-- 只统计未配送的订单（状态为0-2） -->
            <if test="timeoutFilter == '1h'">
                <!-- 1小时内 -->
                and ordermain.create_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
            </if>
            <if test="timeoutFilter == '2h'">
                <!-- 1-2小时 -->
                and ordermain.create_time &lt; DATE_SUB(NOW(), INTERVAL 1 HOUR)
                and ordermain.create_time >= DATE_SUB(NOW(), INTERVAL 2 HOUR)
            </if>
            <if test="timeoutFilter == '4h'">
                <!-- 2-4小时 -->
                and ordermain.create_time &lt; DATE_SUB(NOW(), INTERVAL 2 HOUR)
                and ordermain.create_time >= DATE_SUB(NOW(), INTERVAL 4 HOUR)
            </if>
            <if test="timeoutFilter == '8h'">
                <!-- 4-8小时 -->
                and ordermain.create_time &lt; DATE_SUB(NOW(), INTERVAL 4 HOUR)
                and ordermain.create_time >= DATE_SUB(NOW(), INTERVAL 8 HOUR)
            </if>
            <if test="timeoutFilter == '16h'">
                <!-- 8-16小时 -->
                and ordermain.create_time &lt; DATE_SUB(NOW(), INTERVAL 8 HOUR)
                and ordermain.create_time >= DATE_SUB(NOW(), INTERVAL 16 HOUR)
            </if>
            <if test="timeoutFilter == '24h'">
                <!-- 16-24小时 -->
                and ordermain.create_time &lt; DATE_SUB(NOW(), INTERVAL 16 HOUR)
                and ordermain.create_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
            </if>
            <if test="timeoutFilter == '48h'">
                <!-- 24-48小时 -->
                and ordermain.create_time &lt; DATE_SUB(NOW(), INTERVAL 24 HOUR)
                and ordermain.create_time >= DATE_SUB(NOW(), INTERVAL 48 HOUR)
            </if>
            <if test="timeoutFilter == '72h'">
                <!-- 48-72小时 -->
                and ordermain.create_time &lt; DATE_SUB(NOW(), INTERVAL 48 HOUR)
                and ordermain.create_time >= DATE_SUB(NOW(), INTERVAL 72 HOUR)
            </if>
            <if test="timeoutFilter == '72h+'">
                <!-- 72小时以上 -->
                and ordermain.create_time &lt; DATE_SUB(NOW(), INTERVAL 72 HOUR)
            </if>
        </if>
        <if test="timeoutFilter != null and timeoutFilter != '' and timeoutType != null and timeoutType == 'undelivered_timeout'">
            <!-- 只统计未配送的订单（状态为0-2） -->
            <if test="timeoutFilter == '1h'">
                <!-- 1小时内 -->
                and ordermain.create_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
            </if>
            <if test="timeoutFilter == '2h'">
                <!-- 1-2小时 -->
                and ordermain.create_time &lt; DATE_SUB(NOW(), INTERVAL 1 HOUR)
                and ordermain.create_time >= DATE_SUB(NOW(), INTERVAL 2 HOUR)
            </if>
            <if test="timeoutFilter == '4h'">
                <!-- 2-4小时 -->
                and ordermain.create_time &lt; DATE_SUB(NOW(), INTERVAL 2 HOUR)
                and ordermain.create_time >= DATE_SUB(NOW(), INTERVAL 4 HOUR)
            </if>
            <if test="timeoutFilter == '8h'">
                <!-- 4-8小时 -->
                and ordermain.create_time &lt; DATE_SUB(NOW(), INTERVAL 4 HOUR)
                and ordermain.create_time >= DATE_SUB(NOW(), INTERVAL 8 HOUR)
            </if>
            <if test="timeoutFilter == '16h'">
                <!-- 8-16小时 -->
                and ordermain.create_time &lt; DATE_SUB(NOW(), INTERVAL 8 HOUR)
                and ordermain.create_time >= DATE_SUB(NOW(), INTERVAL 16 HOUR)
            </if>
            <if test="timeoutFilter == '24h'">
                <!-- 16-24小时 -->
                and ordermain.create_time &lt; DATE_SUB(NOW(), INTERVAL 16 HOUR)
                and ordermain.create_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
            </if>
            <if test="timeoutFilter == '48h'">
                <!-- 24-48小时 -->
                and ordermain.create_time &lt; DATE_SUB(NOW(), INTERVAL 24 HOUR)
                and ordermain.create_time >= DATE_SUB(NOW(), INTERVAL 48 HOUR)
            </if>
            <if test="timeoutFilter == '72h'">
                <!-- 48-72小时 -->
                and ordermain.create_time &lt; DATE_SUB(NOW(), INTERVAL 48 HOUR)
                and ordermain.create_time >= DATE_SUB(NOW(), INTERVAL 72 HOUR)
            </if>
            <if test="timeoutFilter == '72h+'">
                <!-- 72小时以上 -->
                and ordermain.create_time &lt; DATE_SUB(NOW(), INTERVAL 72 HOUR)
            </if>
        </if>

        GROUP BY
        ordermain.order_main_id
        order by
        ordermain.order_main_id DESC
        <if test="pageNo != null and pageSize != null">
            LIMIT #{pageSize} OFFSET #{pageNo}
        </if>

    </select>

    <!-- 平台订单count查询 - 优化版本，使用EXISTS替代LEFT JOIN -->
    <select id="selectPingtaiCountByParams" parameterType="java.util.Map" resultType="integer">
        select count(1)
        from szm_c_order_main ordermain
         where
        1= 1
        <if test="orderStatus != null and orderStatus != ''">
            <if test="orderStatus == 10">
                and ordermain.order_status in (5,10)
            </if>
            <if test="orderStatus == 8">
                and ordermain.order_status in (6,7,8)
                and EXISTS (SELECT 1 FROM smz_c_order_returns dd WHERE dd.order_main_id = ordermain.order_main_id AND dd.processstate = 0)
            </if>
            <if test="orderStatus == 11">
                and ordermain.order_status in (6,7,8)
                and EXISTS (SELECT 1 FROM smz_c_order_returns dd WHERE dd.order_main_id = ordermain.order_main_id AND dd.processstate in (1,2))
            </if>
            <if test="orderStatus == 3">
                and ordermain.order_status in (3,4)
                and EXISTS (SELECT 1 FROM smz_c_delivery_info bb WHERE bb.order_main_id = ordermain.order_main_id AND bb.delivery_info_state = 0)
            </if>
            <if test="orderStatus == 4">
                and ordermain.order_status in (3,4)
                and EXISTS (SELECT 1 FROM smz_c_delivery_info bb WHERE bb.order_main_id = ordermain.order_main_id AND bb.delivery_info_state in (2,3))
            </if>
            <if test="orderStatus != 10 and orderStatus != 8 and orderStatus != 3 and orderStatus != 4 and orderStatus != 11">
            and ordermain.order_status = #{orderStatus}
            </if>
        </if>
        <if test="ordersource != null and ordersource != ''">
        and ordermain.ordersource = #{ordersource}
        </if>
        <if test="ordersource == null or ordersource == ''">
        and ordermain.ordersource is not null and ordersource != 0
        </if>
        <if test="storeId != null and storeId != ''">
        and ordermain.store_id = #{storeId}
        </if>

        <if test="userContent != null and userContent != ''">
        and ordermain.user_content like CONCAT("%",#{userContent},"%")
        </if>

        <if test="userId != null and userId != ''">
        and ordermain.user_id = #{userId}
        </if>
        <if test="appkey != null and appkey != ''">
        and ordermain.appkey = #{appkey}
        </if>
        <if test="username != null and username != ''">
            and (ordermain.user_name like CONCAT("%",#{username},"%") or
             ordermain.user_phone like CONCAT("%",#{username},"%") or
             ordermain.user_address like CONCAT("%",#{username},"%")  or
             ordermain.order_num like CONCAT("%",#{username},"%") or
             EXISTS (SELECT 1 FROM smz_c_order_details cc WHERE cc.orderid = ordermain.order_main_id AND cc.product_skuname LIKE CONCAT("%",#{username},"%")))
        </if>
        <if test="addressId != null and addressId != ''">
            and ordermain.income_addr_id = #{addressId}
        </if>
        <if test="startTime != null and startTime != ''">
            and ordermain.create_time >= #{startTime} and ordermain.create_time &lt; #{endTime}
        </if>
        <if test="startTimeFinish != null and startTimeFinish != ''">
            and ordermain.finish_time >= #{startTimeFinish} and ordermain.finish_time &lt; #{endTimeFinish}
        </if>

        <!-- 退款订单筛选 -->
        <if test="back != null and back != ''">
            and ordermain.back = #{back}
        </if>

        <if test="timeoutFilter != null and timeoutFilter != '' and timeoutType != null and timeoutType == 'undelivered'">
            <!-- 只统计未配送的订单（状态为0-2） -->
            <if test="timeoutFilter == '1h'">
                <!-- 1小时内 -->
                and ordermain.create_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
            </if>
            <if test="timeoutFilter == '2h'">
                <!-- 1-2小时 -->
                and ordermain.create_time &lt; DATE_SUB(NOW(), INTERVAL 1 HOUR)
                and ordermain.create_time >= DATE_SUB(NOW(), INTERVAL 2 HOUR)
            </if>
            <if test="timeoutFilter == '4h'">
                <!-- 2-4小时 -->
                and ordermain.create_time &lt; DATE_SUB(NOW(), INTERVAL 2 HOUR)
                and ordermain.create_time >= DATE_SUB(NOW(), INTERVAL 4 HOUR)
            </if>
            <if test="timeoutFilter == '8h'">
                <!-- 4-8小时 -->
                and ordermain.create_time &lt; DATE_SUB(NOW(), INTERVAL 4 HOUR)
                and ordermain.create_time >= DATE_SUB(NOW(), INTERVAL 8 HOUR)
            </if>
            <if test="timeoutFilter == '16h'">
                <!-- 8-16小时 -->
                and ordermain.create_time &lt; DATE_SUB(NOW(), INTERVAL 8 HOUR)
                and ordermain.create_time >= DATE_SUB(NOW(), INTERVAL 16 HOUR)
            </if>
            <if test="timeoutFilter == '24h'">
                <!-- 16-24小时 -->
                and ordermain.create_time &lt; DATE_SUB(NOW(), INTERVAL 16 HOUR)
                and ordermain.create_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
            </if>
            <if test="timeoutFilter == '48h'">
                <!-- 24-48小时 -->
                and ordermain.create_time &lt; DATE_SUB(NOW(), INTERVAL 24 HOUR)
                and ordermain.create_time >= DATE_SUB(NOW(), INTERVAL 48 HOUR)
            </if>
            <if test="timeoutFilter == '72h'">
                <!-- 48-72小时 -->
                and ordermain.create_time &lt; DATE_SUB(NOW(), INTERVAL 48 HOUR)
                and ordermain.create_time >= DATE_SUB(NOW(), INTERVAL 72 HOUR)
            </if>
            <if test="timeoutFilter == '72h+'">
                <!-- 72小时以上 -->
                and ordermain.create_time &lt; DATE_SUB(NOW(), INTERVAL 72 HOUR)
            </if>
        </if>
        <if test="timeoutFilter != null and timeoutFilter != '' and timeoutType != null and timeoutType == 'undelivered_timeout'">
            <!-- 只统计未配送的订单（状态为0-2） -->
            <if test="timeoutFilter == '1h'">
                <!-- 1小时内 -->
                and ordermain.create_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
            </if>
            <if test="timeoutFilter == '2h'">
                <!-- 1-2小时 -->
                and ordermain.create_time &lt; DATE_SUB(NOW(), INTERVAL 1 HOUR)
                and ordermain.create_time >= DATE_SUB(NOW(), INTERVAL 2 HOUR)
            </if>
            <if test="timeoutFilter == '4h'">
                <!-- 2-4小时 -->
                and ordermain.create_time &lt; DATE_SUB(NOW(), INTERVAL 2 HOUR)
                and ordermain.create_time >= DATE_SUB(NOW(), INTERVAL 4 HOUR)
            </if>
            <if test="timeoutFilter == '8h'">
                <!-- 4-8小时 -->
                and ordermain.create_time &lt; DATE_SUB(NOW(), INTERVAL 4 HOUR)
                and ordermain.create_time >= DATE_SUB(NOW(), INTERVAL 8 HOUR)
            </if>
            <if test="timeoutFilter == '16h'">
                <!-- 8-16小时 -->
                and ordermain.create_time &lt; DATE_SUB(NOW(), INTERVAL 8 HOUR)
                and ordermain.create_time >= DATE_SUB(NOW(), INTERVAL 16 HOUR)
            </if>
            <if test="timeoutFilter == '24h'">
                <!-- 16-24小时 -->
                and ordermain.create_time &lt; DATE_SUB(NOW(), INTERVAL 16 HOUR)
                and ordermain.create_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
            </if>
            <if test="timeoutFilter == '48h'">
                <!-- 24-48小时 -->
                and ordermain.create_time &lt; DATE_SUB(NOW(), INTERVAL 24 HOUR)
                and ordermain.create_time >= DATE_SUB(NOW(), INTERVAL 48 HOUR)
            </if>
            <if test="timeoutFilter == '72h'">
                <!-- 48-72小时 -->
                and ordermain.create_time &lt; DATE_SUB(NOW(), INTERVAL 48 HOUR)
                and ordermain.create_time >= DATE_SUB(NOW(), INTERVAL 72 HOUR)
            </if>
            <if test="timeoutFilter == '72h+'">
                <!-- 72小时以上 -->
                and ordermain.create_time &lt; DATE_SUB(NOW(), INTERVAL 72 HOUR)
            </if>
        </if>
    </select>

    <select id="selectAllByParams" parameterType="java.util.Map" resultMap="BaseResultMap">
        select ordermain.order_main_id, ordermain.order_num,ordermain.create_time,
        ordermain.update_time, ordermain.create_iden, ordermain.pay_time,
        ordermain.user_name, ordermain.user_address, ordermain.user_phone,
        ordermain.delivery_info_id, ordermain.payment_mode_id, ordermain.income_addr_id,
        ordermain.freight_payable, ordermain.order_money, ordermain.order_discounts,
        ordermain.is_return, ordermain.is_sms, ordermain.distribution_date,
        ordermain.distribution_time, ordermain.is_replenishment, ordermain.pay_num,
        ordermain.order_status, ordermain.user_content, ordermain.order_del_state,
        ordermain.r_1, ordermain.r_2, ordermain.r_3, ordermain.r_4,ordermain.cuidan,
        ordermain.r_5,ordermain.remind,ordermain.royalty,ordermain.up_price,ordermain.send_state,ordermain.include_dpt,ordermain.back,ordermain.daike,
        ordermain.is_forward,ordermain.forward_company_id,ordermain.company_name,ordermain.bucket_price,ordermain.bucket_beans,
        ordermain.bucket_pay_type,ordermain.is_bank_affirm,ordermain.yf_money,
        ordermain.store_id,ordermain.ticket_user_id,
        ordermain.ticket_price,
        ordermain.yunfei,
        ordermain.drainageuserid,
        ordermain.drainageid,
        ordermain.dikou,
        ordermain.ordersource,
        ordermain.apptoken,
        ordermain.picurl,
        ordermain.lon,
        ordermain.lat,
        ordermain.yuyuetime,
        ordermain.transtatus,
        ordermain.oldstoreid,
        ordermain.tranprice,
        ordermain.oldprice,
        ordermain.platformprice,
        ordermain.mark,
        ordermain.exception,
        ordermain.isprint,
        ordermain.appkey,
        ordermain.finish_time,
        ordermain.shipsn,
        ordermain.shipchannel,
        ordermain.ziti
        from szm_c_order_main ordermain left join smz_c_delivery_info bb on bb.order_main_id = ordermain.order_main_id
        LEFT JOIN smz_c_order_details cc ON cc.orderid = ordermain.order_main_id 
        LEFT JOIN smz_c_order_returns dd ON dd.order_main_id = ordermain.order_main_id 
         where
        1= 1
        <if test="orderStatus != null and orderStatus != ''">
            <if test="orderStatus == 10">
                and ordermain.order_status in (5,10)
            </if>
            <if test="orderStatus == 8">
                and ordermain.order_status in (6,7,8) and dd.processstate = 0
            </if>
            <if test="orderStatus == 11">
                and ordermain.order_status in (6,7,8) and dd.processstate in (1,2)
            </if>
            <if test="orderStatus == 3">
                and ordermain.order_status in (3,4) and bb.delivery_info_state = 0
            </if>
            <if test="orderStatus == 4">
                and ordermain.order_status in (3,4) and bb.delivery_info_state in (2,3)
            </if>
            <if test="orderStatus != 10 and orderStatus != 8 and orderStatus != 3 and orderStatus != 4 and orderStatus != 11">
            and ordermain.order_status = #{orderStatus}
            </if>
        </if>
        <if test="ordersource != null and ordersource != '' and ordersource != 100">
        and ordermain.ordersource = #{ordersource}
        </if>
        <if test="ordersource != null and ordersource != '' and ordersource == 100">
        and ordermain.ordersource is null
        </if>
        <if test="storeId != null and storeId != ''">
        and ordermain.store_id = #{storeId}
        </if>

        <if test="userContent != null and userContent != ''">
        and ordermain.user_content like CONCAT("%",#{userContent},"%")
        </if>

        <if test="userId != null and userId != ''">
        and ordermain.user_id = #{userId}
        </if>
        <if test="appkey != null and appkey != ''">
        and ordermain.appkey = #{appkey}
        </if>
        <if test="username != null and username != ''">
            and (ordermain.user_name like CONCAT("%",#{username},"%") or
             ordermain.user_phone like CONCAT("%",#{username},"%") or 
             ordermain.user_address like CONCAT("%",#{username},"%")  or 
             ordermain.order_num like CONCAT("%",#{username},"%") or
             cc.product_skuname LIKE CONCAT("%",#{username},"%"))
        </if>
        <if test="addressId != null and addressId != ''">
            and ordermain.income_addr_id = #{addressId}
        </if>
        <if test="startTime != null and startTime != ''">
            and ordermain.create_time >= #{startTime} and ordermain.create_time &lt; #{endTime}
        </if>
        <if test="startTimeFinish != null and startTimeFinish != ''">
            and ordermain.finish_time >= #{startTimeFinish} and ordermain.finish_time &lt; #{endTimeFinish}
        </if>

        <!-- 退款订单筛选 -->
        <if test="back != null and back != ''">
            and ordermain.back = #{back}
        </if>

        <if test="timeoutFilter != null and timeoutFilter != '' and timeoutType != null and timeoutType == 'undelivered'">
            <!-- 只统计未配送的订单（状态为0-2） -->
            <if test="timeoutFilter == '1h'">
                <!-- 1小时内 -->
                and ordermain.create_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
            </if>
            <if test="timeoutFilter == '2h'">
                <!-- 1-2小时 -->
                and ordermain.create_time &lt; DATE_SUB(NOW(), INTERVAL 1 HOUR)
                and ordermain.create_time >= DATE_SUB(NOW(), INTERVAL 2 HOUR)
            </if>
            <if test="timeoutFilter == '4h'">
                <!-- 2-4小时 -->
                and ordermain.create_time &lt; DATE_SUB(NOW(), INTERVAL 2 HOUR)
                and ordermain.create_time >= DATE_SUB(NOW(), INTERVAL 4 HOUR)
            </if>
            <if test="timeoutFilter == '8h'">
                <!-- 4-8小时 -->
                and ordermain.create_time &lt; DATE_SUB(NOW(), INTERVAL 4 HOUR)
                and ordermain.create_time >= DATE_SUB(NOW(), INTERVAL 8 HOUR)
            </if>
            <if test="timeoutFilter == '16h'">
                <!-- 8-16小时 -->
                and ordermain.create_time &lt; DATE_SUB(NOW(), INTERVAL 8 HOUR)
                and ordermain.create_time >= DATE_SUB(NOW(), INTERVAL 16 HOUR)
            </if>
            <if test="timeoutFilter == '24h'">
                <!-- 16-24小时 -->
                and ordermain.create_time &lt; DATE_SUB(NOW(), INTERVAL 16 HOUR)
                and ordermain.create_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
            </if>
            <if test="timeoutFilter == '48h'">
                <!-- 24-48小时 -->
                and ordermain.create_time &lt; DATE_SUB(NOW(), INTERVAL 24 HOUR)
                and ordermain.create_time >= DATE_SUB(NOW(), INTERVAL 48 HOUR)
            </if>
            <if test="timeoutFilter == '72h'">
                <!-- 48-72小时 -->
                and ordermain.create_time &lt; DATE_SUB(NOW(), INTERVAL 48 HOUR)
                and ordermain.create_time >= DATE_SUB(NOW(), INTERVAL 72 HOUR)
            </if>
            <if test="timeoutFilter == '72h+'">
                <!-- 72小时以上 -->
                and ordermain.create_time &lt; DATE_SUB(NOW(), INTERVAL 72 HOUR)
            </if>
        </if>
        <if test="timeoutFilter != null and timeoutFilter != '' and timeoutType != null and timeoutType == 'undelivered_timeout'">
            <!-- 只统计未配送的订单（状态为0-2） -->
            <if test="timeoutFilter == '1h'">
                <!-- 1小时内 -->
                and ordermain.create_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
            </if>
            <if test="timeoutFilter == '2h'">
                <!-- 1-2小时 -->
                and ordermain.create_time &lt; DATE_SUB(NOW(), INTERVAL 1 HOUR)
                and ordermain.create_time >= DATE_SUB(NOW(), INTERVAL 2 HOUR)
            </if>
            <if test="timeoutFilter == '4h'">
                <!-- 2-4小时 -->
                and ordermain.create_time &lt; DATE_SUB(NOW(), INTERVAL 2 HOUR)
                and ordermain.create_time >= DATE_SUB(NOW(), INTERVAL 4 HOUR)
            </if>
            <if test="timeoutFilter == '8h'">
                <!-- 4-8小时 -->
                and ordermain.create_time &lt; DATE_SUB(NOW(), INTERVAL 4 HOUR)
                and ordermain.create_time >= DATE_SUB(NOW(), INTERVAL 8 HOUR)
            </if>
            <if test="timeoutFilter == '16h'">
                <!-- 8-16小时 -->
                and ordermain.create_time &lt; DATE_SUB(NOW(), INTERVAL 8 HOUR)
                and ordermain.create_time >= DATE_SUB(NOW(), INTERVAL 16 HOUR)
            </if>
            <if test="timeoutFilter == '24h'">
                <!-- 16-24小时 -->
                and ordermain.create_time &lt; DATE_SUB(NOW(), INTERVAL 16 HOUR)
                and ordermain.create_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
            </if>
            <if test="timeoutFilter == '48h'">
                <!-- 24-48小时 -->
                and ordermain.create_time &lt; DATE_SUB(NOW(), INTERVAL 24 HOUR)
                and ordermain.create_time >= DATE_SUB(NOW(), INTERVAL 48 HOUR)
            </if>
            <if test="timeoutFilter == '72h'">
                <!-- 48-72小时 -->
                and ordermain.create_time &lt; DATE_SUB(NOW(), INTERVAL 48 HOUR)
                and ordermain.create_time >= DATE_SUB(NOW(), INTERVAL 72 HOUR)
            </if>
            <if test="timeoutFilter == '72h+'">
                <!-- 72小时以上 -->
                and ordermain.create_time &lt; DATE_SUB(NOW(), INTERVAL 72 HOUR)
            </if>
        </if>
        GROUP BY
        ordermain.order_main_id 
        order by
        ordermain.order_main_id DESC
        <if test="pageNo != null and pageSize != null">
        limit #{pageNo},#{pageSize}
        </if>

    </select>
    <select id="selectAllCountByParams" resultType="integer">
        select count(1)
        from szm_c_order_main ordermain
         where
        1= 1
        <if test="orderStatus != null and orderStatus != ''">
            <if test="orderStatus == 10">
                and ordermain.order_status in (5,10)
            </if>
            <if test="orderStatus == 8">
                and ordermain.order_status in (6,7,8)
                and EXISTS (SELECT 1 FROM smz_c_order_returns dd WHERE dd.order_main_id = ordermain.order_main_id AND dd.processstate = 0)
            </if>
            <if test="orderStatus == 11">
                and ordermain.order_status in (6,7,8)
                and EXISTS (SELECT 1 FROM smz_c_order_returns dd WHERE dd.order_main_id = ordermain.order_main_id AND dd.processstate in (1,2))
            </if>
            <if test="orderStatus == 3">
                and ordermain.order_status in (3,4)
                and EXISTS (SELECT 1 FROM smz_c_delivery_info bb WHERE bb.order_main_id = ordermain.order_main_id AND bb.delivery_info_state = 0)
            </if>
            <if test="orderStatus == 4">
                and ordermain.order_status in (3,4)
                and EXISTS (SELECT 1 FROM smz_c_delivery_info bb WHERE bb.order_main_id = ordermain.order_main_id AND bb.delivery_info_state in (2,3))
            </if>
            <if test="orderStatus != 10 and orderStatus != 8 and orderStatus != 3 and orderStatus != 4 and orderStatus != 11">
            and ordermain.order_status = #{orderStatus}
            </if>
        </if>
        <if test="ordersource != null and ordersource != '' and ordersource != 100">
        and ordermain.ordersource = #{ordersource}
        </if>
        <if test="ordersource != null and ordersource != '' and ordersource == 100">
        and ordermain.ordersource is null
        </if>
        <if test="storeId != null and storeId != ''">
        and ordermain.store_id = #{storeId}
        </if>

        <if test="userContent != null and userContent != ''">
        and ordermain.user_content like CONCAT("%",#{userContent},"%")
        </if>

        <if test="userId != null and userId != ''">
        and ordermain.user_id = #{userId}
        </if>
        <if test="appkey != null and appkey != ''">
        and ordermain.appkey = #{appkey}
        </if>
        <if test="username != null and username != ''">
            and (ordermain.user_name like CONCAT("%",#{username},"%") or
             ordermain.user_phone like CONCAT("%",#{username},"%") or
             ordermain.user_address like CONCAT("%",#{username},"%")  or
             ordermain.order_num like CONCAT("%",#{username},"%") or
             EXISTS (SELECT 1 FROM smz_c_order_details cc WHERE cc.orderid = ordermain.order_main_id AND cc.product_skuname LIKE CONCAT("%",#{username},"%")))
        </if>
        <if test="addressId != null and addressId != ''">
            and ordermain.income_addr_id = #{addressId}
        </if>
        <if test="startTime != null and startTime != ''">
            and ordermain.create_time >= #{startTime} and ordermain.create_time &lt; #{endTime}
        </if>
        <if test="startTimeFinish != null and startTimeFinish != ''">
            and ordermain.finish_time >= #{startTimeFinish} and ordermain.finish_time &lt; #{endTimeFinish}
        </if>

        <!-- 退款订单筛选 -->
        <if test="back != null and back != ''">
            and ordermain.back = #{back}
        </if>

        <if test="timeoutFilter != null and timeoutFilter != '' and timeoutType != null and timeoutType == 'undelivered'">
            <!-- 只统计未配送的订单（状态为0-2） -->
            <if test="timeoutFilter == '1h'">
                <!-- 1小时内 -->
                and ordermain.create_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
            </if>
            <if test="timeoutFilter == '2h'">
                <!-- 1-2小时 -->
                and ordermain.create_time &lt; DATE_SUB(NOW(), INTERVAL 1 HOUR)
                and ordermain.create_time >= DATE_SUB(NOW(), INTERVAL 2 HOUR)
            </if>
            <if test="timeoutFilter == '4h'">
                <!-- 2-4小时 -->
                and ordermain.create_time &lt; DATE_SUB(NOW(), INTERVAL 2 HOUR)
                and ordermain.create_time >= DATE_SUB(NOW(), INTERVAL 4 HOUR)
            </if>
            <if test="timeoutFilter == '8h'">
                <!-- 4-8小时 -->
                and ordermain.create_time &lt; DATE_SUB(NOW(), INTERVAL 4 HOUR)
                and ordermain.create_time >= DATE_SUB(NOW(), INTERVAL 8 HOUR)
            </if>
            <if test="timeoutFilter == '16h'">
                <!-- 8-16小时 -->
                and ordermain.create_time &lt; DATE_SUB(NOW(), INTERVAL 8 HOUR)
                and ordermain.create_time >= DATE_SUB(NOW(), INTERVAL 16 HOUR)
            </if>
            <if test="timeoutFilter == '24h'">
                <!-- 16-24小时 -->
                and ordermain.create_time &lt; DATE_SUB(NOW(), INTERVAL 16 HOUR)
                and ordermain.create_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
            </if>
            <if test="timeoutFilter == '48h'">
                <!-- 24-48小时 -->
                and ordermain.create_time &lt; DATE_SUB(NOW(), INTERVAL 24 HOUR)
                and ordermain.create_time >= DATE_SUB(NOW(), INTERVAL 48 HOUR)
            </if>
            <if test="timeoutFilter == '72h'">
                <!-- 48-72小时 -->
                and ordermain.create_time &lt; DATE_SUB(NOW(), INTERVAL 48 HOUR)
                and ordermain.create_time >= DATE_SUB(NOW(), INTERVAL 72 HOUR)
            </if>
            <if test="timeoutFilter == '72h+'">
                <!-- 72小时以上 -->
                and ordermain.create_time &lt; DATE_SUB(NOW(), INTERVAL 72 HOUR)
            </if>
        </if>
        <if test="timeoutFilter != null and timeoutFilter != '' and timeoutType != null and timeoutType == 'undelivered_timeout'">
            <!-- 只统计未配送的订单（状态为0-2） -->
            <if test="timeoutFilter == '1h'">
                <!-- 1小时内 -->
                and ordermain.create_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
            </if>
            <if test="timeoutFilter == '2h'">
                <!-- 1-2小时 -->
                and ordermain.create_time &lt; DATE_SUB(NOW(), INTERVAL 1 HOUR)
                and ordermain.create_time >= DATE_SUB(NOW(), INTERVAL 2 HOUR)
            </if>
            <if test="timeoutFilter == '4h'">
                <!-- 2-4小时 -->
                and ordermain.create_time &lt; DATE_SUB(NOW(), INTERVAL 2 HOUR)
                and ordermain.create_time >= DATE_SUB(NOW(), INTERVAL 4 HOUR)
            </if>
            <if test="timeoutFilter == '8h'">
                <!-- 4-8小时 -->
                and ordermain.create_time &lt; DATE_SUB(NOW(), INTERVAL 4 HOUR)
                and ordermain.create_time >= DATE_SUB(NOW(), INTERVAL 8 HOUR)
            </if>
            <if test="timeoutFilter == '16h'">
                <!-- 8-16小时 -->
                and ordermain.create_time &lt; DATE_SUB(NOW(), INTERVAL 8 HOUR)
                and ordermain.create_time >= DATE_SUB(NOW(), INTERVAL 16 HOUR)
            </if>
            <if test="timeoutFilter == '24h'">
                <!-- 16-24小时 -->
                and ordermain.create_time &lt; DATE_SUB(NOW(), INTERVAL 16 HOUR)
                and ordermain.create_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
            </if>
            <if test="timeoutFilter == '48h'">
                <!-- 24-48小时 -->
                and ordermain.create_time &lt; DATE_SUB(NOW(), INTERVAL 24 HOUR)
                and ordermain.create_time >= DATE_SUB(NOW(), INTERVAL 48 HOUR)
            </if>
            <if test="timeoutFilter == '72h'">
                <!-- 48-72小时 -->
                and ordermain.create_time &lt; DATE_SUB(NOW(), INTERVAL 48 HOUR)
                and ordermain.create_time >= DATE_SUB(NOW(), INTERVAL 72 HOUR)
            </if>
            <if test="timeoutFilter == '72h+'">
                <!-- 72小时以上 -->
                and ordermain.create_time &lt; DATE_SUB(NOW(), INTERVAL 72 HOUR)
            </if>
        </if>

    </select>
    <select id="selectTransferByParams" parameterType="java.util.Map" resultMap="BaseResultMap">
        select
        <include refid="SqlList"/>
        from szm_c_order_main where
        1= 1
        <if test="ordersource != null and ordersource != ''">
        and ordersource = #{ordersource}
        </if>
        <if test="transtatus != null and transtatus != ''">
        and transtatus = #{transtatus}
        </if>
        <if test="transtatus == null or transtatus == ''">
        and transtatus is not null and transtatus != 0
        </if>
        <if test="storeId != null and storeId != ''">
        and (store_id = #{storeId} and transtatus = 3)
        </if>
        <if test="oldStoreId != null and oldStoreId != ''">
        and ((store_id = #{oldStoreId} and transtatus &lt; 3) or ( oldstoreid = #{oldStoreId} and transtatus = 3))
        </if>

        <if test="userContent != null and userContent != ''">
        and user_content like CONCAT("%",#{userContent},"%")
        </if>

        <if test="userId != null and userId != ''">
        and user_id = #{userId}
        </if>
        <if test="appkey != null and appkey != ''">
        and appkey = #{appkey}
        </if>
        <if test="username != null and username != ''">
            and (user_name like CONCAT("%",#{username},"%") or user_phone like CONCAT("%",#{username},"%") or user_address like CONCAT("%",#{username},"%")  or order_num like CONCAT("%",#{username},"%"))
        </if>
        <if test="addressId != null and addressId != ''">
            and income_addr_id = #{addressId}
        </if>
        <if test="startTime != null and startTime != ''">
            and create_time >= #{startTime} and create_time &lt; #{endTime}
        </if>
        order by
        order_main_id DESC

    </select>
    <select id="selectTransferFrontCountByParams" parameterType="java.util.Map" resultMap="BaseResultMap">
    
        select
        
        <if test="latitude != null and longitude != null and type == 0">
            (6371 * ACOS(
            COS(RADIANS(#{latitude})) * COS(RADIANS(lat)) * COS(RADIANS(lon) - RADIANS(#{longitude})) +
            SIN(RADIANS(#{latitude})) * SIN(RADIANS(lat))
            )) AS distance,
        </if>
        <include refid="SqlList"/>
        from szm_c_order_main where
        1= 1
        <if test="type == 0">
        and transtatus = 2
        </if>
        <if test="type == 1">
        and transtatus = 3 and store_id = #{storeId} and order_status = 2
        </if>
        <if test="type == 2">
        and (((transtatus = 2 or transtatus = 1) and store_id = #{storeId}) or (transtatus = 3 and oldstoreid = #{storeId})) and order_status = 2
        </if>
        <if test="type == 3">
        and transtatus = 1
        </if>
        <if test="type == 4">
        and transtatus in (2,3)
        </if>


        <if test="userContent != null and userContent != ''">
        and user_content like CONCAT("%",#{userContent},"%")
        </if>

        <if test="userId != null and userId != ''">
        and user_id = #{userId}
        </if>
        <if test="appkey != null and appkey != ''">
        and appkey = #{appkey}
        </if>
        <if test="username != null and username != ''">
            and (user_name like CONCAT("%",#{username},"%") or user_phone like CONCAT("%",#{username},"%") or user_address like CONCAT("%",#{username},"%")  or order_num like CONCAT("%",#{username},"%"))
        </if>
        <if test="addressId != null and addressId != ''">
            and income_addr_id = #{addressId}
        </if>
        <if test="startTime != null and startTime != ''">
            and create_time >= #{startTime} and create_time &lt; #{endTime}
        </if>
        
    <if test="latitude != null and longitude != null and type == 0">
        order by distance is not null desc ,distance, create_time desc
    </if>
    <if test="type != 0">    
        order by create_time desc
    </if>

    </select>
    <select id="selectTransferFrontByParams" parameterType="java.util.Map" resultMap="BaseResultMap">
        select
        
        <if test="latitude != null and longitude != null and type == 0">
            (6371 * ACOS(
            COS(RADIANS(#{latitude})) * COS(RADIANS(lat)) * COS(RADIANS(lon) - RADIANS(#{longitude})) +
            SIN(RADIANS(#{latitude})) * SIN(RADIANS(lat))
            )) AS distance,
        </if>
        <include refid="SqlList"/>
        from szm_c_order_main where
        1= 1
        <if test="ordersource != null and ordersource != ''">
        and ordersource = #{ordersource}
        </if>
        <if test="type == 0">
        and transtatus = 2
        </if>
        <if test="type == 1">
        and transtatus = 3 and store_id = #{storeId}
        </if>
        <if test="type == 2">
        and (((transtatus = 2 or transtatus = 1) and store_id = #{storeId}) or (transtatus = 3 and oldstoreid = #{storeId}))
        </if>
        <if test="type == 3">
        and transtatus = 1
        </if>
        <if test="type == 4">
        and transtatus in (2,3)
        </if>

        <if test="userContent != null and userContent != ''">
        and user_content like CONCAT("%",#{userContent},"%")
        </if>

        <if test="userId != null and userId != ''">
        and user_id = #{userId}
        </if>
        <if test="appkey != null and appkey != ''">
        and appkey = #{appkey}
        </if>
        <if test="username != null and username != ''">
            and (user_name like CONCAT("%",#{username},"%") or user_phone like CONCAT("%",#{username},"%") or user_address like CONCAT("%",#{username},"%")  or order_num like CONCAT("%",#{username},"%"))
        </if>
        <if test="addressId != null and addressId != ''">
            and income_addr_id = #{addressId}
        </if>
        <if test="startTime != null and startTime != ''">
            and create_time >= #{startTime} and create_time &lt; #{endTime}
        </if>
        
    <if test="latitude != null and longitude != null and type == 0">
        order by distance is not null desc ,distance, create_time desc
    </if>
    <if test="type != 0">    
        order by create_time desc
    </if>

    </select>
    <select id="selectByUserAndStoreId" resultMap="BaseResultMap">
        select
        <include refid="SqlList"/>
        from szm_c_order_main where create_iden = #{userId} and store_id = #{storeId} and order_del_state = 0
        <if test="state == 0">
            and order_status = 0
        </if>
        <if test="state == 1">
            and order_status >=1 and order_status &lt;= 4
        </if>
        <if test="state == 2">
            and (order_status = 5 or order_status = 9 or order_status = 10)
        </if>
        <if test="state == 3">
            and order_status = 8
        </if>
        <if test="addressId != null and addressId != ''">
            and income_addr_id = #{addressId}
        </if>
        <if test="ordersource != null and ordersource != '' and ordersource != 100">
            and ordersource = #{ordersource}
        </if>
        <if test="ordersource != null and ordersource != '' and ordersource == 100">
            and ordersource is null
        </if>
        order by
        order_main_id DESC
    </select>

    <select id="selectRemind" resultType="java.lang.Integer">
    select count(1)
    from szm_c_order_main where store_id = #{storeId} and order_status = 2 and is_replenishment = 0 and delivery_info_id is null
    </select>

    <select id="selectRemind1" resultType="java.lang.Integer">
    select count(1)
    from szm_c_order_main where store_id = #{storeId} and order_status = 2
    </select>
    <select id="selectByStoreByStateTwo" resultType="java.lang.Integer">
        select count(1)
        from szm_c_order_main
        where create_time >= #{staterTime} and pay_time &lt; #{endTime} and store_id = #{storeId} and order_status = #{state} and delivery_info_id is not null
    </select>
    <select id="selectByStoreAndSend" resultMap="BaseResultMap">
        select
        <include refid="SqlList"/>
        from szm_c_order_main
        where store_id = #{storeId} and delivery_info_id = #{deliveryId}
    </select>

    <select id="selectByStoreAndUser" resultMap="BaseResultMap">
        select
        <include refid="SqlList"/>
        from szm_c_order_main
        where store_id = #{storeId} and create_iden = #{user} and (order_status = 5 or order_status = 10 or order_status = 9)
        order by order_main_id DESC
    </select>
    <select id="selectByStateStar" resultMap="BaseResultMap">
        select
        <include refid="SqlList"/>
        from szm_c_order_main
        where create_time >= #{stateTime} and pay_time &lt; #{endTime} and store_id = #{storeId} and order_status = #{state}
    </select>

    <update id="updateRemind" parameterType="com.example.waterstationbuyproducer.entity.SzmCOrderMain">
    update szm_c_order_main
    set remind = 0
    where order_main_id = #{orderId}
  </update>

    <select id="selectTotal" resultType="java.lang.String">
        select r_1
        from szm_c_order_main
        where store_id = #{storeId}
        and is_return = 0
        and (order_status = 10 or order_status = 5 or order_status = 9)
        and payment_mode_id <![CDATA[<>]]> 3
        and create_time >= #{stateTime} and create_time &lt; #{endTime}
    </select>
    <select id="selectDeliveryIdAll" resultType="java.lang.Double">
        select royalty
        for szm_c_order_main
        where delivery_info_id = #{deliveryId} and (order_status = 5 or order_status = 9 or order_status = 10)
    </select>

    <select id="selectOneOrder" resultMap="BaseResultMap">
        select
        <include refid="SqlList"/>
        from szm_c_order_main
        where create_iden = #{userId} and order_status > 0 and order_status &lt;&gt; 6 order by create_time DESC
    </select>
    <select id="selectDelliveryId" resultMap="BaseResultMap">
        select
        <include refid="SqlList"/>
        from szm_c_order_main
        where delivery_info_id = #{deliveryId} and (order_status = 5 or order_status = 9 or order_status = 10)
    </select>
    <select id="selectStoreBillCountNew" resultType="java.lang.Double">
        select sum(order_money-up_price-freight_payable)
        from szm_c_order_main
        where store_id = #{storeId} and is_return = 0 and order_status = 10
    </select>

    <select id="selectDel" resultMap="BaseResultMap">
        select
        <include refid="SqlList"/>
        from szm_c_order_main
        where order_status = #{orderStatus,jdbcType=VARCHAR} and is_replenishment = #{state} and store_id = #{storeId} and
        delivery_info_id is null
    </select>

    <select id="selectDelPC" resultMap="BaseResultMap">
        select ordermain.order_main_id, ordermain.order_num,ordermain.create_time,
        ordermain.update_time, ordermain.create_iden, ordermain.pay_time,
        ordermain.user_name, ordermain.user_address, ordermain.user_phone,
        ordermain.delivery_info_id, ordermain.payment_mode_id, ordermain.income_addr_id,
        ordermain.freight_payable, ordermain.order_money, ordermain.order_discounts,
        ordermain.is_return, ordermain.is_sms, ordermain.distribution_date,
        ordermain.distribution_time, ordermain.is_replenishment, ordermain.pay_num,
        ordermain.order_status, ordermain.user_content, ordermain.order_del_state,
        ordermain.r_1, ordermain.r_2, ordermain.r_3, ordermain.r_4,
        ordermain.r_5,ordermain.remind,ordermain.royalty,ordermain.up_price,ordermain.send_state,ordermain.include_dpt,
        ordermain.is_forward,ordermain.forward_company_id,ordermain.company_name,ordermain.bucket_price,ordermain.bucket_beans,
        ordermain.bucket_pay_type,ordermain.is_bank_affirm,ordermain.yf_money,ordermain.back,ordermain.daike,
        ordermain.store_id,ordermain.ticket_user_id,
        ordermain.ticket_price,
        ordermain.yunfei,
        ordermain.dikou,
        ordermain.ordersource,
        ordermain.apptoken,
        ordermain.picurl,
        ordermain.lon,
        ordermain.lat,
        ordermain.yuyuetime,
        ordermain.transtatus,
        ordermain.oldstoreid,
        ordermain.tranprice,
        ordermain.oldprice,
        ordermain.platformprice,
        ordermain.mark,
        ordermain.exception,
        ordermain.isprint,
        ordermain.appkey,
        ordermain.ziti,
        delivery.delivery_user_name,delivery.delivery_user_id,delivery.delivery_user_state
        from szm_c_order_main as ordermain

        LEFT JOIN smz_c_delivery_user as delivery ON ordermain.delivery_info_id = delivery.delivery_user_id
        where order_status = #{orderStatus,jdbcType=VARCHAR} and is_replenishment = #{state} and ordermain.r_2 =
        #{storeId} and delivery_info_id is null
        <if test="payMent!=''">
            and ordermain.payment_mode_id = #{payMent}
        </if>
        <if test="userType!=''">
            <if test="userType==1">
                and company_name is not null and company_name != ''
            </if>
            <if test="userType==0">
                and (company_name is null or company_name = '')
            </if>
        </if>
        <if test="deliveryName!=''">
            and delivery.delivery_user_name = #{deliveryName}
        </if>
        <if test="null!= startTime">
            and ordermain.create_time >= #{startTime}
        </if>
        <if test="null!= endTime">
            and ordermain.create_time &lt;= #{endTime}
        </if>
        order by ordermain.create_time desc
    </select>


    <select id="selectReturnPC" resultMap="BaseResultMap">
        select ordermain.order_main_id, ordermain.order_num,ordermain.create_time,
        ordermain.update_time, ordermain.create_iden, ordermain.pay_time,
        ordermain.user_name, ordermain.user_address, ordermain.user_phone,
        ordermain.delivery_info_id, ordermain.payment_mode_id, ordermain.income_addr_id,
        ordermain.freight_payable, ordermain.order_money, ordermain.order_discounts,
        ordermain.is_return, ordermain.is_sms, ordermain.distribution_date,
        ordermain.distribution_time, ordermain.is_replenishment, ordermain.pay_num,
        ordermain.order_status, ordermain.user_content, ordermain.order_del_state,
        ordermain.r_1, ordermain.r_2, ordermain.r_3, ordermain.r_4,ordermain.cuidan,
        ordermain.r_5,ordermain.remind,ordermain.royalty,ordermain.up_price,ordermain.send_state,ordermain.include_dpt,ordermain.back,ordermain.daike,
        ordermain.is_forward,ordermain.forward_company_id,ordermain.company_name,ordermain.bucket_price,ordermain.bucket_beans,
        ordermain.bucket_pay_type,ordermain.is_bank_affirm,ordermain.yf_money,
        ordermain.store_id,ordermain.ticket_user_id,
        ordermain.ticket_price,
        ordermain.yunfei,
        ordermain.drainageuserid,
        ordermain.drainageid,
        ordermain.dikou,
        ordermain.ordersource,
        ordermain.apptoken,
        ordermain.picurl,
        ordermain.lon,
        ordermain.lat,
        ordermain.yuyuetime,
        ordermain.transtatus,
        ordermain.oldstoreid,
        ordermain.tranprice,
        ordermain.oldprice,
        ordermain.platformprice,
        ordermain.mark,
        ordermain.exception,
        ordermain.isprint,
        ordermain.appkey,
        ordermain.ziti,
        delivery.delivery_user_name,delivery.delivery_user_id,delivery.delivery_user_state,ordermain.user_id as user_id,
        `return`.returns_type,`return`.order_details_id,`return`.processstate,`return`.order_returns_id
        from szm_c_order_main as ordermain
        LEFT JOIN smz_c_delivery_user as delivery ON ordermain.delivery_info_id = delivery.delivery_user_id
        LEFT JOIN smz_c_delivery_info as deliveryinfo ON ordermain.order_main_id = deliveryinfo.order_main_id
        LEFT JOIN smz_c_order_returns as `return` ON ordermain.order_main_id = `return`.order_main_id
        where ordermain.store_id = #{storeId} and order_status > 0 and order_status &lt;&gt; 6 and ordermain.user_name is not null
        <if test="userName!=null">
            and (
             ordermain.user_name like concat(concat("%",#{userName}),"%")
            or ordermain.user_phone like concat(concat("%",#{userName}),"%")
            or ordermain.order_num like concat(concat("%",#{userName}),"%"))
        </if>
        <if test="address!=null">
            and ordermain.user_address like concat(concat("%",#{address}),"%")
        </if>
        <if test="orderStatus!=-1">
            and (order_status = #{orderStatus,jdbcType=VARCHAR}
            <if test="orderStatus==3 and state==0">
                or order_status = 5)
            </if>
            <if test="orderStatus!=3 or state!=0">
                )
            </if>
            <if test="orderStatus==3 and state==1">
                and (deliveryinfo.delivery_info_state = 0)
            </if>
            <if test="orderStatus==2">
                <if test="state==1">
                    and (ordermain.delivery_info_id is not null or ordermain.is_replenishment = 1) and (ordermain.transtatus is null or ordermain.transtatus = 0)
                </if>
                <if test="state==0">
                    and ordermain.delivery_info_id is null and ordermain.is_replenishment = 0 and (ordermain.transtatus is null or ordermain.transtatus = 0)
                </if>
            </if>
        </if>
        <if test="orderStatus == 8">
            and return.processstate is not null 
        </if>

        <if test="payMent!=''">
            and ordermain.payment_mode_id = #{payMent}
        </if>
        <if test="userType!=''">
            <if test="userType==1">
                and company_name is not null and company_name != ''
            </if>
            <if test="userType==0">
                and (company_name is null or company_name = '')
            </if>
        </if>
        <if test="deliveryName!=''">
            and delivery.delivery_user_name = #{deliveryName}
        </if>
        <if test="returnState!=''">
            <if test="returnState==0">
                and `return`.returns_type is not null and `return`.returns_type = '退货退款' and `return`.processstate = 0
            </if>
            <if test="returnState==1">
                and `return`.returns_type is not null and `return`.returns_type = '退款' and `return`.processstate = 0
            </if>
            <if test="returnState==2">
                and `return`.returns_type is not null and `return`.returns_type = '退货退款' and `return`.processstate = 1
            </if>
            <if test="returnState==3">
                and `return`.returns_type is not null and `return`.returns_type = '退款' and `return`.processstate = 1
            </if>
        </if>

        <if test="null!= startTime">
            and ordermain.create_time >= #{startTime}
        </if>
        <if test="null!= endTime">
            and ordermain.create_time &lt;= #{endTime}
        </if>
        <if test="null!= ordersource and ordersource != 100">
            and ordermain.ordersource = #{ordersource}
        </if>
        <if test="null!= ordersource and ordersource == 100">
            and (ordermain.ordersource is null or ordermain.ordersource = 0)
        </if>
        <if test="true == selfStatus">
            and ordermain.delivery_info_id = 0
        </if>
        <if test="null!= sortOrder and sortOrder == 'asc'">
            order by return.processstate,return.order_returns_id desc,create_time 
        </if>
        <if test="null == sortOrder or sortOrder == 'desc'">
            order by return.processstate,return.order_returns_id desc,create_time desc
        </if>
            </select>

    <select id="selectOtherPC" resultMap="BaseResultMap">
        select ordermain.order_main_id, ordermain.order_num,ordermain.create_time,
        ordermain.update_time, ordermain.create_iden, ordermain.pay_time,
        ordermain.user_name, ordermain.user_address, ordermain.user_phone,
        ordermain.delivery_info_id, ordermain.payment_mode_id, ordermain.income_addr_id,
        ordermain.freight_payable, ordermain.order_money, ordermain.order_discounts,
        ordermain.is_return, ordermain.is_sms, ordermain.distribution_date,
        ordermain.distribution_time, ordermain.is_replenishment, ordermain.pay_num,
        ordermain.order_status, ordermain.user_content, ordermain.order_del_state,
        ordermain.r_1, ordermain.r_2, ordermain.r_3, ordermain.r_4,ordermain.cuidan,
        ordermain.r_5,ordermain.remind,ordermain.royalty,ordermain.up_price,ordermain.send_state,ordermain.include_dpt,ordermain.back,ordermain.daike,
        ordermain.is_forward,ordermain.forward_company_id,ordermain.company_name,ordermain.bucket_price,ordermain.bucket_beans,
        ordermain.bucket_pay_type,ordermain.is_bank_affirm,ordermain.yf_money,
        ordermain.store_id,ordermain.ticket_user_id,
        ordermain.ticket_price,
        ordermain.yunfei,
        ordermain.drainageuserid,
        ordermain.drainageid,
        ordermain.dikou,
        ordermain.ordersource,
        ordermain.apptoken,
        ordermain.picurl,
        ordermain.lon,
        ordermain.lat,
        ordermain.yuyuetime,
        ordermain.transtatus,
        ordermain.oldstoreid,
        ordermain.tranprice,
        ordermain.oldprice,
        ordermain.platformprice,
        ordermain.mark,
        ordermain.exception,
        ordermain.isprint,
        ordermain.appkey,
        ordermain.ziti,
        delivery.delivery_user_name,delivery.delivery_user_id,delivery.delivery_user_state,ordermain.user_id as user_id,
        `return`.returns_type,`return`.order_details_id,`return`.processstate,`return`.order_returns_id
        from szm_c_order_main as ordermain
        LEFT JOIN smz_c_delivery_user as delivery ON ordermain.delivery_info_id = delivery.delivery_user_id
        LEFT JOIN smz_c_delivery_info as deliveryinfo ON ordermain.order_main_id = deliveryinfo.order_main_id
        LEFT JOIN smz_c_order_returns as `return` ON ordermain.order_main_id = `return`.order_main_id
        where ordermain.store_id = #{storeId} and order_status > 0 and order_status &lt;&gt; 6 and ordermain.user_name is not null
        <if test="userName!=null">
            and (
             ordermain.user_name like concat(concat("%",#{userName}),"%")
            or ordermain.user_phone like concat(concat("%",#{userName}),"%")
                    or ordermain.order_num like concat(concat("%",#{userName}),"%"))
        </if>
        <if test="address!=null">
            and ordermain.user_address like concat(concat("%",#{address}),"%")
        </if>
        <if test="orderStatus!=-1">
            and (order_status = #{orderStatus,jdbcType=VARCHAR}
            <if test="orderStatus==3 and state==0">
                or order_status = 5)
            </if>
            <if test="orderStatus!=3 or state!=0">
                )
            </if>
            <if test="orderStatus==3 and state==1">
                and (deliveryinfo.delivery_info_state = 0)
            </if>
            <if test="orderStatus==2">
                <if test="state==1">
                    and (ordermain.delivery_info_id is not null or is_replenishment = 1)
                </if>
                <if test="state==0">
                    and ordermain.delivery_info_id is null and is_replenishment = 0
                </if>
            </if>
        </if>
            <if test="orderStatus == 8">
                and return.processstate =0
            </if>

        <if test="payMent != null and payMent!=''">
            and ordermain.payment_mode_id = #{payMent}
        </if>
        <if test="back != null and back!=''">
            and ordermain.back = #{back}
        </if>
        <if test="userType!=''">
            <if test="userType==1">
                and company_name is not null and company_name != ''
            </if>
            <if test="userType==0">
                and (company_name is null or company_name = '')
            </if>
        </if>
        <if test="deliveryName!=''">
            and delivery.delivery_user_name = #{deliveryName}
        </if>
        <if test="returnState!=''">
            <if test="returnState==0">
                and `return`.returns_type is not null and `return`.returns_type = '退货退款' and `return`.processstate = 0
            </if>
            <if test="returnState==1">
                and `return`.returns_type is not null and `return`.returns_type = '退款' and `return`.processstate = 0
            </if>
            <if test="returnState==2">
                and `return`.returns_type is not null and `return`.returns_type = '退货退款' and `return`.processstate = 1
            </if>
            <if test="returnState==3">
                and `return`.returns_type is not null and `return`.returns_type = '退款' and `return`.processstate = 1
            </if>
        </if>

        <if test="null!= startTime">
            and ordermain.create_time >= #{startTime}
        </if>
        <if test="null!= endTime">
            and ordermain.create_time &lt;= #{endTime}
        </if>
        <if test="null!= ordersource and ordersource != 100">
            and ordermain.ordersource = #{ordersource}
        </if>
        <if test="null!= ordersource and ordersource == 100">
            and (ordermain.ordersource is null or ordermain.ordersource = 0)
        </if>
        <if test="true == selfStatus">
            and ordermain.delivery_info_id = 0
        </if>

                    order by return.processstate,return.order_returns_id desc,create_time desc
            </select>

            <select id="selectReturnPCCount" resultType="java.lang.Integer">
                select count(1)
                from szm_c_order_main as ordermain
                LEFT JOIN smz_c_delivery_user as delivery ON ordermain.delivery_info_id = delivery.delivery_user_id
                LEFT JOIN smz_c_delivery_info as deliveryinfo ON ordermain.order_main_id = deliveryinfo.order_main_id
                LEFT JOIN smz_c_order_returns as `return` ON ordermain.order_main_id = `return`.order_main_id
                where ordermain.store_id = #{storeId} and order_status > 0 and order_status &lt;&gt; 6
                <if test="userName!=null">
                    and ( ordermain.user_name like concat(concat("%",#{userName}),"%")
                    or ordermain.user_phone like concat(concat("%",#{userName}),"%")
                    or ordermain.user_address like concat(concat("%",#{userName}),"%") or ordermain.order_num like concat(concat("%",#{userName}),"%"))
                </if>
                <if test="orderStatus!=-1">
                    and order_status = #{orderStatus,jdbcType=VARCHAR}
                </if>
                <if test="orderStatus==3 and state==1">
                    and (deliveryinfo.delivery_info_state = 0)
                </if>
                <if test="orderStatus==2">
                    <if test="state==1">
                        and (ordermain.delivery_info_id is not null or is_replenishment = 1)
                    </if>
                    <if test="state==0">
                        and ordermain.delivery_info_id is null and is_replenishment = 0
                    </if>
                </if>
                <if test="payMent!=''">
                    and ordermain.payment_mode_id = #{payMent}
                </if>
                <if test="userType!=''">
                    <if test="userType==1">
                        and company_name is not null and company_name != ''
                    </if>
                    <if test="userType==0">
                        and (company_name is null or company_name = '')
                    </if>
                </if>
                <if test="deliveryName!=''">
                    and delivery.delivery_user_name = #{deliveryName}
                </if>
                <if test="returnState!=''">
                    <if test="returnState==0">
                        and `return`.returns_type is not null and `return`.returns_type = '退货退款' and `return`.processstate = 0
                    </if>
                    <if test="returnState==1">
                        and `return`.returns_type is not null and `return`.returns_type = '退款' and `return`.processstate = 0
                    </if>
                    <if test="returnState==2">
                        and `return`.returns_type is not null and `return`.returns_type = '退货退款' and `return`.processstate = 1
                    </if>
                    <if test="returnState==3">
                        and `return`.returns_type is not null and `return`.returns_type = '退款' and `return`.processstate = 1
                    </if>
                </if>

                <if test="null!= startTime">
                    and ordermain.create_time >= #{startTime}
                </if>
                <if test="null!= endTime">
                    and ordermain.create_time &lt;= #{endTime}
                </if>
                <if test="back != null and back!=''">
                    and ordermain.back = #{back}
                </if>
        <if test="null!= ordersource and ordersource != 100">
            and ordermain.ordersource = #{ordersource}
        </if>
        <if test="null!= ordersource and ordersource == 100">
            and (ordermain.ordersource is null or ordermain.ordersource = 0)
        </if>
            </select>


            <select id="selectBuckPC" resultMap="BaseResultMap">
                select ordermain.order_main_id, ordermain.order_num,ordermain.create_time,
                ordermain.update_time, ordermain.create_iden, ordermain.pay_time,
                ordermain.user_name, ordermain.user_address, ordermain.user_phone,
                ordermain.delivery_info_id, ordermain.payment_mode_id, ordermain.income_addr_id,
                ordermain.freight_payable, ordermain.order_money, ordermain.order_discounts,
                ordermain.is_return, ordermain.is_sms, ordermain.distribution_date,
                ordermain.distribution_time, ordermain.is_replenishment, ordermain.pay_num,
                ordermain.order_status, ordermain.user_content, ordermain.order_del_state,
                ordermain.r_1, ordermain.r_2, ordermain.r_3, ordermain.r_4,ordermain.cuidan,
                ordermain.r_5,remind,royalty,up_price,send_state,include_dpt,ordermain.back,ordermain.daike,
                ordermain.store_id,ordermain.ticket_user_id,
                ordermain.ticket_price,
                ordermain.yunfei,
                ordermain.drainageuserid,
                ordermain.drainageid,
                ordermain.dikou,
                ordermain.ordersource,
                ordermain.apptoken,
                ordermain.picurl,
                ordermain.lon,
                ordermain.lat,
                ordermain.yuyuetime,
                ordermain.exception,
                ordermain.isprint,
                ordermain.transtatus,
                ordermain.oldstoreid,
                ordermain.tranprice,
                ordermain.oldprice,
                ordermain.platformprice,
                ordermain.mark,
                ordermain.appkey,
                ordermain.ziti,
                ordermain.is_forward,ordermain.forward_company_id,ordermain.company_name,ordermain.bucket_price,ordermain.bucket_beans,
                ordermain.bucket_pay_type,ordermain.is_bank_affirm,ordermain.yf_money,
                delivery.delivery_user_name,delivery.delivery_user_id,delivery.delivery_user_state
                from szm_c_order_main as ordermain
                LEFT JOIN smz_c_delivery_user as delivery ON ordermain.delivery_info_id = delivery.delivery_user_id
                INNER JOIN smz_c_delivery_info deli ON ordermain.order_main_id = deli.order_main_id
                where ordermain.store_id = #{storeId} and ordermain.user_name is not null
                and deli.r_3 = 1
                <if test="userName!=null">
                    and (ordermain.user_name like concat(concat("%",#{userName}),"%")
                    or ordermain.user_phone like concat(concat("%",#{userName}),"%")
                     or ordermain.order_num like concat(concat("%",#{userName}),"%"))
                </if>
                <if test="address!=null">
                    and ordermain.user_address like concat(concat("%",#{address}),"%")
                </if>
                <if test="payMent!=''">
                    and ordermain.payment_mode_id = #{payMent}
                </if>
                <if test="userType!=''">
                    <if test="userType==1">
                        and company_name is not null and company_name != ''
                    </if>
                    <if test="userType==0">
                        and (company_name is null or company_name = '')
                    </if>
                </if>
                <if test="deliveryName!=''">
                    and delivery.delivery_user_name = #{deliveryName}
                </if>
                <if test="null!= startTime">
                    and ordermain.create_time >= #{startTime}
                </if>
                <if test="null!= endTime">
                    and ordermain.create_time &lt;= #{endTime}
                </if>
        <if test="null!= ordersource and ordersource != 100">
            and ordermain.ordersource = #{ordersource}
        </if>
        <if test="null!= ordersource and ordersource == 100">
            and (ordermain.ordersource is null or ordermain.ordersource = 0)
        </if>
                <if test="true == selfStatus">
                    and ordermain.delivery_info_id = 0
                </if>
                order by ordermain.order_main_id desc
            </select>
            <select id="selectBuckPCCount" resultType="java.lang.Integer">
                select 
                count(1)
                from szm_c_order_main as ordermain
                INNER JOIN smz_c_delivery_info deli ON ordermain.order_main_id = deli.order_main_id
                where ordermain.store_id = #{storeId} and ordermain.user_name is not null
                and deli.r_3 = 1
            </select>
            <select id="selectByStoreByStateTwoDelivery" resultType="java.lang.Integer">
                select count(1)
                from szm_c_order_main
                where create_time >= #{staterTime} and pay_time &lt; #{endTime} and store_id = #{storeId} and order_status = #{state} and delivery_info_id is null and is_replenishment = 0
            </select>
            <select id="selectDeliveryBydate" resultMap="BaseResultMap">
                select
                <include refid="SqlList"/>
                from szm_c_order_main
                where delivery_info_id = #{deliveryId} and (order_status = 5 or order_status = 9 or order_status = 10) and
                create_time >= #{stateTime} and pay_time &lt; #{endTime}
            </select>

            <select id="selectAllOrder" resultMap="BaseResultMap">
                select
                <include refid="SqlList"/>
                from szm_c_order_main
                where store_id = #{storeId} and order_status > 0 and order_status &lt;&gt; 6 and user_name is not null
                order by pay_time desc
            </select>

            <select id="selectAllOrderPC" resultMap="BaseResultMap">
                select ordermain.order_main_id, ordermain.order_num,ordermain.create_time,
                ordermain.update_time, ordermain.create_iden, ordermain.pay_time,
                ordermain.user_name, ordermain.user_address, ordermain.user_phone,
                ordermain.delivery_info_id, ordermain.payment_mode_id, ordermain.income_addr_id,
                ordermain.freight_payable, ordermain.order_money, ordermain.order_discounts,
                ordermain.is_return, ordermain.is_sms, ordermain.distribution_date,
                ordermain.distribution_time, ordermain.is_replenishment, ordermain.pay_num,
                ordermain.order_status, ordermain.user_content, ordermain.order_del_state,
                ordermain.r_1, ordermain.r_2, ordermain.r_3, ordermain.r_4,ordermain.cuidan,
                ordermain.r_5,ordermain.remind,ordermain.royalty,ordermain.up_price,ordermain.send_state,ordermain.include_dpt,
                ordermain.is_forward,ordermain.forward_company_id,ordermain.company_name,ordermain.bucket_price,ordermain.bucket_beans,
                ordermain.bucket_pay_type,ordermain.is_bank_affirm,ordermain.yf_money,ordermain.back,ordermain.daike,
                ordermain.store_id,ordermain.ticket_user_id,
                ordermain.ticket_price,
                ordermain.yunfei,
                ordermain.drainageuserid,
                ordermain.drainageid,
                ordermain.dikou,
                ordermain.ordersource,
                ordermain.apptoken,
                ordermain.picurl,
                ordermain.lon,
                ordermain.lat,
                ordermain.yuyuetime,
                ordermain.transtatus,
                ordermain.oldstoreid,
                ordermain.tranprice,
                ordermain.oldprice,
                ordermain.platformprice,
                ordermain.mark,
                ordermain.exception,
                ordermain.isprint,
                ordermain.appkey,
                ordermain.ziti,
                delivery.delivery_user_name,delivery.delivery_user_id,delivery.delivery_user_state
                from szm_c_order_main as ordermain
                LEFT JOIN smz_c_delivery_user as delivery ON ordermain.delivery_info_id = delivery.delivery_user_id
                where ordermain.store_id = #{storeId} and order_status > 0 and order_status &lt;&gt; 6 and ordermain.user_name is not null
                <if test="payMent!='' and payMent!=null">
                    and ordermain.payment_mode_id = #{payMent}
                </if>
                <if test="userType!='' and userType!=null">
                    <if test="userType==1">
                        and company_name is not null and company_name != ''
                    </if>
                    <if test="userType==0">
                        and (company_name is null or company_name = '')
                    </if>
                </if>
                <if test="drainageUserId!=null">
                    and ordermain.drainageuserid = #{drainageUserId}
                </if>
                <if test="drainageid!=null">
                    and ordermain.drainageid = #{drainageid}
                </if>
                <if test="deliveryName!=null">
                    and delivery.delivery_user_name = #{deliveryName}
                </if>
                <if test="null!= startTime">
                    and ordermain.create_time >= #{startTime}
                </if>
                <if test="null!= endTime">
                    and ordermain.create_time &lt;= #{endTime}
                </if>
                order by ordermain.create_time desc
            </select>
            <select id="selectAllOrder1" resultMap="BaseResultMap">
                select
                <include refid="SqlList"/>
                from szm_c_order_main
                where store_id = #{storeId} and order_status > 0 and is_forward = 1 and include_dpt = 1
                order by create_time DESC
            </select>
            <select id="selectStoreOrderCoutn" resultType="java.lang.Integer">
            select count(1)
            from szm_c_order_main
            where store_id = #{storeId} and order_status > 0 and order_status &lt;&gt; 6
            </select>

            <select id="selectStoreDayOrderCoutn" resultType="java.lang.Integer">
            select count(1)
            from szm_c_order_main
            where store_id = #{storeId} and order_status > 0 and order_status &lt;&gt; 6
            and create_time >= #{stateTime} and create_time &lt; #{endTime}
            </select>

            <select id="selectOrderStatus" resultMap="BaseResultMap">
                select
                <include refid="SqlList"/>
                from szm_c_order_main
                where store_id = #{storeId} and order_status = 5
                order by create_time DESC
            </select>

            <select id="selectOrderStatusPC" resultMap="BaseResultMap">
                select ordermain.order_main_id, ordermain.order_num,ordermain.create_time,
                ordermain.update_time, ordermain.create_iden, ordermain.pay_time,
                ordermain.user_name, ordermain.user_address, ordermain.user_phone,
                ordermain.delivery_info_id, ordermain.payment_mode_id, ordermain.income_addr_id,
                ordermain.freight_payable, ordermain.order_money, ordermain.order_discounts,
                ordermain.is_return, ordermain.is_sms, ordermain.distribution_date,
                ordermain.distribution_time, ordermain.is_replenishment, ordermain.pay_num,
                ordermain.order_status, ordermain.user_content, ordermain.order_del_state,
                ordermain.r_1, ordermain.r_2, ordermain.r_3, ordermain.r_4,ordermain.cuidan,
                ordermain.r_5,ordermain.remind,ordermain.royalty,ordermain.up_price,ordermain.send_state,ordermain.include_dpt,ordermain.user_id as user_id,
                ordermain.is_forward,ordermain.forward_company_id,ordermain.company_name,ordermain.bucket_price,ordermain.bucket_beans,
                ordermain.bucket_pay_type,ordermain.is_bank_affirm,ordermain.yf_money,ordermain.back,ordermain.daike,
                ordermain.store_id,ordermain.ticket_user_id,
                ordermain.ticket_price,
                ordermain.yunfei,
                ordermain.dikou,
                ordermain.ordersource,
                ordermain.apptoken,
                ordermain.picurl,
                ordermain.lon,
                ordermain.lat,
                ordermain.appkey,
                ordermain.ziti,
                delivery.delivery_user_name,delivery.delivery_user_id,delivery.delivery_user_state
                from szm_c_order_main as ordermain
                LEFT JOIN smz_c_delivery_user as delivery ON ordermain.delivery_info_id = delivery.delivery_user_id
                where ordermain.store_id = #{storeId} and ordermain.order_status = 5 and ordermain.user_name is not null
                <if test="userName!=null">
                    and (ordermain.user_name like concat(concat("%",#{userName}),"%")
                    or ordermain.user_phone like concat(concat("%",#{userName}),"%")
                    or ordermain.order_num like concat(concat("%",#{userName}),"%"))
                </if>
                <if test="address!=null">
                    and ordermain.user_address like concat(concat("%",#{address}),"%")
                </if>
                <if test="payMent!=''">
                    and ordermain.payment_mode_id = #{payMent}
                </if>
                <if test="userType!=''">
                    <if test="userType==1">
                        and company_name is not null and company_name != ''
                    </if>
                    <if test="userType==0">
                        and (company_name is null or company_name = '')
                    </if>
                </if>
                <if test="deliveryName!=''">
                    and delivery.delivery_user_name = #{deliveryName}
                </if>
                <if test="null!= startTime">
                    and ordermain.create_time >= #{startTime}
                </if>
                <if test="null!= endTime">
                    and ordermain.create_time &lt;= #{endTime}
                </if>
        <if test="null!= ordersource and ordersource != 100">
            and ordermain.ordersource = #{ordersource}
        </if>
        <if test="null!= ordersource and ordersource == 100">
            and (ordermain.ordersource is null or ordermain.ordersource = 0)
        </if>
                <if test="true == selfStatus">
                    and ordermain.delivery_info_id = 0
                </if>
                order by ordermain.order_main_id desc
            </select>

            <select id="selectOrderStatusPCCount" resultType="java.lang.Integer">
                select count(1)
                from szm_c_order_main as ordermain
                LEFT JOIN smz_c_delivery_user as delivery ON ordermain.delivery_info_id = delivery.delivery_user_id
                where ordermain.store_id = #{storeId} and ordermain.order_status = 5
                <if test="userName!=null">
                    and (ordermain.user_name like concat(concat("%",#{userName}),"%")
                    or ordermain.user_phone like concat(concat("%",#{userName}),"%")
                    or ordermain.user_address like concat(concat("%",#{userName}),"%") or ordermain.order_num like concat(concat("%",#{userName}),"%"))
                </if>
                <if test="payMent!=''">
                    and ordermain.payment_mode_id = #{payMent}
                </if>
                <if test="userType!=''">
                    <if test="userType==1">
                        and company_name is not null and company_name != ''
                    </if>
                    <if test="userType==0">
                        and (company_name is null or company_name = '')
                    </if>
                </if>
                <if test="deliveryName!=''">
                    and delivery.delivery_user_name = #{deliveryName}
                </if>
                <if test="null!= startTime">
                    and ordermain.create_time >= #{startTime}
                </if>
                <if test="null!= endTime">
                    and ordermain.create_time &lt;= #{endTime}
                </if>
        <if test="null!= ordersource and ordersource != 100">
            and ordermain.ordersource = #{ordersource}
        </if>
        <if test="null!= ordersource and ordersource == 100">
            and (ordermain.ordersource is null or ordermain.ordersource = 0)
        </if>
            </select>
            <select id="selectUserCount" resultType="java.lang.Integer">
                select count(1)
                from szm_c_order_main
                where create_iden = #{userId} and (order_status >= 5 or order_status = 9 or order_status = 10) and is_return = 0
            </select>
            <select id="selectDeliveryState" resultMap="BaseResultMap">
                select
                <include refid="SqlList"/>
                from szm_c_order_main
                where delivery_info_id = #{deliveryId} and (order_status = 5 or order_status = 9 or order_status = 10) and
                create_time >= #{stateTime} and pay_time &lt; #{endTime}
                and store_id = #{storeId}
            </select>
            <select id="selectNewStoreAll" resultMap="BaseResultMap">
                select
                <include refid="SqlList"/>
                from szm_c_order_main
                where store_id = #{storeId} and is_return = 0 and order_status = 10
            </select>
            <select id="selectStoreBillCountNewByTime" resultType="java.lang.Double">
                select sum(order_money-up_price-freight_payable)
                from szm_c_order_main
                where store_id = #{storeId} and is_return = 0 and order_status = 10 and create_time >= #{stateTime} and pay_time &lt; #{endTime}
            </select>
            <select id="selectStoreIdCount" resultType="java.lang.Integer">
                select count(1)
                from szm_c_order_main
                where store_id = #{storeId} and order_status > 0 and order_status &lt;&gt; 6 and user_name is not null
            </select>

            <select id="selectUserIdCount" resultType="java.lang.Integer">
                select count(1)
                from szm_c_order_main
                where create_iden = #{userId} and order_status > 0 and order_status &lt;&gt; 6
            </select>
            <select id="selectDeliveryAll" resultMap="BaseResultMap">
                select
                <include refid="SqlList"/>
                from szm_c_order_main
                where delivery_info_id = #{deliveryId} and create_time >= #{stateTime} and pay_time &lt; #{endTime}
            </select>
            <select id="selectNewDelivery" resultMap="BaseResultMap">
                select
                <include refid="SqlList"/>
                from szm_c_order_main
                where order_status = #{orderStatus,jdbcType=VARCHAR} and store_id = #{storeId}
            </select>

            <select id="selectNewDeliveryPC" resultMap="BaseResultMap">
                select ordermain.order_main_id, ordermain.order_num,ordermain.create_time,
                ordermain.update_time, ordermain.create_iden, ordermain.pay_time,
                ordermain.user_name, ordermain.user_address, ordermain.user_phone,
                ordermain.delivery_info_id, ordermain.payment_mode_id, ordermain.income_addr_id,
                ordermain.freight_payable, ordermain.order_money, ordermain.order_discounts,
                ordermain.is_return, ordermain.is_sms, ordermain.distribution_date,
                ordermain.distribution_time, ordermain.is_replenishment, ordermain.pay_num,
                ordermain.order_status, ordermain.user_content, ordermain.order_del_state,
                ordermain.r_1, ordermain.r_2, ordermain.r_3, ordermain.r_4,ordermain.cuidan,
                ordermain.r_5,ordermain.remind,ordermain.royalty,ordermain.up_price,ordermain.send_state,ordermain.include_dpt,ordermain.back,ordermain.daike,
                ordermain.is_forward,ordermain.forward_company_id,ordermain.company_name,ordermain.bucket_price,ordermain.bucket_beans,
                ordermain.bucket_pay_type,ordermain.is_bank_affirm,ordermain.yf_money,
                ordermain.store_id,ordermain.ticket_user_id,ordermain.ticket_price,ordermain.user_id as user_id,

                delivery.delivery_user_name,delivery.delivery_user_id,delivery.delivery_user_state
                from szm_c_order_main as ordermain
                LEFT JOIN smz_c_delivery_user as delivery ON ordermain.delivery_info_id = delivery.delivery_user_id
                where ordermain.store_id = #{storeId}
                <if test="orderStatus!=''">
                    <choose>
                        <when test="orderStatus==4">
                            and ordermain.order_status in (4,11)
                        </when>
                        <otherwise>
                            and ordermain.order_status = #{orderStatus,jdbcType=VARCHAR}
                        </otherwise>
                    </choose>
                </if>
                <if test="userName!=null">
                    and ( ordermain.user_name like concat(concat("%",#{userName}),"%")
                    or ordermain.user_phone like concat(concat("%",#{userName}),"%") or ordermain.order_num like concat(concat("%",#{userName}),"%") )
                </if>
                <if test="address!=null">
                    and ordermain.user_address like concat(concat("%",#{address}),"%")
                </if>
                <if test="payMent!=''">
                    and ordermain.payment_mode_id = #{payMent}
                </if>
                <if test="userType!=''">
                    <if test="userType==1">
                        and company_name is not null and company_name != ''
                    </if>
                    <if test="userType==0">
                        and (company_name is null or company_name = '')
                    </if>
                </if>
                <if test="deliveryName!=''">
                    and delivery.delivery_user_name = #{deliveryName}
                </if>
                <if test="null!= startTime">
                    and ordermain.create_time >= #{startTime}
                </if>
                <if test="null!= endTime">
                    and ordermain.create_time &lt;= #{endTime}
                </if>
        <if test="null!= ordersource and ordersource != 100">
            and ordermain.ordersource = #{ordersource}
        </if>
        <if test="null!= ordersource and ordersource == 100">
            and (ordermain.ordersource is null or ordermain.ordersource = 0)
        </if>
                <if test="true == selfStatus">
                    and ordermain.delivery_info_id = 0
                </if>
                order by ordermain.order_main_id desc
            </select>

            <select id="selectNewDeliveryPCCount" resultType="java.lang.Integer">
                select count(1)
                from szm_c_order_main as ordermain
                LEFT JOIN smz_c_delivery_user as delivery ON ordermain.delivery_info_id = delivery.delivery_user_id
                where ordermain.order_status = #{orderStatus,jdbcType=VARCHAR} and ordermain.store_id = #{storeId}
                <if test="userName!=null">
                    and ( ordermain.user_name like concat(concat("%",#{userName}),"%")
                    or ordermain.user_phone like concat(concat("%",#{userName}),"%") or ordermain.order_num like concat(concat("%",#{userName}),"%") or ordermain.user_address like concat(concat("%",#{userName}),"%"))
                </if>
                <if test="payMent!=''">
                    and ordermain.payment_mode_id = #{payMent}
                </if>
                <if test="back != null and back!=''">
                    and ordermain.back = #{back}
                </if>
                <if test="userType!=''">
                    <if test="userType==1">
                        and company_name is not null and company_name != ''
                    </if>
                    <if test="userType==0">
                        and (company_name is null or company_name = '')
                    </if>
                </if>
                <if test="deliveryName!=''">
                    and delivery.delivery_user_name = #{deliveryName}
                </if>
                <if test="null!= startTime">
                    and ordermain.create_time >= #{startTime}
                </if>
                <if test="null!= endTime">
                    and ordermain.create_time &lt;= #{endTime}
                </if>
        <if test="null!= ordersource and ordersource != 100">
            and ordermain.ordersource = #{ordersource}
        </if>
        <if test="null!= ordersource and ordersource == 100">
            and (ordermain.ordersource is null or ordermain.ordersource = 0)
        </if>
            </select>

            <select id="selectSumPCByTime" resultType="java.lang.Double">
                select COALESCE(SUM(r_1),0)
                from szm_c_order_main
                where store_id = #{storeId} and order_status > 0 and order_status &lt;&gt; 6 and user_name is not null
                <if test="null!= startTime">
                    and create_time >= #{startTime}
                </if>
                <if test="null!= endTime">
                    and create_time &lt;= #{endTime}
                </if>
        <if test="null!= ordersource and ordersource != 100">
            and ordersource = #{ordersource}
        </if>
        <if test="null!= ordersource and ordersource == 100">
            and (ordersource is null or ordersource = 0)
        </if>
            </select>

            <select id="selectCountPCByTime" resultType="java.lang.Integer">
                select count(1)
                from szm_c_order_main as ordermain
                where ordermain.store_id = #{storeId} and order_status > 0 and order_status &lt;&gt; 6 and user_name is not null
                <if test="null!= startTime">
                    and ordermain.create_time >= #{startTime}
                </if>
                <if test="null!= endTime">
                    and ordermain.create_time &lt;= #{endTime}
                </if>
        <if test="null!= ordersource and ordersource != 100">
            and ordermain.ordersource = #{ordersource}
        </if>
        <if test="null!= ordersource and ordersource == 100">
            and (ordermain.ordersource is null or ordermain.ordersource = 0)
        </if>
            </select>

            <select id="selectOrderAllCount" resultType="java.lang.Integer">
                select count(1)
                from szm_c_order_main
                where create_time >= #{stateTime} and create_time &lt; #{endTime} and store_id = #{storeId} and order_status = 10
            </select>
            <select id="selectOneUser" resultMap="BaseResultMap">
                select  order_num, create_time from szm_c_order_main
                where create_iden = #{userId} and  store_id = #{storeId} and (order_status = 5 or order_status = 9 or order_status = 10) order by create_time desc;
            </select>
            <select id="selectUserLastBuy" resultMap="BaseResultMap">
                select 
                <include refid="SqlList"/> from szm_c_order_main
                where create_iden = #{userId} and  store_id = #{storeId}  order by create_time desc limit 1
            </select>
            <select id="selectUserLastBuyFinish" resultMap="BaseResultMap">
                select  
                <include refid="SqlList"/> from szm_c_order_main
                where create_iden = #{userId} and  store_id = #{storeId} and (order_status = 5 or order_status = 10)  order by create_time desc limit 1
            </select>

            <select id="selectByPayTimeMax" resultMap="BaseResultMap">
                select
                <include refid="SqlList"/>
                from szm_c_order_main
                where create_iden = #{userId} order by pay_time DESC
            </select>

            <select id="selectByPaymentModeId" resultMap="BaseResultMap">
                select
                <include refid="SqlList"/>
                from szm_c_order_main
                where create_iden = #{userId} and store_id = #{storeId} and payment_mode_id =#{payMentId}
                and order_status != 6 and order_status != 7 order by create_time asc
            </select>

            <select id="selectByPaymentModeIdDesc" resultMap="BaseResultMap">
                select
                <include refid="SqlList"/>
                from szm_c_order_main
                where create_iden = #{userId} and store_id = #{storeId} and payment_mode_id =#{payMentId}
                and order_status != 6 and order_status != 7 order by create_time desc
            </select>

            <select id="selectByPaymentModeIdCount" resultType="java.lang.Integer">
            select count(1)
            from szm_c_order_main
            where  create_iden = #{userId} and  store_id = #{storeId}  and payment_mode_id =#{payMentId}
            </select>
            <select id="selectByStoreIdCount" resultType="java.lang.Integer">
                select count(1)
                from szm_c_order_main
                where store_id = #{storeId}
            </select>
            <select id="selectDeliveryCount" resultType="java.lang.Integer">
                select count(1)
                from szm_c_order_main,smz_c_delivery_info
                where szm_c_order_main.store_id = #{storeId} and szm_c_order_main.delivery_info_id = #{userId}
                <if test="null != orderState">
                    and ${orderState}
                </if>
                <if test="null != delivyState">
                    and ${delivyState}
                </if>
            </select>

            <select id="selectByPaymentCount" resultType="java.lang.Integer">
            select count(1)
            from szm_c_order_main
            where  create_iden = #{userId} and  store_id = #{storeId}  and payment_mode_id =#{payMentId}
            and order_status &lt;&gt; 10
            </select>
            <select id="selectStateCount" resultMap="BaseResultMap">
            select *
            from szm_c_order_main
            where store_id = #{storeId,jdbcType=VARCHAR} and order_status = #{orderStatus} order by create_time desc
            </select>

            <select id="selectAllOrderCompanyId" resultMap="BaseResultMap">
                select
                <include refid="SqlList"/>
                from szm_c_order_main
                where order_status > 0 and is_forward = 0 and forward_company_id = #{companyId}
            </select>
            <select id="selectAllOrderCompanyIdAndUntreated" resultMap="BaseResultMap">
                select
                <include refid="SqlList"/>
                from szm_c_order_main
                where (order_status = 1 or order_status = 2 or order_status = 5 or order_status = 9) and is_forward = 0 and
                forward_company_id = #{companyId}
            </select>
            <select id="selectAllOrderCompanyIdAndFinish" resultMap="BaseResultMap">
                select
                <include refid="SqlList"/>
                from szm_c_order_main
                where order_status = 10 and is_forward = 0 and forward_company_id = #{companyId}
            </select>
            <select id="selectByPayType" resultMap="BaseResultMap">
                select
                <include refid="SqlList"/>
                from szm_c_order_main
                where create_iden = #{userId} and payment_mode_id = #{payType}
            </select>
            <select id="selectByMontu" resultType="java.lang.Double">
                select sum(r_1)
                from szm_c_order_main
                where create_iden = #{userId} and create_time &lt; #{stateTime} and payment_mode_id = #{payId} and is_return = 0
            </select>

            <select id="yingshou" resultType="java.lang.Double">
                select sum(r_1)
                from szm_c_order_main
                where create_iden = #{userId} and create_time >= #{stateTime} and create_time &lt; #{endTime}
                and payment_mode_id = #{payMentId} and is_return = 0
            </select>
            <select id="selectTimeqichu" resultType="java.lang.Double">
                select sum(royalty)
                from szm_c_order_main
                where store_id = #{storeId} and delivery_info_id = #{deliveryId} and order_status &lt; 5 and create_time &lt; #{stateTime}
            </select>
            <select id="selectOrderListCount" resultMap="BaseResultMap">
                select order_num,is_return
                from szm_c_order_main
                where store_id = #{storeId}
            </select>
            <select id="selectOrderListall" resultMap="BaseResultMap">
                select
                <include refid="SqlList"/>
                from szm_c_order_main
            </select>
            <select id="selectOrderSumByStoreId" resultType="java.lang.Double">
                select sum(a.r_1)
                from szm_c_order_main AS a inner join szm_c_user as b
                on a.create_iden = b.user_Id
                inner join
                szm_c_userinfo as c on b.user_id = c.user_id
                inner join szm_c_address as d on d.address_id = a.income_addr_id
                where a.store_id = #{storeId} and (a.order_status = 5 or a.order_status = 9 or a.order_status = 10) and
                a.payment_mode_id =#{payType}
                <if test="startTime !=null">
                    and a.distribution_time >= #{startTime} and a.distribution_time &lt;= #{endTime}
                </if>
                <if test="nameOrPhone!=null">
                    and (b.user_nickname like concat("%",#{nameOrPhone},"%")
                    or b.user_mobile like concat("%",#{nameOrPhone},"%") or
                    c.r_3 like concat("%",#{nameOrPhone},"%"))
                </if>
                <if test="userType!=null">
                    and d.r_1 = #{userType}
                </if>
            </select>
            <select id="selectOrderSumByStoreIdList" resultMap="BaseResultMap">
                select a.*
                from szm_c_order_main AS a inner join szm_c_user as b
                on a.user_id = b.user_id
                inner join
                szm_c_userinfo as c on b.user_id = c.user_id
                left join smz_c_order_returns as reOrder
                on a.order_main_id = reOrder.order_main_id

                where a.store_id = #{storeId} and (a.order_status = 5 or a.order_status = 9 or a.order_status = 10 or a.order_status
                = 8)
                and (reOrder.order_details_id is null or (reOrder.processstate != 2 and reOrder.returns_type != '退款'))
                <if test="payType != null">
                    and a.payment_mode_id =#{payType}
                </if>
                <if test="startTime!=null">
                    and distribution_time >= #{startTime} and distribution_time &lt;= #{endTime}
                </if>
                <if test="nameOrPhone!=null">
                    and (b.user_nickname like concat("%",#{nameOrPhone},"%")
                    or b.user_mobile like concat("%",#{nameOrPhone},"%") or
                    c.r_3 like concat("%",#{nameOrPhone},"%"))
                </if>
                <if test="userType!=null">
                    and EXISTS(select * from szm_c_address as addre
                    where a.user_id =addre.user_id
                    and addre.is_default_address = 0
                    and addre.r_1 = #{userType})
                </if>
                order by a.create_time desc
            </select>
            <select id="selectOrderSumByStoreIdListSum" resultType="java.lang.Double">
                select sum(a.r_1)
                from szm_c_order_main AS a inner join szm_c_user as b
                on a.user_id = b.user_Id
                inner join
                szm_c_userinfo as c on b.user_id = c.user_id
                inner join szm_c_address as d on d.address_id = a.income_addr_id
                where a.store_id = #{storeId} and (a.order_status = 5 or a.order_status = 9 or a.order_status = 10)
                <if test="payType != null">
                    and a.payment_mode_id =#{payType}
                </if>
                <if test="startTime!=null">
                    and distribution_time >= #{startTime} and distribution_time &lt;= #{endTime}
                </if>
                <if test="nameOrPhone!=null">
                    and (b.user_nickname like concat("%",#{nameOrPhone},"%")
                    or b.user_mobile like concat("%",#{nameOrPhone},"%") or
                    c.r_3 like concat("%",#{nameOrPhone},"%"))
                </if>
                <if test="userType!=null">
                    and d.r_1 = #{userType}
                </if>
                order by distribution_time desc
            </select>
            <select id="selectOrderSumByStoreIdListCount" resultType="java.lang.Integer">
                select count(1)
                from szm_c_order_main
                where store_id = #{storeId} and (order_status = 5 or order_status = 9 or order_status = 10)
                <if test="payType != null">
                    and payment_mode_id =#{payType}
                </if>
                <if test="startTime !=null">
                    and distribution_time >= #{startTime} and distribution_time &lt;= #{endTime}
                </if>
                order by distribution_time desc
            </select>
            <select id="selectOrderSumByStoreIdListOrderNum" resultType="java.lang.String">
                select order_num
                from szm_c_order_main
                where store_id = #{storeId} and (order_status = 5 or order_status = 9 or order_status = 10)
                <if test="payType != null">
                    and payment_mode_id =#{payType}
                </if>
                <if test="startTime !=null">
                    and distribution_time >= #{startTime} and distribution_time &lt;= #{endTime}
                </if>
                order by distribution_time desc
            </select>
            <select id="selectByPayTypeNew" resultMap="BaseResultMap">
                select
                <include refid="SqlList"/>
                from szm_c_order_main
                where create_iden = #{userId} and payment_mode_id = #{payType}

                <if test="stateTime!=null">
                    and distribution_time >= #{stateTime}
                </if>
                <if test="endTime!=null">
                    and distribution_time &lt;= #{endTime}
                </if>
            </select>
            <select id="selectNewOrderCount" resultType="java.lang.Integer">
                select count(1)
                from szm_c_order_main
                where store_id = #{storeId} and order_status >= 5 and pay_time BETWEEN #{stateTime} and #{endTime} and order_status != 6 and order_status != 7
            </select>
            <select id="selectNewOrderMoney" resultType="java.lang.Double">
                select sum(order_money)
                from szm_c_order_main
                where create_time >= #{staterTime} and create_time &lt;= #{endTime} and store_id = #{storeId} and order_status != 6 and order_status != 7
            </select>

            <select id="last" resultMap="BaseResultMap">
                select
                <include refid="SqlList"/>
                from szm_c_order_main
                where create_iden = #{userId} and (order_status = 5 or order_status = 9 or order_status = 10)
                order by order_main_id desc
            </select>
            <select id="selectStoreBusinessMoney" resultType="java.lang.Double">
                select sum(order_money)
                from szm_c_order_main
                where store_id = #{storeId} and order_status = 5
                <if test="stateTime!=null">
                    and create_time >= #{stateTime}
                </if>
                <if test="endTime!=null">
                    and create_time &lt;= #{endTime}
                </if>
            </select>
            <select id="selectStoreTodayCount" resultType="java.lang.Integer">
                select count(1)
                from szm_c_order_main
                where store_id = #{storeId} and order_status != 0 and order_status != 6
                <if test="stateTime!=null">
                    and create_time >= #{stateTime}
                </if>
                <if test="endTime!=null">
                    and create_time &lt;= #{endTime}
                </if>
            </select>
            <select id="selectStoreTodayMoneyCount" resultType="java.lang.Double">
                select sum(r_1)
                from szm_c_order_main
                where store_id = #{storeId} and order_status != 0 and order_status != 6
                <if test="stateTime!=null">
                    and create_time >= #{stateTime}
                </if>
                <if test="endTime!=null">
                    and create_time &lt;= #{endTime}
                </if>
            </select>
            <select id="selectDeliveryCountNew" resultType="java.lang.Integer">
                select count(1)
                from szm_c_order_main
                where store_id = #{storeId} and delivery_info_id is null and is_replenishment = 0

                and order_status = 2
                <if test="stateTime!=null">
                    and create_time >= #{stateTime}
                </if>
                <if test="endTime!=null">
                    and create_time &lt;= #{endTime}
                </if>
            </select>
            <select id="selectReturnOrderCount" resultType="java.lang.Integer">
                select count(1)
                from szm_c_order_main as a
                inner join smz_c_order_returns as b
                on a.order_main_id = b.order_main_id
                where a.store_id = #{storeId} and b.processstate = 1
                <if test="stateTime!=null">
                    and handling_time >= #{stateTime}
                </if>
                <if test="endTime!=null">
                    and handling_time &lt;= #{endTime}
                </if>
            </select>
            <select id="selectcdMoneyCount" resultType="java.lang.Double">
                select sum(c_d_money)
                from szm_c_order_main where create_iden = #{userId} and order_del_state = 0 and store_id = #{storeId}
                and order_status >= 5 and order_status != 6
                 order by
                create_time DESC
            </select>
            <select id="selectStoreByUserAndStoreIdAll1" resultMap="BaseResultMap">
                select
                <include refid="SqlList"/>
                from szm_c_order_main where create_iden = #{userId} and order_del_state = 0 and store_id = #{storeId}
                and order_status >= 5 and order_status != 6 and c_d_money > 0
                order by
                create_time DESC
            </select>
            <select id="selectStoreByUserAndStoreIdAll2" resultMap="BaseResultMap">
                select
                <include refid="SqlList"/>
                from szm_c_order_main where create_iden = #{userId} and order_del_state = 0 and store_id = #{storeId}
                and order_status >= 5 and order_status != 6
                <if test="startTime!=null">
                    and create_time >= #{startTime} and create_time &lt;=#{endTime}
                </if>
                order by
                create_time DESC
            </select>
            <select id="selectStoreByUserAndStoreIdAll3" resultMap="BaseResultMap">
                select
                <include refid="SqlList"/>
                from szm_c_order_main where create_iden = #{userId} and order_del_state = 0 and store_id = #{storeId}
                and order_status >= 5
                and order_status != 6 and is_return = 0 and is_invoice = 0 and (r_1 - bucket_price > 0)
                and create_time > #{limitDate}
                order by
                create_time DESC
            </select>
            <select id="selectStoreByUserAndStoreIdAllCount" resultType="integer">
                select count(1)
                from szm_c_order_main where create_iden = #{userId} and order_del_state = 0 and store_id = #{storeId}
                and order_status >= 5 and order_status != 6 and  c_d_money > 0
                order by
                create_time DESC
            </select>
            <select id="selectStoreByUserAndStoreIdAllMoney" resultType="double">
                select sum(c_d_money)
                from szm_c_order_main where create_iden = #{userId} and order_del_state = 0 and store_id = #{storeId}
                and order_status >= 5 and order_status != 6 and  c_d_money > 0
                order by
                create_time DESC
            </select>
            <select id="selectStoreByUserAndStoreIdAllR1" resultType="double">
                select sum(r_1-bucket_price)
                from szm_c_order_main where create_iden = #{userId} and order_del_state = 0 and store_id = #{storeId}
                and order_status >= 5 and order_status != 6
            </select>
            <select id="selectCountCount" resultType="java.lang.Integer">
                select count(1)
                from szm_c_order_main
                where store_id = #{storeId} and order_status = 10
                <if test="stateTime!=null">
                    and finish_time >= #{stateTime}
                </if>
                <if test="endTime!=null">
                    and finish_time &lt;= #{endTime}
                </if>
            </select>
            <select id="selectLastTimeOrder" resultMap="BaseResultMap">
                select *
                from szm_c_order_main where create_iden = #{userId} and store_id = #{storeId}
                and order_status > 0 and order_status &lt;&gt; 6 and order_main_id &lt; #{orderId}
                order by order_main_id DESC
            </select>
            <select id="selectCountNew" resultType="java.lang.Double">
                select sum(r_1)
                from szm_c_order_main where create_iden = #{userId} and order_del_state = 0 and store_id = #{storeId}
                and order_status >= 5 and order_status != 6 and c_d_money > 0
            </select>
            <select id="selectUserCountNew" resultType="java.lang.Integer">
                select count(1)
                from szm_c_order_main
                where create_iden = #{userId} and order_status >= 5 and order_status != 6 and c_d_money > 0
            </select>

            <select id="selectOrderByStoreIdAndDate" resultMap="BaseResultMap">
                select *
                from szm_c_order_main
                where store_id = #{storeId} and order_status > 0
                <if test="stateTime!=null">
                    and create_time >= #{stateTime}
                </if>
                <if test="endTime!=null">
                    and create_time &lt;= #{endTime}
                </if>
            </select>

            <select id="selectTodayOrderByStoreIdAndDate" resultMap="BaseResultMap">
                select *
                from szm_c_order_main
                where store_id = #{storeId} and order_status > 0 and order_status != 6 and user_name is not null
                <if test="stateTime!=null">
                    and create_time >= #{stateTime}
                </if>
                <if test="endTime!=null">
                    and create_time &lt;= #{endTime}
                </if>
                order by
                pay_time desc
            </select>

            <select id="selectTodayOrderByStoreIdAndDateSum" resultType="java.lang.Double">
                select coalesce(sum(r_1),0)
                from szm_c_order_main
                where store_id = #{storeId} and order_status > 0 and order_status != 6
                <if test="stateTime!=null">
                    and create_time >= #{stateTime}
                </if>
                <if test="endTime!=null">
                    and create_time &lt;= #{endTime}
                </if>
            </select>

            <select id="selectYiSongDaOrderByStoreId" resultMap="BaseResultMap">
                select *
                from szm_c_order_main
                where store_id = #{storeId} and order_status = 5 and user_name is not null
                order by
                pay_time desc
            </select>

            <select id="selectYiSongDaOrderByStoreIdCount" resultType="java.lang.Integer">
                select count(1)
                from szm_c_order_main as a inner join smz_c_delivery_info as b on a.order_main_id = b.order_main_id
                where a.store_id = #{storeId} and a.order_status = 5 and delivery_info_state in (2,3)
                <if test="startTime!=null">
                    and b.delivery_info_updatedate >= #{startTime}
                </if>
                <if test="endTime!=null">
                    and b.delivery_info_updatedate &lt;= #{endTime}
                </if>
            </select>

            <select id="selectYiSongDaOrderByStoreIdSum" resultType="java.lang.Double">
                select coalesce(sum(a.r_1),0)
                from szm_c_order_main as a inner join smz_c_delivery_info as b on a.order_main_id = b.order_main_id
                where a.store_id = #{storeId} and a.order_status = 5 and delivery_info_state in (2,3)
                <if test="startTime!=null">
                    and b.delivery_info_updatedate >= #{startTime}
                </if>
                <if test="endTime!=null">
                    and b.delivery_info_updatedate &lt;= #{endTime}
                </if>
            </select>

            <select id="selectBuckOrderByStoreId" resultMap="BaseResultMap">
                select order_main_id, order_num,ordermain.create_time,
                ordermain.update_time, create_iden, pay_time,
                ordermain.user_name, user_address, user_phone,
                delivery_info_id, payment_mode_id, income_addr_id,
                freight_payable, order_money, order_discounts,
                is_return, is_sms, distribution_date,
                distribution_time, is_replenishment, pay_num,
                order_status, user_content, order_del_state,
                ordermain.r_1, ordermain.r_2, ordermain.r_3, ordermain.r_4,
                ordermain.r_5,remind,royalty,up_price,send_state,include_dpt,
                is_forward,forward_company_id,company_name,ordermain.bucket_price,ordermain.bucket_beans,
                ordermain.bucket_pay_type,ordermain.is_bank_affirm,ordermain.yf_money,
                       ordermain.store_id,ordermain.ticket_user_id,
                       ordermain.ticket_price,
                       ordermain.yunfei,
                delivery.delivery_user_name,delivery.delivery_user_id,delivery.delivery_user_state
                from szm_c_order_main as ordermain
                LEFT JOIN smz_c_delivery_user as delivery ON ordermain.delivery_info_id = delivery.delivery_user_id
                inner JOIN szm_c_user as a ON ordermain.user_id = a.user_id
                inner JOIN szm_c_userinfo as b ON ordermain.user_id = b.user_id
                where ordermain.store_id = #{storeId}
                and EXISTS(select * from smz_c_delivery_info as deli
                where ordermain.order_main_id =deli.order_main_id
                and deli.r_3 =1)
                order by ordermain.create_time desc
            </select>


            <select id="selectOneOrderNumber" resultMap="BaseResultMap">
                select *
                from szm_c_order_main
                where create_iden = #{userId} limit 0,1
            </select>
            <select id="selectRepayList" resultType="java.lang.String">
                select
                   a.order_num
                   from
                   szm_c_order_main as a
                where EXISTS(select * from repay_buck_order as b where a.order_num =b.order_number and user_affrim = #{payState}
                and delivery_id = #{deliveryId})
                and store_id = #{storeId}
                <if test="stateTime!=null">
                    and create_time >= #{stateTime}
                </if>
                <if test="endTime!=null">
                    and create_time &lt;= #{endTime}
                </if>
                <if test="userId!=null">
                    and create_iden &lt;= #{userId}
                </if>
            </select>
            <select id="selectUserYfList" resultType="java.lang.Long">
                select a.create_iden
                from szm_c_order_main as a inner join szm_c_user as b
                on a.user_id = b.user_Id
                inner join
                szm_c_userinfo as cc on b.user_id = cc.user_id
                left join smz_c_order_returns as retu on a.order_num = retu.order_details_id
                where a.store_id = #{storeId} and payment_mode_id = 3
                and (retu.order_returns_id is null or (retu.processstate != 2 and retu.returns_type != '退款'))
                <if test="name!=null">
                    and (b.user_nickname like concat(concat("%",#{name}),"%")
                    or b.user_mobile like concat(concat("%",#{name}),"%") or
                    cc.r_3 like concat(concat("%",#{name}),"%"))
                </if>
                <if test="userId!=null">
                    and a.create_iden = #{userId}
                </if>
                <if test="stateTime!=null">
                    and a.create_time >= #{stateTime}
                </if>
                <if test="endTime!=null">
                    and a.create_time &lt;= #{endTime}
                </if>
                <if test="state!=null and state!= 0">
                    <if test="state == 1">
                        and EXISTS(select * from szm_c_address as addre
                        where a.create_iden =addre.user_id
                        and is_default_address = 0 and r_1 = 0)
                    </if>
                    <if test="state == 2">
                        and EXISTS(select * from szm_c_address as addre
                        where a.create_iden =addre.user_id
                        and is_default_address = 0 and r_1 = 1)
                    </if>
                    <if test="state == 3">
                        and not EXISTS(select * from szm_c_address as addre
                        where a.create_iden =addre.user_id)
                    </if>
                    <if test="state == 4">
                        and b.is_user_extract = 1
                    </if>
                </if>
                GROUP BY a.create_iden
            </select>
            <select id="selectUserYfMoney" resultType="java.lang.Double">
                select sum(r_1)
                from szm_c_order_main
                where store_id = #{storeId} and payment_mode_id = 3 and is_return = 0
                <if test="stateTime!=null">
                    and create_time >= #{stateTime}
                </if>
                <if test="endTime!=null">
                    and create_time &lt;= #{endTime}
                </if>
                <if test="userId!=null">
                    and create_iden = #{userId}
                </if>
            </select>
            <select id="selectStoreIdAll" resultMap="BaseResultMap">
                select
                <include refid="SqlList"/>
                from szm_c_order_main where payment_mode_id = 3 and order_del_state = 0 and store_id = #{storeId}
                and order_status >= 1 and order_status != 6 and order_status != 8
                <if test="userId!=null and userId != 0">
                    and create_iden = #{userId}
                </if>
                order by
                create_time asc
            </select>

            <select id="selectBankTransfer" resultType="java.util.Map">
                select
                  a.*
                from
                  bank_transfer a
                  left join szm_b_order_store_new b on b.order_num =  a.orderNum
                where
                  a.userId = #{userId}
                  and (b.delivery_status is null or b.delivery_status != 0)
                <if test="isPay==1">
                    and a.affirm = #{isPay}
                </if>
                <if test="isPay==0">
                    and (a.affirm is null or a.affirm = 0)
                </if>
                <if test="stateTime!=null">
                    and a.`time` >= #{stateTime}
                </if>
                <if test="endTime!=null">
                    and a.`time` &lt;= #{endTime}
                </if>
                order by a.`time` desc
            </select>

            <select id="selectBankTransferC" resultType="java.util.Map">
                select * from szm_c_bank_transfer
                where userId = #{userId}
                <if test="isPay==1">
                    and affirm = #{isPay}
                </if>
                <if test="isPay==0">
                    and (affirm is null or affirm = 0 or affirm = 2)
                </if>
                <if test="stateTime!=null">
                    and `time` >= #{stateTime}
                </if>
                <if test="endTime!=null">
                    and `time` &lt;= #{endTime}
                </if>
                order by `time` desc
            </select>

            <select id="selectBankTransferNumberSum" resultType="java.lang.Integer">
                select
                  coalesce(sum(a.`number`),0)
                from
                  bank_transfer a
                  left join szm_b_order_store_new b on b.order_num =  a.orderNum
                where
                  a.userId = #{userId}
                and (b.delivery_status is null or b.delivery_status != 0)
                <if test="isPay==1">
                    and a.affirm = #{isPay}
                </if>
                <if test="isPay==0">
                    and (a.affirm is null or a.affirm = 0)
                </if>
                <if test="stateTime!=null">
                    and `time` >= #{stateTime}
                </if>
                <if test="endTime!=null">
                    and `time` &lt;= #{endTime}
                </if>
            </select>
            <select id="selectBankTransferPriceSum" resultType="java.lang.Double">
                select
                  coalesce(sum(a.price),0)
                from
                  bank_transfer a
                  left join szm_b_order_store_new b on b.order_num =  a.orderNum
                where
                  a.userId = #{userId}
                and (b.delivery_status is null or b.delivery_status != 0)
                <if test="isPay==1">
                    and a.affirm = #{isPay}
                </if>
                <if test="isPay==0">
                    and (a.affirm is null or a.affirm = 0)
                </if>
                <if test="stateTime!=null">
                    and a.`time` >= #{stateTime}
                </if>
                <if test="endTime!=null">
                    and a.`time` &lt;= #{endTime}
                </if>
                <if test="wire==0">
                    and a.a = 1
                </if>
                <if test="wire==1">
                    and (a.a = 2 or a.a = 3)
                </if>
            </select>

            <select id="selectBankTransferYetPriceHeadSum" resultType="java.lang.Double">
                select
                  coalesce(sum(a.price),0)
                from
                  bank_transfer a
                  left join szm_b_order_store_new b on b.order_num =  a.orderNum
                where
                  a.userId = #{userId}
                and (a.affirm is null or a.affirm = 0)
                and (b.delivery_status is null or b.delivery_status != 0)
                <if test="stateTime!=null">
                    and a.`time` >= #{stateTime}
                </if>
                <if test="endTime!=null">
                    and a.`time` &lt;= #{endTime}
                </if>
                <if test="wire==0">
                    and a.a = 1
                </if>
                <if test="wire==1">
                    and (a.a = 2 or a.a = 3)
                </if>
            </select>

            <select id="selectBankTransferYetPriceSum" resultType="java.lang.Double">
                select
                  coalesce(sum(a.price),0)
                from
                  bank_transfer a
                  left join szm_b_order_store_new b on b.order_num =  a.orderNum
                where
                  a.userId = #{userId}
                and a.affirm = 1
                and (b.delivery_status is null or b.delivery_status != 0)
                <if test="stateTime!=null">
                    and a.`time` >= #{stateTime}
                </if>
                <if test="endTime!=null">
                    and a.`time` &lt;= #{endTime}
                </if>
                <if test="wire==0">
                    and a.a = 1
                </if>
                <if test="wire==1">
                    and (a.a = 2 or a.a = 3)
                </if>
            </select>

            <select id="selectBankTransferPriceAllSum" resultType="java.lang.Double">
                select coalesce(sum(price),0) from bank_transfer
                where userId = #{userId}
            </select>

            <select id="selectBankTransferYetPriceAllSum" resultType="java.lang.Double">
                select coalesce(sum(price),0) from bank_transfer
                where userId = #{userId}
                and affirm = 1
            </select>

            <select id="selectQuanOrder" resultMap="BaseResultMap">
                select
                <include refid="SqlList"/>
                from szm_c_order_main
            </select>
            <select id="selectStoreIdAll2" resultMap="BaseResultMap">
                select
                <include refid="SqlList"/>
                from szm_c_order_main where payment_mode_id = 3 and order_del_state = 0 and store_id = #{storeId}
                and order_status >= 1 and order_status != 6
                <if test="userId!=null and userId != 0">
                    and create_iden = #{userId}
                </if>
                order by
                create_time asc
            </select>
            <select id="selectUserYfListMoney" resultType="java.lang.Double">
                select sum(a.r_1)
                from szm_c_order_main as a inner join szm_c_user as b
                on a.user_id = b.user_Id
                inner join
                szm_c_userinfo as cc on b.user_id = cc.user_id
                left join smz_c_order_returns as retu on a.order_num = retu.order_details_id
                where a.store_id = #{storeId} and payment_mode_id = 3
                and (retu.order_returns_id is null or retu.processstate != 1 or retu.returns_type != '退款')
                <if test="name!=null">
                    and (b.user_nickname like concat(concat("%",#{name}),"%")
                    or b.user_mobile like concat(concat("%",#{name}),"%") or
                    cc.r_3 like concat(concat("%",#{name}),"%"))
                </if>
                <if test="stateTime!=null">
                    and a.create_time >= #{stateTime}
                </if>
                <if test="endTime!=null">
                    and a.create_time &lt;= #{endTime}
                </if>
                <if test="userId!=null and userId != 0">
                    and a.create_iden = #{userId}
                </if>
                <if test="state!=null and state!= 0">
                    <if test="state == 1">
                        and EXISTS(select * from szm_c_address as addre
                        where a.create_iden =addre.user_id
                        and is_default_address = 0 and r_1 = 0)
                    </if>
                    <if test="state == 2">
                        and EXISTS(select * from szm_c_address as addre
                        where a.create_iden =addre.user_id
                        and is_default_address = 0 and r_1 = 1)
                    </if>
                    <if test="state == 3">
                        and not EXISTS(select * from szm_c_address as addre
                        where a.create_iden =addre.user_id)
                    </if>
                    <if test="state == 4">
                        and b.is_user_extract = 1
                    </if>
                </if>
            </select>
            <select id="selectOrderSumByStoreIdMoney" resultType="java.lang.Double">
                select sum(a.r_1 - a.bucket_price)
                from szm_c_order_main AS a

                where a.store_id = #{storeId} and (a.order_status = 5 or a.order_status = 10)
                <if test="payType != null">
                    and a.payment_mode_id =#{payType}
                </if>
                <if test="startTime!=null">
                    and a.distribution_time >= #{startTime} and a.distribution_time &lt;= #{endTime}
                </if>
                <if test="nameOrPhone!=null">
                    and (a.user_name like concat("%",#{nameOrPhone},"%")
                    or a.user_phone like concat("%",#{nameOrPhone},"%") or a.order_num like concat("%",#{nameOrPhone},"%") or a.user_address like concat("%",#{nameOrPhone},"%"))
                </if>
                <if test="userType!=null">
                    and EXISTS(select * from szm_c_address as addre
                    where a.create_iden =addre.user_id
                    and addre.is_default_address = 0
                    and addre.r_1 = #{userType}
                    )
                </if>
            </select>
            <select id="selectOrderSumByStoreIdMoneyByStoreIds" resultType="com.example.waterstationbuyproducer.vo.StoreDoubleValueVo">
                select a.store_id as storeId, sum(a.r_1 - a.bucket_price) as number
                from szm_c_order_main AS a inner join szm_c_user as b
                on a.user_id = b.user_Id
                inner join
                szm_c_userinfo as c on b.user_id = c.user_id
                left join smz_c_order_returns as reOrder
                on a.order_main_id = reOrder.order_main_id

                where a.store_id in
                <foreach collection="storeIds" item="storeId" separator="," open="(" close=")">
                    #{storeId}
                </foreach>
                and (a.order_status = 5 or a.order_status = 9 or a.order_status = 10 or a.order_status
                = 8)
                and (reOrder.order_details_id is null or (reOrder.processstate != 2 and reOrder.returns_type != '退款'))
                <if test="payType != null">
                    and a.payment_mode_id =#{payType}
                </if>
                <if test="startTime!=null">
                    and distribution_time >= #{startTime} and distribution_time &lt;= #{endTime}
                </if>
                <if test="nameOrPhone!=null">
                    and (b.user_nickname like concat("%",#{nameOrPhone},"%")
                    or b.user_mobile like concat("%",#{nameOrPhone},"%") or
                    c.r_3 like concat("%",#{nameOrPhone},"%"))
                </if>
                <if test="userType!=null">
                    and EXISTS(select * from szm_c_address as addre
                    where a.create_iden =addre.user_id
                    and addre.is_default_address = 0
                    and addre.r_1 = #{userType}
                    )
                </if>
                group by a.store_id
            </select>

            <select id="selectOrderPayInfo" resultType="com.example.waterstationbuyproducer.vo.Turnover.PayInfo">
                select payment_mode_id as payModeId
                    , case payment_mode_id
                        when 1 then '线上微信'
                        when 2 then '线下付款'
                        when 3 then '月结付款'
                        when 4 then '现金'
                        when 5 then '线上支付宝'
                        when 6 then '水票支付'
                        when 7 then '银行转账'
                        else '' end as payName
                    , round(sum(a.r_1) - sum(a.bucket_price), 2) as payAmount
                from szm_c_order_main AS a
                left join smz_c_order_returns as reOrder on a.order_main_id = reOrder.order_main_id
                where a.store_id = #{storeId}
                and (a.order_status = 5 or a.order_status = 9 or a.order_status = 10 or a.order_status = 8)
                and (reOrder.order_details_id is null or (reOrder.processstate != 2 and reOrder.returns_type != '退款'))
                <if test="startTime!=null">
                    and distribution_time >= #{startTime} and distribution_time &lt;= #{endTime}
                </if>
                group by payment_mode_id
            </select>

            <select id="selectOrderPayAllInfo" resultType="com.example.waterstationbuyproducer.vo.Turnover.PayInfo">
                select payment_mode_id as payModeId
                    , case payment_mode_id
                        when 1 then '线上微信'
                        when 2 then '线下付款'
                        when 3 then '月结付款'
                        when 4 then '现金'
                        when 5 then '线上支付宝'
                        when 6 then '水票支付'
                        when 7 then '银行转账'
                        else '' end as payName
                    , round(sum(a.order_money), 2) as payAmount
                from szm_c_order_main AS a
                left join smz_c_order_returns as reOrder on a.order_main_id = reOrder.order_main_id
                where a.store_id = #{storeId}
                and (a.order_status > 0 and a.order_status &lt;&gt; 6)
                and (reOrder.order_details_id is null or (reOrder.processstate != 2 and reOrder.returns_type != '退款'))
                <if test="startTime!=null">
                    and a.pay_time >= #{startTime} and a.pay_time &lt;= #{endTime}
                </if>
                group by payment_mode_id
            </select>

            <select id="selectShopOrderPayInfo" resultType="com.example.waterstationbuyproducer.vo.Turnover.PayInfo">
                select r_1 as payModeId,
                case r_1
                when 0 then '现金'
                when 1 then '线下微信'
                when 2 then '线下支付宝'
                when 3 then '银行转账'
                when 4 then '月结付款'
                else '' end as payName
                , round(sum(order_money), 2) as payAmount
                from szm_b_user_extract
                where store_id = #{storeId} and order_status = 0
                <if test="startTime!=null">
                    and order_time >= #{startTime} and order_time &lt;= #{endTime}
                </if>
                group by r_1
            </select>

            <select id="selectFillInPayInfo" resultType="com.example.waterstationbuyproducer.vo.Turnover.PayInfo">
                select a.pay_name as payName
                , round(sum(order_money) - ifnull(sum(store.buck_number*store.buck_money), 0), 2) as payAmount
                from szm_b_order_store_new AS a
                left join pledge_buck_order as store on a.order_main_id = store.order_main_id
                where a.store_id = #{storeId} and (a.delivery_status = 1 or a.delivery_status = 2)
                <if test="startTime!=null">
                    and a.update_time >= #{startTime} and a.update_time &lt;= #{endTime}
                </if>
                group by a.pay_name
            </select>

            <select id="selectServicePayInfo" resultType="com.example.waterstationbuyproducer.vo.Turnover.PayInfo">
                select r_1 as payModeId
                    , case r_1
                    when 0 then '现金'
                    when 1 then '线上微信'
                    when 2 then '线下付款'
                    when 3 then '月结付款'
                    when 4 then '余额'
                    when 5 then '线下支付宝'
                    else '' end as payName
                    , round(sum(a.service_order_pirce),2) as payAmount
                from smz_c_service_order as a
                WHERE a.r_6 = #{storeId} and a.service_status_id = 1
                <if test="startTime!=null">
                    and a.service_order_create_date >= #{startTime} and a.service_order_create_date &lt;= #{endTime}
                </if>
                group by r_1
            </select>

            <select id="selectTicketPayInfo" resultType="com.example.waterstationbuyproducer.vo.Turnover.PayInfo">
                select payModeId, payName, round(sum(payAmount) - sum(sxAmount), 2) as payAmount from(
                    SELECT a.r_5 AS payModeId
                        , CASE a.r_5 WHEN 0 THEN '线上微信' WHEN 1 THEN '线上支付宝' WHEN 2 THEN '余额' WHEN 4 THEN '银行转账' WHEN 5 THEN '线下付款' ELSE '' END AS payName
                        , ifnull(SUM((a.`use` + a.unuse - a.r_1) * a.face), 0) AS payAmount
                        , CASE WHEN a.refund = 1 THEN SUM(a.unuse * a.r_2) ELSE 0 END as sxAmount
                    FROM wc_relevance a
                    WHERE a.r_4 = #{storeId}
                    <if test="startTime!=null">
                        and a.create_time >= #{startTime} and a.create_time &lt;= #{endTime}
                    </if>
                    GROUP BY r_5, a.refund
                ) a GROUP BY payModeId, payName
            </select>

            <select id="selectPledgeBuckAmountInfo" resultType="com.example.waterstationbuyproducer.vo.Turnover.PayInfo">
                select payName, source, sum(payAmount) payAmount from(
                    select a.r_1 as source
                    , a.r_4 as payModeId
                    , case when a.r_4 = '2' then '余额'
                        when a.r_4 = '3' then '线下支付宝'
                        when a.r_4 = '4' then '现金'
                        when a.r_4 = '5' then '银行转账'
                        when a.r_4 = '6' then '线下付款'
                        when a.r_4 = '1' then (case when a.r_1 != '0' || a.r_5 = 3 then '线下微信' else  '线上微信' end)
                        else '' end as payName
                    , sum(buck_number*buck_money) as payAmount
                    from pledge_buck_order as a
                    where store_id = #{storeId} and del_state != 2
                    <if test="startTime!=null">
                        and a.create_time >= #{startTime}
                    </if>
                    <if test="endTime!=null">
                        and a.create_time &lt;= #{endTime}
                    </if>
                    GROUP BY a.order_number, a.r_4, a.r_1
                )a group by payName, source
            </select>

            <select id="selectBackBuckAmountInfo" resultType="com.example.waterstationbuyproducer.vo.Turnover.PayInfo">
                select payName, source, sum(payAmount) payAmount from(
                    select a.r_1 as source,
                            case when a.r_4 = '2' then '余额'
                            when a.r_4 = '3' then '线下支付宝'
                            when a.r_4 = '4' then '现金'
                            when a.r_4 = '5' then '银行转账'
                            when a.r_4 = '6' then '线下付款'
                            when a.r_4 = '1' then (case when a.r_1 != '0' || a.r_5 = 3 then '线下微信' else  '线上微信' end)
                            else '' end as payName
                        , coalesce(sum(bucket.replace_number*bucket.buck_price),0) as payAmount
                    from pledge_buck_order as a
                    inner join szm_c_user as b on a.user_id = b.user_Id
                    inner join szm_c_userinfo as cc on b.user_id = cc.user_id
                    inner join bucket_buck_order as bucket on a.order_number = bucket.order_number
                    where a.store_id = #{storeId}
                        and a.del_state != 2
                        and a.store_id = bucket.new_order
                        and a.brand_id = bucket.replace_brand_id
                        and bucket.`replace` = 0
                        and bucket.user_affrim = 1
                        <if test="startTime!=null">
                            and a.create_time >= #{startTime}
                        </if>
                        <if test="endTime!=null">
                            and a.create_time &lt;= #{endTime}
                        </if>
                    GROUP BY a.order_number, a.r_4, a.r_1
                )a GROUP BY payName, source
            </select>

            <select id="selectDeductionBuckMoneyInfo" resultType="com.example.waterstationbuyproducer.vo.Turnover.PayInfo">
                select payName, source, sum(payAmount) payAmount from(
                    select a.r_1 as source,
                        case when a.r_4 = '2' then '余额'
                        when a.r_4 = '3' then '线下支付宝'
                        when a.r_4 = '4' then '现金'
                        when a.r_4 = '5' then '银行转账'
                        when a.r_4 = '6' then '线下付款'
                        when a.r_4 = '1' then (case when a.r_1 != '0' || a.r_5 = 3 then '线下微信' else  '线上微信' end)
                        else '' end as payName
                        , coalesce(sum(bucket.buck_number*bucket.buck_price),0) as payAmount
                    from pledge_buck_order as a
                    inner join szm_c_user as b on a.user_id = b.user_Id
                    inner join szm_c_userinfo as cc on b.user_id = cc.user_id
                    inner join bucket_buck_order as bucket on a.order_number = bucket.order_number
                    where a.store_id = #{storeId}
                        and a.del_state != 2
                        and bucket.`replace` = 1
                        and bucket.user_affrim = 1
                        and a.brand_id = bucket.r_1
                        and a.store_id = bucket.new_order
                        <if test="startTime!=null">
                            and a.create_time >= #{startTime}
                        </if>
                        <if test="endTime!=null">
                            and a.create_time &lt;= #{endTime}
                        </if>
                    GROUP BY a.order_number, a.r_4, a.r_1
                )a GROUP BY payName, source
            </select>

            <select id="selectDiffPriceInfo" resultType="com.example.waterstationbuyproducer.vo.Turnover.PayInfo">
                select payName, source, sum(payAmount) payAmount from(
                    select a.r_1 as source,
                        case when a.r_4 = '2' then '余额'
                            when a.r_4 = '3' then '线下支付宝'
                            when a.r_4 = '4' then '现金'
                            when a.r_4 = '5' then '银行转账'
                            when a.r_4 = '6' then '线下付款'
                            when a.r_4 = '1' then (case when a.r_1 != '0' || a.r_5 = 3 then '线下微信' else  '线上微信' end)
                            else '' end as payName
                        , coalesce(sum(bucket.money),0) as payAmount
                    from pledge_buck_order as a
                    inner join bucket_buck_order as bucket on a.order_number = bucket.order_number
                    where a.store_id = #{storeId}
                        and a.del_state != 2
                        and bucket.`replace` = 1
                        and bucket.user_affrim = 1
                        and a.brand_id = bucket.r_1
                        and a.store_id = bucket.new_order
                        <if test="startTime!=null">
                            and a.create_time >= #{startTime}
                        </if>
                        <if test="endTime!=null">
                            and a.create_time &lt;= #{endTime}
                        </if>
                    GROUP BY a.order_number, a.r_4, a.r_1
                )a GROUP BY payName, source
            </select>

            <select id="selectDiffPricePayInfo" resultType="com.example.waterstationbuyproducer.vo.Turnover.PayInfo">
                select  payName, sum(payAmount) as payAmount from (
                select case
                    when r4 = '2' then '余额'
                    when r4 = '3' then '线下支付宝'
                    when r4 = '4' then '现金'
                    when r4 = '5' then '银行转账'
                    when r4 = '1' then (case when r5 != 0 then '线下微信' else '线上微信' end)
                    else '' end as payName
                    , round(payAmount, 2) payAmount
                    , r4 payModeId
                    , r5 source
                from (
                    select a.order_number, max(a.money) payAmount, max(a.r_4) r4, max(a.r_5) r5
                    from repay_buck_order  as a
                    inner join szm_c_user as b on a.user_id = b.user_Id
                    inner join szm_c_userinfo as cc on b.user_id = cc.user_id
                    and (
                        EXISTS(
                            select 1 from szm_c_order_main as b
                            left join smz_c_order_returns as reOrder on b.order_main_id = reOrder.order_main_id
                            where a.order_number =b.order_num
                            and (reOrder.order_details_id is null or reOrder.processstate != 1)
                        )
                        or
                        EXISTS(
                            select 1 from szm_b_order_store_new as b where a.order_number =b.order_num and b.is_return = 0
                        )
                        or
                        EXISTS(
                            select 1 from szm_b_user_extract as b where a.order_number =b.order_num
                        )
                        or
                        EXISTS(
                            select 1 from debt_buck_order as b where a.order_number =b.order_number and b.brand_id = 0
                        )
                    )
                where a.store_id = #{storeId}  and a.r_4 is not null
                    <if test="startTime!=null">
                        and a.create_time >= #{startTime}
                    </if>
                    <if test="endTime!=null">
                        and a.create_time &lt;= #{endTime}
                </if>
                group by a.order_number
                )a
                )a group by payName
            </select>

            <select id="selectOrderSumByStoreIdListCountNewNew" resultType="java.lang.Integer">
                select sum(a.r_5)
                from szm_c_order_main AS a inner join szm_c_user as b
                on a.user_id = b.user_Id
                inner join
                szm_c_userinfo as c on b.user_id = c.user_id

                where a.store_id = #{storeId} and (a.order_status = 5 or a.order_status = 9 or a.order_status = 10)
                <if test="payType != null">
                    and a.payment_mode_id =#{payType}
                </if>
                <if test="startTime!=null">
                    and distribution_time >= #{startTime} and distribution_time &lt;= #{endTime}
                </if>
                <if test="nameOrPhone!=null">
                    and (b.user_nickname like concat("%",#{nameOrPhone},"%")
                    or b.user_mobile like concat("%",#{nameOrPhone},"%") or
                    c.r_3 like concat("%",#{nameOrPhone},"%"))
                </if>
                <if test="userType!=null">
                    and EXISTS(select * from szm_c_address as addre
                    where a.create_iden =addre.user_id
                    and addre.is_default_address = 0
                    and addre.r_1 = #{userType}
                    )
                </if>
            </select>
            <select id="selectOrderRepayMoney" resultType="java.lang.Double">
                select sum(money)
                from (select repay.money as money
                from szm_c_order_main AS a inner join szm_c_user as b
                on a.user_id = b.user_Id
                inner join
                szm_c_userinfo as c on b.user_id = c.user_id
                inner join szm_c_address as d on d.address_id = a.income_addr_id
                inner join repay_buck_order as repay on a.order_main_id = repay.order_main_id
                where a.store_id = #{storeId} and (a.order_status = 5 or a.order_status = 9 or a.order_status = 10)
                <if test="payType != null">
                    and a.payment_mode_id =#{payType}
                </if>
                <if test="startTime!=null">
                    and distribution_time >= #{startTime} and distribution_time &lt;= #{endTime}
                </if>
                <if test="nameOrPhone!=null">
                    and (b.user_nickname like concat("%",#{nameOrPhone},"%")
                    or b.user_mobile like concat("%",#{nameOrPhone},"%") or
                    c.r_3 like concat("%",#{nameOrPhone},"%"))
                </if>
                <if test="userType!=null">
                    and d.r_1 = #{userType}
                </if>
                group by repay.money) as repayOrder

            </select>
            <select id="selectOrderBucketMoney" resultType="java.lang.Double">
                select sum(money)
                from (select repay.money as money
                from szm_c_order_main AS a inner join szm_c_user as b
                on a.user_id = b.user_Id
                inner join
                szm_c_userinfo as c on b.user_id = c.user_id
                inner join szm_c_address as d on d.address_id = a.income_addr_id
                inner join bucket_buck_order as repay on a.order_main_id = repay.order_main_id
                where a.store_id = #{storeId} and (a.order_status = 5 or a.order_status = 9 or a.order_status = 10)
                <if test="payType != null">
                    and a.payment_mode_id =#{payType}
                </if>
                <if test="startTime!=null">
                    and distribution_time >= #{startTime} and distribution_time &lt;= #{endTime}
                </if>
                <if test="nameOrPhone!=null">
                    and (b.user_nickname like concat("%",#{nameOrPhone},"%")
                    or b.user_mobile like concat("%",#{nameOrPhone},"%") or
                    c.r_3 like concat("%",#{nameOrPhone},"%"))
                </if>
                <if test="userType!=null">
                    and d.r_1 = #{userType}
                </if>
                group by repay.money) as repayOrder
            </select>
            <select id="selectOrderDeatilNum" resultType="java.lang.Integer">
                select sum(deatil.order_product_num)
                from szm_c_order_main AS a inner join szm_c_user as b
                on a.user_id = b.user_Id
                inner join
                szm_c_userinfo as c on b.user_id = c.user_id
                inner join smz_c_order_details as deatil
                on a.order_num = deatil.order_main_id
                left join smz_c_order_returns as reOrder
                on a.order_main_id = reOrder.order_main_id

                where a.store_id = #{storeId} and (a.order_status = 5 or a.order_status = 9 or a.order_status = 10 or a.order_status
                = 8)
                and (reOrder.order_details_id is null or (reOrder.processstate != 2 and reOrder.returns_type != '退款'))
                <if test="payType != null">
                    and a.payment_mode_id =#{payType}
                </if>
                <if test="startTime!=null">
                    and distribution_time >= #{startTime} and distribution_time &lt;= #{endTime}
                </if>
                <if test="nameOrPhone!=null">
                    and (b.user_nickname like concat("%",#{nameOrPhone},"%")
                    or b.user_mobile like concat("%",#{nameOrPhone},"%") or
                    c.r_3 like concat("%",#{nameOrPhone},"%"))
                </if>
                <if test="userType!=null">
                    and EXISTS(select * from szm_c_address as addre
                    where a.user_id =addre.user_id
                    and addre.is_default_address = 0
                    and addre.r_1 = #{userType})
                </if>
            </select>
            <select id="selectOrderGroupNum" resultType="java.lang.Integer">
                select sum(orderGroup.order_product_num)
                from szm_c_order_main AS a inner join szm_c_user as b
                on a.user_id = b.user_Id
                inner join
                szm_c_userinfo as c on b.user_id = c.user_id
                inner join smz_c_group_order as orderGroup
                on a.order_num = orderGroup.order_main_id
                left join smz_c_order_returns as reOrder
                on a.order_main_id = reOrder.order_main_id

                where a.store_id = #{storeId} and (a.order_status = 5 or a.order_status = 9 or a.order_status = 10 or a.order_status
                = 8)
                and (reOrder.order_details_id is null or (reOrder.processstate != 2 and reOrder.returns_type != '退款'))
                <if test="payType != null">
                    and a.payment_mode_id =#{payType}
                </if>
                <if test="startTime!=null">
                    and distribution_time >= #{startTime} and distribution_time &lt;= #{endTime}
                </if>
                <if test="nameOrPhone!=null">
                    and (b.user_nickname like concat("%",#{nameOrPhone},"%")
                    or b.user_mobile like concat("%",#{nameOrPhone},"%") or
                    c.r_3 like concat("%",#{nameOrPhone},"%"))
                </if>
                <if test="userType!=null">
                    and EXISTS(select * from szm_c_address as addre
                    where a.create_iden =addre.user_id
                    and addre.is_default_address = 0
                    and addre.r_1 = #{userType})
                </if>
            </select>
            <select id="selectPledgMoney" resultType="java.lang.Double">
                select sum(pledg.buck_number*pledg.buck_money)
                from szm_c_order_main AS a inner join szm_c_user as b
                on a.create_iden = b.user_Id
                inner join
                szm_c_userinfo as c on b.user_id = c.user_id
                inner join pledge_buck_order as pledg
                on a.order_main_id = pledg.order_main_id
                left join smz_c_order_returns as reOrder
                on a.order_main_id = reOrder.order_main_id

                where a.store_id = #{storeId} and (a.order_status = 5 or a.order_status = 9 or a.order_status = 10 or a.order_status
                = 8)
                and (reOrder.order_details_id is null or (reOrder.processstate != 2 and reOrder.returns_type != '退款'))
                and pledg.r_1 = 0 and pledg.r_5 = 0
                <if test="payType != null">
                    and a.payment_mode_id =#{payType}
                </if>
                <if test="startTime!=null">
                    and distribution_time >= #{startTime} and distribution_time &lt;= #{endTime}
                </if>
                <if test="nameOrPhone!=null">
                    and (b.user_nickname like concat("%",#{nameOrPhone},"%")
                    or b.user_mobile like concat("%",#{nameOrPhone},"%") or
                    c.r_3 like concat("%",#{nameOrPhone},"%"))
                </if>
                <if test="userType!=null">
                    and EXISTS(select * from szm_c_address as addre
                    where a.create_iden =addre.user_id
                    and addre.is_default_address = 0
                    and addre.r_1 = #{userType}
                    )
                </if>
            </select>
            <select id="selectUserYfListMoneyNew" resultType="java.lang.Double">
                select sum(a.r_1 - a.bucket_price)
                from szm_c_order_main as a inner join szm_c_user as b
                on a.create_iden = b.user_Id
                inner join
                szm_c_userinfo as cc on b.user_id = cc.user_id
                left join smz_c_order_returns as retu on a.order_main_id = retu.order_main_id
                where a.store_id = #{storeId} and payment_mode_id = 3
                and (retu.order_returns_id is null or retu.processstate != 1 or retu.returns_type != '退款')
                <if test="name!=null">
                    and (b.user_nickname like concat(concat("%",#{name}),"%")
                    or b.user_mobile like concat(concat("%",#{name}),"%") or
                    cc.r_3 like concat(concat("%",#{name}),"%"))
                </if>
                <if test="stateTime!=null">
                    and a.create_time >= #{stateTime}
                </if>
                <if test="endTime!=null">
                    and a.create_time &lt;= #{endTime}
                </if>
                <if test="userId!=null and userId != 0">
                    and a.create_iden = #{userId}
                </if>
                <if test="state!=null and state!= 0">
                    <if test="state == 1">
                        and EXISTS(select * from szm_c_address as addre
                        where a.create_iden =addre.user_id
                        and is_default_address = 0 and r_1 = 0)
                    </if>
                    <if test="state == 2">
                        and EXISTS(select * from szm_c_address as addre
                        where a.create_iden =addre.user_id
                        and is_default_address = 0 and r_1 = 1)
                    </if>
                    <if test="state == 3">
                        and not EXISTS(select * from szm_c_address as addre
                        where a.create_iden =addre.user_id)
                    </if>
                    <if test="state == 4">
                        and b.is_user_extract = 1
                    </if>
                </if>
            </select>

            <select id="selectBankTransferPriceAllSumC" resultType="java.lang.Double">
                select coalesce(sum(price),0) from szm_c_bank_transfer
                where userId = #{userId} and ((a != 4 and a != 5) or (a = 4 and payMent = 3) or (a = 5 and payMent = '银行转账'))
            </select>

            <select id="selectBankTransferYetPriceAllSumC" resultType="java.lang.Double">
                select coalesce(sum(price),0) from szm_c_bank_transfer
                where userId = #{userId}
                and affirm = 1
                and ((a != 4 and a != 5) or (a = 4 and payMent = 3) or (a = 5 and payMent = '银行转账'))
            </select>
            <select id="selectByPaymentModeIdNew" resultMap="BaseResultMap">
                select
              *
                from szm_c_order_main as a
                left join smz_c_order_returns as reOrder
                on a.order_main_id = reOrder.order_main_id
                where create_iden = #{userId} and a.r_2 = #{storeId} and payment_mode_id =#{payMentId}
                and (reOrder.order_details_id is null or reOrder.returns_type != '退款' or (reOrder.processstate != 1 and reOrder.returns_type != '退款'))
                and order_status != 6 and order_status != 7 order by a.create_time asc


            </select>


            <select id="selectListUserTotal" resultType="java.lang.Integer">
                select count(1)
                from
                (select a.order_number from pledge_buck_order as a
                left join szm_c_order_main as b
                on a.order_main_id= b.order_main_id
                left join szm_b_order_store_new as c
                on a.order_number = c.order_num
                where a.store_id = #{storeId}
                <if test="userId!= null">
                    and a.user_id= #{userId}
                </if>
                and ((a.r_1 = 0 and b.payment_mode_id != 1 and b.payment_mode_id != 2 and b.payment_mode_id != 7
                and a.del_state = 0 and a.r_5 = 0) or (a.r_1 = 0 and a.r_5 = 1 and a.del_state = 0)
                or (a.r_1 = 0 and a.r_5 = 3 and a.del_state = 0))
                group by a.order_number ) t
            </select>

            <select id="selectBankTransferNumberCount" resultType="java.lang.Integer">
                select count(1) from szm_c_order_main as a
                left join smz_c_order_returns as reOrder
                on a.order_main_id = reOrder.order_main_id
                where a.store_id = #{storeId} and a.create_iden = #{userId}
                and (reOrder.order_details_id is null or reOrder.returns_type != '退款')
                <if test="isPay==1">
                    and is_bank_affirm = #{isPay}
                </if>
                <if test="isPay==0">
                    and (is_bank_affirm is null or is_bank_affirm = 0)
                </if>
                and payment_mode_id = 7
            </select>
            <select id="selectOtherByOrderNum" resultMap="BaseResultMap">
                select
                order_num ,r_1,r_2,bucket_price,is_bank_affirm
                from szm_c_order_main
                where order_num = #{orderNum,jdbcType=VARCHAR}
            </select>

            <select id="selectOtherByPaymentModeId" resultMap="BaseResultMap">
                select
                order_main_id,order_num ,bucket_price,is_bank_affirm,delivery_info_id,create_time,r_1,r_5
                from szm_c_order_main
                where create_iden = #{userId} and store_id = #{storeId} and payment_mode_id =#{payMentId}
                and order_status != 6 and order_status != 7 order by order_main_id asc
            </select>
            <select id="selectAllListYf" resultMap="BaseResultMap">
                select *
                from szm_c_order_main
                where payment_mode_id = 3 and order_del_state = 0
                and order_status >= 5 and order_status != 6
            </select>

            <select id="selectUserIdByStoreIdAndDate" resultType="java.lang.Long">
                select create_iden
                from szm_c_order_main
                where store_id = #{storeId} and order_status > 0
                <if test="stateTime!=null">
                    and create_time >= #{stateTime}
                </if>
                <if test="endTime!=null">
                    and create_time &lt;= #{endTime}
                </if>
            </select>
            <select id="selectByOrderNumOptimizt" resultMap="BaseResultMap">
                select payment_mode_id,
                is_return,
                order_status,is_bank_affirm
                from szm_c_order_main
                where order_num = #{orderNum,jdbcType=VARCHAR}
            </select>

            <select id="selectstoreOrderCount" resultType="java.lang.Integer"
                    parameterType="com.example.waterstationbuyproducer.entity.SzmCOrderMain">
                select count(1) from szm_c_order_main aa left join smz_c_delivery_info bb on aa.order_main_id = bb.order_main_id
                where 1 = 1
                <if test="orderStatus!=null">
                    and aa.order_status = #{orderStatus}
                <if test="orderStatus == 3">
                    and bb.delivery_info_state = 0
                </if>
                </if>
                <if test="isReplenishment!=null">
                    and aa.is_replenishment = #{isReplenishment}
                </if>
                <if test="deliveryInfoId!=null">
                    and aa.delivery_info_id = #{deliveryInfoId}
                </if>
                <if test="orderDelState!=null">
                    and aa.order_del_state = #{orderDelState}
                </if>
                <if test="r2!=null">
                    and aa.store_id = #{r2}
                </if>
            </select>

            <select id="selectOtherByOrderNum1" resultMap="BaseResultMap">
                select
                order_main_id,order_num ,r_2,create_iden,create_time,user_name,user_id
                from szm_c_order_main
                where order_num = #{orderNum,jdbcType=VARCHAR}
            </select>

            <select id="selectOtherR4ByOrderNum" resultType="java.lang.String">
                select
                r_4
                from szm_c_order_main
                where order_num = #{orderNum,jdbcType=VARCHAR}
            </select>

            <select id="selectThreeCollect" resultType="java.util.Map">

            select t.* from (
               SELECT
            `szm_c_order_main`.`pay_time` AS `time`,
            `szm_c_order_main`.`order_num` AS `orderNum`,
            1 AS `a`,
            `szm_c_order_main`.`r_5` AS `number`,
            `szm_c_order_main`.`r_1` AS `price`,
            `szm_c_order_main`.`delivery_info_id` AS `deliveryId`,
            `szm_c_order_main`.`is_bank_affirm` AS `affirm`,
            `szm_c_order_main`.`create_iden` AS `userId`
            FROM
            `szm_c_order_main`
            WHERE
            (
            `szm_c_order_main`.`create_iden` = #{userId}
            )
            UNION ALL
            SELECT
            `extract`.`order_time` AS `time`,
            `extract`.`order_num` AS `orderNum`,
            2 AS `a`,
            `extract`.`order_count` AS `number`,
            `extract`.`order_money` AS `price`,
            '' AS `deliveryId`,
            `extract`.`del_state` AS `affirm`,
            `extract`.`r_4` AS `userId`
            FROM
            `szm_b_user_extract` `extract`
            WHERE
            (`extract`.`r_4` = #{userId})
            UNION ALL
            SELECT
            `hand`.`order_time` AS `time`,
            `hand`.`order_num` AS `orderNum`,
            3 AS `a`,
            `hand`.`order_count` AS `number`,
            `hand`.`order_money` AS `price`,
            `hand`.`delivery_user` AS `deliveryId`,
            `hand`.`del_state` AS `affirm`,
            `hand`.`user_id` AS `userId`
            FROM
            `szm_b_order_store_new` `hand`
            WHERE
            (
            `hand`.`user_id` = #{userId}
            )
            ) t order by t.`time` desc
            </select>

            <select id="selectStoreUser"  resultType="com.example.waterstationbuyproducer.entity.BankTransferApp">
                SELECT
                    a.store_id as storeId,
                    b.user_id as userId
                FROM
                    szm_c_store a
                LEFT JOIN szm_c_user b ON a.store_id = b.r_2
                WHERE
                    a.store_id = #{storeId}
            </select>

            <select id="selectBankTransferPriceSumApp" parameterType="Long" resultType="com.example.waterstationbuyproducer.entity.BankTransferApp">
                SELECT
                  coalesce(sum(a.price),0) as reBankTransferTotal,
                (SELECT
                    coalesce(sum(a.price),0)
                FROM
                    bank_transfer a
                    LEFT JOIN szm_c_userinfo b on a.userId = b.user_id
                    LEFT JOIN szm_c_user c on  b.user_id = c.user_id
                    LEFT JOIN szm_b_order_store_new d on d.order_num =  a.orderNum
                WHERE 1=1
                    <if test="storeId !=null">
                        and c.r_2 = #{storeId}
                    </if>
                    <if test="userId != null">
                        and a.userId = #{userId}
                    </if>
                    and a.affirm = 1
                    and (d.delivery_status is null or d.delivery_status != 0)) as acBankTransferTotal,
                (SELECT
                    coalesce(sum(a.price),0)
                FROM
                    bank_transfer a
                    LEFT JOIN szm_c_userinfo b on a.userId = b.user_id
                    LEFT JOIN szm_c_user c on  b.user_id = c.user_id
                    LEFT JOIN szm_b_order_store_new d on d.order_num =  a.orderNum
                WHERE 1=1
                    <if test="storeId !=null">
                        and c.r_2 = #{storeId}
                    </if>
                    <if test="userId != null">
                        and a.userId = #{userId}
                    </if>
                    and (a.affirm is null or a.affirm = 0)
                    and (d.delivery_status is null or d.delivery_status != 0)) as unBankTransferTotal
                FROM
                    bank_transfer a
                    LEFT JOIN szm_c_userinfo b on a.userId = b.user_id
                    LEFT JOIN szm_c_user c on  b.user_id = c.user_id
                    LEFT JOIN szm_b_order_store_new d on d.order_num =  a.orderNum
                WHERE 1=1
                and (d.delivery_status is null or d.delivery_status != 0)
                <if test="storeId !=null">
                  and c.r_2 = #{storeId}
                </if>
                <if test="userId != null">
                  and a.userId = #{userId}
                </if>
            </select>

            <select id="selectStoreUserList"  resultType="com.example.waterstationbuyproducer.entity.BankTransferApp">
                SELECT
                    a.time,
                    a.userId,
                    c.user_nickname as userNickname,
                    b.userinfo_pic_url as userinfoPicUrl,
                    b.r_3 as r3,
                    b.r_4 as r4,
                    c.user_mobile as userMobile,
                    c.r_2,
                    d.r_1 as customerType,
                    (
                    SELECT
                      COALESCE (sum(a1.price), 0)
                    FROM
                      bank_transfer a1
                      left join szm_b_order_store_new b on b.order_num =  a1.orderNum
                WHERE
                        a1.userId = a.userId
                        AND (a1.affirm IS NULL OR a1.affirm = 0)
                        AND (b.delivery_status is null or b.delivery_status != 0)
                        <if test="stateTime!=null">
                            and a1.time >= #{stateTime}
                        </if>
                        <if test="endTime!=null">
                            and a1.time &lt;= #{endTime}
                        </if>
                        ) as userUnBankTransferTotal
                FROM
                    bank_transfer a
                    LEFT JOIN szm_c_userinfo b on a.userId = b.user_id
                    LEFT JOIN szm_c_user c on  b.user_id = c.user_id
                    LEFT JOIN szm_c_address d ON d.user_id = a.userId
                    LEFT JOIN szm_b_order_store_new e on e.order_num =  a.orderNum
                WHERE
                    c.r_2 = #{storeId}
                    and d.is_default_address = 0
                    and (e.delivery_status is null or e.delivery_status != 0)
                <if test="concat != null and concat != ''">
                    and (
                    c.user_nickname like concat("%",#{concat},"%")
                    or b.r_3 like concat("%",#{concat},"%")
                    or b.userinfo_mobile like concat("%",#{concat},"%")
                    )
                </if>
                <if test="stateTime!=null">
                    and a.time >= #{stateTime}
                </if>
                <if test="endTime!=null">
                    and a.time &lt;= #{endTime}
                </if>
                GROUP BY userId
                <if test="moneySort !=null and moneySort == 1">
                    ORDER BY userUnBankTransferTotal ASC
                </if>
                <if test="moneySort !=null and moneySort == 2">
                    ORDER BY userUnBankTransferTotal DESC
                </if>
            </select>

            <select id="userUnBankTransferTotal" parameterType="Long" resultType="com.example.waterstationbuyproducer.entity.BankTransferApp">
                SELECT
                    COALESCE (sum(a.price), 0) as userUnBankTransferTotal,
                    COUNT(DISTINCT a.orderNum)as subscriptNumber
                FROM
                    bank_transfer a
                    left join szm_b_order_store_new b on b.order_num = a.orderNum
                WHERE
                    a.userId = #{userId}
                <if test="stateTime!=null">
                    and a.time >= #{stateTime}
                </if>
                <if test="endTime!=null">
                    and a.time &lt;= #{endTime}
                </if>
                AND (a.affirm IS NULL OR a.affirm = 0)
                and (b.delivery_status is null or b.delivery_status != 0)
            </select>

            <select id="userAcBankTransferTotal" parameterType="Long" resultType="com.example.waterstationbuyproducer.entity.BankTransferApp">
                SELECT
                COALESCE (sum(a.price), 0) as userAcBankTransferTotal
                FROM
                bank_transfer a
                left join szm_b_order_store_new b on b.order_num =  a.orderNum
                WHERE
                a.userId = #{userId}
                <if test="stateTime!=null">
                    and a.time >= #{stateTime}
                </if>
                <if test="endTime!=null">
                    and a.time &lt;= #{endTime}
                </if>
                and  a.affirm = 1
                and (b.delivery_status is null or b.delivery_status != 0)
            </select>

            <select id="userReBankTransferTotal" parameterType="Long" resultType="com.example.waterstationbuyproducer.entity.BankTransferApp">
                SELECT
                COALESCE (sum(a.price), 0) as userReBankTransferTotal
                FROM
                bank_transfer a
                left join szm_b_order_store_new b on b.order_num =  a.orderNum
                WHERE
                a.userId = #{userId}
                <if test="stateTime!=null">
                    and a.time >= #{stateTime}
                </if>
                <if test="endTime!=null">
                    and a.time &lt;= #{endTime}
                </if>
                and (b.delivery_status is null or b.delivery_status != 0)
            </select>

            <select id="selectUser" parameterType="Long" resultType="com.example.waterstationbuyproducer.entity.BankTransferApp">
              SELECT
                a.user_id AS userId,
                a.user_nickname AS userNickname,
                a.user_mobile AS userMobile,
                b.userinfo_pic_url AS userinfoPicUrl,
                b.r_3 AS r3,
                c.province,
                c.city,
                c.area,
                c.street
              FROM
                szm_c_user a
              LEFT JOIN szm_c_userinfo b ON a.user_id = b.user_id
              LEFT JOIN szm_c_address c ON a.user_id = c.user_id
              WHERE
                a.user_id = #{userId}
                and (c.is_default_address is null or c.is_default_address = 0)
                and c.state = 0
            </select>
            <select id="selectBankTransferApp" resultType="com.example.waterstationbuyproducer.entity.BankTransferApp">
                select
                    a.orderNum,
                    a.time,
                    a.a,
                    a.number,
                    a.price,
                    a.affirm,
                    a.userId
                from
                  bank_transfer a
                  left join szm_b_order_store_new b on b.order_num =  a.orderNum
                where
                    a.userId = #{userId}
                    and (b.delivery_status is null or b.delivery_status != 0)
                    <if test="affirm==1">
                        and a.affirm = #{affirm}
                    </if>
                    <if test="affirm==0">
                        and (a.affirm is null or a.affirm = 0)
                    </if>
                    order by a.`time` desc
            </select>

            <select id="singlePersonvalet"  resultType="com.example.waterstationbuyproducer.entity.BankTransferApp">
              SELECT
                order_products as orderProducts,
                order_count as orderProductNum,
                up_price as upPrice,
                order_time as time
              FROM
                szm_b_order_store_new
              WHERE
                order_num = #{orderNum}
            </select>

            <select id="merchantValet"  resultType="com.example.waterstationbuyproducer.entity.BankTransferApp">
              SELECT
                order_products as orderProducts,
                order_time as time
              FROM
                szm_b_user_extract
              WHERE
                order_num = #{orderNum}
            </select>

            <select id="pledgeBucket"  resultType="com.example.waterstationbuyproducer.entity.BankTransferApp">
              SELECT
                coalesce(sum(buck_number*buck_money),0) as pledgeBucketTotal
              FROM
                pledge_buck_order
              WHERE
                order_number = #{orderNum}
                and r_1 = 2
            </select>

            <select id="selectPledgeBucket"  resultType="com.example.waterstationbuyproducer.entity.BankTransferApp">
              SELECT
                brand_name as brandName,
                buck_number as buckNumber,
                buck_money as buckMoney
              FROM
                pledge_buck_order
              WHERE
                order_number  = #{orderNum}
                and r_5 != 1
                and r_3 is not null
            </select>

            <select id="selectWaterTicket"  resultType="com.example.waterstationbuyproducer.entity.BankTransferApp">
              SELECT
                a.price,
                (a.waterNum - a.presenter) as  waterNum,
                c.product_title as productTitle,
                a.refund,
                a.a
              FROM
                water_coupon_record_user  as a
                left join szm_c_product_model as  b on a.skuId = b.product_model_id
                left join szm_c_product as  c on b.product_id = c.product_id
              WHERE
                a.orderNum = #{orderNum}
            </select>

            <select id="selectSubscript"  resultType="com.example.waterstationbuyproducer.entity.BankTransferApp">
              SELECT
                COUNT(DISTINCT a.orderNum) as orderNumTotal,
                (
                  select
                    COUNT(DISTINCT a.orderNum)
                  from
                    bank_transfer a
                    left join szm_b_order_store_new b on b.order_num =  a.orderNum
                  where
                    a.userId = #{userId}
                    and a.affirm =1
                    and (b.delivery_status is null or b.delivery_status != 0)) as receivedSubscriptNumber
              FROM
                bank_transfer a
                left join szm_b_order_store_new b on b.order_num =  a.orderNum
              WHERE
                a.userId = #{userId}
                and (b.delivery_status is null or b.delivery_status != 0)
            </select>

            <select id="selectSubscriptByStoreId" resultType="java.lang.Integer">
                  select
                    COUNT(DISTINCT a.orderNum)
                  from
                   bank_transfer a
                    left join szm_b_order_store_new b on b.order_num =  a.orderNum
                    inner join szm_c_user as usr on a.userId = usr.user_id
                  where
                    usr.r_2 = #{storeId}
                    and ( a.affirm IS NULL OR a.affirm = 0 )
                    and (b.delivery_status is null or b.delivery_status != 0)
            </select>

            <select id="selectuserShopList"  resultType="com.example.waterstationbuyproducer.entity.BankTransferApp">
              SELECT
                a.order_main_id,
                a.order_num as orderNum,
                b.product_skuname as productSkuname,
                b.order_product_num as orderProductNum,
                cast((b.order_details_product_price/b.order_product_num) as decimal(18,2)) as yhprice,
                a.up_price as upPrice,
                a.create_time as time
              FROM
                smz_c_order_details b
              LEFT JOIN szm_c_order_main a on a.order_num = b.order_main_id
              WHERE
                a.order_num = #{orderNum}
            </select>

            <select id="confirmCollection"  resultType="com.example.waterstationbuyproducer.entity.BankTransferApp">
                SELECT
                    order_num as orderNum,
                    is_bank_affirm as isBankAffirm
                FROM
                    szm_c_order_main
                WHERE
                    order_num = #{orderNum}
            </select>

            <update id="updateIsBankAffirm" parameterType="com.example.waterstationbuyproducer.entity.BankTransferApp">
                update szm_c_order_main set is_bank_affirm = #{isBankAffirm,jdbcType=INTEGER} where order_num = #{orderNum}
            </update>
    <update id="updateUserId">
        update szm_c_order_main
        set user_id = #{newUserId},create_iden = #{newUserId} where user_id = #{userId}
    </update>

    <select id="selecComboPackage"  resultType="com.example.waterstationbuyproducer.entity.BankTransferApp">
                SELECT
                    a.r_1 as productSkuname,
                    a.order_product_num as orderProductNum,
                    b.shopgroup_price as price,
                    a.group_order_create_date as time,
                    a.r_3 as doller,
                    c.up_price as upPrice,
                    a.r_4 as r4
                FROM
                    smz_c_group_order a
                    LEFT JOIN  szm_c_shopgroup b on b.shopgroup_id = a.shopgroup_id
                    LEFT JOIN szm_c_order_main c ON c.order_num = a.order_main_id
                WHERE
                    a.order_main_id = #{orderNum}
            </select>

            <select id="selectCustomerSubscript" resultType="java.lang.Integer">
                select
                  sum(t.subscriptNumber) as customerSubscriptTotal
                 from (
                    SELECT
                      COUNT(DISTINCT a.orderNum)as subscriptNumber
                    FROM
                      bank_transfer a
                      LEFT JOIN szm_c_userinfo b on a.userId = b.user_id
                      LEFT JOIN szm_c_user c on  b.user_id = c.user_id
                      LEFT JOIN szm_b_order_store_new d on d.order_num =  a.orderNum
                    WHERE
                      c.r_2 = #{storeId}
                      and (a.affirm is null or a.affirm = 0)
                      and (d.delivery_status is null or d.delivery_status != 0)
                       GROUP BY a.userId
                 )t
            </select>
            <select id="selectOrderListByStoreId" resultMap="BaseResultMap">
                SELECT * from szm_c_order_main scom WHERE scom.r_2 = #{storeId} and scom.delivery_info_id = 0
            </select>

            <select id="selectStoreOrderDetails"  resultType="com.example.waterstationbuyproducer.entity.BankTransferApp">
                SELECT
                    a.time,
                    a.userId,
                    c.user_nickname as userNickname,
                    b.userinfo_pic_url as userinfoPicUrl,
                    b.r_3 as r3,
                    b.r_4 as r4,
                    c.user_mobile as userMobile,
                    c.r_2,
                    d.r_1 as customerType,
                    a.orderNum,
                    a.a,
                    a.number,
                    a.price,
                    a.affirm,
                    f.delivery_user_name as deliveryUserName,
                    f.delivery_user_phone as deliveryUserPhone
                FROM
                    bank_transfer a
                    LEFT JOIN szm_c_userinfo b on a.userId = b.user_id
                    LEFT JOIN szm_c_user c on  b.user_id = c.user_id
                    LEFT JOIN szm_c_address d ON d.user_id = a.userId
                    LEFT JOIN smz_c_delivery_user f ON f.delivery_user_id = a.deliveryId
                    LEFT JOIN szm_b_order_store_new g on g.order_num =  a.orderNum
                WHERE
                    c.r_2 = #{storeId}
                    and d.is_default_address = 0
                    and (g.delivery_status is null or g.delivery_status != 0)
                    <if test = "userId != null">
                        and a.userId = #{userId}
                    </if>
                    <if test="affirm==1">
                        and affirm = #{affirm}
                    </if>
                    <if test="affirm==0">
                        and (affirm is null or affirm = 0)
                    </if>
                    <if test="concat != null and concat != ''">
                        and (
                        c.user_nickname like concat("%",#{concat},"%")
                        or b.r_3 like concat("%",#{concat},"%")
                        or b.userinfo_mobile like concat("%",#{concat},"%")
                        )
                    </if>
                    <if test="stateTime!=null">
                        and a.time >= #{stateTime}
                    </if>
                    <if test="endTime!=null">
                        and a.time &lt;= #{endTime}
                    </if>
                order by a.time desc
            </select>

            <select id="selectOrderMains" resultMap="BaseResultMap">
                SELECT
                    order_main_id,
                    create_iden,
                    r_5,
                    up_price,
                    order_num,
                    update_time,
                    user_name,
                    user_phone,
                    income_addr_id,
                    royalty,
                    r_2,
                    user_address,
                    order_money
                FROM
                  szm_c_order_main
                WHERE
                  order_status = 2
                  AND is_replenishment = 0
                  AND order_del_state = 0
                  <if test="deliveryUserId!=null">
                    AND delivery_info_id = #{deliveryUserId}
                  </if>
                  <if test="storeId!=null">
                    AND store_id = #{storeId}
                  </if>
                  ORDER BY
                  create_time DESC
            </select>

            <select id="selectNoDeal" resultMap="BaseResultMap">
                SELECT
                    aa.*
                FROM
                    szm_c_order_main aa
                        LEFT JOIN smz_c_delivery_info bb ON aa.order_main_id = bb.order_main_id
                        LEFT JOIN szm_b_wallet_detail cc ON cc.order_num = aa.order_num
                WHERE
                    aa.order_status IN ( 2, 3, 4,5 )
                  AND bb.delivery_info_state in (2,3)
                  AND aa.create_time > '2024-11-29 00:00:00'
                  AND cc.wallet_id is null
                  AND aa.payment_mode_id = 1 and aa.store_id = #{storeId}
            </select>

            <select id="selectYfNoDeal" resultMap="BaseResultMap">

                SELECT
                    aa.*
                FROM
                    szm_c_order_main aa
                        LEFT JOIN smz_c_delivery_info bb ON aa.order_main_id = bb.order_main_id
                        LEFT JOIN order_yf dd ON dd.order_number = aa.order_num
                WHERE
                    aa.order_status IN ( 2, 3, 4,5 )
                  AND bb.delivery_info_state in (2,3)
                  AND aa.create_time > '2024-11-29 00:00:00'
                  AND (aa.payment_mode_id = 3)
                  and dd.order_yf_id is null
            </select>
    <select id="countByCuidan" resultType="java.lang.Integer">
        select count(1) from szm_c_order_main where cuidan = #{cuidan} and store_id = #{storeId} and order_status IN ( 1,2, 3, 4 )
    </select>
    <select id="selectByCuidan" resultMap="BaseResultMap">
        select
        <include refid="SqlList"/>
        from szm_c_order_main
        where cuidan = #{cuidan} and store_id = #{storeId} and order_status IN ( 1,2, 3, 4 )
    </select>
    <select id="selectZitiByMobile" resultMap="BaseResultMap">
        select
        <include refid="SqlList"/>
        from szm_c_order_main
        where user_phone like CONCAT("%",#{mobile},"%") and ziti = 0 and order_main_id = #{orderId}
    </select>
    <select id="selectByUserIdAndStoreIdAndPayTimeDikou" resultMap="BaseResultMap">
        select
        <include refid="SqlList"/>
        from szm_c_order_main
        where dikou > 0 and store_id = #{storeId} and user_id = #{userId} and order_status != 6 and order_status != 7 and order_status != 8 and DATE_FORMAT( pay_time, '%Y/%m' ) = #{payTime}
    </select>
    <select id="selectTiaoJianResult" resultMap="BaseResultMap">
        SELECT * FROM szm_c_order_main WHERE store_id = 43 and order_num > '202310010000000000000'
    </select>

    <select id="querysmcorderAll"  resultType="com.example.waterstationbuyproducer.vo.RetuenOrderList">
        SELECT delivery_user_name,
               s.`user_name` AS userName,
               s.`user_phone` AS userPhone,
               s.`user_address` AS userAddress,
               s.`order_discounts` AS discounPrice,
               DATE_ADD(s.`create_time`,
                        INTERVAL 1 DAY) AS endTime,
               s.`delivery_info_id` AS deliverId,
               v.`store_ame` AS storeName,
               s.`payment_mode_id` AS PayMentId,
               s.`r_1` AS orderTotalPrice,
               s.`order_num` AS orderNumber,
               s.`order_main_id` AS orderId,
               s.`r_5` AS orderTotalNumber,
               s.`order_money` AS orderPrice,
               s.`order_status` AS orderState,
               s.`create_time` AS orderDate,
               t.`delivery_user_name` as deliveryName,
               CASE
                   WHEN s.`order_status` = '0'
                       AND a.`payment_mode_id` = '1' THEN
                       '微信付款'
                   WHEN s.`order_status` = '0'
                       AND a.`payment_mode_id` = '4' THEN
                       '钱包代付款'
                   WHEN s.`order_status` = '1' THEN
                       a.`payment_mode_name`
                   WHEN s.order_status = '2' THEN
                       '代发货'
                   WHEN s.`order_status` = '3' THEN
                       '已发货'
                   WHEN s.order_status ='4' THEN
                       '代签收'
                   WHEN s.order_status = '5' THEN
                       '已签收'
                   WHEN s.`order_status` = '6' THEN
                       '已取消'
                   WHEN s.order_status = '7' THEN
                       '拒单'
                   WHEN s.order_status ='8' then
                       CASE
                           WHEN b.`returns_type`='退款' THEN
                               '退款'
                           ELSE '退货'
                           END
                   WHEN s.order_status = '9' THEN
                       '已评价'
                   WHEN s.order_status = '10' THEN
                       '已完成'
                   END AS newOrderState,
               CASE
                   WHEN s.`order_status` ='1'
                       OR s.order_status = '2' then
                       CASE
                           WHEN s.`is_return` = '1'
                               AND b.`processstate` !='2' THEN
                               1
                           END
                   WHEN s.order_status = '5'
                       AND s.`is_return`='1' THEN
                       0
                   END AS isReturnMoney,
               CASE
                   WHEN s.`order_status` ='1'
                       OR s.order_status = '2' then
                       CASE
                           WHEN s.`is_return` = '1'
                               AND b.`processstate` !='2' THEN
                               0
                           END
                   WHEN s.order_status = '5'
                       AND s.`is_return`='1' THEN
                       1
                   END AS isReturnGoods
        FROM smz_c_delivery_user t
                 LEFT JOIN szm_c_order_main s
                           ON t.`delivery_user_id` = s.`delivery_info_id`
                 LEFT JOIN szm_c_store v
                           ON v.`store_id` = s.`store_id`
                 LEFT JOIN smz_c_payment_mode a
                           ON a.`payment_mode_id` = s.`payment_mode_id`
                 LEFT JOIN smz_c_order_returns b
                           ON b.`order_details_id` = s.`order_num`
        WHERE s.`store_id` = #{storeId}
            <if test="userId != null and userId != ''">
                and user_id = #{userId}
            </if>
            <if test="username != null and username != ''">
                and (user_name like CONCAT("%",#{username},"%") or user_phone like CONCAT("%",#{username},"%") or user_address like CONCAT("%",#{username},"%")  or order_num like CONCAT("%",#{username},"%"))
            </if>
            <if test="addressId != null and addressId != ''">
                and income_addr_id = #{addressId}
            </if>
            <if test="startTime != null and startTime != ''">
                and create_time >= #{startTime} and create_time &lt; #{endTime}
            </if>


    </select>

    <!-- findTransferOrderNoArrivedByTime --> 

    <select id="findTransferOrderNoArrivedByTime" resultMap="BaseResultMap">
        select ordermain.order_main_id, ordermain.order_num,ordermain.create_time,
        ordermain.update_time, ordermain.create_iden, ordermain.pay_time,
        ordermain.user_name, ordermain.user_address, ordermain.user_phone,
        ordermain.delivery_info_id, ordermain.payment_mode_id, ordermain.income_addr_id,
        ordermain.freight_payable, ordermain.order_money, ordermain.order_discounts,
        ordermain.is_return, ordermain.is_sms, ordermain.distribution_date,
        ordermain.distribution_time, ordermain.is_replenishment, ordermain.pay_num,
        ordermain.order_status, ordermain.user_content, ordermain.order_del_state,
        ordermain.r_1, ordermain.r_2, ordermain.r_3, ordermain.r_4,ordermain.cuidan,
        ordermain.r_5,ordermain.remind,ordermain.royalty,ordermain.up_price,ordermain.send_state,ordermain.include_dpt,ordermain.back,ordermain.daike,
        ordermain.is_forward,forward_company_id,company_name,ordermain.bucket_price,ordermain.bucket_beans,
        ordermain.bucket_pay_type,ordermain.is_bank_affirm,ordermain.yf_money,
        ordermain.store_id,ordermain.ticket_user_id,
        ordermain.ticket_price,
        ordermain.yunfei,
        ordermain.drainageuserid,
        ordermain.drainageid,
        ordermain.dikou,
        ordermain.ordersource,
        ordermain.apptoken,
        ordermain.picurl,
        ordermain.lon,
        ordermain.lat,
        ordermain.yuyuetime,
        ordermain.transtatus,
        ordermain.oldstoreid,
        ordermain.tranprice,
        ordermain.oldprice,
        ordermain.platformprice,
        ordermain.mark,
        ordermain.exception,
        ordermain.isprint,
        ordermain.appkey,
        ordermain.ziti
        from szm_c_order_main ordermain left join smz_c_delivery_info bb on bb.order_main_id = ordermain.order_main_id where
        ordermain.transtatus = 3 and ordermain.order_status in (2,3,4) and bb.delivery_info_state = 0 and ordermain.create_time &lt;= #{startTime}
    </select>

    <!-- selectByStoreIdAndUserNameLikeAndUserContentLike --> 

    <select id="selectByStoreIdAndUserNameLikeAndUserContentLike" resultMap="BaseResultMap">
        select
        <include refid="SqlList"/>
        from szm_c_order_main
        where store_id = #{storeId} and order_status != 6 and order_status != 7 and order_status != 8
        and (user_name like CONCAT("%",#{userName},"%")
        or user_phone like CONCAT("%",#{userName},"%")
        or user_address like CONCAT("%",#{userName},"%")
        or order_num like CONCAT("%",#{userName},"%"))
        and (user_content like CONCAT("%",#{userContent},"%") )
    </select>

    <!-- updatemark --> 

    <update id="updatemark">
    update szm_c_order_main
    set mark = #{mark}
    where order_main_id = #{orderMainId}
    </update>
    
    
    </mapper>