(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["financereport"],{"117e":function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e._self._c;return t("div",{staticClass:"padding-bottom-30"},[t("div",[t("div",{staticClass:"content-box"},[t("div",{staticClass:"content-item2"},[[t("div",[t("span",[e._v("今日水票购买数量：")]),t("span",{staticClass:"color-red"},[e._v(e._s(e.totalBuyAllTicket))])]),t("div",[t("span",[e._v("今日水票使用数量：")]),t("span",{staticClass:"color-red"},[e._v(e._s(e.totalUseAllTicket))])])]],2)]),t("div",{staticClass:"cont-cent"},[[t("el-form",{attrs:{inline:!0}},[t("el-form-item",{directives:[{name:"show",rawName:"v-show",value:"third"==e.activeName,expression:"activeName == 'third'"}]},[t("el-select",{staticClass:"select-salesman",attrs:{placeholder:"按摘要筛选"},model:{value:e.queryParam.payMent,callback:function(t){e.$set(e.queryParam,"payMent",t)},expression:"queryParam.payMent"}},[e._l(e.abstractScreeningList,(function(e,a){return[t("el-option",{key:a,attrs:{label:e.name,value:e.value}})]}))],2)],1),t("el-form-item",{directives:[{name:"show",rawName:"v-show",value:"third"==e.activeName,expression:"activeName == 'third'"}]},[t("el-select",{staticClass:"select-salesman",attrs:{placeholder:"按确认状态筛选",disabled:2!=e.queryParam.payMent&&3!=e.queryParam.payMent,clearable:""},model:{value:e.queryParam.type,callback:function(t){e.$set(e.queryParam,"type",t)},expression:"queryParam.type"}},[e._l(e.confirmList,(function(e,a){return[t("el-option",{key:a,attrs:{label:e.name,value:e.value}})]}))],2)],1),t("el-form-item",[t("el-input",{directives:[{name:"show",rawName:"v-show",value:"third"!=e.activeName&&"four"!=e.activeName,expression:"activeName != 'third' && activeName != 'four'"}],staticStyle:{"margin-right":"20px"},attrs:{placeholder:"输入水票名称筛选"},model:{value:e.queryParam.waterName,callback:function(t){e.$set(e.queryParam,"waterName",t)},expression:"queryParam.waterName"}})],1),"first"!=e.activeName&&"second"!=e.activeName?t("el-form-item",[t("el-date-picker",{staticStyle:{width:"250px"},attrs:{"value-format":"yyyy-MM-dd",type:"daterange",align:"right","unlink-panels":"","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":e.pickerOptions},model:{value:e.selectDataDate,callback:function(t){e.selectDataDate=t},expression:"selectDataDate"}})],1):e._e(),t("el-form-item",{directives:[{name:"show",rawName:"v-show",value:"second"==e.activeName||"third"==e.activeName,expression:"activeName == 'second' || activeName == 'third'"}]},[t("el-input",{staticStyle:{width:"250px","margin-right":"20px"},attrs:{placeholder:"输入客户名或备注名搜索"},model:{value:e.queryParam.userName,callback:function(t){e.$set(e.queryParam,"userName",t)},expression:"queryParam.userName"}})],1),t("el-form-item",{staticStyle:{"margin-left":"20px"}},[t("el-button",{attrs:{type:"primary"},on:{click:e.searchTotal00}},[e._v("查询")]),t("el-button",{on:{click:e.clearSearch}},[e._v("清空筛选条件")])],1)],1),t("el-button",{attrs:{type:"success",icon:"el-icon-download",size:"mini"},on:{click:e.exportList}},[e._v("导出数据")])]],2),t("div",[t("div",{staticClass:"water-ticket-total"},[t("span",[e._v("\n          线上微信的水票收入\n          "),t("span",{staticClass:"color-red"},[e._v("￥"+e._s(e.waterMoneyLii.weiXinPrice))])]),t("span",{staticClass:"margin-left-20"},[e._v("\n          银行转账的水票收入\n          "),t("span",{staticClass:"color-red"},[e._v("￥"+e._s(e.waterMoneyLii.bankPrice))])]),t("span",{staticClass:"margin-left-20"},[e._v("\n          线下付款的水票收入\n          "),t("span",{staticClass:"color-red"},[e._v("￥"+e._s(e.waterMoneyLii.payOnDelivery))])]),t("span",{staticClass:"margin-left-20"},[e._v("\n          失效水票总金额\n          "),t("span",{staticClass:"color-red"},[e._v("￥"+e._s(e.waterMoneyLii.losePrice))])]),t("span",{staticClass:"margin-left-20"},[e._v("\n          实际水票总收入\n          "),t("span",{staticClass:"color-red"},[e._v("￥"+e._s(e.waterMoneyLii.practical))])])]),t("el-tabs",{on:{"tab-click":e.tabClick},model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[t("el-tab-pane",{attrs:{label:"水票汇总表",name:"first"}},[t("el-table",{staticStyle:{width:"100%"},attrs:{data:e.firstTableData,"max-height":e.tableHeight+50,border:"","header-cell-style":{"text-align":"center",background:"#EFF2F7",color:"#3D4C66"},"cell-style":{"text-align":"center","font-size":"16px",color:"#3D4C66"},"empty-text":"暂无数据",size:"small"}},[t("el-table-column",{attrs:{label:"序号",type:"index",width:"60"}}),t("el-table-column",{attrs:{label:"水票名称",prop:"waterName"}}),t("el-table-column",{attrs:{label:"水票价格",prop:"waterPrice"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                ￥"+e._s(t.row.waterPrice)+"\n              ")]}}])}),t("el-table-column",{attrs:{label:"水票购买总数量",prop:"payNum"}}),t("el-table-column",{attrs:{label:"水票购买总金额",prop:"payTotalPrice"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                ￥"+e._s(t.row.payTotalPrice)+"\n              ")]}}])}),t("el-table-column",{attrs:{label:"已使用水票总数",prop:"useNum"}}),t("el-table-column",{attrs:{label:"已使用水票总金额",prop:"useTotalPrice"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                ￥"+e._s(t.row.useTotalPrice)+"\n              ")]}}])}),t("el-table-column",{attrs:{label:"未使用水票总数",prop:"unuseNum"}}),t("el-table-column",{attrs:{label:"操作"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-button",{attrs:{type:"text",size:"medium"},on:{click:function(t){return e.firstLookDes(a.row)}}},[e._v("查看水票明细")])]}}])})],1),t("div",{staticClass:"total-box2"},[t("div",[e._v("合计")]),t("div",[t("span",[e._v("\n                水票购买总数量：\n                "),t("span",{staticClass:"color-red"},[e._v("\n                  "+e._s(e.firstDataTotal.payNumTotal)+"\n                ")])]),t("span",[e._v("\n                水票购买总金额：\n                "),t("span",{staticClass:"color-red"},[e._v("\n                  ￥"+e._s(e.firstDataTotal.payTotalPriceTotal)+"\n                ")])]),t("span",[e._v("\n                已使用水票总数：\n                "),t("span",{staticClass:"color-red"},[e._v("\n                  "+e._s(e.firstDataTotal.useNumTotal)+"\n                ")])]),t("span",[e._v("\n                已使用水票总金额：\n                "),t("span",{staticClass:"color-red"},[e._v("\n                  ￥"+e._s(e.firstDataTotal.useTotalPriceTotal)+"\n                ")])]),t("span",[e._v("\n                未使用水票总数：\n                "),t("span",{staticClass:"color-red"},[e._v("\n                  "+e._s(e.firstDataTotal.unuseNumTotal)+"\n                ")])])])]),t("div",{staticClass:"pages-box"},[t("el-pagination",{attrs:{layout:"total,prev,pager,next,sizes,jumper",background:"","current-page":e.firstPagesData.currentPage,"page-sizes":e.firstPagesData.currentPageSizes,"page-size":e.firstPagesData.pageSize,total:e.firstPagesData.pageTotal},on:{"size-change":function(t){return e.handleSizeChange(t,1)},"current-change":function(t){return e.handleCurrentChange(t,1)}}})],1)],1),t("el-tab-pane",{attrs:{label:"水票明细表",name:"second"}},[t("el-table",{staticStyle:{width:"100%"},attrs:{data:e.secondTableData,"max-height":e.tableHeight+50,border:"","header-cell-style":{"text-align":"center",background:"#EFF2F7",color:"#3D4C66"},"cell-style":{"text-align":"center","font-size":"16px",color:"#3D4C66"},"empty-text":"暂无数据",size:"small"}},[t("el-table-column",{attrs:{label:"序号",type:"index",width:"60"}}),t("el-table-column",{attrs:{label:"客户名 | 备注名"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("span",[e._v("\n                  "+e._s(a.row.userName)+"\n                  "),a.row.userName&&a.row.remark?[e._v("|")]:e._e(),e._v("\n                  "+e._s(a.row.remark)+"\n                ")],2)]}}])}),t("el-table-column",{attrs:{label:"水票名称",prop:"waterName"}}),t("el-table-column",{attrs:{label:"水票价格",prop:"waterPrice"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                ￥"+e._s(t.row.waterPrice)+"\n              ")]}}])}),t("el-table-column",{attrs:{label:"水票购买总数量",prop:"payNum"}}),t("el-table-column",{attrs:{label:"水票购买总金额",prop:"payTotalPrice"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                ￥"+e._s(t.row.payTotalPrice)+"\n              ")]}}])}),t("el-table-column",{attrs:{label:"已使用水票总数",prop:"useNum"}}),t("el-table-column",{attrs:{label:"已使用水票总金额",prop:"useTotalPrice"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                ￥"+e._s(t.row.useTotalPrice)+"\n              ")]}}])}),t("el-table-column",{attrs:{label:"未使用水票总数",prop:"unuseNum"}}),t("el-table-column",{attrs:{label:"操作"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-button",{attrs:{type:"text",size:"medium"},on:{click:function(t){return e.secondLookDes(a.row)}}},[e._v("查看记录")])]}}])})],1),t("div",{staticClass:"total-box2"},[t("div",[e._v("合计")]),t("div",[t("span",[e._v("\n                水票购买总数量：\n                "),t("span",{staticClass:"color-red"},[e._v("\n                  "+e._s(e.secondDataTotal.payNumTotal)+"\n                ")])]),t("span",[e._v("\n                水票购买总金额：\n                "),t("span",{staticClass:"color-red"},[e._v("\n                  ￥"+e._s(e.secondDataTotal.payTotalPriceTotal)+"\n                ")])]),t("span",[e._v("\n                已使用水票总数：\n                "),t("span",{staticClass:"color-red"},[e._v("\n                  "+e._s(e.secondDataTotal.useNumTotal)+"\n                ")])]),t("span",[e._v("\n                已使用水票总金额：\n                "),t("span",{staticClass:"color-red"},[e._v("\n                  ￥"+e._s(e.secondDataTotal.useTotalPriceTotal)+"\n                ")])]),t("span",[e._v("\n                未使用水票总数：\n                "),t("span",{staticClass:"color-red"},[e._v("\n                  "+e._s(e.secondDataTotal.unuseNumTotal)+"\n                ")])])])]),t("div",{staticClass:"pages-box"},[t("el-pagination",{attrs:{layout:"total, prev, pager, next,sizes, jumper",background:"","current-page":e.secondPagesData.currentPage,"page-sizes":e.secondPagesData.currentPageSizes,"page-size":e.secondPagesData.pageSize,total:e.secondPagesData.pageTotal},on:{"size-change":function(t){return e.handleSizeChange(t,2)},"current-change":function(t){return e.handleCurrentChange(t,2)}}})],1)],1),t("el-tab-pane",{attrs:{label:"水票记录表",name:"third"}},[t("el-table",{staticStyle:{width:"100%"},attrs:{data:e.thirdTableData,"max-height":e.tableHeight+50,border:"","header-cell-style":{"text-align":"center",background:"#EFF2F7",color:"#3D4C66"},"cell-style":{"text-align":"center","font-size":"16px",color:"#3D4C66"},"empty-text":"暂无数据",size:"small"}},[t("el-table-column",{attrs:{label:"序号",type:"index",width:"60"}}),t("el-table-column",{attrs:{label:"交易编号",prop:"orderNum"}}),t("el-table-column",{attrs:{label:"购买时间",prop:"createTime"}}),t("el-table-column",{attrs:{label:"客户名 | 备注名","min-width":"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                "+e._s(t.row.userName)+"\n                "),t.row.userName&&t.row.remark?[e._v("|")]:e._e(),e._v("\n                "+e._s(t.row.remark)+"\n              ")]}}])}),t("el-table-column",{attrs:{label:"水票名称",prop:"waterName"}}),t("el-table-column",{attrs:{label:"水票总金额"},scopedSlots:e._u([{key:"default",fn:function(a){return[a.row.waterTotalPrice>0?t("span",[e._v("￥"+e._s(a.row.waterTotalPrice))]):e._e()]}}])}),t("el-table-column",{attrs:{label:"水票总数量",prop:"payNum"}}),t("el-table-column",{attrs:{label:"购买数量",prop:"payNum"},scopedSlots:e._u([{key:"default",fn:function(a){return t("div",{},[e._v(e._s(a.row.payNum&&a.row.presenter?a.row.payNum-a.row.presenter:0))])}}])}),t("el-table-column",{attrs:{label:"赠送数量",prop:"presenter"}}),t("el-table-column",{attrs:{label:"摘要",prop:"payMent"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("span",[e._v(e._s(a.row.payMent))])]}}])},[t("el-table-column",{attrs:{prop:"remarks",label:"备注"}})],1),t("el-table-column",{attrs:{label:"补差价"},scopedSlots:e._u([{key:"default",fn:function(a){return[a.row.makePrice>0?t("span",[e._v("￥"+e._s(a.row.makePrice))]):t("span",[e._v("-")])]}}])}),t("el-table-column",{attrs:{prop:"",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(a){return[a.row.orderId?t("el-button",{attrs:{type:"text"},on:{click:function(t){return e.lookOrderDetail(a.row)}}},[e._v("查看详情")]):e._e(),"银行转账购买"==a.row.payMent&&0==a.row.affirm?t("el-button",{attrs:{type:"text"},on:{click:function(t){return e.makeSureGetMoney2(a.row)}}},[e._v("确认到账")]):e._e(),"线下付款购买"==a.row.payMent&&0==a.row.affirm&&1==a.row.userConfirm?t("el-button",{staticStyle:{color:"red"},attrs:{type:"text"}},[e._v("待用户确认")]):e._e(),"线下付款购买"==a.row.payMent&&0==a.row.affirm&&2==a.row.userConfirm?t("el-button",{attrs:{type:"text"},on:{click:function(t){return e.makeSureGetMoney4(a.row)}}},[e._v("确认到账")]):e._e(),"线下付款购买"==a.row.payMent&&1!=a.row.userConfirm&&0==a.row.wcDetailsState&&1!=a.row.affirm?t("el-button",{attrs:{type:"text"},on:{click:function(t){return e.dongjieshuipiao(a.row,1)}}},[e._v("冻结")]):e._e(),"线下付款购买"==a.row.payMent&&1!=a.row.userConfirm&&1==a.row.wcDetailsState&&1!=a.row.affirm?t("el-button",{staticStyle:{color:"green"},attrs:{type:"text"},on:{click:function(t){return e.dongjieshuipiao(a.row,0)}}},[e._v("解除冻结")]):e._e(),"线下付款购买,水票失效"==a.row.payMent&&0==a.row.affirm&&2!=a.row.refund?[t("el-button",{attrs:{type:"text"},on:{click:function(t){return e.makeSureGetMoney3(a.row)}}},[e._v("确认未到账")]),t("el-button",{attrs:{type:"text"},on:{click:function(t){return e.makeSureGetMoney2(a.row)}}},[e._v("确认到账")])]:e._e(),"银行转账购买,水票失效"==a.row.payMent&&0==a.row.affirm&&2!=a.row.refund?[t("el-button",{attrs:{type:"text"},on:{click:function(t){return e.makeSureGetMoney3(a.row)}}},[e._v("确认未到账")]),t("el-button",{attrs:{type:"text"},on:{click:function(t){return e.makeSureGetMoney2(a.row)}}},[e._v("确认到账")])]:e._e(),1!=a.row.affirm&&"微信购买"!=a.row.payMent||a.row.refund?e._e():t("el-button",{attrs:{type:"text",disabled:""}},[e._v("已到账")]),2==a.row.refund?t("el-button",{attrs:{type:"text",disabled:""}},[e._v("未到账")]):e._e(),"微信购买"==a.row.payMent&&0==a.row.refund?t("el-button",{attrs:{type:"text"},on:{click:function(t){return e.confirmRefund(a.row,1)}}},[e._v("同意退款")]):e._e(),"微信购买"==a.row.payMent&&0==a.row.refund?t("el-button",{attrs:{type:"text"},on:{click:function(t){return e.confirmRefund(a.row,0)}}},[e._v("拒绝退款")]):e._e(),"微信购买"==a.row.payMent&&1==a.row.refund?t("el-button",{attrs:{type:"text",disabled:""}},[e._v("已退款")]):e._e()]}}])})],1),t("div",{staticClass:"pages-box"},[t("el-pagination",{attrs:{layout:"total, prev, pager, next,sizes, jumper",background:"","current-page":e.thirdPagesData.currentPage,"page-sizes":e.thirdPagesData.currentPageSizes,"page-size":e.thirdPagesData.pageSize,total:e.thirdPagesData.pageTotal},on:{"size-change":function(t){return e.handleSizeChange(t,3)},"current-change":function(t){return e.handleCurrentChange(t,3)}}})],1)],1),t("el-tab-pane",{attrs:{label:"失效水票记录表",name:"four"}},[t("el-table",{key:"sxsp",staticStyle:{width:"100%"},attrs:{data:e.fourTableData,"max-height":e.tableHeight+50,border:"","header-cell-style":{"text-align":"center",background:"#EFF2F7",color:"#3D4C66"},"cell-style":{"text-align":"center","font-size":"16px",color:"#3D4C66"},"empty-text":"暂无数据",size:"small"}},[t("el-table-column",{attrs:{label:"序号",type:"index",width:"60"}}),t("el-table-column",{attrs:{label:"失效日期",prop:"time"}}),t("el-table-column",{attrs:{label:"客户名 | 备注名","min-width":"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                "+e._s(t.row.userName)+"\n                "),t.row.userName&&t.row.remarkName?[e._v("|")]:e._e(),e._v("\n                "+e._s(t.row.remarkName)+"\n              ")]}}])}),t("el-table-column",{attrs:{label:"水票名称",prop:"waterName"}}),t("el-table-column",{attrs:{label:"失效水票总数量",prop:"waterNumber"}}),t("el-table-column",{attrs:{label:"失效水票总金额",prop:"totalPrice"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("￥"+e._s(t.row.totalPrice))]}}])}),t("el-table-column",{attrs:{label:"操作"},scopedSlots:e._u([{key:"default",fn:function(a){return[0==a.row.refund?t("el-button",{attrs:{type:"text"},on:{click:function(t){return e.comfirmRefund(a.row)}}},[e._v("确认退款")]):e._e(),2==a.row.refund?t("el-button",{attrs:{type:"text",disabled:""}},[e._v("未收款")]):e._e(),1==a.row.refund?t("el-button",{attrs:{disabled:"",type:"text"}},[e._v("已退款")]):e._e()]}}])})],1),t("div",{staticClass:"pages-box"},[t("el-pagination",{attrs:{layout:"total, prev, pager, next,sizes, jumper",background:"","current-page":e.fourPagesData.currentPage,"page-sizes":e.fourPagesData.currentPageSizes,"page-size":e.fourPagesData.pageSize,total:e.fourPagesData.pageTotal},on:{"size-change":function(t){return e.handleSizeChange(t,4)},"current-change":function(t){return e.handleCurrentChange(t,4)}}})],1)],1)],1)],1)]),t("el-dialog",{staticClass:"detailDialog",attrs:{title:"订单详情",visible:e.orderDetailDialog,width:"600px"},on:{"update:visible":function(t){e.orderDetailDialog=t}}},[t("el-form",{attrs:{model:e.orderDetailData,"label-width":"120px"}},[t("el-form-item",{attrs:{label:"用户名称"}},[t("div",[e._v(e._s(e.orderDetailData.userName))])]),t("el-form-item",{attrs:{label:"联系方式"}},[t("div",[e._v(e._s(e.orderDetailData.userPhone))])]),t("el-form-item",{attrs:{label:"收货地址"}},[t("div",[e._v(e._s(e.orderDetailData.address))])]),e.orderDetailData.specList?t("el-form-item",{attrs:{label:"普通商品"}},e._l(e.orderDetailData.specList,(function(a,s){return t("div",{key:s,staticClass:"goodsCard flex box-shadow"},[t("div",{staticClass:"flex align-items-center"},[t("el-image",{staticStyle:{width:"80px",height:"80px"},attrs:{src:a.pic,fit:"fit"}})],1),t("div",[t("div",[e._v(e._s(a.goodsName))]),t("div",[t("el-tag",[e._v(e._s(a.specName))]),a.discounts?t("el-tag",{staticClass:"margin-left-10",attrs:{type:"success"}},[e._v(e._s(a.discounts))]):e._e()],1),t("div",{staticClass:"flex align-items-center justify-content-between"},[a.price?t("div",{staticClass:"flex align-items-center",staticStyle:{color:"#ff2f2f"}},[t("div",{staticClass:"color-red font-size-16"},[t("span",{staticClass:"font-size-12"},[e._v("￥")]),e._v("\n                  "+e._s(a.price)+"\n                ")]),t("div",{staticStyle:{"text-decoration":"line-through","margin-left":"10px",color:"#d2d2d2","font-size":"16px"}},[t("span",{staticClass:"font-size-12"},[e._v("￥")]),e._v("\n                  "+e._s(a.doller)+"\n                ")])]):t("div",{staticClass:"color-red font-size-16"},[t("span",{staticClass:"font-size-12"},[e._v("￥")]),e._v("\n                "+e._s(a.doller)+"\n              ")]),t("div",[e._v("x"+e._s(a.standardNumber))])])])])})),0):e._e(),e.orderDetailData.shopGroupList?t("el-form-item",{attrs:{label:"组合商品"}},e._l(e.orderDetailData.shopGroupList,(function(a,s){return t("el-card",{key:s},[t("div",{staticClass:"goodsCard flex",staticStyle:{"margin-bottom":"0"},attrs:{slot:"header"},slot:"header"},[t("div",{staticClass:"flex align-items-center"},[t("el-image",{staticStyle:{width:"80px",height:"80px"},attrs:{src:a.pic,fit:"fit"}})],1),t("div",[t("div",[e._v(e._s(a.goodsName))]),t("div",{staticClass:"flex align-items-center justify-content-between"},[a.price?t("div",{staticClass:"flex align-items-center",staticStyle:{color:"#ff2f2f"}},[t("div",{staticClass:"color-red font-size-16"},[t("span",{staticClass:"font-size-12"},[e._v("￥")]),e._v("\n                    "+e._s(a.price)+"\n                  ")]),t("div",{staticStyle:{"text-decoration":"line-through","margin-left":"10px",color:"#d2d2d2","font-size":"16px"}},[t("span",{staticClass:"font-size-12"},[e._v("￥")]),e._v("\n                    "+e._s(a.doller)+"\n                  ")])]):t("div",{staticClass:"color-red font-size-16"},[t("span",{staticClass:"font-size-12"},[e._v("￥")]),e._v("\n                  "+e._s(a.doller)+"\n                ")]),t("div",[e._v("x"+e._s(a.standardNumber))])])])]),e._l(a.specList,(function(a,s){return t("div",{key:s,staticClass:"flex align-items-center justify-content-between"},[t("div",[e._v(e._s(a.goodsName))]),t("div",{staticClass:"color-grey"},[e._v("x"+e._s(a.standardNumber))])])}))],2)})),1):e._e(),t("el-form-item",{attrs:{label:"下单时间"}},[t("div",[e._v(e._s(e.orderDetailData.orderDate))])]),t("el-form-item",{attrs:{label:"订单编号"}},[t("div",[e._v(e._s(e.orderDetailData.orderNumber))])]),t("el-form-item",{attrs:{label:"订单总额"}},[t("div",[e._v(e._s(e.orderDetailData.footing)+"元")])]),t("div",{staticClass:"el-form-item-flex"},[t("el-form-item",{attrs:{label:"商品数量"}},[t("div",[e._v("共"+e._s(e.orderDetailData.totalPieces)+"件商品")])]),t("el-form-item",[t("div",e._l(e.orderDetailData.brand,(function(a,s){return t("div",{key:s},[t("span",[e._v("品牌桶："+e._s(a.buckName))]),t("span",[e._v("回桶数："+e._s(a.buckNumber)+"个")])])})),0),t("div",e._l(e.orderDetailData.noBrand,(function(a,s){return t("div",{key:s},[t("span",[e._v("其它品牌：")]),t("span",[e._v("回桶数："+e._s(a.buckNumber)+"个")])])})),0),e.orderDetailData.buckMoney>0?t("p",[t("span",[e._v("\n              补差价：\n              "),t("span",{staticClass:"color-red"},[e._v("¥ "+e._s(e.orderDetailData.buckMoney))])]),e.orderDetailData.buckMoney>0?t("span",[e._v("\n              支付方式:\n              "),0==e.orderDetailData.payType||null==e.orderDetailData.payType||""==e.orderDetailData.payType?t("span",[e._v("待支付")]):e._e(),2==e.orderDetailData.payType?t("span",[e._v("余额支付")]):e._e(),1==e.orderDetailData.payType?t("span",[e._v("微信支付")]):e._e(),3==e.orderDetailData.payType?t("span",[e._v("支付宝支付")]):e._e(),4==e.orderDetailData.payType?t("span",[e._v("现金支付")]):e._e()]):e._e()]):e._e()])],1),t("div",{staticClass:"flex align-items-start"},[t("div",{staticClass:"moneyBox margin-right-30"},[t("el-form-item",{attrs:{label:"客户付上楼费"}},[t("div",[e._v(e._s(e.orderDetailData.upPrice)+"元")])]),t("el-form-item",{attrs:{label:"水票抵扣"}},[t("div",[e._v(e._s(e.orderDetailData.orderDiscounts)+"元")])]),t("el-form-item",{attrs:{label:e.orderDetailData.payment}},[t("div",[e._v(e._s(e.orderDetailData.total))])])],1),t("div",{staticClass:"moneyBox"},[e.orderDetailData.totalMoeny>0?t("el-form-item",{attrs:{label:"配送总提成"}},[t("div",[e._v("\n              "+e._s(e.orderDetailData.totalMoeny?e.orderDetailData.totalMoeny:"0.00")+"元\n            ")])]):e._e(),e.orderDetailData.shopMoney>0?t("el-form-item",{attrs:{label:"单件提成"}},[t("div",[e._v("\n              "+e._s(e.orderDetailData.shopMoney?e.orderDetailData.shopMoney:"0.00")+"元\n            ")])]):e._e(),e.orderDetailData.distanceMoney>0?t("el-form-item",{attrs:{label:"距离提成"}},[t("div",[e._v("\n              "+e._s(e.orderDetailData.distanceMoney?e.orderDetailData.distanceMoney:"0.00")+"元\n            ")])]):e._e(),e.orderDetailData.deliveryMoney>0?t("el-form-item",{attrs:{label:"业务提成"}},[t("div",[e._v("\n              "+e._s(e.orderDetailData.deliveryMoney?e.orderDetailData.deliveryMoney:"0.00")+"元\n            ")])]):e._e(),e.orderDetailData.floorMonety>0?t("el-form-item",{attrs:{label:"水站付上楼提成"}},[t("div",[e._v("\n              "+e._s(e.orderDetailData.floorMonety?e.orderDetailData.floorMonety:"0.00")+"元\n            ")])]):e._e()],1)]),t("el-form-item",{attrs:{label:"买家留言"}},[t("div",{staticClass:"color-red"},[e._v("\n          "+e._s(null==e.orderDetailData.userLeaveWord?"买家没有留言哦~":e.orderDetailData.userLeaveWord)+"\n        ")])])],1)],1),t("el-dialog",{staticClass:"new-dialog-css",attrs:{visible:e.refundVisible,width:"30%",center:""},on:{"update:visible":function(t){e.refundVisible=t}}},[t("div",{staticClass:"new-dialog-title",attrs:{slot:"title"},slot:"title"},[e._v("确认退款")]),t("div",{staticClass:"new-dialog-two-title"},[e._v("失效水票退款")]),t("div",{staticClass:"new-dialog-body"},[t("el-form",{staticStyle:{padding:"0 30px"},attrs:{"label-width":"150px"}},[t("el-form-item",{attrs:{label:"水票名称："}},[t("span",[e._v(e._s(e.WaterPiao.productName))])]),t("el-form-item",{attrs:{label:"失效水票数量："}},[t("span",[e._v("￥"+e._s(e.WaterPiao.number))])]),t("el-form-item",{attrs:{label:"退款总金额："}},[t("span",[e._v("￥"+e._s(e.WaterPiao.price))])])],1)],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.refundVisible=!1}}},[e._v("取 消")]),t("el-button",{attrs:{type:"primary"},on:{click:e.refundMOney}},[e._v("确认退款")])],1)])],1)},r=[];function i(e){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i(e)}function o(e,t,a){return(t=l(t))in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}function l(e){var t=n(e,"string");return"symbol"==i(t)?t:t+""}function n(e,t){if("object"!=i(e)||!e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var s=a.call(e,t||"default");if("object"!=i(s))return s;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}var c={props:{},data:function(){return{imgUri:this.$imgUri,queryParam:{payMent:"",waterId:"",waterName:"",userName:"",type:"",startTime:"",endTime:""},ticketPrice:"",userId:"",abstractScreeningList:[{name:"微信支付",value:1},{name:"银行转账",value:2},{name:"线下付款",value:3},{name:"兑换",value:5},{name:"抵扣",value:4}],confirmList:[{name:"未确认",value:"0"},{name:"已确认",value:"1"}],activeName:"first",selectDataDate:[],ticketListData:[],totalBuyAllTicket:0,totalUseAllTicket:0,firstTableData:[],firstPagesData:{pageTotal:0,currentPage:1,currentPageSizes:[10,15,20],pageSize:10},secondTableData:[],secondPagesData:{pageTotal:0,currentPage:1,currentPageSizes:[10,15,20],pageSize:10},thirdTableData:[],thirdPagesData:{pageTotal:0,currentPage:1,currentPageSizes:[10,15,20],pageSize:10},fourTableData:[],fourPagesData:{pageTotal:0,currentPage:1,currentPageSizes:[10,15,20],pageSize:10},waterMoneyLii:{losePrice:"0.00",practical:"0.00",weiXinPrice:"0.00",bankPrice:"0.00",payOnDelivery:"0.00"},refundVisible:!1,WaterPiao:{},pickerOptions:{disabledDate:function(e){return e.getTime()>Date.now()},shortcuts:[{text:"最近一周",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-6048e5),e.$emit("pick",[a,t])}},{text:"最近一个月",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-2592e6),e.$emit("pick",[a,t])}},{text:"最近三个月",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-7776e6),e.$emit("pick",[a,t])}}]},firstDataTotal:{payNumTotal:0,payTotalPriceTotal:0,useNumTotal:0,useTotalPriceTotal:0,unuseNumTotal:0,unuseTotalPriceTotal:0},secondDataTotal:{payNumTotal:0,payTotalPriceTotal:0,useNumTotal:0,useTotalPriceTotal:0,unuseNumTotal:0,unuseTotalPriceTotal:0},orderDetailDialog:!1,orderDetailData:{}}},computed:{tableHeight:function(){console.log(this.$store.getters.getGlobalHeight);var e=Number(this.$store.getters.getGlobalHeight)-450;return e>=300?e:300},inTableHeight:function(){var e=this.$store.getters.getGlobalHeight;return e>=400?parseInt(this.$util.mul(e,.5)):400}},created:function(){this.searchTotal()},mounted:function(){},watch:{selectDataDate:function(e){e?(this.queryParam.startTime=e[0],this.queryParam.endTime=e[1]):(this.queryParam.startTime="",this.queryParam.endTime="")}},methods:o({requestClassApi:function(){var e=this,t=this;this.$post("/szmb/water/selstorewater",{storeId:t.Cookies.get("storeId")}).then((function(a){1===a.code?t.ticketListData=a.data:0===a.code?t.ticketListData=[]:(console.log(a.msg),e.$message.error(a.data))})).catch((function(e){console.log(e)}))},handleSizeChange:function(e,t){1==t?this.firstPagesData.pageSize=e:2==t?this.secondPagesData.pageSize=e:3==t?this.thirdPagesData.pageSize=e:4==t&&(this.fourPagesData.pageSize=e),this.searchTotal()},handleCurrentChange:function(e,t){1==t?this.firstPagesData.currentPage=e:2==t?this.secondPagesData.currentPage=e:3==t?this.thirdPagesData.currentPage=e:4==t&&(this.fourPagesData.currentPage=e),this.searchTotal()},searchTotal00:function(){"first"==this.activeName?this.firstPagesData.currentPage=1:"second"==this.activeName?this.secondPagesData.currentPage=1:"third"==this.activeName&&(this.thirdPagesData.currentPage=1),this.searchTotal()},searchTotal:function(){this.userId="",this.queryParam.waterId="-1",this.ticketPrice="","first"==this.activeName?this.firstRequestListApi():"second"==this.activeName?this.secondRequestListApi():"third"==this.activeName?this.thirdRequestListApi():"four"==this.activeName&&this.fourRequestListApi()},clearSearch:function(){this.queryParam={payMent:"",waterId:"",waterName:"",type:"",userName:"",startTime:"",endTime:""},this.userId="",this.ticketPrice="",this.selectDataDate=[],"first"==this.activeName?(this.firstPagesData.currentPage=1,this.firstRequestListApi()):"second"==this.activeName?(this.secondPagesData.currentPage=1,this.secondRequestListApi()):"third"==this.activeName?(this.thirdPagesData.currentPage=1,this.thirdRequestListApi()):"four"==this.activeName&&(this.fourPagesData.currentPage=1,this.fourRequestListApi())},tabClick:function(e,t){console.log(e.name,t),this.activeName=e.name,this.clearSearch()},firstRequestListApi:function(){var e=this,t={storeId:this.Cookies.get("storeId"),waterId:this.queryParam.waterId?this.queryParam.waterId:-1,waterName:this.queryParam.waterName,startTime:this.queryParam.startTime,endTime:this.queryParam.endTime,pageNo:this.firstPagesData.currentPage,pageSize:this.firstPagesData.pageSize},a="szmb/water/seluserwatercouponcollect";this.$post(a,t).then((function(t){1==t.code?(e.firstTableData=t.data.list,e.totalBuyAllTicket=Number(t.data.unuse)+Number(t.data.use),e.totalUseAllTicket=t.data.useNum,e.firstDataTotal={payNumTotal:t.data.totalPayNum,payTotalPriceTotal:t.data.totalPayPrice,useNumTotal:t.data.totalUseNum,useTotalPriceTotal:t.data.totalUsePrice,unuseNumTotal:t.data.totalUnuseNum,unuseTotalPriceTotal:t.data.totalUnusePrice},e.firstPagesData.pageTotal=t.data.count,e.waterMoneyLii={losePrice:t.data.losePrice,practical:t.data.practical,weiXinPrice:t.data.weiXinPrice,bankPrice:t.data.bankPrice,payOnDelivery:t.data.payOnDelivery}):(e.firstTableData=[],e.firstPagesData.pageTotal=0,e.waterMoneyLii={losePrice:"0.00",practical:"0.00",weiXinPrice:"0.00",bankPrice:"0.00",payOnDelivery:"0.00"})})).catch((function(e){console.log(e)}))},firstLookDes:function(e){this.activeName="second",this.queryParam.waterId=e.waterId,this.queryParam.waterName=e.waterName,this.ticketPrice=e.waterPrice,this.secondPagesData.currentPage=1,this.secondRequestListApi()},secondRequestListApi:function(){var e=this,t={storeId:this.Cookies.get("storeId"),waterId:this.queryParam.waterId?this.queryParam.waterId:-1,waterName:this.queryParam.waterName,userName:this.queryParam.userName,price:this.ticketPrice,startTime:this.queryParam.startTime,endTime:this.queryParam.endTime,pageNo:this.secondPagesData.currentPage,pageSize:this.secondPagesData.pageSize};this.$post("szmb/water/selwatercouponinfonew",t).then((function(t){1==t.code?(e.secondTableData=t.data.list,e.totalBuyAllTicket=Number(t.data.unuse)+Number(t.data.use),e.totalUseAllTicket=t.data.useNum,e.secondDataTotal={payNumTotal:t.data.totalPayNum,payTotalPriceTotal:t.data.totalPayPrice,useNumTotal:t.data.totalUseNum,useTotalPriceTotal:t.data.totalUsePrice,unuseNumTotal:t.data.totalUnuseNum,unuseTotalPriceTotal:t.data.totalUnusePrice},e.secondPagesData.pageTotal=t.data.count,e.waterMoneyLii={losePrice:t.data.losePrice,practical:t.data.practical,weiXinPrice:t.data.weiXinPrice,bankPrice:t.data.bankPrice,payOnDelivery:t.data.payOnDelivery}):(e.secondTableData=[],e.secondPagesData.pageTotal=0,e.waterMoneyLii={losePrice:"0.00",practical:"0.00",weiXinPrice:"0.00",bankPrice:"0.00",payOnDelivery:"0.00"})})).catch((function(e){console.log(e)}))},secondLookDes:function(e){this.activeName="third",this.queryParam.userName=e.userName,this.queryParam.waterId=e.waterId,this.queryParam.waterName=e.waterName,this.ticketPrice=e.waterPrice,this.userId=e.userId,this.thirdPagesData.currentPage=1,this.thirdRequestListApi()},thirdRequestListApi:function(){var e=this,t={storeId:this.Cookies.get("storeId"),waterId:this.queryParam.waterId?this.queryParam.waterId:-1,waterName:this.queryParam.waterName,userName:this.queryParam.userName,type:this.queryParam.type?this.queryParam.type:-1,price:this.ticketPrice,userId:this.userId,payMent:this.queryParam.payMent?this.queryParam.payMent:-1,stateTime:this.queryParam.startTime,endTime:this.queryParam.endTime,pageNo:this.thirdPagesData.currentPage,pageSize:this.thirdPagesData.pageSize};this.$post("szmb/water/seluserwatercouponrecord",t).then((function(t){1==t.code?(e.thirdTableData=t.data.list,e.totalBuyAllTicket=Number(t.data.unuse)+Number(t.data.use),e.totalUseAllTicket=t.data.useNum,e.thirdPagesData.pageTotal=t.data.count,e.waterMoneyLii={losePrice:t.data.losePrice,practical:t.data.practical,weiXinPrice:t.data.weiXinPrice,bankPrice:t.data.bankPrice,payOnDelivery:t.data.payOnDelivery}):(e.thirdTableData=[],e.thirdPagesData.pageTotal=0,e.waterMoneyLii={losePrice:"0.00",practical:"0.00",weiXinPrice:"0.00",bankPrice:"0.00",payOnDelivery:"0.00"})})).catch((function(e){console.log(e)}))},thirdLookDes:function(e){var t=this,a="/szmb/szmborder/selectorderone",s={orderId:e.orderId};this.$post(a,s).then((function(e){1==e.code?(t.orderDetailData=e.data,t.orderDetailDialog=!0):t.$message.error(e.data)}))},thirdSureTicketToUser:function(e){var t=this;this.$confirm("确认将水票发放给买家？").then((function(){t.$post("/szmb/water/affirbank",{r2:e.orderNum,isBankAffirm:1,header:"json"}).then((function(e){1==e.code?t.thirdRequestListApi():t.$message.error(e.data)})).catch((function(e){console.log(e)}))})).catch((function(){}))},exportList:function(){"first"==this.activeName?this.firstRequestListApiExport():"second"==this.activeName?this.secondRequestListApiExport():"third"==this.activeName?this.thirdRequestListApiExport():"four"==this.activeName&&this.fourRequestListApiExport()},firstRequestListApiExport:function(){var e=this,t={storeId:this.Cookies.get("storeId"),waterId:this.queryParam.waterId?this.queryParam.waterId:-1,waterName:this.queryParam.waterName,startTime:this.queryParam.startTime,endTime:this.queryParam.endTime,pageNo:1,pageSize:this.firstPagesData.pageTotal},a="/szmb/water/seluserwatercouponcollectexport";this.$post(a,t).then((function(t){1==t.code?window.location.href=t.data:0==t.code?e.$message({type:"warning",message:t.data}):(console.log(t.msg),e.$message.error(t.data))})).catch((function(e){console.log(e)}))},secondRequestListApiExport:function(){var e=this,t={storeId:this.Cookies.get("storeId"),waterId:this.queryParam.waterId?this.queryParam.waterId:-1,waterName:this.queryParam.waterName,userName:this.queryParam.userName,price:this.ticketPrice,startTime:this.queryParam.startTime,endTime:this.queryParam.endTime,pageNo:1,pageSize:this.secondPagesData.pageTotal};this.$post("/szmb/water/selwatercouponinfonewexport",t).then((function(t){1==t.code?window.location.href=t.data:0==t.code?e.$message({type:"warning",message:t.data}):(console.log(t.msg),e.$message.error(t.data))})).catch((function(e){console.log(e)}))},thirdRequestListApiExport:function(){var e=this,t={storeId:this.Cookies.get("storeId"),waterId:this.queryParam.waterId?this.queryParam.waterId:-1,waterName:this.queryParam.waterName,userName:this.queryParam.userName,type:this.queryParam.type?this.queryParam.type:-1,price:this.ticketPrice,userId:this.userId,payMent:this.queryParam.payMent?this.queryParam.payMent:-1,startTime:this.queryParam.startTime,endTime:this.queryParam.endTime,pageNo:1,pageSize:this.thirdPagesData.pageTotal};this.$post("/szmb/water/seluserwatercouponrecordexport",t).then((function(t){1==t.code?window.location.href=t.data:0==t.code?e.$message({type:"warning",message:t.data}):(console.log(t.msg),e.$message.error(t.data))})).catch((function(e){console.log(e)}))},fourRequestListApiExport:function(){var e=this,t={storeId:this.Cookies.get("storeId"),startTime:this.queryParam.startTime,endTime:this.queryParam.endTime,source:0};this.$post("/szmb/water/selwaterloserecordexport",t).then((function(t){1==t.code?window.location.href=t.data:0==t.code?e.$message({type:"warning",message:t.data}):(console.log(t.msg),e.$message.error(t.data))})).catch((function(e){console.log(e)}))},fourRequestListApi:function(){var e=this,t={storeId:this.Cookies.get("storeId"),startTime:this.queryParam.startTime,endTime:this.queryParam.endTime,source:0,pageNo:this.fourPagesData.currentPage,pageSize:this.fourPagesData.pageSize};this.$post("/szmb/water/selwaterloserecord",t).then((function(t){1==t.code?(e.fourTableData=t.data.list,e.waterMoneyLii={losePrice:t.data.losePrice,practical:t.data.practical,weiXinPrice:t.data.weiXinPrice,bankPrice:t.data.bankPrice,payOnDelivery:t.data.payOnDelivery},e.fourPagesData.pageTotal=t.data.count):(e.fourTableData=[],e.waterMoneyLii={losePrice:"0.00",practical:"0.00",weiXinPrice:"0.00",bankPrice:"0.00",payOnDelivery:"0.00"},e.fourPagesData.pageTotal=0)})).catch((function(e){console.log(e)}))},makeSureGetMoney2:function(e){var t=this,a="/szmb/water/affirbank",s={r2:e.orderNum,isBankAffirm:1,refund:1,header:"json"};t.$post(a,s).then((function(e){1==e.code?(t.$message({message:"确认成功!",type:"success"}),t.thirdRequestListApi()):t.$message.error(e.data)}))},makeSureGetMoney3:function(e){var t=this,a="/szmb/water/affirbank",s={r2:e.orderNum,isBankAffirm:1,refund:2,header:"json"};t.$post(a,s).then((function(e){1==e.code?(t.$message({message:"确认成功!",type:"success"}),t.thirdRequestListApi()):t.$message.error(e.data)}))},comfirmRefund:function(e){console.log("1212",e),this.rowListPiao=e;var t=this,a="/szmb/water/waterloseprice",s={storeId:t.Cookies.get("storeId"),userId:e.userId,price:e.price,waterId:e.waterId};t.$post(a,s).then((function(e){console.log(e),1==e.code?(t.WaterPiao=e.data,t.refundVisible=!0):t.$message.error(e.data)}))},refundMOney:function(){var e=this,t="/szmb/water/confirmrefund",a={storeId:e.Cookies.get("storeId"),userId:e.rowListPiao.userId,price:e.rowListPiao.price,waterId:e.rowListPiao.waterId};e.$post(t,a).then((function(t){console.log(t),1==t.code?(e.$message({type:"success",message:"退款成功"}),e.refundVisible=!1,e.fourRequestListApi()):e.$message.error(t.data)}))},confirmRefund:function(e,t){var a=this,s="/smzcwcuse/refundConfirm",r={wcRelevanceId:e.reId,flag:t};this.$confirm("确认退款操作?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){a.$post(s,r).then((function(e){1==e.code?(a.$message({message:"退款成功!",type:"success"}),a.page=1,a.getticket()):a.$message.error(e.data)}))}))},dongjieshuipiao:function(e,t){var a=this,s="/szmb/water/dongjie",r={orderNum:e.orderNum,delState:t};a.$post(s,r).then((function(e){1==e.code?(a.$message({message:"冻结成功!",type:"success"}),a.lookUpWaterTicket1()):a.$message.error(e.data)}))}},"makeSureGetMoney3",(function(e){var t=this,a="/szmb/water/affirbank",s={r2:e.orderNum,isBankAffirm:1,refund:2,header:"json"};t.$post(a,s).then((function(e){1==e.code?(t.$message({message:"确认成功!",type:"success"}),t.lookUpWaterTicket1()):t.$message.error(e.data)}))})),filters:{},components:{}},d=c,u=(a("dc46"),a("0c7c")),m=Object(u["a"])(d,s,r,!1,null,"177bd962",null);t["default"]=m.exports},"15ba":function(e,t,a){},"19f1":function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e._self._c;return t("div",{staticClass:"mod-config"},[t("el-form",{attrs:{inline:!0,model:e.dataForm},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.onSearch()}}},[t("el-form-item",[t("el-input",{attrs:{placeholder:"品牌名称",clearable:""},model:{value:e.dataForm.brandName,callback:function(t){e.$set(e.dataForm,"brandName",t)},expression:"dataForm.brandName"}})],1),t("el-form-item",[t("el-button",{on:{click:function(t){return e.onSearch()}}},[e._v("查询")]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.brandAddOrUpdate()}}},[e._v("新增")])],1)],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.dataListLoading,expression:"dataListLoading"}],staticStyle:{width:"100%"},attrs:{data:e.dataList,border:""},on:{"selection-change":e.selectionChangeHandle}},[t("el-table-column",{attrs:{prop:"brandname","header-align":"center",align:"center",label:"品牌名称"}}),t("el-table-column",{attrs:{prop:"操作","header-align":"center",align:"center",width:"200",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(a){return t("div",{},[t("el-button",{staticStyle:{color:"blue"},attrs:{type:"text",size:"small"},on:{click:function(t){return e.brandAddOrUpdate(a.row.brandid)}}},[e._v("修改")]),t("el-button",{staticStyle:{color:"red"},attrs:{type:"text",size:"small"},on:{click:function(t){return e.deleteHandle(a.row.brandid)}}},[e._v("删除")])],1)}}])})],1),e.brandAddOrUpdateVisible?t("brand-add-or-update",{ref:"brandAddOrUpdate",on:{refreshDataList:e.getDataList}}):e._e()],1)},r=[],i=function(){var e=this,t=e._self._c;return t("el-dialog",{attrs:{title:e.dataForm.brandId?"修改品牌":"新增品牌","close-on-click-modal":!1,visible:e.visible},on:{"update:visible":function(t){e.visible=t}}},[t("el-form",{ref:"dataForm",attrs:{model:e.dataForm,rules:e.dataRule,"label-width":"120px"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.dataFormSubmit()}}},[t("el-form-item",{attrs:{label:"品牌名称",prop:"brandName"}},[t("el-input",{attrs:{placeholder:"请输入品牌名称"},model:{value:e.dataForm.brandName,callback:function(t){e.$set(e.dataForm,"brandName",t)},expression:"dataForm.brandName"}})],1)],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.visible=!1}}},[e._v("取消")]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.dataFormSubmit()}}},[e._v("确定")])],1)],1)},o=[],l={data:function(){return{visible:!1,dataForm:{brandId:"",brandName:""},dataRule:{brandName:[{required:!0,message:"品牌名称不能为空",trigger:"blur"}]}}},methods:{init:function(e){var t=this;this.dataForm.brandId=e||"",this.visible=!0,this.$nextTick((function(){t.$refs["dataForm"].resetFields(),t.dataForm.brandId&&t.$post("/szmb/newclasscontroller/selectbrandbyid",{brandId:e}).then((function(e){1===e.code&&(t.dataForm.brandName=e.data.brandname)}))}))},dataFormSubmit:function(){var e=this;this.$refs["dataForm"].validate((function(t){if(t){var a=e.dataForm.brandId?"/szmb/newclasscontroller/updatebrand":"/szmb/newclasscontroller/addbrand";e.$post(a,e.dataForm).then((function(t){1===t.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.visible=!1,e.$emit("refreshDataList")}}):e.$message.error(t.data||t.msg)}))}}))}}},n=l,c=a("0c7c"),d=Object(c["a"])(n,i,o,!1,null,null,null),u=d.exports,m={data:function(){return{dataForm:{brandName:""},dataList:[],dataListLoading:!1,dataListSelections:[],brandAddOrUpdateVisible:!1}},components:{BrandAddOrUpdate:u},mounted:function(){this.getDataList()},methods:{onSearch:function(){this.getDataList()},getDataList:function(){var e=this;this.dataListLoading=!0,this.$post("/szmb/newclasscontroller/selectbrand",{brandName:this.dataForm.brandName}).then((function(t){e.dataListLoading=!1,1===t.code?e.dataList=t.data:e.dataList=[]}))},selectionChangeHandle:function(e){this.dataListSelections=e},brandAddOrUpdate:function(e){var t=this;this.brandAddOrUpdateVisible=!0,this.$nextTick((function(){t.$refs.brandAddOrUpdate.init(e)}))},deleteHandle:function(e){var t=this;this.$confirm("确定删除此品牌?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.$post("/szmb/newclasscontroller/deletebrand",{brandId:e}).then((function(e){1===e.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){t.getDataList()}}):t.$message.error(e.data||e.msg)}))}))}}},p=m,f=Object(c["a"])(p,s,r,!1,null,null,null);t["default"]=f.exports},"22ba":function(e,t,a){},5236:function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e._self._c;return t("div",{staticStyle:{"min-width":"1651px","box-sizing":"border-box",margin:"0 auto"}},[t("div",{staticClass:"flex align-items-center justify-content-between"},[t("div",[t("el-input",{staticStyle:{width:"370px","margin-right":"20px"},attrs:{placeholder:"输入水票名称搜索"},model:{value:e.selectData.name,callback:function(t){e.$set(e.selectData,"name",t)},expression:"selectData.name"}}),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.load()}}},[e._v("查询")]),t("el-button",{staticStyle:{"margin-left":"20px"},on:{click:e.clearSearch}},[e._v("清空筛选条件")])],1),t("div",[t("el-button",{staticClass:"el-icon-document",attrs:{type:"success"},on:{click:function(t){return e.openSetTicketDialog(null,1)}}},[e._v("批量设置买赠规则")])],1)]),t("div",{staticClass:"margin-top-20 royalty-cont"},[t("el-table",{ref:"waterTicketMultipleTable",staticStyle:{width:"100%"},attrs:{data:e.ticketSetList,"header-cell-style":{"text-align":"center","background-color":"rgba(239,242,247,1)"},"cell-style":{"text-align":"center","font-size":"13px",color:"#333C48"},stripe:"",border:"","max-height":e.tableHeight+100},on:{select:e.selectItemChange}},[t("el-table-column",{attrs:{type:"selection",width:"55",label:"选择"}}),t("el-table-column",{attrs:{label:"商品图",width:"120"},scopedSlots:e._u([{key:"default",fn:function(e){return[t("img",{staticStyle:{width:"75px",height:"75px"},attrs:{src:e.row.img,alt:""}})]}}])}),t("el-table-column",{attrs:{prop:"productModuleName",label:"商品名称"}}),t("el-table-column",{attrs:{label:"水票价格"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("span",[e._v("￥"+e._s(a.row.price)+"元")])]}}])}),t("el-table-column",{attrs:{label:"优惠规则"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("div",{staticClass:"flex"},e._l(a.row.list,(function(s,r){return t("el-tag",{key:r,staticStyle:{"margin-bottom":"10px","margin-right":"10px"},attrs:{closable:""},on:{close:function(t){return e.tableDeleteTicketRule(a.row,r)}}},[e._v(e._s(s))])})),1)]}}])}),t("el-table-column",{attrs:{label:"操作",width:"200px"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("div",[t("el-button",{attrs:{type:"text"},on:{click:function(t){return e.sendWater(a.row)}}},[e._v("发送水票")]),t("el-button",{staticStyle:{color:"red"},attrs:{type:"text"},on:{click:function(t){return e.openSetTicketDialog(a.row,2)}}},[e._v("设置买赠优惠")])],1)]}}])})],1),t("div",{staticClass:"flex align-items-center justify-content-center"},[t("el-pagination",{attrs:{layout:"total,prev,pager,next,sizes,jumper",background:"","current-page":e.pagesData.currentPage2,"page-sizes":e.pagesData.currentPageSizes2,"page-size":e.pagesData.pageSize2,total:e.pagesData.pageTotal2},on:{"size-change":e.handleSizeChange2,"current-change":e.handleCurrentChange2}})],1)],1),t("el-dialog",{staticClass:"new-dialog-css",attrs:{visible:e.setTicketRuleShow,width:"30%",center:""},on:{"update:visible":function(t){e.setTicketRuleShow=t}}},[t("div",{staticClass:"new-dialog-title",attrs:{slot:"title"},slot:"title"},[e._v("设置买赠规则")]),t("div",{staticStyle:{height:"400px"}},[t("el-scrollbar",{attrs:{"wrap-class":"default-scrollbar__wrap"}},[t("div",{staticClass:"new-dialog-two-title"},[e._v("已选择的商品")]),t("div",{staticClass:"new-dialog-body"},[t("div",{staticClass:"water-ticket-set-dialog"},e._l(e.chooseTicketDataList,(function(a,s){return t("div",{key:s,staticClass:"padding-tb-10"},[t("span",[e._v(e._s(a.productModuleName?a.productModuleName:a.waterCouponName))])])})),0)]),t("div",{staticClass:"new-dialog-two-title"},[e._v("买赠规则")]),t("div",{staticClass:"margin-top-30"},[e._l(e.rulesList,(function(a,s){return t("el-tag",{key:s,staticStyle:{"margin-right":"10px","margin-bottom":"10px"},attrs:{closable:""},on:{close:function(t){return e.delDetailRule(s)}}},[e._v("\n            "+e._s(a)+"\n          ")])})),t("el-button",{staticStyle:{height:"24px",width:"24px",padding:"0"},attrs:{type:"primary",plain:""},on:{click:e.ruleDetailDialogFun}},[e._v("+")])],2)])],1),t("el-dialog",{staticClass:"new-dialog-css",attrs:{width:"30%",visible:e.ruleDetailDialog,"append-to-body":""},on:{"update:visible":function(t){e.ruleDetailDialog=t}}},[t("div",{staticClass:"new-dialog-title text-align-center",attrs:{slot:"title"},slot:"title"},[e._v("\n        设置买赠规则\n      ")]),t("div",{staticClass:"new-dialog-two-title"},[e._v("买赠规则设置")]),t("el-form",{staticClass:"flex margin-top-30 justify-content-between",attrs:{model:e.waterTicketData,"label-width":"30px"}},[t("el-form-item",{attrs:{label:"买"}},[t("el-input",{attrs:{oninput:"value=value.replace(/[^\\d]/g,'')"},model:{value:e.waterTicketData.buy,callback:function(t){e.$set(e.waterTicketData,"buy",t)},expression:"waterTicketData.buy"}})],1),t("el-form-item",{attrs:{label:"赠"}},[t("el-input",{attrs:{oninput:"value=value.replace(/[^\\d]/g,'')"},model:{value:e.waterTicketData.send,callback:function(t){e.$set(e.waterTicketData,"send",t)},expression:"waterTicketData.send"}})],1)],1),t("div",{staticClass:"text-align-center",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.ruleDetailDialog=!1,e.waterTicketData.buy="",e.waterTicketData.send=""}}},[e._v("取 消")]),t("el-button",{attrs:{type:"primary"},on:{click:e.sureDetailRule}},[e._v("确 定")])],1)],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){return e.closeDialog()}}},[e._v("取 消")]),t("el-button",{attrs:{type:"primary"},on:{click:e.saveTicketSubmit}},[e._v("确 定")])],1)],1),t("el-dialog",{staticClass:"new-dialog-css",attrs:{width:"30%",visible:e.sendVisible,"append-to-body":""},on:{"update:visible":function(t){e.sendVisible=t}}},[t("div",{staticClass:"new-dialog-title text-align-center",attrs:{slot:"title"},slot:"title"},[e._v("\n        发送水票\n      ")]),t("el-form",{attrs:{model:e.sendForm,"label-width":"130px"}},[t("el-form-item",{attrs:{label:"联系人"}},[t("el-input",{model:{value:e.sendForm.waterCouponContent,callback:function(t){e.$set(e.sendForm,"waterCouponContent",t)},expression:"sendForm.waterCouponContent"}})],1),t("el-form-item",{attrs:{label:"水票数量"}},[t("el-input",{model:{value:e.sendForm.waterCouponNum,callback:function(t){e.$set(e.sendForm,"waterCouponNum",t)},expression:"sendForm.waterCouponNum"}})],1)],1),t("div",{staticClass:"text-align-center",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.ruleDetailDialog=!1,e.sendForm.waterCouponId="",e.sendForm.waterCouponContent="",e.sendForm.waterCouponNum=1}}},[e._v("取 消")]),t("el-button",{attrs:{type:"primary"},on:{click:e.sendWaterConfirm}},[e._v("确 定")])],1)],1)],1)},r=[],i={props:{},data:function(){var e=this;return{sendVisible:!1,sendForm:{waterCouponId:"",waterCouponContent:"",waterCouponNum:1},imgUri:this.$imgUri,ticketSetList:[],selectData:{storeId:e.Cookies.get("storeId"),name:"",index:1,pageSize:10},pagesData:{pageTotal2:0,currentPage2:1,currentPageSizes2:[10,15,20],pageSize2:0},setTicketRuleShow:!1,ruleDetailDialog:!1,waterTicketData:{buy:"",send:""},chooseTicketDataList:[],rulesList:[],setRuleType:""}},computed:{tableHeight:function(){console.log(this.$store.getters.getGlobalHeight);var e=Number(this.$store.getters.getGlobalHeight)-300;return e>=300?e:300}},created:function(){},mounted:function(){var e=this;e.load()},watch:{},methods:{sendWaterConfirm:function(){var e=this;return this.sendForm.waterCouponContent?this.sendForm.waterCouponNum?void this.$post("/szmb/szmshopwatercontroller/sendWaterCouponPc",this.sendForm).then((function(t){1===t.code?(e.$message({type:"success",message:"发送成功"}),e.sendVisible=!1,that.load()):that.$message({type:"warning",message:t.data})})).catch((function(e){console.log(e)})):(this.$message({type:"error",message:"请输入数量"}),!1):(this.$message({type:"error",message:"请输入手机号"}),!1)},sendWater:function(e){this.sendForm={waterCouponContent:"",waterCouponNum:1,waterCouponId:e.waterId},this.sendVisible=!0},clearSearch:function(){var e=this;e.selectData={storeId:e.Cookies.get("storeId"),name:"",index:1,pageSize:e.pagesData.pageSize2},e.load()},load:function(){var e=this,t="/szmb/szmshopwatercontroller/selctshopwaterpc";e.$post(t,e.selectData).then((function(t){1==t.code?(e.ticketSetList=t.data.list,e.pagesData.pageTotal2=t.data.total,setTimeout((function(){e.ticketSetList.forEach((function(t){1==t.state&&e.$refs.waterTicketMultipleTable.toggleRowSelection(t,!0)}))}),0)):(e.ticketSetList=[],e.pagesData.pageTotal2=0)}))},handleSizeChange2:function(e){this.pagesData.pageSize2=e,this.selectData.pageSize=e,this.load()},handleCurrentChange2:function(e){this.pagesData.currentPage2=e,this.selectData.index=e,this.load()},selectItemChange:function(e,t){var a=0;console.log(565656,e,t);var s=e.length&&-1!==e.indexOf(t);console.log(s),a=s?1:0,this.updateSelectedState(t.waterId,a)},updateSelectedState:function(e,t){var a=this;a.$post("/szmb/szmshopwatercontroller/updatepitch",{waterId:e,state:t}).then((function(e){1===e.code?a.load():a.$message({type:"warning",message:e.data})})).catch((function(e){console.log(e)}))},openSetTicketDialog:function(e,t){if(this.setRuleType=t,1==t)this.selectStorePitch();else if(2==t){var a=Object.assign([],e.list);this.chooseTicketDataList=[e],this.rulesList=a,this.setTicketRuleShow=!0}},selectStorePitch:function(){var e=this;this.$post("/szmb/szmshopwatercontroller/selectstorepitch",{storeId:this.Cookies.get("storeId")}).then((function(t){1==t.code?t.data.length>0?(e.chooseTicketDataList=t.data,e.rulesList=[],e.setTicketRuleShow=!0):e.$message.warning("至少选择一条记录"):e.$message.error(t.data)})).catch((function(e){console.log(e)}))},closeDialog:function(){this.setTicketRuleShow=!1},ruleDetailDialogFun:function(){this.ruleDetailDialog=!0},closeRuleDetailDialog:function(){this.ruleDetailDialog=!1},sureDetailRule:function(){var e=this;if("0"!=e.waterTicketData.buy&&e.waterTicketData.buy&&e.waterTicketData.send&&"0"!=e.waterTicketData.send){var t="买"+e.waterTicketData.buy+"赠"+e.waterTicketData.send;e.rulesList.push(t),e.waterTicketData.buy="",e.waterTicketData.send="",e.ruleDetailDialog=!1}else e.$message.error("请设置有效数值")},delDetailRule:function(e){this.rulesList.splice(e,1)},saveTicketSubmit:function(){var e="",t={},a="",s=[],r=[];this.rulesList.forEach((function(e){var t=e.replace(/[^0-9]/gi,",");r.push(t.substr(1))})),a=r.join("|"),console.log(a),1==this.setRuleType?(this.chooseTicketDataList.forEach((function(e){s.push(e.waterCouponId)})),console.log(JSON.stringify(s)),e="/szmb/szmshopwatercontroller/updatewaterrule",t={waterList:JSON.stringify({list:s,discount:a})}):2==this.setRuleType&&(e="/szmb/szmshopwatercontroller/updatewaterall",t={waterCouponId:this.chooseTicketDataList[0].waterId,r5:a,header:"json"}),this.goSaveSubmitApi(e,t)},goSaveSubmitApi:function(e,t){var a=this;this.$post(e,t).then((function(e){1==e.code?(a.$message.success("设置成功！"),1==a.setRuleType?a.$post("/szmb/szmshopwatercontroller/updatestorepitchall",{storeId:a.Cookies.get("storeId")}).then((function(e){a.load()})).catch((function(e){console.log(e)})):2==a.setRuleType&&a.load()):a.$message.error(e.data),a.closeDialog()})).catch((function(e){console.log(e)}))},tableDeleteTicketRule:function(e,t){var a=this,s="",r=[];e.list.splice(t,1),e.list.forEach((function(e){var t=e.replace(/[^0-9]/gi,",");r.push(t.substr(1))})),s=r.join("|");var i={waterCouponId:e.waterId,r5:s,header:"json"};this.$post("/szmb/szmshopwatercontroller/updatewaterall",i).then((function(e){1==e.code?(a.$message.success("删除成功！"),a.load()):a.$message.error(e.data)})).catch((function(e){console.log(e)}))}},components:{}},o=i,l=(a("ddeb"),a("0c7c")),n=Object(l["a"])(o,s,r,!1,null,"9cb57112",null);t["default"]=n.exports},"6d2b":function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e._self._c;return t("div",[t("div",{staticClass:"headBox"},[t("div",{staticClass:"flex algin-items-center padding-tb-10"},[t("div",{staticClass:"optionBox"},[t("el-select",{attrs:{clearable:!0,placeholder:"按商品分类筛选",filterable:""},on:{change:e.chooseClassify},model:{value:e.selectData.classId,callback:function(t){e.$set(e.selectData,"classId",t)},expression:"selectData.classId"}},e._l(e.classifyOptions,(function(e){return t("el-option",{key:e.classifyId,attrs:{label:e.classifyName,value:e.classifyId}})})),1)],1),t("div",{staticClass:"optionBox"},[t("el-select",{attrs:{clearable:!0,placeholder:"按商品品牌筛选",filterable:""},model:{value:e.selectData.brandId,callback:function(t){e.$set(e.selectData,"brandId",t)},expression:"selectData.brandId"}},e._l(e.bucketBrandList,(function(e,a){return t("el-option",{key:a,attrs:{label:e.brandname,value:e.brandid}})})),1)],1),t("div",{staticClass:"optionBox"},[t("el-select",{attrs:{clearable:!0,placeholder:"按商品上架状态筛选",filterable:""},model:{value:e.selectData.state,callback:function(t){e.$set(e.selectData,"state",t)},expression:"selectData.state"}},e._l(e.upShelfList,(function(e){return t("el-option",{key:e.classifyId,attrs:{label:e.name,value:e.value}})})),1)],1),t("div",{staticClass:"margin-right-10"},[t("el-input",{attrs:{placeholder:"请输入商品名称",clearable:""},model:{value:e.selectData.isKey,callback:function(t){e.$set(e.selectData,"isKey",t)},expression:"selectData.isKey"}})],1),t("el-button",{attrs:{type:"primary"},on:{click:e.toSearch}},[e._v("查询")]),t("el-button",{staticStyle:{"margin-left":"20px"},on:{click:e.clearSearch}},[e._v("清空筛选条件")]),t("el-button",{attrs:{type:"primary"},on:{click:e.toBrand}},[e._v("品牌管理")]),t("el-button",{attrs:{type:"primary"},on:{click:e.toClassify}},[e._v("分类管理")]),t("el-button",{attrs:{type:"primary"},on:{click:e.toAddGood}},[e._v("新增商品")]),t("el-button",{attrs:{type:"primary",disabled:0===e.multipleSelection.length},on:{click:e.batchUpShelf}},[e._v("批量上架")]),t("el-button",{attrs:{type:"warning"},on:{click:e.showBatchDeliveryFeeDialog}},[e._v("一键设置配送费")])],1)]),t("div",{staticClass:"tableBox margin-top-10"},[t("el-table",{staticStyle:{width:"100%"},attrs:{data:e.list,"max-height":e.tableHeight+70,"header-cell-style":{"text-align":"center","background-color":"#EFF2F7"},"cell-style":{"text-align":"center","font-size":"13px",color:"#333C48"},stripe:"",border:""},on:{"selection-change":e.handleSelectionChange}},[t("el-table-column",{attrs:{type:"selection",width:"55",selectable:e.checkSelectable}}),t("el-table-column",{attrs:{prop:"",label:"商品图",width:"100"},scopedSlots:e._u([{key:"default",fn:function(e){return[t("img",{staticStyle:{width:"75px",height:"75px"},attrs:{src:e.row.img,alt:""}})]}}])}),t("el-table-column",{attrs:{prop:"commodityName",label:"商品名称"}}),t("el-table-column",{attrs:{label:"桶类型"},scopedSlots:e._u([{key:"default",fn:function(t){return[0!=t.row.lists?[e._v("\n            "+e._s("0"==t.row.lists[0].buckState?"常规桶":"1"==t.row.lists[0].buckState?"一次性桶":"-")+"\n          ")]:[e._v("-")]]}}])}),t("el-table-column",{attrs:{label:"商品类型"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n          "+e._s(null!==t.row.type&&void 0!==t.row.type&&""!==t.row.type?e.productTypeList[t.row.type].value:"-")+"\n        ")]}}])}),t("el-table-column",{attrs:{label:"商品重量"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n          "+e._s(t.row.weight)+"L\n        ")]}}])}),t("el-table-column",{attrs:{label:"配送费",width:"200"},scopedSlots:e._u([{key:"default",fn:function(a){return[a.row.deliveryfee?t("div",{staticClass:"delivery-fee-table-cell",on:{click:function(t){return e.update(a.row)}}},e._l(e.parseDeliveryFee(a.row.deliveryfee),(function(s,r){return t("div",{key:r,staticClass:"delivery-fee-item"},[t("span",{staticClass:"delivery-range"},[t("span",0===r?[e._v("0件")]:[e._v(e._s(e.parseDeliveryFee(a.row.deliveryfee)[r-1].max)+"件")]),t("span",[e._v(" - ")]),r!==e.parseDeliveryFee(a.row.deliveryfee).length-1?t("span",[e._v(e._s(s.max)+"件")]):t("span",[e._v("不限")])]),t("span",{staticClass:"delivery-fee"},[e._v(e._s(s.fee)+"元")])])})),0):t("span",[e._v("-")])]}}])}),t("el-table-column",{attrs:{label:"零售价",width:"180"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-input",{attrs:{placeholder:"请输入零售价",disabled:!!a.row.storeState},on:{input:function(t){return e.getRetail(t,a.row.lists[0].retail,a.row.lists[0].cost,a.$index)}},model:{value:a.row.lists[0].retail,callback:function(t){e.$set(a.row.lists[0],"retail",t)},expression:"scope.row.lists[0].retail"}})]}}])}),t("el-table-column",{attrs:{prop:"",label:"成本价",width:"180"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-input",{attrs:{placeholder:"请输入成本价",disabled:!!a.row.storeState},on:{input:function(t){return e.getCost(t,a.row.lists[0].retail,a.row.lists[0].cost,a.$index)}},model:{value:a.row.lists[0].cost,callback:function(t){e.$set(a.row.lists[0],"cost",t)},expression:"scope.row.lists[0].cost"}})]}}])}),t("el-table-column",{attrs:{prop:"",label:"毛利",width:"100"},scopedSlots:e._u([{key:"default",fn:function(a){return[a.row.lists[0].profit>0?t("span",{staticClass:"color-green"},[e._v("￥"+e._s(a.row.lists[0].profit?a.row.lists[0].profit:"0.00"))]):a.row.lists[0].profit<0?t("span",{staticClass:"color-red"},[e._v("￥"+e._s(a.row.lists[0].profit?a.row.lists[0].profit:"0.00"))]):t("span",[e._v("￥"+e._s(a.row.lists[0].profit?a.row.lists[0].profit:"0.00"))])]}}])}),t("el-table-column",{attrs:{prop:"",label:"实际库存",width:"180"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-input",{attrs:{placeholder:"请输入实际库存",disabled:!!a.row.storeState},on:{input:function(t){return e.getStock(t,a.row.lists[0].stock,a.$index)}},model:{value:a.row.lists[0].stock,callback:function(t){e.$set(a.row.lists[0],"stock",t)},expression:"scope.row.lists[0].stock"}})]}}])}),t("el-table-column",{attrs:{prop:"",label:"虚拟库存",width:"180"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-input",{attrs:{oninput:"value=value.replace(/[^\\d]/g,'')",placeholder:"请输入虚拟库存",disabled:!!a.row.storeState},model:{value:a.row.lists[0].virtualStock,callback:function(t){e.$set(a.row.lists[0],"virtualStock",t)},expression:"scope.row.lists[0].virtualStock"}})]}}])}),t("el-table-column",{attrs:{prop:"",label:"操作",width:"120"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-button",{attrs:{type:"text"},on:{click:function(t){return e.update(a.row)}}},[e._v("修改")]),a.row.storeState?t("el-button",{attrs:{disabled:!!a.row.storeState,type:"text"}},[e._v("已上架")]):t("el-button",{attrs:{type:"text"},on:{click:function(t){return e.upShelf(a.row)}}},[e._v("上架")])]}}])})],1),t("div",{staticClass:"flex align-items-center justify-content-center"},[t("el-pagination",{staticStyle:{padding:"15px"},attrs:{background:"",layout:"prev, pager, next",total:e.pageCount,"current-page":e.page},on:{"current-change":e.clickPage}})],1)],1),t("el-dialog",{attrs:{title:"新增商品",visible:e.addGoodDialog,width:"50%"},on:{"update:visible":function(t){e.addGoodDialog=t}}},[t("el-form",{ref:"addGoodForm",attrs:{model:e.addGoodForm,"label-width":"100px"}},[t("el-form-item",{attrs:{label:"商品分类",prop:"classifyId",required:""}},[t("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择商品分类"},on:{change:e.handleClassifyChange},model:{value:e.addGoodForm.classifyId,callback:function(t){e.$set(e.addGoodForm,"classifyId",t)},expression:"addGoodForm.classifyId"}},e._l(e.classifyOptions,(function(e){return t("el-option",{key:e.classifyId,attrs:{label:e.classifyName,value:e.classifyId}})})),1)],1),t("el-form-item",{attrs:{label:"品牌分类",prop:"brandId",required:""}},[t("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择品牌"},on:{change:e.handleBrandChange},model:{value:e.addGoodForm.brandId,callback:function(t){e.$set(e.addGoodForm,"brandId",t)},expression:"addGoodForm.brandId"}},e._l(e.bucketBrandList,(function(e){return t("el-option",{key:e.brandid,attrs:{label:e.brandname,value:e.brandid}})})),1)],1),t("el-form-item",{attrs:{label:"商品主图"}},[t("el-upload",{ref:"uploadMain",attrs:{action:"https://szmsh.waterstation.com.cn/szm/uploadcontroller/upload","list-type":"picture-card","file-list":e.mainImageList,"on-preview":e.handlePictureCardPreview1,"on-remove":e.handleMainImgRemove,"on-success":e.uploadMainImg,"on-error":e.uploadError,"on-exceed":e.uploadExceed,"before-upload":e.beforeUpload,multiple:!1,accept:".jpg,.png,.jpeg,.gif",limit:1}},[t("i",{staticClass:"el-icon-plus"})]),t("div",[t("span",{staticClass:"font-size-12 color-grey"},[e._v("商品主图，用于商品列表展示(1张)")])])],1),t("el-form-item",{attrs:{label:"商品轮播图"}},[t("el-upload",{ref:"uploadCarousel",attrs:{action:"https://szmsh.waterstation.com.cn/szm/uploadcontroller/upload","list-type":"picture-card","file-list":e.carouselImageList,"on-preview":e.handlePictureCardPreview1,"on-remove":e.handleCarouselImgRemove,"on-success":e.uploadCarouselImg,"on-error":e.uploadError,"on-exceed":e.uploadExceed,"before-upload":e.beforeUpload,multiple:!1,accept:".jpg,.png,.jpeg,.gif",limit:1}},[t("i",{staticClass:"el-icon-plus"})]),t("div",[t("span",{staticClass:"font-size-12 color-grey"},[e._v("商品详情页轮播图片(1张)")])])],1),t("el-form-item",{attrs:{label:"商品详情图"}},[t("el-upload",{ref:"uploadDetail",attrs:{action:"https://szmsh.waterstation.com.cn/szm/uploadcontroller/upload","list-type":"picture-card","file-list":e.detailImageList,"on-preview":e.handlePictureCardPreview1,"on-remove":e.handleDetailImgRemove,"on-success":e.uploadDetailImg,"on-error":e.uploadError,"on-exceed":e.uploadExceed,"before-upload":e.beforeUpload,multiple:!1,accept:".jpg,.png,.jpeg,.gif",limit:1}},[t("i",{staticClass:"el-icon-plus"})]),t("div",[t("span",{staticClass:"font-size-12 color-grey"},[e._v("商品详情描述图片(1张)")])])],1),t("el-form-item",{attrs:{label:"商品名称",prop:"commodityName",required:""}},[t("el-input",{attrs:{placeholder:"请输入商品名称"},on:{input:e.handleTitleChange},model:{value:e.addGoodForm.commodityName,callback:function(t){e.$set(e.addGoodForm,"commodityName",t)},expression:"addGoodForm.commodityName"}})],1),t("el-form-item",{attrs:{label:"69码",prop:"sixnight"}},[t("el-input",{attrs:{placeholder:"请输入69码"},model:{value:e.addGoodForm.sixnight,callback:function(t){e.$set(e.addGoodForm,"sixnight",t)},expression:"addGoodForm.sixnight"}})],1),t("el-form-item",{attrs:{label:"商品规格",prop:"details"}},[t("el-input",{attrs:{placeholder:"请输入商品规格"},on:{input:e.handleSpecChange},model:{value:e.addGoodForm.details,callback:function(t){e.$set(e.addGoodForm,"details",t)},expression:"addGoodForm.details"}})],1),t("el-form-item",{attrs:{label:"商品重量",prop:"weight"}},[t("el-input",{attrs:{placeholder:"请输入商品重量",type:"number"},model:{value:e.addGoodForm.weight,callback:function(t){e.$set(e.addGoodForm,"weight",t)},expression:"addGoodForm.weight"}},[t("template",{slot:"append"},[e._v("L")])],2)],1),t("el-form-item",{attrs:{label:"商品类型",prop:"type"}},[t("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择商品类型"},model:{value:e.addGoodForm.type,callback:function(t){e.$set(e.addGoodForm,"type",t)},expression:"addGoodForm.type"}},e._l(e.productTypeList,(function(e){return t("el-option",{key:e.key,attrs:{label:e.value,value:e.key.toString()}})})),1)],1),t("el-form-item",{attrs:{label:"配送费设置"}},[t("div",{staticClass:"delivery-fee-steps"},[e._l(e.addGoodForm.deliveryfee,(function(a,s){return t("div",{key:s,staticStyle:{"margin-bottom":"10px",display:"flex","align-items":"center"}},[t("span",0===s?[e._v("0件")]:[e._v(e._s(e.addGoodForm.deliveryfee[s-1].max)+"件")]),t("span",{staticStyle:{margin:"0 10px"}},[e._v("至")]),s!==e.addGoodForm.deliveryfee.length-1?t("el-input",{staticStyle:{width:"150px"},attrs:{placeholder:"件数"},model:{value:a.max,callback:function(t){e.$set(a,"max",t)},expression:"item.max"}},[t("template",{slot:"append"},[e._v("件")])],2):t("span",[e._v("不限")]),t("span",{staticStyle:{margin:"0 10px"}},[e._v("收费")]),t("el-input",{staticStyle:{width:"150px"},attrs:{placeholder:"配送费"},model:{value:a.fee,callback:function(t){e.$set(a,"fee",t)},expression:"item.fee"}},[t("template",{slot:"append"},[e._v("元")])],2),e.addGoodForm.deliveryfee.length>1?t("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"danger",icon:"el-icon-delete",circle:"",size:"mini"},on:{click:function(t){return e.removeDeliveryFeeStep(s)}}}):e._e()],1)})),t("el-button",{staticStyle:{"margin-top":"10px"},attrs:{type:"primary",size:"small"},on:{click:e.addDeliveryFeeStep}},[t("i",{staticClass:"el-icon-plus"}),e._v(" 添加阶梯\n          ")])],2)]),t("el-form-item",{attrs:{label:"预估成本",prop:"price"}},[t("el-input",{attrs:{placeholder:"请输入商品预估成本",type:"number"},model:{value:e.addGoodForm.price,callback:function(t){e.$set(e.addGoodForm,"price",t)},expression:"addGoodForm.price"}},[t("template",{slot:"append"},[e._v("元")])],2)],1),t("el-form-item",{attrs:{label:"预估零售价",prop:"sellprice"}},[t("el-input",{attrs:{placeholder:"请输入商品预估零售价",type:"number"},model:{value:e.addGoodForm.sellprice,callback:function(t){e.$set(e.addGoodForm,"sellprice",t)},expression:"addGoodForm.sellprice"}},[t("template",{slot:"append"},[e._v("元")])],2)],1)],1),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.addGoodDialog=!1}}},[e._v("取 消")]),t("el-button",{attrs:{type:"primary"},on:{click:e.submitAddGood}},[e._v("确 定")])],1)],1),t("el-dialog",{attrs:{title:"阶梯配送费详情",visible:e.deliveryFeeDialogVisible,width:"500px"},on:{"update:visible":function(t){e.deliveryFeeDialogVisible=t}}},[t("div",{staticClass:"delivery-fee-details"},e._l(e.currentDeliveryFeeSteps,(function(a,s){return t("div",{key:s,staticClass:"delivery-fee-step"},[t("div",{staticClass:"step-range"},[t("span",0===s?[e._v("0件")]:[e._v(e._s(e.currentDeliveryFeeSteps[s-1].max)+"件")]),t("span",[e._v(" - ")]),s!==e.currentDeliveryFeeSteps.length-1?t("span",[e._v(e._s(a.max)+"件")]):t("span",[e._v("不限")])]),t("div",{staticClass:"step-fee"},[e._v(e._s(a.fee)+"元")])])})),0)]),t("el-dialog",{attrs:{title:"批量设置配送费",visible:e.batchDeliveryFeeDialogVisible,width:"600px"},on:{"update:visible":function(t){e.batchDeliveryFeeDialogVisible=t}}},[t("div",{staticClass:"batch-delivery-fee"},[t("el-alert",{attrs:{title:"注意：此操作将为所有商品设置统一的阶梯配送费，并覆盖之前所有的配送费设置！",type:"warning",closable:!1,"show-icon":""}}),t("div",{staticClass:"delivery-fee-steps",staticStyle:{"margin-top":"20px"}},[e._l(e.batchDeliveryFeeSteps,(function(a,s){return t("div",{key:s,staticStyle:{"margin-bottom":"10px",display:"flex","align-items":"center"}},[t("span",0===s?[e._v("0件")]:[e._v(e._s(e.batchDeliveryFeeSteps[s-1].max)+"件")]),t("span",{staticStyle:{margin:"0 10px"}},[e._v("至")]),s!==e.batchDeliveryFeeSteps.length-1?t("el-input",{staticStyle:{width:"150px"},attrs:{placeholder:"件数"},model:{value:a.max,callback:function(t){e.$set(a,"max",t)},expression:"item.max"}},[t("template",{slot:"append"},[e._v("件")])],2):t("span",[e._v("不限")]),t("span",{staticStyle:{margin:"0 10px"}},[e._v("收费")]),t("el-input",{staticStyle:{width:"150px"},attrs:{placeholder:"配送费"},model:{value:a.fee,callback:function(t){e.$set(a,"fee",t)},expression:"item.fee"}},[t("template",{slot:"append"},[e._v("元")])],2),e.batchDeliveryFeeSteps.length>1?t("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"danger",icon:"el-icon-delete",circle:"",size:"mini"},on:{click:function(t){return e.removeBatchDeliveryFeeStep(s)}}}):e._e()],1)})),t("el-button",{staticStyle:{"margin-top":"10px"},attrs:{type:"primary",size:"small"},on:{click:e.addBatchDeliveryFeeStep}},[t("i",{staticClass:"el-icon-plus"}),e._v(" 添加阶梯\n        ")])],2)],1),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.batchDeliveryFeeDialogVisible=!1}}},[e._v("取 消")]),t("el-button",{attrs:{type:"primary"},on:{click:e.applyBatchDeliveryFee}},[e._v("确 定")])],1)])],1)},r=[],i=a("81d3");function o(e){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o(e)}function l(e,t,a){return(t=n(t))in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}function n(e){var t=c(e,"string");return"symbol"==o(t)?t:t+""}function c(e,t){if("object"!=o(e)||!e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var s=a.call(e,t||"default");if("object"!=o(s))return s;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}var d={props:{},data:function(){return{imgUri:this.$imgUri,classifyOptions:[],bucketBrandList:[],upShelfList:[{name:"已上架",value:1},{name:"未上架",value:2}],productTypeList:i["a"],list:[],multipleSelection:[],selectData:{classId:"",brandId:"",state:"",isKey:""},retail:"",cost:"",profit:"",stock:"",virtualStock:"",page:1,pageCount:0,bucketDialog:!1,bucketList:[],addGoodDialog:!1,specImgsModal1:!1,dialogImageUrl:"",productImgs:[],mainImage:"",carouselImages:[],detailImage:"",goodsClassifyName:"请选择分类",brandName:"请选择品牌",goodsTitle:"",goodsSpec:"",spuId:0,skuId:0,waterList:[],fileList:[],mainImageList:[],carouselImageList:[],detailImageList:[],isSubmit:0,addGoodForm:{classifyId:"",brandId:"",commodityName:"",details:"",productImgs:[],mainImage:"",carouselImages:[],detailImage:"",weight:0,type:0,deliveryfee:[{max:null,fee:0}],price:0,sellprice:0,sixnight:""},deliveryFeeDialogVisible:!1,currentDeliveryFeeSteps:[],batchDeliveryFeeDialogVisible:!1,batchDeliveryFeeSteps:[{max:null,fee:0}]}},computed:{tableHeight:function(){console.log(this.$store.getters.getGlobalHeight);var e=Number(this.$store.getters.getGlobalHeight)-300;return e>=300?e:300},inTableHeight:function(){var e=this.$store.getters.getGlobalHeight;return e>=400?parseInt(this.$util.mul(e,.5)):400}},created:function(){},mounted:function(){var e=this;e.load(),e.lookUpBrand()},watch:{},methods:{toBrand:function(){this.$router.push({path:"/brandProductLibrary"})},toClassify:function(){this.$router.push({path:"/classifyProductLibrary"})},load:function(){var e=this,t="/szmb/newinsertproductcontroller/selectstoreproductpc",a={storeId:e.Cookies.get("storeId"),classId:e.selectData.classId?e.selectData.classId:0,brandId:e.selectData.brandId,index:e.page,productName:e.selectData.isKey,state:e.selectData.state?e.selectData.state:0};e.$post(t,a).then((function(t){if(e.classifyLoad(),1==t.code){var a=t.data.shopContents;a.forEach((function(t){var a=t.lists[0].cost,s=t.lists[0].retail;t.lists[0].profit=e.$util.sub(s,a),t.detailImages&&t.detailImages.length>0&&console.log("商品详情图数据:",t.commodityName,t.detailImages)})),e.list=a,e.pageCount=t.data.pageCount}}))},classifyLoad:function(){var e=this,t=this,a="/szmb/newclasscontroller/selectclass";t.$post(a,{}).then((function(a){1==a.code?t.classifyOptions=a.data:e.$alert(a.data,"温馨提示",{confirmButtonText:"确定",callback:function(e){}})}))},lookUpBrand:function(){var e=this,t="/szmb/newclasscontroller/selectbrand",a={};e.$post(t,a).then((function(t){1==t.code?e.bucketBrandList=t.data:e.$message.error(t.data)}))},clickPage:function(e){var t=this;t.page=e,t.load()},chooseClassify:function(e){var t=this;e||(t.selectData.classId=""),t.selectData.isKey=""},clearSearch:function(){var e=this;e.page=1,e.selectData={classId:"",brandId:"",state:"",isKey:""},e.load()},toSearch:function(){var e=this;e.page=1,e.load()},getRetail:function(e,t,a,s){var r=this;e=e.replace(/[^\d.]/g,""),r.list[s].lists[0].retail=e,isNaN(t)||isNaN(a)||r.$set(r.list[s].lists[0],"profit",r.$util.sub(t,a).toFixed(2))},getCost:function(e,t,a,s){var r=this;e=e.replace(/[^\d.]/g,""),r.list[s].lists[0].cost=e,isNaN(t)||isNaN(a)||r.$set(r.list[s].lists[0],"profit",r.$util.sub(t,a).toFixed(2))},getMarketPrice:function(e,t,a,s){var r=this;e=e.replace(/[^\d.]/g,""),r.list[s].lists[0].marketPrice=e},getStock:function(e,t,a){var s=this;e=e.replace(/[^\d]/g,""),s.list[a].lists[0].stock=e,isNaN(t)||s.$set(s.list[a].lists[0],"virtualStock",2*t)},upShelf:function(e){console.log(e);var t=this,a=e;if(!Number(a.lists[0].retail))return console.log(Number(a.lists[0].profit.retail)),void t.$message.error("请输入有效的零售价");if(Number(a.lists[0].cost))if(Number(a.lists[0].stock)){var s=[l(l(l(l(l({storeId:t.Cookies.get("storeId"),id:a.classId,classNmae:a.className,brandId:a.brandId,brandName:a.brandName,commodityName:a.commodityName,source:1,spuId:0,type:0,productDescribe:a.productDescribe,weight:a.weight},"type",a.type),"deliveryfee",a.deliveryfee),"price",a.price),"sellprice",a.sellprice),"lists",[{imgList:a.lists[0].imgList,skuId:0,cost:a.lists[0].cost,marketPrice:a.lists[0].marketPrice,retail:a.lists[0].retail,stock:a.lists[0].stock,virtualStock:a.lists[0].virtualStock,source:0,details:a.lists[0].skuName,isWater:0,integralChecked:1,buckState:a.lists[0].buckState}])],r="/szmb/newinsertproductcontroller/insertproductlist",i={list:s,header:"json"};t.$post(r,i).then((function(e){1==e.code?(t.$message({message:"上架成功！",type:"success"}),t.load()):t.$message.error(e.data)}))}else t.$message.error("请输入有效的库存数量");else t.$message.error("请输入有效的成本价")},setBucketMoney:function(){var e=this,t="/szmb/szmbuserandstorebillcontroller/selectstorebuck",a={storeId:e.Cookies.get("storeId")};e.$post(t,a).then((function(t){1==t.code?(e.bucketList=t.data,e.bucketDialog=!0):e.$message.error(t.data)}))},bucketSetSubmit:function(){var e=this,t=e.bucketList,a=t.map((function(e){var t={brandId:e.brandId,money:e.money?e.money:0};return t})),s="/szmb/szmbuserandstorebillcontroller/insertstorebuck",r={storeId:e.Cookies.get("storeId"),brandList:JSON.stringify(a)};e.$post(s,r).then((function(t){1==t.code?(e.$message({message:"设置成功!",type:"success"}),e.bucketDialog=!1):e.$message.error(t.data)}))},handleClassifyChange:function(e){var t=this,a=t.classifyOptions.find((function(t){return t.classifyId===e}));t.goodsClassifyName=a?a.classifyName:"请选择分类"},handleBrandChange:function(e){var t=this,a=t.bucketBrandList.find((function(t){return t.brandid===e}));t.brandName=a?a.brandname:"请选择品牌"},handleTitleChange:function(e){this.goodsTitle=e},handleSpecChange:function(e){this.goodsSpec=e},toAddGood:function(){var e=this;e.addGoodDialog=!0,e.productImgs=[],e.mainImage="",e.carouselImages=[],e.detailImage="",e.goodsClassifyName="请选择分类",e.brandName="请选择品牌",e.goodsTitle="",e.goodsSpec="",e.spuId=0,e.skuId=0,e.waterList=[],e.fileList=[],e.mainImageList=[],e.carouselImageList=[],e.detailImageList=[],e.addGoodForm={classifyId:"",brandId:"",commodityName:"",details:"",productImgs:[],mainImage:"",carouselImages:[],detailImage:"",weight:0,type:0,deliveryfee:[{max:null,fee:0}],price:0,sellprice:0,sixnight:"",buckState:"0"},e.$nextTick((function(){e.$refs.uploadMain&&e.$refs.uploadMain.clearFiles(),e.$refs.uploadCarousel&&e.$refs.uploadCarousel.clearFiles(),e.$refs.uploadDetail&&e.$refs.uploadDetail.clearFiles(),e.$refs.upload1&&e.$refs.upload1.clearFiles()}))},handlePictureCardPreview1:function(e){this.dialogImageUrl=e.url,this.specImgsModal1=!0},beforeUpload:function(e){var t=/^image\//.test(e.type),a=e.size/1024/1024<2;return t?!!a||(this.$message.error("上传图片大小不能超过 2MB!"),!1):(this.$message.error("只能上传图片文件!"),!1)},uploadMainImg:function(e,t,a){console.log("主图上传:",e,a);var s=this;1==e.code?(s.mainImageList=a,s.mainImage="https://waterstation.com.cn/szm"+e.data,s.addGoodForm.mainImage="https://waterstation.com.cn/szm"+e.data,console.log(s.addGoodForm.mainImage),s.$message.success("主图上传成功")):(s.$message.error(e.data||"主图上传失败"),s.mainImageList=a.filter((function(e){return e.uid!==t.uid})))},handleMainImgRemove:function(e,t){console.log("主图删除:",e,t);var a=this;a.mainImageList=t,0===t.length&&(a.mainImage="",a.addGoodForm.mainImage="")},uploadCarouselImg:function(e,t,a){console.log("轮播图上传:",e,a);var s=this;1==e.code?(s.carouselImageList=a,s.carouselImages=["https://waterstation.com.cn/szm"+e.data],s.addGoodForm.carouselImages=["https://waterstation.com.cn/szm"+e.data],s.productImgs=["https://waterstation.com.cn/szm"+e.data],s.addGoodForm.productImgs=["https://waterstation.com.cn/szm"+e.data],s.$message.success("轮播图上传成功")):(s.$message.error(e.data||"轮播图上传失败"),s.carouselImageList=a.filter((function(e){return e.uid!==t.uid})))},handleCarouselImgRemove:function(e,t){console.log("轮播图删除:",e,t);var a=this;a.carouselImageList=t,0===t.length&&(a.carouselImages=[],a.addGoodForm.carouselImages=[],a.productImgs=[],a.addGoodForm.productImgs=[])},uploadDetailImg:function(e,t,a){console.log("详情图上传:",e,a);var s=this;1==e.code?(s.detailImageList=a,s.detailImage="https://waterstation.com.cn/szm"+e.data,s.addGoodForm.detailImage="https://waterstation.com.cn/szm"+e.data,s.$message.success("详情图上传成功")):(s.$message.error(e.data||"详情图上传失败"),s.detailImageList=a.filter((function(e){return e.uid!==t.uid})))},handleDetailImgRemove:function(e,t){console.log("详情图删除:",e,t);var a=this;a.detailImageList=t,0===t.length&&(a.detailImage="",a.addGoodForm.detailImage="")},uploadImg1:function(e,t,a){this.uploadCarouselImg(e,t,a)},handleImgRemove:function(e,t){this.handleCarouselImgRemove(e,t)},uploadError:function(){var e=this;e.$message.error("图片上传失败!")},uploadExceed:function(){var e=this;e.$message.error("图片数量已超上限!")},submitAddGood:function(){var e=this;if(1!=e.isSubmit){e.isSubmit=1,setTimeout((function(){e.isSubmit=0}),2e3);var t=e.addGoodForm.classifyId,a=e.addGoodForm.brandId,s=e.addGoodForm.commodityName,r=e.addGoodForm.details,i=e.addGoodForm.mainImage||"",o=e.addGoodForm.carouselImages||[],l=e.addGoodForm.detailImage||"",n=o.length>0?o:i?[i]:[],c=e.addGoodForm.weight,d=e.addGoodForm.type,u=e.addGoodForm.deliveryfee,m=e.addGoodForm.price,p=e.addGoodForm.sellprice;if(!t)return e.$message.error("请选择商品分类"),void(e.isSubmit=0);if(!a)return e.$message.error("请选择品牌分类"),void(e.isSubmit=0);if(!s)return e.$message.error("请输入商品名称"),void(e.isSubmit=0);if(!r)return e.$message.error("请输入商品规格"),void(e.isSubmit=0);if(!u||0===u.length)return e.$message.error("请设置阶梯配送费"),void(e.isSubmit=0);for(var f=0;f<u.length;f++){if(f<u.length-1){if(!u[f].max)return e.$message.error("请输入第".concat(f+1,"个配送区间的上限件数")),void(e.isSubmit=0);if(f>0&&Number(u[f].max)<=Number(u[f-1].max))return e.$message.error("第".concat(f+1,"个配送区间的上限必须大于前一个区间的上限")),void(e.isSubmit=0)}if(void 0===u[f].fee||null===u[f].fee||""===u[f].fee)return e.$message.error("请输入第".concat(f+1,"个配送区间的配送费")),void(e.isSubmit=0)}var g=e.classifyOptions.find((function(e){return e.classifyId===t})),h=e.bucketBrandList.find((function(e){return e.brandid===a})),b=g?g.classifyName:"",v=h?h.brandname:"",y={commodityName:s,lists:[{details:r,imgList:n,skuId:e.skuId}],id:t,brandId:a,spuId:e.spuId,brandName:v,className:b,waterList:JSON.stringify(e.waterList||[]),weight:c,productNewType:d,deliveryfee:JSON.stringify(u),price:m,sellprice:p,spec:r,sixnight:e.addGoodForm.sixnight,mainImage:i,detailImage:l,header:"json"},w="/szmb/newinsertproductcontroller/insertproduct";e.$post(w,y).then((function(t){1==t.code?(e.$message({message:"新增商品成功！",type:"success"}),e.addGoodDialog=!1,e.load()):e.$message.error(t.data),e.isSubmit=0})).catch((function(t){e.isSubmit=0,e.$message.error("提交失败，请稍后重试")}))}else e.$message.error("操作过于频繁，请稍后再试~")},update:function(e){var t=this;if(t.addGoodDialog=!0,t.spuId=e.commodityId||0,t.mainImage=e.img||"",t.addGoodForm.mainImage=t.mainImage,t.mainImageList=t.mainImage?[{name:t.mainImage.split("/").pop(),url:t.mainImage}]:[],e.lists&&e.lists.length>0){t.skuId=e.lists[0].skuId||0;var a=e.lists[0].imgList||[],s=a.length>0?a[0]:"";t.carouselImages=s?[s]:[],t.addGoodForm.carouselImages=t.carouselImages,t.carouselImageList=s?[{name:s.split("/").pop(),url:s}]:[],t.productImgs=t.carouselImages,t.addGoodForm.productImgs=t.carouselImages,t.fileList=t.carouselImageList,t.addGoodForm.details=e.lists[0].skuName||"",t.addGoodForm.skuId=e.lists[0].skuId||""}t.detailImage=e.detailsUrl||e.detailImage||e.r4||"",t.addGoodForm.detailImage=t.detailImage,e.detailImages&&e.detailImages.length>0&&(t.detailImage=e.detailImages[0],t.addGoodForm.detailImage=t.detailImage),t.detailImageList=t.detailImage?[{name:t.detailImage.split("/").pop(),url:t.detailImage}]:[],t.goodsClassifyName=e.className||"请选择分类",t.brandName=e.brandName||"请选择品牌",t.goodsTitle=e.commodityName||"",t.goodsSpec=t.addGoodForm.details,t.addGoodForm.classifyId=e.classId||"",t.addGoodForm.brandId=e.brandId||"",t.addGoodForm.commodityName=e.commodityName||"",t.addGoodForm.weight=e.weight||"",t.addGoodForm.type=void 0!==e.type&&null!==e.type&&""!==e.type?e.type.toString():"",t.addGoodForm.sixnight=e.sixnight||"",t.addGoodForm.deliveryfee=e.deliveryfee?JSON.parse(e.deliveryfee):[{max:null,fee:0}],console.log(t.addGoodForm.deliveryfee),t.addGoodForm.price=e.price||"",t.addGoodForm.sellprice=e.sellprice||""},getWaterList:function(){var e=this;e.spuId},addDeliveryFeeStep:function(){var e=this;e.addGoodForm.deliveryfee.push({max:null,fee:0})},removeDeliveryFeeStep:function(e){var t=this;t.addGoodForm.deliveryfee.splice(e,1)},showDeliveryFeeDetails:function(e){this.currentDeliveryFeeSteps=e.deliveryfee?JSON.parse(e.deliveryfee):[{max:null,fee:0}],this.deliveryFeeDialogVisible=!0},showBatchDeliveryFeeDialog:function(){this.batchDeliveryFeeSteps=[{max:null,fee:0}],this.batchDeliveryFeeDialogVisible=!0},addBatchDeliveryFeeStep:function(){var e=this;if(e.batchDeliveryFeeSteps.length>0){var t=e.batchDeliveryFeeSteps[e.batchDeliveryFeeSteps.length-1];null===t.max&&e.batchDeliveryFeeSteps.splice(e.batchDeliveryFeeSteps.length-1,1,{max:"",fee:t.fee})}e.batchDeliveryFeeSteps.push({max:null,fee:0})},removeBatchDeliveryFeeStep:function(e){var t=this;if(t.batchDeliveryFeeSteps.splice(e,1),t.batchDeliveryFeeSteps.length>0){var a=t.batchDeliveryFeeSteps[t.batchDeliveryFeeSteps.length-1];null!==a.max&&(a.max=null)}},applyBatchDeliveryFee:function(){for(var e=this,t=0;t<e.batchDeliveryFeeSteps.length;t++){if(t<e.batchDeliveryFeeSteps.length-1){if(!e.batchDeliveryFeeSteps[t].max)return void e.$message.error("请输入第".concat(t+1,"个配送区间的上限件数"));if(t>0&&Number(e.batchDeliveryFeeSteps[t].max)<=Number(e.batchDeliveryFeeSteps[t-1].max))return void e.$message.error("第".concat(t+1,"个配送区间的上限必须大于前一个区间的上限"))}if(void 0===e.batchDeliveryFeeSteps[t].fee||null===e.batchDeliveryFeeSteps[t].fee||""===e.batchDeliveryFeeSteps[t].fee)return void e.$message.error("请输入第".concat(t+1,"个配送区间的配送费"));e.batchDeliveryFeeSteps[t].fee=Number(e.batchDeliveryFeeSteps[t].fee),t<e.batchDeliveryFeeSteps.length-1&&(e.batchDeliveryFeeSteps[t].max=Number(e.batchDeliveryFeeSteps[t].max))}var a=JSON.stringify(e.batchDeliveryFeeSteps);e.$confirm("确认为所有商品设置阶梯配送费吗？此操作将覆盖所有商品的配送费设置。","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){var t="/szmb/newinsertproductcontroller/updateBatchDeliveryFee",s=e.$loading({lock:!0,text:"正在更新配送费...",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"});e.$get(t,{deliveryfee:a}).then((function(t){s.close(),1==t.code?(e.$message({message:"批量设置配送费成功！",type:"success"}),e.load()):e.$message.error(t.data||"操作失败")})).catch((function(t){s.close(),e.$message.error("操作失败，请稍后重试"),console.error(t)})),e.batchDeliveryFeeDialogVisible=!1})).catch((function(){}))},handleSelectionChange:function(e){this.multipleSelection=e.filter((function(e){return!e.storeState}))},batchUpShelf:function(){var e=this;if(0!==e.multipleSelection.length){var t=e.multipleSelection.filter((function(e){return!Number(e.lists[0].retail)||!Number(e.lists[0].cost)||!Number(e.lists[0].stock)}));if(t.length>0)e.$message.error("选中的商品中有未设置零售价、成本价或库存的商品，请检查");else{var a=e.multipleSelection.map((function(t){return l(l(l(l(l({storeId:e.Cookies.get("storeId"),id:t.classId,classNmae:t.className,brandId:t.brandId,brandName:t.brandName,commodityName:t.commodityName,source:1,spuId:0,type:0,productDescribe:t.productDescribe,weight:t.weight},"type",t.type),"deliveryfee",t.deliveryfee),"price",t.price),"sellprice",t.sellprice),"lists",[{imgList:t.lists[0].imgList,skuId:0,cost:t.lists[0].cost,marketPrice:t.lists[0].marketPrice,retail:t.lists[0].retail,stock:t.lists[0].stock,virtualStock:t.lists[0].virtualStock,source:0,details:t.lists[0].skuName,isWater:0,integralChecked:1,buckState:t.lists[0].buckState}])})),s="/szmb/newinsertproductcontroller/insertproductlist",r={list:a,header:"json"};e.$confirm("确认批量上架选中的"+a.length+"件商品?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.$post(s,r).then((function(t){1==t.code?(e.$message({message:"批量上架成功！",type:"success"}),e.multipleSelection=[],e.load()):e.$message.error(t.data)}))})).catch((function(){}))}}else e.$message.error("请选择需要上架的商品")},checkSelectable:function(e){return!e.storeState},parseDeliveryFee:function(e){try{return JSON.parse(e)||[{max:null,fee:0}]}catch(t){return[{max:null,fee:0}]}}},components:{}},u=d,m=(a("ccda"),a("db2a"),a("0c7c")),p=Object(m["a"])(u,s,r,!1,null,"52ea68ba",null);t["default"]=p.exports},7418:function(e,t,a){},"81d3":function(e,t,a){"use strict";a.d(t,"b",(function(){return s})),a.d(t,"a",(function(){return r}));var s=[{key:0,value:"未审核"},{key:1,value:"审核通过"},{key:2,value:"审核不通过"}],r=[{key:0,value:"重物桶装水"},{key:1,value:"重货箱装水"},{key:2,value:"轻货"}]},ccda:function(e,t,a){"use strict";a("7418")},d283:function(e,t,a){},db2a:function(e,t,a){"use strict";a("22ba")},dc46:function(e,t,a){"use strict";a("15ba")},ddeb:function(e,t,a){"use strict";a("d283")},e871:function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e._self._c;return t("div",{staticClass:"mod-config"},[t("el-form",{attrs:{inline:!0,model:e.dataForm},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.onSearch()}}},[t("el-form-item",[t("el-input",{attrs:{placeholder:"分类名称",clearable:""},model:{value:e.dataForm.classifyName,callback:function(t){e.$set(e.dataForm,"classifyName",t)},expression:"dataForm.classifyName"}})],1),t("el-form-item",[t("el-button",{on:{click:function(t){return e.onSearch()}}},[e._v("查询")]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.classAddOrUpdate()}}},[e._v("新增")])],1)],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.dataListLoading,expression:"dataListLoading"}],staticStyle:{width:"100%"},attrs:{data:e.dataList,border:""},on:{"selection-change":e.selectionChangeHandle}},[t("el-table-column",{attrs:{prop:"classifyName","header-align":"center",align:"center",label:"分类名称"}}),t("el-table-column",{attrs:{prop:"操作","header-align":"center",align:"center",width:"200",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(a){return t("div",{},[t("el-button",{staticStyle:{color:"blue"},attrs:{type:"text",size:"small"},on:{click:function(t){return e.classAddOrUpdate(a.row.classifyId)}}},[e._v("修改")]),t("el-button",{staticStyle:{color:"red"},attrs:{type:"text",size:"small"},on:{click:function(t){return e.deleteHandle(a.row.classifyId)}}},[e._v("删除")])],1)}}])})],1),e.classAddOrUpdateVisible?t("class-add-or-update",{ref:"classAddOrUpdate",on:{refreshDataList:e.getDataList}}):e._e()],1)},r=[],i=function(){var e=this,t=e._self._c;return t("el-dialog",{attrs:{title:e.dataForm.classId?"修改分类":"新增分类","close-on-click-modal":!1,visible:e.visible},on:{"update:visible":function(t){e.visible=t}}},[t("el-form",{ref:"dataForm",attrs:{model:e.dataForm,rules:e.dataRule,"label-width":"120px"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.dataFormSubmit()}}},[t("el-form-item",{attrs:{label:"分类名称",prop:"className"}},[t("el-input",{attrs:{placeholder:"请输入分类名称"},model:{value:e.dataForm.className,callback:function(t){e.$set(e.dataForm,"className",t)},expression:"dataForm.className"}})],1)],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.visible=!1}}},[e._v("取消")]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.dataFormSubmit()}}},[e._v("确定")])],1)],1)},o=[],l={data:function(){return{visible:!1,dataForm:{classId:"",className:""},dataRule:{className:[{required:!0,message:"分类名称不能为空",trigger:"blur"}]}}},methods:{init:function(e){var t=this;this.dataForm.classId=e||"",this.visible=!0,this.$nextTick((function(){if(t.$refs["dataForm"].resetFields(),t.dataForm.classId){var e=t.$parent.dataList.find((function(e){return e.classifyId===t.dataForm.classId}));console.log(e),e&&(t.dataForm.className=e.classifyName)}}))},dataFormSubmit:function(){var e=this;this.$refs["dataForm"].validate((function(t){t&&(e.dataForm.classId?e.$post("/szmb/newclasscontroller/updateclass",{classId:e.dataForm.classId,className:e.dataForm.className}).then((function(t){1===t.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.visible=!1,e.$emit("refreshDataList")}}):e.$message.error(t.data||t.msg)})):e.$post("/szmb/newclasscontroller/insertclass",{className:e.dataForm.className}).then((function(t){1===t.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.visible=!1,e.$emit("refreshDataList")}}):e.$message.error(t.data||t.msg)})))}))}}},n=l,c=a("0c7c"),d=Object(c["a"])(n,i,o,!1,null,null,null),u=d.exports,m={data:function(){return{dataForm:{classifyName:""},dataList:[],dataListLoading:!1,dataListSelections:[],classAddOrUpdateVisible:!1}},components:{ClassAddOrUpdate:u},mounted:function(){this.getDataList()},methods:{onSearch:function(){this.getDataList()},getDataList:function(){var e=this;this.dataListLoading=!0,this.$post("/szmb/newclasscontroller/selectclass").then((function(t){e.dataListLoading=!1,1===t.code?e.dataForm.classifyName?e.dataList=t.data.filter((function(t){return t.classifyName.includes(e.dataForm.classifyName)})):e.dataList=t.data:e.dataList=[]}))},selectionChangeHandle:function(e){this.dataListSelections=e},classAddOrUpdate:function(e){var t=this;this.classAddOrUpdateVisible=!0,this.$nextTick((function(){t.$refs.classAddOrUpdate.init(e)}))},deleteHandle:function(e){var t=this;this.$confirm("确定删除此分类?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.$post("/szmb/newclasscontroller/deleteclass",{classId:e}).then((function(e){1===e.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){t.getDataList()}}):t.$message.error(e.data||e.msg)}))}))}}},p=m,f=Object(c["a"])(p,s,r,!1,null,null,null);t["default"]=f.exports}}]);