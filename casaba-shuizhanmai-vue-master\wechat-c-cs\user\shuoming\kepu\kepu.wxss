/* user/shuoming/kepu/kepu.wxss */

/* Container */
.kepu-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding-bottom: 40rpx;
}

/* Banner Section */
.banner-section {
  padding: 20rpx 30rpx 0;
}

.banner-swiper {
  height: 280rpx;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.banner-image {
  width: 100%;
  height: 100%;
  border-radius: 20rpx;
}

/* Content Section */
.content-section {
  padding: 40rpx 30rpx 0;
}

/* Section Header */
.section-header {
  margin-bottom: 40rpx;
  text-align: center;
}

.section-title {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
}

.title-icon {
  font-size: 48rpx;
  margin-right: 16rpx;
}

.title-text {
  font-size: 48rpx;
  font-weight: 700;
  color: #2c3e50;
  letter-spacing: 2rpx;
}

.section-subtitle {
  font-size: 28rpx;
  color: #7f8c8d;
  line-height: 1.5;
}

/* Article List */
.article-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.article-item {
  background: #ffffff;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
}

.article-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6rpx;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.article-item:active::before {
  opacity: 1;
}

.article-item:active {
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
}

.article-image-container {
  position: relative;
  height: 200rpx;
  overflow: hidden;
}

.article-image {
  width: 100%;
  height: 100%;
  transition: transform 0.3s ease;
}

.article-item:active .article-image {
  transform: scale(1.05);
}

.image-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 60rpx;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.3));
}

.article-content {
  padding: 32rpx;
}

.article-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2c3e50;
  line-height: 1.4;
  margin-bottom: 24rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.article-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.meta-icon {
  font-size: 24rpx;
}

.meta-text {
  font-size: 24rpx;
  color: #95a5a6;
}

.read-more {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 24rpx;
  color: #667eea;
  font-weight: 500;
}

.arrow {
  font-size: 20rpx;
  transition: transform 0.3s ease;
}

.article-item:active .arrow {
  transform: translateX(4rpx);
}

/* Empty State */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 120rpx 40rpx;
  text-align: center;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  opacity: 0.6;
  margin-bottom: 40rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #7f8c8d;
  font-weight: 500;
  margin-bottom: 16rpx;
}

.empty-subtitle {
  font-size: 26rpx;
  color: #bdc3c7;
  line-height: 1.5;
}
