<template>
  <div>
    <el-dialog :title="!dataForm.id ? '新增订单来源关联' : '修改订单来源关联'" :close-on-click-modal="false" :visible.sync="visible"
      width="600px">
      <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
        label-width="120px">
        <el-form-item label="唯一标识" prop="unionCode">
          <el-input v-model="dataForm.unionCode" placeholder="请输入唯一标识(通常为skuId)"></el-input>
          <div class="form-tip">通常为商品的skuId，用于关联订单商品</div>
        </el-form-item>
        <el-form-item label="名称" prop="name">
          <el-input v-model="dataForm.name" placeholder="请输入关联关系名称"></el-input>
          <div class="form-tip">便于识别的名称，如：商品名称</div>
        </el-form-item>
        <el-form-item label="订单来源" prop="orderSourceId">
          <el-select v-model="dataForm.orderSourceId" placeholder="请选择订单来源" style="width: 100%;">
            <el-option v-for="item in orderSourceList" :key="item.id" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="关联产品" prop="productNewId">
          <div style="display: flex; gap: 10px; align-items: flex-start;">
            <el-select v-model="dataForm.productNewId" placeholder="请选择关联的产品" style="flex: 1;" filterable remote
              :remote-method="searchProducts" :loading="productLoading" clearable>
              <el-option v-for="item in productList" :key="item.productId"
                :label="`${item.productTitle} ${item.brandName ? '(' + item.brandName + ')' : ''} - ¥${item.productPrice}`"
                :value="item.productId">
                <div style="display: flex; justify-content: space-between; align-items: center;">
                  <span>{{ item.productTitle }}</span>
                  <span style="color: #999; font-size: 12px;">
                    {{ item.brandName ? item.brandName + ' - ' : '' }}¥{{ item.productPrice }}
                  </span>
                </div>
              </el-option>
            </el-select>
            <el-button type="primary" @click="openProductDialog" size="small">选择产品</el-button>
          </div>
          <div class="form-tip">选择关联的产品，用于价格计算。可留空后续配置。支持搜索产品名称</div>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
      </span>
    </el-dialog>

    <!-- 产品选择对话框 -->
    <el-dialog title="选择产品" :visible.sync="productDialogVisible" width="800px" :close-on-click-modal="false">
      <div style="margin-bottom: 20px;">
        <el-row :gutter="10">
          <!-- <el-col :span="6">
            <el-select v-model="productSearch.classId" placeholder="选择分类" clearable @change="loadDialogProducts">
              <el-option v-for="item in classifyOptions" :key="item.classifyId" :label="item.classifyName" :value="item.classifyId"></el-option>
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-select v-model="productSearch.brandId" placeholder="选择品牌" clearable @change="loadDialogProducts">
              <el-option v-for="item in brandOptions" :key="item.brandid" :label="item.brandname" :value="item.brandid"></el-option>
            </el-select>
          </el-col> -->
          <el-col :span="8">
            <el-input v-model="productSearch.keyword" placeholder="输入产品名称搜索" clearable
              @keyup.enter.native="loadDialogProducts"></el-input>
          </el-col>
          <el-col :span="4">
            <el-button type="primary" @click="loadDialogProducts">搜索</el-button>
          </el-col>
        </el-row>
      </div>

      <el-table :data="dialogProductList" v-loading="dialogProductLoading" height="400" @row-click="selectProduct"
        highlight-current-row>
        <el-table-column prop="productTitle" label="产品名称" min-width="200">
          <template slot-scope="scope">
            <div style="display: flex; align-items: center;">
              <img v-if="scope.row.r1" :src="scope.row.r1"
                style="width: 40px; height: 40px; margin-right: 10px; border-radius: 4px;" />
              <div>
                <div>{{ scope.row.productTitle }}</div>
                <!-- <div style="color: #999; font-size: 12px;">ID: {{ scope.row.productId }}</div> -->
              </div>
            </div>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="brandName" label="品牌" width="120"></el-table-column>
        <el-table-column prop="categoryName" label="分类" width="120"></el-table-column> -->
        <el-table-column label="零售价" width="100">
          <template slot-scope="scope">
            <span style="color: #f56c6c; font-weight: bold;">
              ¥{{ scope.row.sellprice || 0 }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="成本价" width="100">
          <template slot-scope="scope">
            <span>¥{{ scope.row.price || 0 }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="80">
          <template slot-scope="scope">
            <el-button type="primary" size="mini" @click="selectProduct(scope.row)">选择</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div style="margin-top: 20px; text-align: center;">
        <el-pagination @current-change="handleDialogPageChange" :current-page="productSearch.page"
          :page-size="productSearch.pageSize" :total="productSearch.total" layout="total, prev, pager, next">
        </el-pagination>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="productDialogVisible = false">取消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      dataForm: {
        id: 0,
        unionCode: '',
        name: '',
        orderSourceId: null,
        productNewId: null
      },
      dataRule: {
        unionCode: [
          { required: true, message: '唯一标识不能为空', trigger: 'blur' },
          { min: 1, max: 100, message: '唯一标识长度在1到100个字符', trigger: 'blur' }
        ],
        name: [
          { required: true, message: '名称不能为空', trigger: 'blur' },
          { min: 1, max: 255, message: '名称长度在1到255个字符', trigger: 'blur' }
        ],
        orderSourceId: [
          { required: true, message: '请选择订单来源', trigger: 'change' }
        ]
      },
      orderSourceList: [],
      productList: [],
      productLoading: false,
      // 产品选择对话框相关
      productDialogVisible: false,
      dialogProductList: [],
      dialogProductLoading: false,
      classifyOptions: [],
      brandOptions: [],
      productSearch: {
        classId: null,
        brandId: null,
        keyword: '',
        page: 1,
        pageSize: 10,
        total: 0
      }
    }
  },
  methods: {
    init(id) {
      this.dataForm.id = id || 0
      this.visible = true
      this.getOrderSourceList()
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          // 编辑模式，获取数据
          this.$get('/szmb/orderSourceConnect/detail/' + id, {}).then((res) => {
            if (res.code === 1) {
              this.dataForm.unionCode = res.data.unionCode
              this.dataForm.name = res.data.name
              this.dataForm.orderSourceId = res.data.orderSourceId
              this.dataForm.productNewId = res.data.productNewId

              // 如果有产品ID，加载产品信息
              if (res.data.productNewId) {
                this.loadProductById(res.data.productNewId)
              }
            } else {
              this.$message.error(res.data || '获取数据失败')
            }
          })
        } else {
          // 新增模式，设置默认值
          this.dataForm.unionCode = ''
          this.dataForm.name = ''
          this.dataForm.orderSourceId = null
          this.dataForm.productNewId = null
          this.productList = []
        }
      })
    },
    // 获取订单来源列表
    getOrderSourceList() {
      this.$get('/szmb/order-source/all', {}).then((res) => {
        if (res.code === 1) {
          this.orderSourceList = res.data || []
        }
      })
    },
    // 搜索产品
    searchProducts(query) {
      if (query !== '') {
        this.productLoading = true
        // 使用新的简洁产品搜索接口
        this.$post('/szmb/orderSourceConnect/product/search', {
          storeId: this.Cookies.get("storeId"),
          keyword: query,
          pageNo: 1,
          pageSize: 20,
          status: 0
        }).then((res) => {
          this.productLoading = false
          if (res.code === 1 && res.data && res.data.list && res.data.list.length > 0) {
            this.productList = res.data.list.map(item => ({
              productId: item.productId,
              productTitle: item.productName,
              productPrice: item.retailPrice || 0,
              productImage: item.productImage || '',
              brandName: item.brandName || '',
              classifyName: item.categoryName || '',
              skuId: item.skuId
            }))
          } else {
            // 如果没有搜索结果，显示空列表
            this.productList = []
          }
        }).catch(() => {
          this.productLoading = false
          // 网络错误时显示空列表
          this.productList = []
          this.$message.error('搜索产品失败，请稍后重试')
        })
      } else {
        this.productList = []
      }
    },
    // 根据ID加载产品信息
    loadProductById(productId) {
      // 使用新的简洁产品详情接口
      this.$get('/szmb/orderSourceConnect/product/detail/' + productId, {
        storeId: this.Cookies.get("storeId")
      }).then((res) => {
        if (res.code === 1 && res.data) {
          const product = {
            productId: res.data.productId || productId,
            productTitle: res.data.productName || `产品${productId}`,
            productPrice: res.data.retailPrice || 0,
            productImage: res.data.productImage || '',
            brandName: res.data.brandName || '',
            classifyName: res.data.categoryName || '',
            skuId: res.data.skuId
          }
          this.productList = [product]
        } else {
          // 如果接口不存在或失败，创建一个基本的产品信息
          const basicProduct = {
            productId: productId,
            productTitle: `产品${productId}`,
            productPrice: 0,
            productImage: '',
            brandName: '',
            classifyName: '',
            skuId: null
          }
          this.productList = [basicProduct]
        }
      }).catch(() => {
        // 网络错误时创建一个基本的产品信息
        const basicProduct = {
          productId: productId,
          productTitle: `产品${productId}`,
          productPrice: 0,
          productImage: '',
          brandName: '',
          classifyName: '',
          skuId: null
        }
        this.productList = [basicProduct]
      })
    },
    // 打开产品选择对话框
    openProductDialog() {
      this.productDialogVisible = true
      this.loadClassifyOptions()
      this.loadBrandOptions()
      this.loadDialogProducts()
    },
    // 加载分类选项
    loadClassifyOptions() {
      this.$get('/szmb/orderSourceConnect/product/categories').then((res) => {
        if (res.code === 1 && res.data) {
          this.classifyOptions = res.data
        }
      }).catch(() => {
        this.classifyOptions = []
      })
    },
    // 加载品牌选项
    loadBrandOptions() {
      this.$get('/szmb/orderSourceConnect/product/brands', {
        storeId: this.Cookies.get("storeId")
      }).then((res) => {
        if (res.code === 1 && res.data) {
          this.brandOptions = res.data
        }
      }).catch(() => {
        this.brandOptions = []
      })
    },
    // 加载对话框中的产品列表
    loadDialogProducts() {
      this.dialogProductLoading = true
      this.$post('/szmb/orderSourceConnect/product/search', {
        storeId: this.Cookies.get("storeId"),
        categoryId: this.productSearch.classId,
        brandId: this.productSearch.brandId,
        pageNo: this.productSearch.page,
        pageSize: this.productSearch.pageSize,
        keyword: this.productSearch.keyword,
        status: 0
      }).then((res) => {
        this.dialogProductLoading = false
        if (res.code === 1 && res.data) {
          this.dialogProductList = res.data.list || []
          this.productSearch.total = res.data.total || 0
        } else {
          this.dialogProductList = []
          this.productSearch.total = 0
        }
      }).catch(() => {
        this.dialogProductLoading = false
        this.dialogProductList = []
        this.productSearch.total = 0
        this.$message.error('加载产品列表失败')
      })
    },
    // 处理对话框分页变化
    handleDialogPageChange(page) {
      this.productSearch.page = page
      this.loadDialogProducts()
    },
    // 选择产品
    selectProduct(row) {
      this.dataForm.productNewId = row.productId
      // 更新下拉框显示的产品信息
      const selectedProduct = {
        productId: row.productId,
        productTitle: row.productTitle,
        productPrice: row.sellprice || 0,
        productImage: row.r1 || '',
      }
      this.productList = [selectedProduct]
      this.productDialogVisible = false
      this.$message.success('产品选择成功')
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const url = this.dataForm.id ?
            '/szmb/orderSourceConnect/update' :
            '/szmb/orderSourceConnect/add'
          this.dataForm.header = 'json'
          this.$post(url, this.dataForm).then((res) => {
            if (res.code === 1) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else {
              this.$message.error(res.data || '操作失败')
            }
          })
        }
      })
    }
  }
}
</script>

<style scoped>
.form-tip {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}
</style>
