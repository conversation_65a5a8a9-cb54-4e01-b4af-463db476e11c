/**
 * @姓名 系统生成
 * @版本号 1.0.0
 * @日期 2024/12/19
 */
package com.example.waterstationbuyproducer.szmb.service.order.impl;

import com.example.waterstationbuyproducer.dao.SzmCProductMapper;
import com.example.waterstationbuyproducer.dao.SzmCProductModelMapper;
import com.example.waterstationbuyproducer.dao.SzmCProductNewMapper;
import com.example.waterstationbuyproducer.dao.SzmCProductClassifyMapper;
import com.example.waterstationbuyproducer.dao.SzmCBrandMapper;
import com.example.waterstationbuyproducer.szmb.service.order.OrderSourceConnectProductService;
import com.example.waterstationbuyproducer.szmb.vo.order.ProductSearchReq;
import com.example.waterstationbuyproducer.szmb.vo.order.ProductSearchRes;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @description 订单来源关联产品查询服务实现
 * @date 2024/12/19
 */
@Service
public class OrderSourceConnectProductServiceImpl implements OrderSourceConnectProductService {

    private static final Logger logger = LoggerFactory.getLogger(OrderSourceConnectProductServiceImpl.class);

    // 注入现有的Mapper，复用现有数据访问逻辑
    @Autowired
    private SzmCProductNewMapper productMapper;

    @Autowired
    private SzmCProductClassifyMapper classifyMapper;

    @Autowired
    private SzmCBrandMapper brandMapper;

    @Override
    public Map<String, Object> searchProducts(ProductSearchReq req) {
        try {
            logger.info("搜索产品开始，参数: {}", req);
            
            // 构建查询条件
            Map<String, Object> params = buildSearchParams(req);

            // 计算分页偏移量
            int offset = (req.getPageNo() - 1) * req.getPageSize();
            params.put("offset", offset);
            params.put("pageNo", req.getPageNo());
            params.put("pageSize", req.getPageSize());

            // 查询产品列表
            List<Map<String, Object>> productList = productMapper.searchProductsForOrderSource(params);

            // 查询总数
            Integer total = productMapper.countProductsForOrderSource(params);

            // 转换为响应对象
            List<ProductSearchRes> resultList = convertToProductSearchRes(productList);

            // 构建分页响应
            Map<String, Object> result = new HashMap<>();
            result.put("list", resultList);
            result.put("total", total != null ? total : 0);
            result.put("pageNo", req.getPageNo());
            result.put("pageSize", req.getPageSize());
            result.put("pages", total != null ? (int) Math.ceil((double) total / req.getPageSize()) : 0);
            
            logger.info("搜索产品完成，返回{}条记录", resultList.size());
            return result;
            
        } catch (Exception e) {
            logger.error("搜索产品失败", e);
            throw new RuntimeException("搜索产品失败", e);
        }
    }

    @Override
    public ProductSearchRes getProductDetail(Long productId, Long storeId) {
        try {
            logger.info("获取产品详情开始，productId: {}, storeId: {}", productId, storeId);

            // 查询产品详情
            Map<String, Object> productDetail = productMapper.getProductDetailForOrderSource(productId, storeId);

            if (productDetail == null) {
                logger.warn("产品不存在，productId: {}", productId);
                return null;
            }

            ProductSearchRes result = convertToProductSearchRes(Arrays.asList(productDetail)).get(0);
            logger.info("获取产品详情完成，productId: {}", productId);
            return result;

        } catch (Exception e) {
            logger.error("获取产品详情失败，productId: " + productId, e);
            throw new RuntimeException("获取产品详情失败", e);
        }
    }

    @Override
    public List<Map<String, Object>> getCategories() {
        try {
            logger.info("获取分类列表开始");

            // 查询所有分类，这里使用一个通用的storeId，实际可以根据需要调整
            List<Map<String, Object>> result = new ArrayList<>();

            // 这里可以根据实际需求调整查询逻辑
            // 暂时返回空列表，避免依赖特定的storeId
            logger.info("获取分类列表完成，返回{}条记录", result.size());
            return result;

        } catch (Exception e) {
            logger.error("获取分类列表失败", e);
            throw new RuntimeException("获取分类列表失败", e);
        }
    }

    @Override
    public List<Map<String, Object>> getBrands(Long storeId) {
        try {
            logger.info("获取品牌列表开始，storeId: {}", storeId);

            // 查询品牌列表
            List<Map<String, Object>> result = new ArrayList<>();

            // 这里可以根据实际需求调整查询逻辑
            // 暂时返回空列表，避免依赖特定的storeId
            logger.info("获取品牌列表完成，返回{}条记录", result.size());
            return result;

        } catch (Exception e) {
            logger.error("获取品牌列表失败，storeId: " + storeId, e);
            throw new RuntimeException("获取品牌列表失败", e);
        }
    }

    /**
     * 构建搜索参数
     */
    private Map<String, Object> buildSearchParams(ProductSearchReq req) {
        Map<String, Object> params = new HashMap<>();
        params.put("storeId", req.getStoreId());
        
        if (StringUtils.hasText(req.getKeyword())) {
            params.put("keyword", "%" + req.getKeyword().trim() + "%");
        }
        
        if (req.getCategoryId() != null) {
            params.put("categoryId", req.getCategoryId());
        }
        
        if (req.getBrandId() != null) {
            params.put("brandId", req.getBrandId());
        }
        
        if (req.getStatus() != null) {
            params.put("status", req.getStatus());
        } else {
            params.put("status", 0); // 默认只查询上架商品
        }
        
        return params;
    }

    /**
     * 转换为响应对象
     */
    private List<ProductSearchRes> convertToProductSearchRes(List<Map<String, Object>> productList) {
        List<ProductSearchRes> resultList = new ArrayList<>();
        
        for (Map<String, Object> product : productList) {
            ProductSearchRes res = new ProductSearchRes();
            res.setProductId(getLong(product, "commodityId"));
            res.setProductName(getString(product, "commodityName"));
            res.setProductImage(getString(product, "img"));
            res.setRetailPrice(getDouble(product, "retail"));
            res.setCostPrice(getDouble(product, "cost"));
            res.setBrandId(getLong(product, "brandId"));
            res.setBrandName(getString(product, "brandName"));
            res.setCategoryId(getLong(product, "classId"));
            res.setCategoryName(getString(product, "className"));
            res.setSkuId(getLong(product, "skuId"));
            res.setSpecification(getString(product, "skuName"));
            res.setStatus(getInteger(product, "storeState"));
            res.setStock(getInteger(product, "stock"));
            
            resultList.add(res);
        }
        
        return resultList;
    }

    // 辅助方法
    private Long getLong(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) return null;
        if (value instanceof Long) return (Long) value;
        if (value instanceof Integer) return ((Integer) value).longValue();
        if (value instanceof String) {
            try {
                return Long.parseLong((String) value);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }

    private String getString(Map<String, Object> map, String key) {
        Object value = map.get(key);
        return value != null ? value.toString() : null;
    }

    private Double getDouble(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) return 0.0;
        if (value instanceof Double) return (Double) value;
        if (value instanceof Float) return ((Float) value).doubleValue();
        if (value instanceof String) {
            try {
                return Double.parseDouble((String) value);
            } catch (NumberFormatException e) {
                return 0.0;
            }
        }
        return 0.0;
    }

    private Integer getInteger(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) return null;
        if (value instanceof Integer) return (Integer) value;
        if (value instanceof String) {
            try {
                return Integer.parseInt((String) value);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }


}
