package com.example.waterstationbuyproducer.service.sfexpress.impl;

import com.alibaba.fastjson.JSON;
import com.example.waterstationbuyproducer.config.SfExpressConfig;
import com.example.waterstationbuyproducer.dao.SmzCOrderDetailsMapper;
import com.example.waterstationbuyproducer.dao.SzmCAddressMapper;
import com.example.waterstationbuyproducer.dao.SzmCOrderMainMapper;
import com.example.waterstationbuyproducer.dto.sfexpress.*;
import com.example.waterstationbuyproducer.entity.SmzCOrderDetails;
import com.example.waterstationbuyproducer.entity.SzmCAddress;
import com.example.waterstationbuyproducer.entity.SzmCOrderMain;
import com.example.waterstationbuyproducer.service.sfexpress.SfExpressTokenService;
import com.example.waterstationbuyproducer.szmb.service.hz.order.SzmBOrderService;
import com.example.waterstationbuyproducer.service.sfexpress.SfExpressService;
import com.example.waterstationbuyproducer.util.ResultBean;
import com.example.waterstationbuyproducer.util.logger.LoggerUtil;
import com.example.waterstationbuyproducer.util.sfexpress.SfExpressHttpClient;
import com.example.waterstationbuyproducer.util.sfexpress.SfExpressSignatureUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * SF Express Service Implementation
 * 顺丰快递服务实现类
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Service
public class SfExpressServiceImpl implements SfExpressService {

    @Autowired
    private SmzCOrderDetailsMapper smzCOrderDetailsMapper;
    @Autowired
    private SzmCAddressMapper szmCAddressMapper;

    @Autowired
    private SfExpressConfig sfExpressConfig;

    @Autowired
    private SfExpressHttpClient sfExpressHttpClient;

    @Autowired
    private SfExpressSignatureUtil sfExpressSignatureUtil;

    @Autowired
    private SzmCOrderMainMapper szmCOrderMainMapper;

    @Autowired
    private SfExpressTokenService sfExpressTokenService;

    @Autowired
    private SzmBOrderService szmBOrderService;
    /**
     * Service code for create order API
     * 下订单接口服务代码
     */
    private static final String SERVICE_CODE_CREATE_ORDER = "EXP_RECE_CREATE_ORDER";

    /**
     * Service code for update order API
     * 订单确认/取消接口服务代码
     */
    private static final String SERVICE_CODE_UPDATE_ORDER = "EXP_RECE_UPDATE_ORDER";

    /**
     * Service code for search order API
     * 订单结果查询接口服务代码
     */
    private static final String SERVICE_CODE_SEARCH_ORDER = "EXP_RECE_SEARCH_ORDER_RESP";

    /**
     * Service code for register route API
     * 路由注册接口服务代码
     */
    private static final String SERVICE_CODE_REGISTER_ROUTE = "EXP_RECE_REGISTER_ROUTE";

    /**
     * Service code for search routes API
     * 路由查询接口服务代码
     */
    private static final String SERVICE_CODE_SEARCH_ROUTES = "EXP_RECE_SEARCH_ROUTES";

    /**
     * Service code for print waybill API
     * 打印面单接口服务代码
     */
    private static final String SERVICE_CODE_PRINT_WAYBILL = "COM_RECE_CLOUD_PRINT_WAYBILLS";

    /**
     * API success code
     * API成功代码
     */
    private static final String API_SUCCESS_CODE = "A1000";

    /**
     * Data success code
     * 数据成功代码
     */
    private static final String DATA_SUCCESS_CODE = "S0000";

    /**
     * Default template code for waybill printing
     * 默认面单打印模板编码
     */
    private static final String DEFAULT_TEMPLATE_CODE = "fm_76130_fyp_standard_YQDZS7KIXFPK";

    @Override
    public ResultBean createOrder(SfExpressCreateOrderRequest request) {
        LoggerUtil.info("SF Express create order started, orderId: {}", request.getOrderId());

        try {
            // Validate request
            ResultBean validationResult = validateCreateOrderRequest(request);
            if (validationResult != null) {
                return validationResult;
            }

            // Prepare base request
            SfExpressBaseRequest baseRequest = prepareBaseRequest(request);

            // Send request to SF Express API
            SfExpressBaseResponse baseResponse = sfExpressHttpClient.sendRequest(baseRequest);

            // Process response
            return processCreateOrderResponse(baseResponse, request.getOrderId());

        } catch (Exception e) {
            LoggerUtil.error("SF Express create order failed, orderId: {}, error: {}",
                    request.getOrderId(), e.getMessage(), e);
            return new ResultBean().error("顺丰下订单失败: " + e.getMessage());
        }
    }

    @Override
    public ResultBean createOrderFromOrderId(Long orderId) {
        LoggerUtil.info("SF Express create order from orderId started, orderId: {}", orderId);

        try {
            // Get order information from database
            SzmCOrderMain orderMain = szmCOrderMainMapper.selectByPrimaryKey(orderId);
            if (orderMain == null) {
                return new ResultBean().error("订单不存在");
            }

            // Convert order to SF Express request
            SfExpressCreateOrderRequest request = convertOrderToSfRequest(orderMain);

            // Create order
            return createOrder(request);

        } catch (Exception e) {
            LoggerUtil.error("SF Express create order from orderId failed, orderId: {}, error: {}",
                    orderId, e.getMessage(), e);
            return new ResultBean().error("根据订单ID创建顺丰订单失败: " + e.getMessage());
        }
    }

    /**
     * Validate create order request
     * 验证创建订单请求
     *
     * @param request Create order request
     * @return Result with error if validation fails, null if validation passes
     */
    private ResultBean validateCreateOrderRequest(SfExpressCreateOrderRequest request) {
        if (request == null) {
            return new ResultBean().error("请求参数不能为空");
        }

        if (StringUtils.isEmpty(request.getOrderId())) {
            return new ResultBean().error("订单ID不能为空");
        }

        if (request.getContactInfoList() == null || request.getContactInfoList().isEmpty()) {
            return new ResultBean().error("联系人信息不能为空");
        }

        boolean hasSender = false;
        boolean hasReceiver = false;

        for (SfExpressCreateOrderRequest.ContactInfo contactInfo : request.getContactInfoList()) {
            if (contactInfo.getContactType() == 1) {
                hasSender = true;
            } else if (contactInfo.getContactType() == 2) {
                hasReceiver = true;
            }

            if (StringUtils.isEmpty(contactInfo.getContact())) {
                return new ResultBean().error("联系人姓名不能为空");
            }

            if (StringUtils.isEmpty(contactInfo.getMobile()) && StringUtils.isEmpty(contactInfo.getTel())) {
                return new ResultBean().error("联系人电话不能为空");
            }

            if (StringUtils.isEmpty(contactInfo.getProvince()) ||
                    StringUtils.isEmpty(contactInfo.getCity()) ||
                    StringUtils.isEmpty(contactInfo.getAddress())) {
                return new ResultBean().error("联系人地址信息不完整");
            }
        }

        if (!hasSender) {
            return new ResultBean().error("寄件人信息不能为空");
        }

        if (!hasReceiver) {
            return new ResultBean().error("收件人信息不能为空");
        }

        if (request.getCargoDetails() == null || request.getCargoDetails().isEmpty()) {
            return new ResultBean().error("货物信息不能为空");
        }

        return null;
    }

    /**
     * Prepare base request for SF Express API
     * 准备顺丰快递API基础请求
     *
     * @param request Create order request
     * @return Base request
     * @throws Exception if preparation fails
     */
    private SfExpressBaseRequest prepareBaseRequest(SfExpressCreateOrderRequest request) throws Exception {
        SfExpressBaseRequest baseRequest = new SfExpressBaseRequest();

        // Set basic parameters
        baseRequest.setPartnerID(sfExpressConfig.getClientCode());
        baseRequest.setRequestID(UUID.randomUUID().toString().replace("-", ""));
        baseRequest.setServiceCode(SERVICE_CODE_CREATE_ORDER);
        baseRequest.setLanguage(sfExpressConfig.getLanguage());

        // Set timestamp
        String timestamp = String.valueOf(System.currentTimeMillis());
        baseRequest.setTimestamp(timestamp);

        // Convert request to JSON
        String msgData = JSON.toJSONString(request);
        baseRequest.setMsgData(msgData);

        // Generate signature
        String msgDigest = sfExpressSignatureUtil.generateSignature(msgData, timestamp, sfExpressConfig.getCheckWord());
        baseRequest.setMsgDigest(msgDigest);

        LoggerUtil.info("SF Express base request prepared, RequestID: {}, MsgData: {}",
                baseRequest.getRequestID(), msgData);

        return baseRequest;
    }

    /**
     * Process create order response
     * 处理创建订单响应
     *
     * @param baseResponse Base response from SF Express API
     * @param orderId      Original order ID
     * @return Result with processed response
     */
    private ResultBean processCreateOrderResponse(SfExpressBaseResponse baseResponse, String orderId) {
        if (baseResponse == null) {
            return new ResultBean().error("顺丰API返回空响应");
        }

        LoggerUtil.info("SF Express API response, orderId: {}, apiResultCode: {}, apiResponseID: {}",
                orderId, baseResponse.getApiResultCode(), baseResponse.getApiResponseID());

        // Check API result code
        if (!API_SUCCESS_CODE.equals(baseResponse.getApiResultCode())) {
            String errorMsg = StringUtils.isEmpty(baseResponse.getApiErrorMsg()) ? "API调用失败"
                    : baseResponse.getApiErrorMsg();
            return new ResultBean().error("顺丰API调用失败: " + errorMsg + " (代码: " + baseResponse.getApiResultCode() + ")");
        }

        // Parse API result data (it's a JSON string in the response)
        String apiResultDataStr = baseResponse.getApiResultData();
        if (StringUtils.isEmpty(apiResultDataStr)) {
            return new ResultBean().error("顺丰API返回数据为空");
        }

        try {
            // Parse the nested JSON structure
            SfExpressApiResultData apiResultData = JSON.parseObject(apiResultDataStr, SfExpressApiResultData.class);
            if (apiResultData == null) {
                return new ResultBean().error("解析顺丰API结果数据失败");
            }

            LoggerUtil.info("SF Express API result data, orderId: {}, success: {}, errorCode: {}, errorMsg: {}",
                    orderId, apiResultData.isSuccess(), apiResultData.getErrorCode(), apiResultData.getErrorMsg());

            // Check if the business operation was successful
            if (!apiResultData.isSuccess() || !DATA_SUCCESS_CODE.equals(apiResultData.getErrorCode())) {
                String errorMsg = StringUtils.isEmpty(apiResultData.getErrorMsg()) ? "未知错误"
                        : apiResultData.getErrorMsg();

                // Handle specific error codes
                String userFriendlyMsg = getUserFriendlyErrorMessage(apiResultData.getErrorCode(), errorMsg);

                return new ResultBean()
                        .error("顺丰下订单失败: " + userFriendlyMsg + " (错误代码: " + apiResultData.getErrorCode() + ")");
            }

            // Parse the actual order response data
            Object msgDataObj = apiResultData.getMsgData();
            if (msgDataObj == null) {
                return new ResultBean().error("顺丰API返回订单数据为空");
            }

            // Convert msgData to SfExpressCreateOrderResponse
            SfExpressCreateOrderResponse orderResponse;
            if (msgDataObj instanceof String) {
                orderResponse = JSON.parseObject((String) msgDataObj, SfExpressCreateOrderResponse.class);
            } else {
                orderResponse = JSON.parseObject(JSON.toJSONString(msgDataObj), SfExpressCreateOrderResponse.class);
            }

            if (orderResponse == null) {
                return new ResultBean().error("解析顺丰订单响应数据失败");
            }

            // Extract waybill number for logging
            String waybillNo = extractWaybillNumber(orderResponse);
            // 更新订单SF号进系统

            SzmCOrderMain szmCOrderMain = szmCOrderMainMapper.selectByOrderNum(orderId);
            szmBOrderService.updateship(szmCOrderMain.getOrderMainId(), waybillNo, "顺丰");
            LoggerUtil.info("SF Express create order success, orderId: {}, waybillNo: {}, originCode: {}, destCode: {}",
                    orderId, waybillNo, orderResponse.getOriginCode(), orderResponse.getDestCode());

            return new ResultBean().success(orderResponse);

        } catch (Exception e) {
            LoggerUtil.error("Parse SF Express response failed, orderId: {}, error: {}",
                    orderId, e.getMessage(), e);
            return new ResultBean().error("解析顺丰API响应数据失败: " + e.getMessage());
        }
    }

    /**
     * Get user-friendly error message
     * 获取用户友好的错误信息
     *
     * @param errorCode Error code from SF Express API
     * @param errorMsg  Original error message
     * @return User-friendly error message
     */
    private String getUserFriendlyErrorMessage(String errorCode, String errorMsg) {
        if (StringUtils.isEmpty(errorCode)) {
            return errorMsg;
        }

        switch (errorCode) {
            case "8016":
                return "重复下单，该订单已存在";
            case "8001":
                return "订单信息验证失败";
            case "8002":
                return "收寄地址不在服务范围内";
            case "8003":
                return "货物信息不符合要求";
            case "8004":
                return "联系人信息不完整";
            case "8005":
                return "付款方式不支持";
            default:
                return errorMsg;
        }
    }

    /**
     * Extract waybill number from order response
     * 从订单响应中提取运单号
     *
     * @param orderResponse Order response
     * @return Waybill number
     */
    private String extractWaybillNumber(SfExpressCreateOrderResponse orderResponse) {
        if (orderResponse.getWaybillNoInfoList() != null && !orderResponse.getWaybillNoInfoList().isEmpty()) {
            //
            return orderResponse.getWaybillNoInfoList().stream()
                    .map(SfExpressCreateOrderResponse.WaybillNoInfo::getWaybillNo).collect(Collectors.joining(","));
        }
        return orderResponse.getMailno(); // Fallback to mailno field
    }

    /**
     * SF Express API Result Data
     * 顺丰API结果数据
     */
    public static class SfExpressApiResultData {
        private boolean success;
        private String errorCode;
        private String errorMsg;
        private Object msgData;

        // Getters and setters
        public boolean isSuccess() {
            return success;
        }

        public void setSuccess(boolean success) {
            this.success = success;
        }

        public String getErrorCode() {
            return errorCode;
        }

        public void setErrorCode(String errorCode) {
            this.errorCode = errorCode;
        }

        public String getErrorMsg() {
            return errorMsg;
        }

        public void setErrorMsg(String errorMsg) {
            this.errorMsg = errorMsg;
        }

        public Object getMsgData() {
            return msgData;
        }

        public void setMsgData(Object msgData) {
            this.msgData = msgData;
        }
    }

    /**
     * Convert order main to SF Express request
     * 将订单主表转换为顺丰快递请求
     *
     * @param orderMain Order main entity
     * @return SF Express create order request
     */
    private SfExpressCreateOrderRequest convertOrderToSfRequest(SzmCOrderMain orderMain) {
        SfExpressCreateOrderRequest request = new SfExpressCreateOrderRequest();

        // Set order ID
        // request.setOrderId("SF_" + orderMain.getOrderMainId() + "_" +
        // System.currentTimeMillis());
        request.setOrderId(orderMain.getOrderNum());

        // Set express type (default to standard express)
        request.setExpressTypeId(331);
        // request.setExpressTypeId(1);
        request.setParcelQty(StringUtils.isEmpty(orderMain.getR5()) ? 1 : Integer.parseInt(orderMain.getR5()));

        // Set pay method (default to sender pay)
        request.setPayMethod(1);
        request.setMonthlyCard("0211347247");

        // Set document flag (default to non-document)
        request.setIsDocall(0);

        // Set contact info list
        List<SfExpressCreateOrderRequest.ContactInfo> contactInfoList = new ArrayList<>();

        // Add sender info (water station/store info)
        SfExpressCreateOrderRequest.ContactInfo senderInfo = new SfExpressCreateOrderRequest.ContactInfo();
        senderInfo.setContactType(1); // Sender
        senderInfo.setCompany("水站买水"); // Default company name
        senderInfo.setContact("吴斌"); // Default contact name
        senderInfo.setMobile("13681669688"); // Default phone
        senderInfo.setProvince("上海市"); // Default province
        senderInfo.setCity("上海市"); // Default city
        senderInfo.setCounty("青浦区"); // Default county
        senderInfo.setAddress("新团路501号6栋"); // Default address
        contactInfoList.add(senderInfo);

        // Add receiver info (customer info)
        SfExpressCreateOrderRequest.ContactInfo receiverInfo = new SfExpressCreateOrderRequest.ContactInfo();
        receiverInfo.setContactType(2); // Receiver
        receiverInfo.setContact(!StringUtils.isEmpty(orderMain.getUserName()) ? orderMain.getUserName() : "客户");
        receiverInfo.setMobile(!StringUtils.isEmpty(orderMain.getUserPhone()) ? orderMain.getUserPhone() : "");

        // Parse address (assuming address format: province-city-county-detail)
        String fullAddress = orderMain.getUserAddress() != null ? orderMain.getUserAddress() : "";
        SzmCAddress szmCaddress = szmCAddressMapper.selectByUserId(orderMain.getUserId());
        receiverInfo.setProvince(szmCaddress.getProvince());
        receiverInfo.setCity(szmCaddress.getCity());
        receiverInfo.setCounty(szmCaddress.getArea());
        receiverInfo.setAddress(fullAddress);
        contactInfoList.add(receiverInfo);

        request.setContactInfoList(contactInfoList);
        List<SmzCOrderDetails> smzCOrderDetails = smzCOrderDetailsMapper.selectByOrderNum(orderMain.getOrderNum());
        // Set cargo details
        List<SfExpressCreateOrderRequest.CargoDetail> cargoDetails = new ArrayList<>();

        if (smzCOrderDetails != null && !smzCOrderDetails.isEmpty()) {
            for (SmzCOrderDetails orderDetail : smzCOrderDetails) {
                SfExpressCreateOrderRequest.CargoDetail cargoDetail = new SfExpressCreateOrderRequest.CargoDetail();

                // Use product name from order details, fallback to default if empty
                cargoDetail
                        .setName(!StringUtils.isEmpty(orderDetail.getProductSkuname()) ? orderDetail.getProductSkuname()
                                : "桶装水及相关商品");

                // Use product quantity from order details
                cargoDetail.setCount(orderDetail.getOrderProductNum() != null ? orderDetail.getOrderProductNum() : 1);

                cargoDetail.setUnit("件"); // Default unit
                cargoDetail.setWeight(20.0); // Default weight (20kg for water bucket)

                // Use product price from order details
                cargoDetail.setAmount(
                        orderDetail.getOrderDetailsProductPrice() != null ? orderDetail.getOrderDetailsProductPrice()
                                : 0.0);

                cargoDetail.setCurrency("CNY");
                cargoDetails.add(cargoDetail);
            }
        } else {
            // Fallback to original logic if no order details found
            SfExpressCreateOrderRequest.CargoDetail cargoDetail = new SfExpressCreateOrderRequest.CargoDetail();
            cargoDetail.setName("桶装水及相关商品");
            cargoDetail.setCount(StringUtils.isEmpty(orderMain.getR5()) ? 1 : Integer.parseInt(orderMain.getR5()));
            cargoDetail.setUnit("件");
            cargoDetail.setWeight(20.0);
            cargoDetail
                    .setAmount(!StringUtils.isEmpty(orderMain.getR1()) ? orderMain.getOrderMoney().doubleValue() : 0.0);
            cargoDetail.setCurrency("CNY");
            cargoDetails.add(cargoDetail);
        }

        request.setCargoDetails(cargoDetails);

        return request;
    }

    @Override
    public ResultBean updateOrder(SfExpressUpdateOrderRequest request) {
        LoggerUtil.info("SF Express update order started, orderId: {}, dealType: {}",
                request.getOrderId(), request.getDealType());

        try {
            // Validate request
            ResultBean validationResult = validateUpdateOrderRequest(request);
            if (validationResult != null) {
                return validationResult;
            }

            // Prepare base request
            SfExpressBaseRequest baseRequest = prepareUpdateOrderBaseRequest(request);

            // Send request to SF Express API
            SfExpressBaseResponse baseResponse = sfExpressHttpClient.sendRequest(baseRequest);

            // Process response
            return processUpdateOrderResponse(baseResponse, request.getOrderId(), request.getDealType());

        } catch (Exception e) {
            LoggerUtil.error("SF Express update order failed, orderId: {}, dealType: {}, error: {}",
                    request.getOrderId(), request.getDealType(), e.getMessage(), e);
            return new ResultBean().error("顺丰订单操作失败: " + e.getMessage());
        }
    }

    @Override
    public ResultBean confirmOrder(String orderId, String waybillNo) {
        LoggerUtil.info("SF Express confirm order started, orderId: {}, waybillNo: {}", orderId, waybillNo);

        SfExpressUpdateOrderRequest request = new SfExpressUpdateOrderRequest();
        request.setOrderId(orderId);
        request.setDealType(1); // 1: 确认
        request.setWaybillNo(waybillNo);

        return updateOrder(request);
    }

    @Override
    public ResultBean cancelOrder(String orderId) {
        LoggerUtil.info("SF Express cancel order started, orderId: {}", orderId);

        SfExpressUpdateOrderRequest request = new SfExpressUpdateOrderRequest();
        request.setOrderId(orderId);
        request.setDealType(2); // 2: 取消

        return updateOrder(request);
    }

    /**
     * Validate update order request
     * 验证更新订单请求
     *
     * @param request Update order request
     * @return Result with error if validation fails, null if validation passes
     */
    private ResultBean validateUpdateOrderRequest(SfExpressUpdateOrderRequest request) {
        if (request == null) {
            return new ResultBean().error("请求参数不能为空");
        }

        if (StringUtils.isEmpty(request.getOrderId())) {
            return new ResultBean().error("订单ID不能为空");
        }

        if (request.getDealType() == null) {
            return new ResultBean().error("处理类型不能为空");
        }

        if (request.getDealType() != 1 && request.getDealType() != 2) {
            return new ResultBean().error("处理类型必须为1(确认)或2(取消)");
        }

        return null;
    }

    /**
     * Prepare base request for update order API
     * 准备订单更新API基础请求
     *
     * @param request Update order request
     * @return Base request
     * @throws Exception if preparation fails
     */
    private SfExpressBaseRequest prepareUpdateOrderBaseRequest(SfExpressUpdateOrderRequest request) throws Exception {
        SfExpressBaseRequest baseRequest = new SfExpressBaseRequest();

        // Set basic parameters
        baseRequest.setPartnerID(sfExpressConfig.getClientCode());
        baseRequest.setRequestID(UUID.randomUUID().toString().replace("-", ""));
        baseRequest.setServiceCode(SERVICE_CODE_UPDATE_ORDER);
        baseRequest.setLanguage(sfExpressConfig.getLanguage());

        // Set timestamp
        String timestamp = String.valueOf(System.currentTimeMillis());
        baseRequest.setTimestamp(timestamp);

        // Convert request to JSON
        String msgData = JSON.toJSONString(request);
        baseRequest.setMsgData(msgData);

        // Generate signature
        String msgDigest = sfExpressSignatureUtil.generateSignature(msgData, timestamp, sfExpressConfig.getCheckWord());
        baseRequest.setMsgDigest(msgDigest);

        LoggerUtil.info("SF Express update order base request prepared, RequestID: {}, MsgData: {}",
                baseRequest.getRequestID(), msgData);

        return baseRequest;
    }

    /**
     * Process update order response
     * 处理更新订单响应
     *
     * @param baseResponse Base response from SF Express API
     * @param orderId      Original order ID
     * @param dealType     Deal type (1: confirm, 2: cancel)
     * @return Result with processed response
     */
    private ResultBean processUpdateOrderResponse(SfExpressBaseResponse baseResponse, String orderId,
            Integer dealType) {
        if (baseResponse == null) {
            return new ResultBean().error("顺丰API返回空响应");
        }

        LoggerUtil.info(
                "SF Express update order API response, orderId: {}, dealType: {}, apiResultCode: {}, apiResponseID: {}",
                orderId, dealType, baseResponse.getApiResultCode(), baseResponse.getApiResponseID());

        // Check API result code
        if (!API_SUCCESS_CODE.equals(baseResponse.getApiResultCode())) {
            String errorMsg = StringUtils.isEmpty(baseResponse.getApiErrorMsg()) ? "API调用失败"
                    : baseResponse.getApiErrorMsg();
            return new ResultBean().error("顺丰API调用失败: " + errorMsg + " (代码: " + baseResponse.getApiResultCode() + ")");
        }

        // Parse API result data (it's a JSON string in the response)
        String apiResultDataStr = baseResponse.getApiResultData();
        if (StringUtils.isEmpty(apiResultDataStr)) {
            return new ResultBean().error("顺丰API返回数据为空");
        }

        try {
            // Parse the nested JSON structure
            SfExpressApiResultData apiResultData = JSON.parseObject(apiResultDataStr, SfExpressApiResultData.class);
            if (apiResultData == null) {
                return new ResultBean().error("解析顺丰API结果数据失败");
            }

            LoggerUtil.info(
                    "SF Express update order API result data, orderId: {}, dealType: {}, success: {}, errorCode: {}, errorMsg: {}",
                    orderId, dealType, apiResultData.isSuccess(), apiResultData.getErrorCode(),
                    apiResultData.getErrorMsg());

            // Check if the business operation was successful
            if (!apiResultData.isSuccess() || !DATA_SUCCESS_CODE.equals(apiResultData.getErrorCode())) {
                String errorMsg = StringUtils.isEmpty(apiResultData.getErrorMsg()) ? "未知错误"
                        : apiResultData.getErrorMsg();

                // Handle specific error codes
                String userFriendlyMsg = getUserFriendlyErrorMessage(apiResultData.getErrorCode(), errorMsg);

                return new ResultBean()
                        .error("顺丰订单操作失败: " + userFriendlyMsg + " (错误代码: " + apiResultData.getErrorCode() + ")");
            }

            // Parse the actual update response data
            Object msgDataObj = apiResultData.getMsgData();
            if (msgDataObj == null) {
                // For update operations, msgData might be null on success
                // Create a simple response object
                SfExpressUpdateOrderResponse updateResponse = new SfExpressUpdateOrderResponse();
                updateResponse.setOrderId(orderId);
                updateResponse.setDealType(dealType);
                updateResponse.setStatus(1); // Success
                updateResponse.setMessage("操作成功");

                String operationType = dealType == 1 ? "确认" : "取消";
                LoggerUtil.info("SF Express update order success, orderId: {}, operation: {}", orderId, operationType);

                return new ResultBean().success(updateResponse);
            }

            // Convert msgData to SfExpressUpdateOrderResponse
            SfExpressUpdateOrderResponse updateResponse;
            if (msgDataObj instanceof String) {
                updateResponse = JSON.parseObject((String) msgDataObj, SfExpressUpdateOrderResponse.class);
            } else {
                updateResponse = JSON.parseObject(JSON.toJSONString(msgDataObj), SfExpressUpdateOrderResponse.class);
            }

            if (updateResponse == null) {
                return new ResultBean().error("解析顺丰订单更新响应数据失败");
            }

            String operationType = dealType == 1 ? "确认" : "取消";
            LoggerUtil.info("SF Express update order success, orderId: {}, operation: {}, waybillNo: {}",
                    orderId, operationType, updateResponse.getWaybillNo());

            return new ResultBean().success(updateResponse);

        } catch (Exception e) {
            LoggerUtil.error("Parse SF Express update order response failed, orderId: {}, dealType: {}, error: {}",
                    orderId, dealType, e.getMessage(), e);
            return new ResultBean().error("解析顺丰API响应数据失败: " + e.getMessage());
        }
    }

    @Override
    public ResultBean searchOrder(SfExpressSearchOrderRequest request) {
        LoggerUtil.info("SF Express search order started, orderId: {}, waybillNo: {}, searchType: {}",
                request.getOrderId(), request.getWaybillNo(), request.getSearchType());

        try {
            // Validate request
            ResultBean validationResult = validateSearchOrderRequest(request);
            if (validationResult != null) {
                return validationResult;
            }

            // Prepare base request
            SfExpressBaseRequest baseRequest = prepareSearchOrderBaseRequest(request);

            // Send request to SF Express API
            SfExpressBaseResponse baseResponse = sfExpressHttpClient.sendRequest(baseRequest);

            // Process response
            return processSearchOrderResponse(baseResponse, request.getOrderId(), request.getWaybillNo());

        } catch (Exception e) {
            LoggerUtil.error("SF Express search order failed, orderId: {}, waybillNo: {}, error: {}",
                    request.getOrderId(), request.getWaybillNo(), e.getMessage(), e);
            return new ResultBean().error("顺丰订单查询失败: " + e.getMessage());
        }
    }

    @Override
    public ResultBean searchOrderByOrderId(String orderId) {
        LoggerUtil.info("SF Express search order by orderId started, orderId: {}", orderId);

        SfExpressSearchOrderRequest request = new SfExpressSearchOrderRequest();
        request.setOrderId(orderId);
        request.setSearchType(1); // 1: 根据订单号查询

        return searchOrder(request);
    }

    @Override
    public ResultBean searchOrderByWaybillNo(String waybillNo) {
        LoggerUtil.info("SF Express search order by waybillNo started, waybillNo: {}", waybillNo);

        SfExpressSearchOrderRequest request = new SfExpressSearchOrderRequest();
        request.setWaybillNo(waybillNo);
        request.setSearchType(2); // 2: 根据运单号查询

        return searchOrder(request);
    }

    /**
     * Validate search order request
     * 验证查询订单请求
     *
     * @param request Search order request
     * @return Result with error if validation fails, null if validation passes
     */
    private ResultBean validateSearchOrderRequest(SfExpressSearchOrderRequest request) {
        if (request == null) {
            return new ResultBean().error("请求参数不能为空");
        }

        if (request.getSearchType() == null) {
            return new ResultBean().error("查询类型不能为空");
        }

        if (request.getSearchType() != 1 && request.getSearchType() != 2) {
            return new ResultBean().error("查询类型必须为1(根据订单号查询)或2(根据运单号查询)");
        }

        if (request.getSearchType() == 1) {
            if (StringUtils.isEmpty(request.getOrderId())) {
                return new ResultBean().error("根据订单号查询时，订单ID不能为空");
            }
        } else if (request.getSearchType() == 2) {
            if (StringUtils.isEmpty(request.getWaybillNo())) {
                return new ResultBean().error("根据运单号查询时，运单号不能为空");
            }
        }

        return null;
    }

    /**
     * Prepare base request for search order API
     * 准备订单查询API基础请求
     *
     * @param request Search order request
     * @return Base request
     * @throws Exception if preparation fails
     */
    private SfExpressBaseRequest prepareSearchOrderBaseRequest(SfExpressSearchOrderRequest request) throws Exception {
        SfExpressBaseRequest baseRequest = new SfExpressBaseRequest();

        // Set basic parameters
        baseRequest.setPartnerID(sfExpressConfig.getClientCode());
        baseRequest.setRequestID(UUID.randomUUID().toString().replace("-", ""));
        baseRequest.setServiceCode(SERVICE_CODE_SEARCH_ORDER);
        baseRequest.setLanguage(sfExpressConfig.getLanguage());

        // Set timestamp
        String timestamp = String.valueOf(System.currentTimeMillis());
        baseRequest.setTimestamp(timestamp);

        // Convert request to JSON
        String msgData = JSON.toJSONString(request);
        baseRequest.setMsgData(msgData);

        // Generate signature
        String msgDigest = sfExpressSignatureUtil.generateSignature(msgData, timestamp, sfExpressConfig.getCheckWord());
        baseRequest.setMsgDigest(msgDigest);

        LoggerUtil.info("SF Express search order base request prepared, RequestID: {}, MsgData: {}",
                baseRequest.getRequestID(), msgData);

        return baseRequest;
    }

    /**
     * Process search order response
     * 处理查询订单响应
     *
     * @param baseResponse Base response from SF Express API
     * @param orderId      Original order ID
     * @param waybillNo    Original waybill number
     * @return Result with processed response
     */
    private ResultBean processSearchOrderResponse(SfExpressBaseResponse baseResponse, String orderId,
            String waybillNo) {
        if (baseResponse == null) {
            return new ResultBean().error("顺丰API返回空响应");
        }

        LoggerUtil.info(
                "SF Express search order API response, orderId: {}, waybillNo: {}, apiResultCode: {}, apiResponseID: {}",
                orderId, waybillNo, baseResponse.getApiResultCode(), baseResponse.getApiResponseID());

        // Check API result code
        if (!API_SUCCESS_CODE.equals(baseResponse.getApiResultCode())) {
            String errorMsg = StringUtils.isEmpty(baseResponse.getApiErrorMsg()) ? "API调用失败"
                    : baseResponse.getApiErrorMsg();
            return new ResultBean().error("顺丰API调用失败: " + errorMsg + " (代码: " + baseResponse.getApiResultCode() + ")");
        }

        // Parse API result data (it's a JSON string in the response)
        String apiResultDataStr = baseResponse.getApiResultData();
        if (StringUtils.isEmpty(apiResultDataStr)) {
            return new ResultBean().error("顺丰API返回数据为空");
        }

        try {
            // Parse the nested JSON structure
            SfExpressApiResultData apiResultData = JSON.parseObject(apiResultDataStr, SfExpressApiResultData.class);
            if (apiResultData == null) {
                return new ResultBean().error("解析顺丰API结果数据失败");
            }

            LoggerUtil.info(
                    "SF Express search order API result data, orderId: {}, waybillNo: {}, success: {}, errorCode: {}, errorMsg: {}",
                    orderId, waybillNo, apiResultData.isSuccess(), apiResultData.getErrorCode(),
                    apiResultData.getErrorMsg());

            // Check if the business operation was successful
            if (!apiResultData.isSuccess() || !DATA_SUCCESS_CODE.equals(apiResultData.getErrorCode())) {
                String errorMsg = StringUtils.isEmpty(apiResultData.getErrorMsg()) ? "未知错误"
                        : apiResultData.getErrorMsg();

                // Handle specific error codes
                String userFriendlyMsg = getUserFriendlyErrorMessage(apiResultData.getErrorCode(), errorMsg);

                return new ResultBean()
                        .error("顺丰订单查询失败: " + userFriendlyMsg + " (错误代码: " + apiResultData.getErrorCode() + ")");
            }

            // Parse the actual search response data
            Object msgDataObj = apiResultData.getMsgData();
            if (msgDataObj == null) {
                return new ResultBean().error("顺丰API返回查询数据为空");
            }

            // Convert msgData to SfExpressSearchOrderResponse
            SfExpressSearchOrderResponse searchResponse;
            if (msgDataObj instanceof String) {
                searchResponse = JSON.parseObject((String) msgDataObj, SfExpressSearchOrderResponse.class);
            } else {
                searchResponse = JSON.parseObject(JSON.toJSONString(msgDataObj), SfExpressSearchOrderResponse.class);
            }

            if (searchResponse == null) {
                return new ResultBean().error("解析顺丰订单查询响应数据失败");
            }

            LoggerUtil.info(
                    "SF Express search order success, orderId: {}, waybillNo: {}, orderStatus: {}, filterResult: {}",
                    searchResponse.getOrderId(), searchResponse.getWaybillNo(),
                    searchResponse.getOrderStatus(), searchResponse.getFilterResult());

            return new ResultBean().success(searchResponse);

        } catch (Exception e) {
            LoggerUtil.error("Parse SF Express search order response failed, orderId: {}, waybillNo: {}, error: {}",
                    orderId, waybillNo, e.getMessage(), e);
            return new ResultBean().error("解析顺丰API响应数据失败: " + e.getMessage());
        }
    }

    @Override
    public ResultBean getAccessToken() {
        LoggerUtil.info("SF Express get access token started");

        try {
            String accessToken = sfExpressTokenService.getAccessToken();
            if (accessToken != null) {
                LoggerUtil.info("SF Express access token retrieved successfully");
                return new ResultBean().success(accessToken);
            } else {
                LoggerUtil.error("SF Express access token is null");
                return new ResultBean().error("获取顺丰访问令牌失败");
            }
        } catch (Exception e) {
            LoggerUtil.error("SF Express get access token failed: {}", e.getMessage(), e);
            return new ResultBean().error("获取顺丰访问令牌异常: " + e.getMessage());
        }
    }

    @Override
    public ResultBean refreshAccessToken() {
        LoggerUtil.info("SF Express refresh access token started");

        try {
            SfExpressTokenResponse tokenResponse = sfExpressTokenService.refreshAccessToken();
            if (tokenResponse != null && tokenResponse.isSuccess()) {
                LoggerUtil.info("SF Express access token refreshed successfully, expiresIn: {}",
                        tokenResponse.getExpiresIn());
                return new ResultBean().success(tokenResponse);
            } else {
                String errorMsg = tokenResponse != null ? tokenResponse.getApiErrorMsg() : "未知错误";
                LoggerUtil.error("SF Express refresh access token failed: {}", errorMsg);
                return new ResultBean().error("刷新顺丰访问令牌失败: " + errorMsg);
            }
        } catch (Exception e) {
            LoggerUtil.error("SF Express refresh access token failed: {}", e.getMessage(), e);
            return new ResultBean().error("刷新顺丰访问令牌异常: " + e.getMessage());
        }
    }

    @Override
    public ResultBean clearCachedToken() {
        LoggerUtil.info("SF Express clear cached token started");

        try {
            sfExpressTokenService.clearCachedToken();
            LoggerUtil.info("SF Express cached token cleared successfully");
            return new ResultBean().success("缓存的顺丰访问令牌已清除");
        } catch (Exception e) {
            LoggerUtil.error("SF Express clear cached token failed: {}", e.getMessage(), e);
            return new ResultBean().error("清除缓存的顺丰访问令牌异常: " + e.getMessage());
        }
    }

    @Override
    public ResultBean printWaybill(SfExpressPrintWaybillRequest request) {
        LoggerUtil.info("SF Express print waybill started, templateCode: {}, documents count: {}",
                request.getTemplateCode(), request.getDocuments() != null ? request.getDocuments().size() : 0);

        try {
            // Validate request
            ResultBean validationResult = validatePrintWaybillRequest(request);
            if (validationResult != null) {
                return validationResult;
            }

            // Prepare base request
            SfExpressBaseRequest baseRequest = preparePrintWaybillBaseRequest(request);

            // Send request to SF Express API
            SfExpressBaseResponse baseResponse = sfExpressHttpClient.sendRequest(baseRequest);

            // Process response
            return processPrintWaybillResponse(baseResponse, request.getTemplateCode());

        } catch (Exception e) {
            LoggerUtil.error("SF Express print waybill failed, templateCode: {}, error: {}",
                    request.getTemplateCode(), e.getMessage(), e);
            return new ResultBean().error("顺丰打印面单失败: " + e.getMessage());
        }
    }

    @Override
    public ResultBean printWaybillByWaybillNo(String waybillNo, String templateCode) {
        LoggerUtil.info("SF Express print waybill by waybillNo started, waybillNo: {}, templateCode: {}",
                waybillNo, templateCode);

        if (StringUtils.isEmpty(waybillNo)) {
            return new ResultBean().error("运单号不能为空");
        }

        // Use default template code if not provided
        String finalTemplateCode = StringUtils.isEmpty(templateCode) ? DEFAULT_TEMPLATE_CODE : templateCode;

        // Create print request
        SfExpressPrintWaybillRequest request = new SfExpressPrintWaybillRequest();
        request.setTemplateCode(finalTemplateCode);

        // Create document
        String[] waybillNos = waybillNo.split(",");
        String masterWaybillNo = waybillNos[0];
        List<SfExpressPrintWaybillRequest.Document> documents = new ArrayList<>();

        for (int i = 0; i < waybillNos.length; i++) {
            SfExpressPrintWaybillRequest.Document document = new SfExpressPrintWaybillRequest.Document();
            document.setMasterWaybillNo(masterWaybillNo);
            document.setBranchWaybillNo(waybillNos[i]);
            document.setSeq(String.valueOf(i + 1)); // 序号从1开始
            document.setSum(String.valueOf(waybillNos.length)); // 总数量
            documents.add(document);
        }
        request.setDocuments(documents);
        SfExpressPrintWaybillRequest.ExtJson extJson = new SfExpressPrintWaybillRequest.ExtJson();
        extJson.setMergePdf(true);
        extJson.setMergeType("all");
        request.setExtJson(extJson);

        return printWaybill(request);
    }

    @Override
    public ResultBean printWaybillByOrderId(String orderId, String templateCode) {
        LoggerUtil.info("SF Express print waybill by orderId started, orderId: {}, templateCode: {}",
                orderId, templateCode);

        if (StringUtils.isEmpty(orderId)) {
            return new ResultBean().error("订单ID不能为空");
        }

        try {
            // Get order information from database
            SzmCOrderMain orderMain = szmCOrderMainMapper.selectByOrderNum(orderId);
            if (orderMain == null) {
                return new ResultBean().error("订单不存在");
            }

            // Check if waybill number exists
            String waybillNo = orderMain.getShipsn();
            if (StringUtils.isEmpty(waybillNo)) {
                return new ResultBean().error("订单尚未生成运单号，请先创建顺丰订单");
            }

            // Print waybill using waybill number
            return printWaybillByWaybillNo(waybillNo, templateCode);

        } catch (Exception e) {
            LoggerUtil.error("SF Express print waybill by orderId failed, orderId: {}, error: {}",
                    orderId, e.getMessage(), e);
            return new ResultBean().error("根据订单ID打印面单失败: " + e.getMessage());
        }
    }

    /**
     * Validate print waybill request
     * 验证打印面单请求
     *
     * @param request Print waybill request
     * @return Result with error if validation fails, null if validation passes
     */
    private ResultBean validatePrintWaybillRequest(SfExpressPrintWaybillRequest request) {
        if (request == null) {
            return new ResultBean().error("请求参数不能为空");
        }

        if (StringUtils.isEmpty(request.getTemplateCode())) {
            return new ResultBean().error("模板编码不能为空");
        }

        if (request.getDocuments() == null || request.getDocuments().isEmpty()) {
            return new ResultBean().error("业务数据不能为空");
        }

        // Validate each document
        for (SfExpressPrintWaybillRequest.Document document : request.getDocuments()) {
            if (StringUtils.isEmpty(document.getMasterWaybillNo())) {
                return new ResultBean().error("主运单号不能为空");
            }
        }

        return null;
    }

    /**
     * Prepare base request for print waybill API
     * 准备打印面单API基础请求
     *
     * @param request Print waybill request
     * @return Base request
     * @throws Exception if preparation fails
     */
    private SfExpressBaseRequest preparePrintWaybillBaseRequest(SfExpressPrintWaybillRequest request) throws Exception {
        SfExpressBaseRequest baseRequest = new SfExpressBaseRequest();

        // Set basic parameters
        baseRequest.setPartnerID(sfExpressConfig.getClientCode());
        baseRequest.setRequestID(UUID.randomUUID().toString().replace("-", ""));
        baseRequest.setServiceCode(SERVICE_CODE_PRINT_WAYBILL);
        baseRequest.setLanguage(sfExpressConfig.getLanguage());

        // Set timestamp
        String timestamp = String.valueOf(System.currentTimeMillis());
        baseRequest.setTimestamp(timestamp);

        // Get access token
        // String accessToken = sfExpressTokenService.getAccessToken();
        // if (StringUtils.isEmpty(accessToken)) {
        // throw new Exception("获取访问令牌失败，无法打印面单");
        // }

        // Create message data with access token
        PrintWaybillMsgData msgData = new PrintWaybillMsgData();
        msgData.setTemplateCode(request.getTemplateCode());
        msgData.setDocuments(request.getDocuments());
        msgData.setVersion(request.getVersion());
        msgData.setFileType(request.getFileType());
        msgData.setSync(request.getSync());
        msgData.setExtJson(request.getExtJson());
        // msgData.setAccessToken(accessToken);

        // Convert to JSON
        String msgDataJson = JSON.toJSONString(msgData);
        baseRequest.setMsgData(msgDataJson);

        // Generate signature
        String msgDigest = sfExpressSignatureUtil.generateSignature(msgDataJson, timestamp,
                sfExpressConfig.getCheckWord());
        baseRequest.setMsgDigest(msgDigest);

        LoggerUtil.info("SF Express print waybill base request prepared, RequestID: {}, TemplateCode: {}",
                baseRequest.getRequestID(), request.getTemplateCode());

        return baseRequest;
    }

    /**
     * Process print waybill response
     * 处理打印面单响应
     *
     * @param baseResponse Base response from SF Express API
     * @param templateCode Original template code
     * @return Result with processed response
     */
    private ResultBean processPrintWaybillResponse(SfExpressBaseResponse baseResponse, String templateCode) {
        if (baseResponse == null) {
            return new ResultBean().error("顺丰API返回空响应");
        }

        LoggerUtil.info("SF Express print waybill API response, templateCode: {}, apiResultCode: {}, apiResponseID: {}",
                templateCode, baseResponse.getApiResultCode(), baseResponse.getApiResponseID());

        // Check API result code
        if (!API_SUCCESS_CODE.equals(baseResponse.getApiResultCode())) {
            String errorMsg = StringUtils.isEmpty(baseResponse.getApiErrorMsg()) ? "API调用失败"
                    : baseResponse.getApiErrorMsg();
            return new ResultBean().error("顺丰API调用失败: " + errorMsg + " (代码: " + baseResponse.getApiResultCode() + ")");
        }

        // Parse API result data (it's a JSON string in the response)
        String apiResultDataStr = baseResponse.getApiResultData();
        if (StringUtils.isEmpty(apiResultDataStr)) {
            return new ResultBean().error("顺丰API返回数据为空");
        }

        try {
            // 直接解析打印面单响应数据
            SfExpressPrintWaybillResponse printResponse = JSON.parseObject(apiResultDataStr,
                    SfExpressPrintWaybillResponse.class);
            if (printResponse == null) {
                return new ResultBean().error("解析顺丰打印面单响应数据失败");
            }

            LoggerUtil.info("SF Express print waybill API result data, templateCode: {}, success: {}, requestId: {}",
                    templateCode, printResponse.getSuccess(), printResponse.getRequestId());

            // Check if the business operation was successful
            if (!printResponse.getSuccess()) {
                String errorMsg = StringUtils.isEmpty(printResponse.getErrorMessage()) ? "未知错误"
                        : printResponse.getErrorMessage();
                return new ResultBean()
                        .error("顺丰打印面单失败: " + errorMsg + " (错误代码: " + printResponse.getErrorCode() + ")");
            }

            // 提取文件URL列表
            List<String> fileUrls = new ArrayList<>();
            if (printResponse.getObj() != null && printResponse.getObj().getFiles() != null) {
                for (SfExpressPrintWaybillResponse.PrintFile file : printResponse.getObj().getFiles()) {
                    if (!StringUtils.isEmpty(file.getUrl())) {
                        fileUrls.add(file.getUrl());
                        LoggerUtil.info("SF Express print waybill file URL: {}, waybillNo: {}, token: {}",
                                file.getUrl(), file.getWaybillNo(),
                                file.getToken() != null ? "***" : null);
                    }
                }
            }

            LoggerUtil.info(
                    "SF Express print waybill success, templateCode: {}, success: {}, files count: {}, URLs: {}",
                    templateCode, printResponse.getSuccess(), fileUrls.size(), fileUrls);

            // 创建简化的响应对象，主要返回文件URL
            PrintWaybillResult result = new PrintWaybillResult();
            result.setSuccess(printResponse.getSuccess());
            result.setRequestId(printResponse.getRequestId());
            result.setFileUrls(fileUrls);
            result.setOriginalResponse(printResponse);

            return new ResultBean().success(result);

        } catch (Exception e) {
            LoggerUtil.error("Parse SF Express print waybill response failed, templateCode: {}, error: {}",
                    templateCode, e.getMessage(), e);
            return new ResultBean().error("解析顺丰API响应数据失败: " + e.getMessage());
        }
    }

    /**
     * Print Waybill Message Data
     * 打印面单消息数据
     */
    private static class PrintWaybillMsgData {
        private String templateCode;
        private List<SfExpressPrintWaybillRequest.Document> documents;
        private SfExpressPrintWaybillRequest.ExtJson extJson;
        public SfExpressPrintWaybillRequest.ExtJson getExtJson() {
            return extJson;
        }

        public void setExtJson(SfExpressPrintWaybillRequest.ExtJson extJson) {
            this.extJson = extJson;
        }

        private String version;
        private String fileType;
        private Boolean sync;
        // private String accessToken;

        public String getTemplateCode() {
            return templateCode;
        }

        public void setTemplateCode(String templateCode) {
            this.templateCode = templateCode;
        }

        public List<SfExpressPrintWaybillRequest.Document> getDocuments() {
            return documents;
        }

        public void setDocuments(List<SfExpressPrintWaybillRequest.Document> documents) {
            this.documents = documents;
        }

        public String getVersion() {
            return version;
        }

        public void setVersion(String version) {
            this.version = version;
        }

        public String getFileType() {
            return fileType;
        }

        public void setFileType(String fileType) {
            this.fileType = fileType;
        }

        public Boolean getSync() {
            return sync;
        }

        public void setSync(Boolean sync) {
            this.sync = sync;
        }

        // public String getAccessToken() {
        // return accessToken;
        // }

        // public void setAccessToken(String accessToken) {
        // this.accessToken = accessToken;
        // }
    }

    /**
     * Print Waybill Result
     * 打印面单结果
     */
    public static class PrintWaybillResult {
        private Boolean success;
        private String requestId;
        private List<String> fileUrls;
        private SfExpressPrintWaybillResponse originalResponse;

        public Boolean getSuccess() {
            return success;
        }

        public void setSuccess(Boolean success) {
            this.success = success;
        }

        public String getRequestId() {
            return requestId;
        }

        public void setRequestId(String requestId) {
            this.requestId = requestId;
        }

        public List<String> getFileUrls() {
            return fileUrls;
        }

        public void setFileUrls(List<String> fileUrls) {
            this.fileUrls = fileUrls;
        }

        public SfExpressPrintWaybillResponse getOriginalResponse() {
            return originalResponse;
        }

        public void setOriginalResponse(SfExpressPrintWaybillResponse originalResponse) {
            this.originalResponse = originalResponse;
        }
    }

    /**
     * Validate register route request
     * 验证路由注册请求
     *
     * @param request Register route request
     * @return Validation result (null if valid)
     */
    private ResultBean validateRegisterRouteRequest(SfExpressRegisterRouteRequest request) {
        if (request == null) {
            return new ResultBean().error("路由注册请求不能为空");
        }

        if (StringUtils.isEmpty(request.getPartnerID())) {
            return new ResultBean().error("合作伙伴编码不能为空");
        }

        if (StringUtils.isEmpty(request.getRequestID())) {
            return new ResultBean().error("请求唯一号不能为空");
        }

        if (StringUtils.isEmpty(request.getMsgData())) {
            return new ResultBean().error("业务数据内容不能为空");
        }

        return null; // Valid
    }

    /**
     * Prepare base request for register route API
     * 准备路由注册API基础请求
     *
     * @param request Register route request
     * @return Base request
     * @throws Exception if preparation fails
     */
    private SfExpressBaseRequest prepareRegisterRouteBaseRequest(SfExpressRegisterRouteRequest request)
            throws Exception {
        SfExpressBaseRequest baseRequest = new SfExpressBaseRequest();

        // Set basic parameters
        baseRequest.setPartnerID(request.getPartnerID());
        baseRequest.setRequestID(request.getRequestID());
        baseRequest.setServiceCode(SERVICE_CODE_REGISTER_ROUTE);
        baseRequest.setLanguage(sfExpressConfig.getLanguage());

        // Set timestamp
        String timestamp = String.valueOf(System.currentTimeMillis());
        baseRequest.setTimestamp(timestamp);

        // Set message data
        baseRequest.setMsgData(request.getMsgData());

        // Get access token
        String accessToken = sfExpressTokenService.getAccessToken();
        if (StringUtils.isEmpty(accessToken)) {
            throw new Exception("获取访问令牌失败");
        }

        // Generate signature
        String msgDigest = sfExpressSignatureUtil.generateSignature(
                baseRequest.getMsgData(),
                baseRequest.getTimestamp(),
                sfExpressConfig.getCheckWord());
        baseRequest.setMsgDigest(msgDigest);

        LoggerUtil.info(
                "SF Express register route base request prepared, partnerID: {}, requestID: {}, serviceCode: {}",
                baseRequest.getPartnerID(), baseRequest.getRequestID(), baseRequest.getServiceCode());

        return baseRequest;
    }

    /**
     * Process register route response
     * 处理路由注册响应
     *
     * @param baseResponse Base response from SF Express API
     * @param requestID    Original request ID
     * @return Result with processed response
     */
    private ResultBean processRegisterRouteResponse(SfExpressBaseResponse baseResponse, String requestID) {
        if (baseResponse == null) {
            return new ResultBean().error("顺丰API返回空响应");
        }

        LoggerUtil.info(
                "SF Express register route response received, requestID: {}, apiResultCode: {}, apiResultMessage: {}",
                requestID, baseResponse.getApiResultCode(), baseResponse.getApiResultMessage());

        if (!baseResponse.isSuccess()) {
            String errorMsg = "顺丰路由注册失败: " + baseResponse.getApiResultMessage();
            if (!StringUtils.isEmpty(baseResponse.getApiErrorMsg())) {
                errorMsg += " - " + baseResponse.getApiErrorMsg();
            }
            return new ResultBean().error(errorMsg);
        }

        try {
            // Parse response data
            String apiResultData = baseResponse.getApiResultData();
            if (StringUtils.isEmpty(apiResultData)) {
                return new ResultBean().error("顺丰API返回数据为空");
            }

            // Convert JSON to response object
            SfExpressRegisterRouteResponse registerRouteResponse = JSON.parseObject(apiResultData,
                    SfExpressRegisterRouteResponse.class);
            if (registerRouteResponse == null) {
                return new ResultBean().error("解析顺丰路由注册响应数据失败");
            }

            LoggerUtil.info("SF Express register route success, requestID: {}, success: {}",
                    requestID, registerRouteResponse.isSuccess());

            return new ResultBean().success(registerRouteResponse);

        } catch (Exception e) {
            LoggerUtil.error("Parse SF Express register route response failed, requestID: {}, error: {}",
                    requestID, e.getMessage(), e);
            return new ResultBean().error("解析顺丰路由注册API响应数据失败: " + e.getMessage());
        }
    }

    @Override
    public ResultBean registerRoute(SfExpressRegisterRouteRequest request) {
        LoggerUtil.info("SF Express register route started, partnerID: {}, requestID: {}",
                request.getPartnerID(), request.getRequestID());

        try {
            // Validate request
            ResultBean validationResult = validateRegisterRouteRequest(request);
            if (validationResult != null) {
                return validationResult;
            }

            // Prepare base request
            SfExpressBaseRequest baseRequest = prepareRegisterRouteBaseRequest(request);

            // Send request to SF Express API
            SfExpressBaseResponse baseResponse = sfExpressHttpClient.sendRequest(baseRequest);

            // Process response
            return processRegisterRouteResponse(baseResponse, request.getRequestID());

        } catch (Exception e) {
            LoggerUtil.error("SF Express register route failed, partnerID: {}, requestID: {}, error: {}",
                    request.getPartnerID(), request.getRequestID(), e.getMessage(), e);
            return new ResultBean().error("顺丰路由注册失败: " + e.getMessage());
        }
    }

    /**
     * Validate search routes request
     * 验证路由查询请求
     *
     * @param request Search routes request
     * @return Validation result (null if valid)
     */
    private ResultBean validateSearchRoutesRequest(SfExpressSearchRoutesRequest request) {
        if (request == null) {
            return new ResultBean().error("路由查询请求不能为空");
        }

        if (StringUtils.isEmpty(request.getPartnerID())) {
            return new ResultBean().error("合作伙伴编码不能为空");
        }

        if (StringUtils.isEmpty(request.getRequestID())) {
            return new ResultBean().error("请求唯一号不能为空");
        }

        if (StringUtils.isEmpty(request.getMsgData())) {
            return new ResultBean().error("业务数据内容不能为空");
        }

        return null; // Valid
    }

    /**
     * Prepare base request for search routes API
     * 准备路由查询API基础请求
     *
     * @param request Search routes request
     * @return Base request
     * @throws Exception if preparation fails
     */
    private SfExpressBaseRequest prepareSearchRoutesBaseRequest(SfExpressSearchRoutesRequest request) throws Exception {
        SfExpressBaseRequest baseRequest = new SfExpressBaseRequest();

        // Set basic parameters
        baseRequest.setPartnerID(request.getPartnerID());
        baseRequest.setRequestID(request.getRequestID());
        baseRequest.setServiceCode(SERVICE_CODE_SEARCH_ROUTES);
        baseRequest.setLanguage(sfExpressConfig.getLanguage());

        // Set timestamp
        String timestamp = String.valueOf(System.currentTimeMillis());
        baseRequest.setTimestamp(timestamp);

        // Set message data
        baseRequest.setMsgData(request.getMsgData());

        // Get access token
        String accessToken = sfExpressTokenService.getAccessToken();
        if (StringUtils.isEmpty(accessToken)) {
            throw new Exception("获取访问令牌失败");
        }

        // Generate signature
        String msgDigest = sfExpressSignatureUtil.generateSignature(
                baseRequest.getMsgData(),
                baseRequest.getTimestamp(),
                sfExpressConfig.getCheckWord());
        baseRequest.setMsgDigest(msgDigest);

        LoggerUtil.info("SF Express search routes base request prepared, partnerID: {}, requestID: {}, serviceCode: {}",
                baseRequest.getPartnerID(), baseRequest.getRequestID(), baseRequest.getServiceCode());

        return baseRequest;
    }

    /**
     * Process search routes response
     * 处理路由查询响应
     *
     * @param baseResponse Base response from SF Express API
     * @param requestID    Original request ID
     * @return Result with processed response
     */
    private ResultBean processSearchRoutesResponse(SfExpressBaseResponse baseResponse, String requestID) {
        if (baseResponse == null) {
            return new ResultBean().error("顺丰API返回空响应");
        }

        LoggerUtil.info(
                "SF Express search routes response received, requestID: {}, apiResultCode: {}, apiResultMessage: {}",
                requestID, baseResponse.getApiResultCode(), baseResponse.getApiResultMessage());

        if (!baseResponse.isSuccess()) {
            String errorMsg = "顺丰路由查询失败: " + baseResponse.getApiResultMessage();
            if (!StringUtils.isEmpty(baseResponse.getApiErrorMsg())) {
                errorMsg += " - " + baseResponse.getApiErrorMsg();
            }
            return new ResultBean().error(errorMsg);
        }

        try {
            // Parse response data
            String apiResultData = baseResponse.getApiResultData();
            if (StringUtils.isEmpty(apiResultData)) {
                return new ResultBean().error("顺丰API返回数据为空");
            }

            // Convert JSON to response object
            SfExpressSearchRoutesResponse searchRoutesResponse = JSON.parseObject(apiResultData,
                    SfExpressSearchRoutesResponse.class);
            if (searchRoutesResponse == null) {
                return new ResultBean().error("解析顺丰路由查询响应数据失败");
            }

            LoggerUtil.info("SF Express search routes success, requestID: {}, success: {}",
                    requestID, searchRoutesResponse.isSuccess());

            return new ResultBean().success(searchRoutesResponse);

        } catch (Exception e) {
            LoggerUtil.error("Parse SF Express search routes response failed, requestID: {}, error: {}",
                    requestID, e.getMessage(), e);
            return new ResultBean().error("解析顺丰路由查询API响应数据失败: " + e.getMessage());
        }
    }

    @Override
    public ResultBean searchRoutes(SfExpressSearchRoutesRequest request) {
        LoggerUtil.info("SF Express search routes started, partnerID: {}, requestID: {}",
                request.getPartnerID(), request.getRequestID());

        try {
            // Validate request
            ResultBean validationResult = validateSearchRoutesRequest(request);
            if (validationResult != null) {
                return validationResult;
            }

            // Prepare base request
            SfExpressBaseRequest baseRequest = prepareSearchRoutesBaseRequest(request);

            // Send request to SF Express API
            SfExpressBaseResponse baseResponse = sfExpressHttpClient.sendRequest(baseRequest);

            // Process response
            return processSearchRoutesResponse(baseResponse, request.getRequestID());

        } catch (Exception e) {
            LoggerUtil.error("SF Express search routes failed, partnerID: {}, requestID: {}, error: {}",
                    request.getPartnerID(), request.getRequestID(), e.getMessage(), e);
            return new ResultBean().error("顺丰路由查询失败: " + e.getMessage());
        }
    }

    @Override
    public ResultBean registerRouteByOrderId(String orderId) {
        LoggerUtil.info("SF Express register route by orderId started, orderId: {}", orderId);

        if (StringUtils.isEmpty(orderId)) {
            return new ResultBean().error("订单ID不能为空");
        }

        try {
            // Get order information from database
            SzmCOrderMain orderMain = szmCOrderMainMapper.selectByOrderNum(orderId);
            if (orderMain == null) {
                return new ResultBean().error("订单不存在");
            }

            // Create register route data
            RegisterRouteData routeData = new RegisterRouteData();
            routeData.setType("1"); // 1:投递订单类型
            routeData.setAttributeNo(orderMain.getOrderNum());
            routeData.setOrderId(orderMain.getOrderNum());
            routeData.setLanguage("zh-CN");
            routeData.setCountry("CN");

            // Set phone number if available
            if (!StringUtils.isEmpty(orderMain.getUserPhone())) {
                routeData.setCheckPhoneNo(orderMain.getUserPhone());
            }

            // Set contact info
            RegisterRouteData.ContactInfo contactInfo = new RegisterRouteData.ContactInfo();
            if (!StringUtils.isEmpty(orderMain.getUserName())) {
                contactInfo.setContact(orderMain.getUserName());
            }

            // Use basic address info from order
            if (!StringUtils.isEmpty(orderMain.getUserAddress())) {
                contactInfo.setProvince(""); // 需要从地址解析或其他方式获取
                contactInfo.setCity("");
                contactInfo.setCounty("");
            }

            contactInfo.setContactType(2); // 2:收件人
            routeData.setContactInfo(contactInfo);

            // Create request
            SfExpressRegisterRouteRequest request = new SfExpressRegisterRouteRequest();
            request.setPartnerID(sfExpressConfig.getClientCode());
            request.setRequestID(UUID.randomUUID().toString().replace("-", ""));
            request.setTimestamp(System.currentTimeMillis());
            request.setMsgData(JSON.toJSONString(routeData));

            return registerRoute(request);

        } catch (Exception e) {
            LoggerUtil.error("SF Express register route by orderId failed, orderId: {}, error: {}",
                    orderId, e.getMessage(), e);
            return new ResultBean().error("根据订单ID注册顺丰路由失败: " + e.getMessage());
        }
    }

    @Override
    public ResultBean searchRoutesByTrackingNo(String trackingNo, Integer trackingType) {
        LoggerUtil.info("SF Express search routes by trackingNo started, trackingNo: {}, trackingType: {}",
                trackingNo, trackingType);

        if (StringUtils.isEmpty(trackingNo)) {
            return new ResultBean().error("查询号不能为空");
        }

        if (trackingType == null) {
            trackingType = 1; // 默认为运单号查询
        }

        try {
            // Create search routes data
            SearchRoutesData routesData = new SearchRoutesData();
            routesData.setLanguage("zh-CN");
            routesData.setTrackingType(trackingType);
            routesData.setTrackingNumber(Arrays.asList(trackingNo));
            routesData.setMethodType(1); // 1:标准查询

            // Create request
            SfExpressSearchRoutesRequest request = new SfExpressSearchRoutesRequest();
            request.setPartnerID(sfExpressConfig.getClientCode());
            request.setRequestID(UUID.randomUUID().toString().replace("-", ""));
            request.setTimestamp(System.currentTimeMillis());
            request.setMsgData(JSON.toJSONString(routesData));

            return searchRoutes(request);

        } catch (Exception e) {
            LoggerUtil.error(
                    "SF Express search routes by trackingNo failed, trackingNo: {}, trackingType: {}, error: {}",
                    trackingNo, trackingType, e.getMessage(), e);
            return new ResultBean().error("根据查询号查询顺丰路由失败: " + e.getMessage());
        }
    }
}
