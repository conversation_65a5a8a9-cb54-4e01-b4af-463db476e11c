/**
 * @姓名 杨强
 * @版本号 1.1.4
 * @日期 2019/6/25
 */
package com.example.waterstationbuyproducer.szmb.service.yq.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Validator;
import com.alibaba.fastjson.JSON;
import com.example.waterstationbuyproducer.dao.*;
import com.example.waterstationbuyproducer.entity.*;
import com.example.waterstationbuyproducer.szmb.service.yq.NewSelectProductService;
import com.example.waterstationbuyproducer.szmb.service.yq.SzmbStoreShopClassService;
import com.example.waterstationbuyproducer.szmb.util.Text;
import com.example.waterstationbuyproducer.szmb.vo.hz.Stock;
import com.example.waterstationbuyproducer.szmb.vo.yq.*;
import com.example.waterstationbuyproducer.util.DateUtil;
import com.example.waterstationbuyproducer.util.ResultBean;
import com.example.waterstationbuyproducer.vo.WaterCoupon;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;


/**
 * 查看字典商品
 */
@Service
public class NewSelectProductServiceImpl implements NewSelectProductService {
    @Autowired
    private SzmCProductNewMapper szmCProductNewMapper;

    @Autowired
    private SzmCProductModelNewMapper szmCProductModelNewMapper;
    @Autowired
    private ClassifyInfoMapper classifyInfoMapper;
    @Autowired
    private SzmBBranddeatilMapper szmBBranddeatilMapper;
    @Autowired
    private SzmCProductMapper szmCProductMapper;
    @Autowired
    private SzmCProductModelMapper szmCProductModelMapper;
    @Autowired
    private SzmbStoreShopClassService szmbStoreShopClassService;
    @Autowired
    private WaterProductMapper waterProductMapper;
    @Autowired
    private WaterProductClassifyMapper waterProductClassifyMapper;
    @Autowired
    private WaterBrandMapper waterBrandMapper;

    @Autowired
    private WaterProductModelMapper waterProductModelMapper;
    @Autowired
    private WaterWorksMapper waterWorksMapper;
    @Autowired
    private SzmCProductClassifyMapper szmCProductClassifyMapper;
    @Autowired
    private BuckInventoryMapper buckInventoryMapper;
    @Autowired
    private SzmCBrandMapper szmCBrandMapper;
    @Autowired
    private StoreLableMapper storeLableMapper;


    /**
     * 查看字典表商品
     *
     * @return
     */
    @Override
    public ResultBean selectProduct(Long classId, Integer pageindex) {
        ResultBean resultBean = new ResultBean();
        List<ClassifyInfo> classifyInfos = classifyInfoMapper.selectAll();
        ShopAll shopAll = new ShopAll();
        List<ShopClass> classes = new ArrayList<>();
        //返回分类信息
        for (ClassifyInfo szmCProductClassify : classifyInfos) {
            ShopClass shopClass = new ShopClass();
            shopClass.setId(szmCProductClassify.getClassifyId());
            shopClass.setName(szmCProductClassify.getClassifyName());
            classes.add(shopClass);
        }
        if (0 == classId) {
            PageHelper.startPage(pageindex, 10);
            List<SzmCProductNew> szmCProductNews = szmCProductNewMapper.selectAll();
            PageInfo<SzmCProductNew> pageInfo = new PageInfo<>(szmCProductNews);
            if (pageindex + 1 > pageInfo.getPages()) {
                shopAll.setPageState(0);
            } else {
                shopAll.setPageState(1);
            }
            shopAll.setPageCount(pageInfo.getTotal());
            List<ShopContent> skuShop = getSkuShop(szmCProductNews);
            shopAll.setShopContents(skuShop);
        } else {
            PageHelper.startPage(pageindex, 10);
            List<SzmCProductNew> szmCProductNews = szmCProductNewMapper.selectByClasssAll(classId);
            PageInfo<SzmCProductNew> pageInfo = new PageInfo<>(szmCProductNews);
            if (pageindex + 1 > pageInfo.getPages()) {
                shopAll.setPageState(0);
            } else {
                shopAll.setPageState(1);
            }
            shopAll.setPageCount(pageInfo.getTotal());
            List<ShopContent> skuShop = getSkuShop(szmCProductNews);
            shopAll.setShopContents(skuShop);
        }
        List<ShopContent> todayList = new ArrayList<>();
        List<ShopContent> addList = new ArrayList<>();

        List<ShopContent> shopContents2 = shopAll.getShopContents();
        //统计今天的时间
        Date todayStartTime = DateUtil.getTodayStartTime();
        Date todayEndTime = DateUtil.getTodayEndTime();
        for (ShopContent shopContent : shopContents2) {
            int i = todayStartTime.compareTo(shopContent.getDate());
            int i1 = todayEndTime.compareTo(shopContent.getDate());
            if (i <= 0 && i1 >= 0) {
                todayList.add(shopContent);
            } else {
                addList.add(shopContent);
            }
        }
        Collections.sort(todayList, new Comparator<ShopContent>() {
            @Override
            public int compare(ShopContent o1, ShopContent o2) {
                try {
                    if (o1.getDate().getTime() > o2.getDate().getTime()) {
                        return 1;
                    } else if (o1.getDate().getTime() < o2.getDate().getTime()) {
                        return -1;
                    } else {
                        return 0;
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                return 0;
            }
        });
        Collections.reverse(todayList);
        //排序
        Map<Integer, String> map = new HashMap<>();
        for (int i = 0; i < addList.size(); i++) {
            map.put(i, addList.get(i).getCommodityName());
        }
        shopAll.setTodayList(todayList);
        Map<String, List<String>> pinyin = getPinyin(map);
        List<ShopList> getshuoindex = getshuoindex(pinyin);
        List<ShopMapList> mapLists = new ArrayList<>();
        for (ShopList shopList : getshuoindex) {
            List<ShopContent> lists = new ArrayList<>();
            List<Integer> list1 = shopList.getList();
            for (Integer index : list1) {
                ShopContent shopContent = addList.get(index);
                lists.add(shopContent);
            }
            ShopMapList shopMapList = new ShopMapList();
            shopMapList.setZm(shopList.getHanzi());
            shopMapList.setList(lists);
            mapLists.add(shopMapList);

        }
        shopAll.setRemap(mapLists);
        return resultBean.success(shopAll);
    }

    public ResultBean selectPorudctAllList(Long classId) {
        ResultBean resultBean = new ResultBean();
        List<ClassifyInfo> classifyInfos = classifyInfoMapper.selectAll();
        ShopAll shopAll = new ShopAll();
        List<ShopClass> classes = new ArrayList<>();
        //返回分类信息
        for (ClassifyInfo szmCProductClassify : classifyInfos) {
            ShopClass shopClass = new ShopClass();
            shopClass.setId(szmCProductClassify.getClassifyId());
            shopClass.setName(szmCProductClassify.getClassifyName());
            classes.add(shopClass);
        }
        if (0 == classId) {
            List<SzmCProductNew> szmCProductNews = szmCProductNewMapper.selectAll();
            PageInfo<SzmCProductNew> pageInfo = new PageInfo<>(szmCProductNews);
            shopAll.setPageCount(pageInfo.getTotal());
            List<ShopContent> skuShop = getSkuShop(szmCProductNews);
            shopAll.setShopContents(skuShop);
        } else {
            List<SzmCProductNew> szmCProductNews = szmCProductNewMapper.selectByClasssAll(classId);
            PageInfo<SzmCProductNew> pageInfo = new PageInfo<>(szmCProductNews);
            shopAll.setPageCount(pageInfo.getTotal());
            List<ShopContent> skuShop = getSkuShop(szmCProductNews);
            shopAll.setShopContents(skuShop);
        }
        List<ShopContent> todayList = new ArrayList<>();
        List<ShopContent> addList = new ArrayList<>();

        List<ShopContent> shopContents2 = shopAll.getShopContents();
        //统计今天的时间
        Date todayStartTime = DateUtil.getTodayStartTime();
        Date todayEndTime = DateUtil.getTodayEndTime();
        for (ShopContent shopContent : shopContents2) {
            int i = todayStartTime.compareTo(shopContent.getDate());
            int i1 = todayEndTime.compareTo(shopContent.getDate());
            if (i <= 0 && i1 >= 0) {
                todayList.add(shopContent);
            } else {
                addList.add(shopContent);
            }
        }
        Collections.sort(todayList, new Comparator<ShopContent>() {
            @Override
            public int compare(ShopContent o1, ShopContent o2) {
                try {
                    if (o1.getDate().getTime() > o2.getDate().getTime()) {
                        return 1;
                    } else if (o1.getDate().getTime() < o2.getDate().getTime()) {
                        return -1;
                    } else {
                        return 0;
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                return 0;
            }
        });
        Collections.reverse(todayList);
        //排序
        Map<Integer, String> map = new HashMap<>();
        for (int i = 0; i < addList.size(); i++) {
            map.put(i, addList.get(i).getCommodityName());
        }
        shopAll.setTodayList(todayList);
        Map<String, List<String>> pinyin = getPinyin(map);
        List<ShopList> getshuoindex = getshuoindex(pinyin);
        List<ShopMapList> mapLists = new ArrayList<>();
        for (ShopList shopList : getshuoindex) {
            List<ShopContent> lists = new ArrayList<>();
            List<Integer> list1 = shopList.getList();
            for (Integer index : list1) {
                ShopContent shopContent = addList.get(index);
                lists.add(shopContent);
            }
            ShopMapList shopMapList = new ShopMapList();
            shopMapList.setZm(shopList.getHanzi());
            shopMapList.setList(lists);
            mapLists.add(shopMapList);

        }
        shopAll.setRemap(mapLists);
        return resultBean.success(shopAll);
    }

    /**
     * 查看商家添加的字典商品
     *
     * @param storeId
     * @param classId
     * @return
     */
    @Override
    public ResultBean selectProductByStoreId(Long storeId, Long classId, Integer pageIndex) {
        List<SzmCProduct> list = szmCProductMapper.selectALLbySpu(storeId.toString());
        ResultBean resultBean1 = selectProduct(classId, pageIndex);
        if (resultBean1.getCode() != 1) {
            return resultBean1;
        }
        if (list.size() <= 0) {
            return resultBean1;
        } else {
            for (SzmCProduct szmCProduct : list) {
                ShopAll shopAll = (ShopAll) resultBean1.getData();
                for (ShopContent shopCountent : shopAll.getShopContents()) {
                    if (szmCProduct.getProductTitle().equals(shopCountent.getCommodityName())) {
                        shopCountent.setStoreState(1);
                        shopCountent.setSpuId(szmCProduct.getProductId().intValue());
                        List<Map<String, Object>> lists = shopCountent.getLists();
                        for (Map<String, Object> map : lists) {
                            Object name = map.get("skuName");
                            Map<String, String> map1 = new HashMap<>();
                            map1.put("rule", name.toString());
                            String string = JSON.toJSONString(map1);
                            SzmCProductModel szmCProductModel = szmCProductModelMapper.
                                    selectByStoreId(szmCProduct.getProductId(), string);
                            if (null == szmCProductModel) {
                                continue;
                            }
                            map.put("cost", szmCProductModel.getR2());
                            map.put("retail", szmCProductModel.getProductOriginalPrice());
                            map.put("marketPrice", szmCProductModel.getProductWeight());
                            map.put("stock", szmCProductModel.getProductInventory());
                            map.put("virtualStock", szmCProductModel.getVirtualInventory());
                        }
                    }
                }
            }
        }
        return resultBean1;
    }


    /**
     * 查看商家已经添加的字典商品(pc)
     *
     * @param storeId
     * @param classId
     * @param pageIndex
     * @param productName
     * @param state
     * @return
     */
    @Override
    public ResultBean selectProductByStoreIdPc(Long storeId, Long classId, Integer pageIndex, String productName, Integer state, Long brandId) {
        List<SzmCProduct> list = szmCProductMapper.selectALLbySpu(storeId.toString());
        ResultBean resultBean1 = selectProductPc(classId, pageIndex, productName, state, storeId, brandId);
        if (resultBean1.getCode() != 1) {
            return resultBean1;
        }
        if (list.size() <= 0) {
            return resultBean1;
        } else {
            for (SzmCProduct szmCProduct : list) {
                ShopAll shopAll = (ShopAll) resultBean1.getData();
                for (ShopContent shopCountent : shopAll.getShopContents()) {
                    if (szmCProduct.getProductTitle().equals(shopCountent.getCommodityName())) {
                        shopCountent.setStoreState(1);
                        shopCountent.setSpuId(szmCProduct.getProductId().intValue());
                        List<Map<String, Object>> lists = shopCountent.getLists();
                        for (Map<String, Object> map : lists) {
                            Object name = map.get("skuName");
                            Map<String, String> map1 = new HashMap<>();
                            map1.put("rule", name.toString());
                            String string = JSON.toJSONString(map1);
                            SzmCProductModel szmCProductModel = szmCProductModelMapper.
                                    selectByStoreId(szmCProduct.getProductId(), string);
                            if (null == szmCProductModel) {
                                continue;
                            }
                            map.put("cost", szmCProductModel.getR2());
                            map.put("retail", szmCProductModel.getProductOriginalPrice());
                            map.put("stock", szmCProductModel.getProductInventory());
                            map.put("virtualStock", szmCProductModel.getVirtualInventory());
                            map.put("marketPrice", szmCProductModel.getProductWeight());
                        }
                    }
                }
            }
        }
        return resultBean1;
    }


    /**
     * 查看商品
     *
     * @param list
     * @return
     */
    public List<ShopContent> getSkuShop(List<SzmCProductNew> list) {
        List<ShopContent> list1 = new ArrayList<>();
        for (SzmCProductNew szmCProduct : list) {
            ShopContent shopContent = new ShopContent();
            shopContent.setProductDescribe(szmCProduct.getProductDescribe());
            shopContent.setClassId(szmCProduct.getProductClassifyId());
            // 设置商品详情图
            shopContent.setDetailsUrl(szmCProduct.getR4());
            if (StringUtils.isNotEmpty(szmCProduct.getR4())) {
                List<String> detailImages = new ArrayList<>();
                String[] imageUrls = szmCProduct.getR4().split(",");
                for (String imageUrl : imageUrls) {
                    if (StringUtils.isNotEmpty(imageUrl.trim())) {
                        detailImages.add(imageUrl.trim());
                    }
                }
                shopContent.setDetailImages(detailImages);
            }
            ClassifyInfo classifyInfo = classifyInfoMapper.selectByPrimaryKey(szmCProduct.getProductClassifyId());
            if (null != classifyInfo) {
                shopContent.setClassId(classifyInfo.getClassifyId());
                shopContent.setClassName(classifyInfo.getClassifyName());
            }
            shopContent.setBrandId(Long.parseLong(szmCProduct.getR3()));
            SzmBBranddeatil szmBBranddeatil = szmBBranddeatilMapper.selectByPrimaryKey(Integer.parseInt(szmCProduct.getR3()));
            shopContent.setBrandName(szmBBranddeatil == null ? "" : szmBBranddeatil.getBrandname());
            shopContent.setCommodityId(szmCProduct.getProductId());
            shopContent.setCommodityName(szmCProduct.getProductTitle());
            shopContent.setImg(szmCProduct.getR1());
            shopContent.setType(szmCProduct.getType());
            shopContent.setWeight(szmCProduct.getWeight());
            shopContent.setPrice(szmCProduct.getPrice());
            shopContent.setSellprice(szmCProduct.getSellprice());
            shopContent.setDeliveryfee(szmCProduct.getDeliveryfee());
            List<Map<String, Object>> lists = new ArrayList<>();
            List<SzmCProductModelNew> szmCProductModelNews = szmCProductModelNewMapper.selectByProductId(szmCProduct.getProductId());
            for (SzmCProductModelNew szmCProductModelNew : szmCProductModelNews) {
                Map map = JSON.parseObject(szmCProductModelNew.getSpecificationsDescribe(), Map.class);
                StringBuffer stringBuffer = new StringBuffer();
//                stringBuffer.append(szmCProduct1.getProductTitle());
                for (Object obj : map.keySet()) {
//                                        stringBuffer.append(obj.toString());
                    stringBuffer.append(map.get(obj) + "");
                }
                List<String> list2 = new ArrayList<>();
                if (StringUtils.isNotEmpty(szmCProduct.getR1())) {
                    list2.add(szmCProduct.getR1());
                }
                if (StringUtils.isNotEmpty(szmCProductModelNew.getR1())) {
                    list2.add(szmCProductModelNew.getR1());
                }
                if (StringUtils.isNotEmpty(szmCProduct.getR4())) {
                    list2.add(szmCProduct.getR4());
                }
                if (StringUtils.isNotEmpty(szmCProductModelNew.getProductCode())) {
                    list2.add(szmCProductModelNew.getProductCode());
                }
                Map<String, Object> map1 = new HashMap<>();
                map1.put("skuName", stringBuffer.toString());
                map1.put("imgList", list2);
                map1.put("cost", szmCProduct.getPrice() != null ? szmCProduct.getPrice() : "");
                map1.put("retail", szmCProduct.getSellprice() != null ? szmCProduct.getSellprice() : "");
                map1.put("stock", "");
                map1.put("skuId", szmCProductModelNew.getProductModelId());
                map1.put("buckState", szmCProductModelNew.getProductModelName());  
                lists.add(map1);
            }
            shopContent.setLists(lists);
            //修改时间
            shopContent.setDate(szmCProduct.getUpdateTime());
            list1.add(shopContent);
        }
        return list1;
    }


    /**
     * 获取字母集合
     *
     * @param map
     * @return
     */
    public Map<String, List<String>> getPinyin(Map<Integer, String> map) {
        Map<String, List<String>> returnMap = new HashMap<>();
        //传入的下标和索引
        Integer count = 0;
        for (Integer index : map.keySet()) {
            //获取商品名称
            String s = map.get(index);
            Set<String> strings = returnMap.keySet();
            if (strings.size() > 0) {
                Integer result = 0;
                for (String returenIndex : strings) {
                    String upperCase = Text.getUpperCase(s, false);
                    char c = upperCase.charAt(0);
                    String s1 = String.valueOf(c);
                    if (s1.equals(returenIndex)) {
                        List<String> list = returnMap.get(returenIndex);
                        String addString = index + "," + upperCase;
                        list.add(addString);
                        result = 1;
                        break;
                    } else {
                        result = 2;
                    }
                }
                if (result == 2) {
                    String upperCase = Text.getUpperCase(s, false);
                    char c = upperCase.charAt(0);
                    List<String> list = new ArrayList<>();
                    String addString = index + "," + upperCase;
                    list.add(addString);
                    returnMap.put(String.valueOf(c), list);
                }
            } else {
                String upperCase = Text.getUpperCase(s, false);
                char c = upperCase.charAt(0);
                List<String> list = new ArrayList<>();
                String addString = index + "," + upperCase;
                list.add(addString);
                returnMap.put(String.valueOf(c), list);
            }
        }
        return returnMap;
    }

    //比较下标
    public List<ShopList> getshuoindex(Map<String, List<String>> map) {
        List<ShopList> lists = new ArrayList<>();
        List<String> list = new ArrayList<>();
        Set<String> strings = map.keySet();
        //比较下标值

        for (String string : strings) {
            list.add(string);
        }
        list = getlist(list);
        for (String string : list) {
            ShopList shopList = new ShopList();
            //逗号隔开键值集合
            List<String> list1 = map.get(string);
            List<Integer> addList = new ArrayList<>();
            list1 = getlist(list1);
            for (String string1 : list1) {
                String[] split = string1.split(",");
                addList.add(Integer.parseInt(split[0]));
            }
            shopList.setHanzi(string);
            shopList.setList(addList);
            lists.add(shopList);
        }
        return lists;
    }


    //倒序
    public List<String> getlist(List<String> list) {
        Collections.sort(list, new Comparator<String>() {
            @Override
            public int compare(String o1, String o2) {
                try {
                    int i = o1.compareTo(o2);
                    if (i > 0) {
                        return 1;
                    } else if (i == 0) {
                        return 0;
                    } else {
                        return -1;
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                return 0;
            }
        });
        return list;
    }


    /**
     * 查看商品的spu信息
     *
     * @param spuId
     * @return
     */
    public ResultBean selectShopSpu(Long spuId) {
        ResultBean resultBean = new ResultBean();
        if (null == spuId) {
            return resultBean.error("商品id不能为空");
        }
        ShopSpuDeatil shopSpuDeatil = new ShopSpuDeatil();
        List<SzmCProductModelNew> szmCProductModels = szmCProductModelNewMapper.selectByProductId(spuId);
        if (szmCProductModels.size() == 0) {
            return resultBean.error("暂无数据");
        }
        SzmCProductNew szmCProduct = szmCProductNewMapper.selectByPrimaryKey(spuId);
        if (null == szmCProduct) {
            return resultBean.error("暂无数据");
        }
        shopSpuDeatil.setSpuId(szmCProduct.getProductId());
        shopSpuDeatil.setType(0);
        //类别id
        shopSpuDeatil.setId(szmCProduct.getProductClassifyId());
        ClassifyInfo classifyInfo = classifyInfoMapper.selectByPrimaryKey(szmCProduct.getProductClassifyId());
        if (null != classifyInfo) {
            //类别名称
            shopSpuDeatil.setClassName(classifyInfo.getClassifyName());
        }
        //品牌id
        SzmBBranddeatil szmBBranddeatil = szmBBranddeatilMapper.selectByPrimaryKey(Integer.parseInt(szmCProduct.getR3()));
        shopSpuDeatil.setBrandId(szmBBranddeatil.getBrandid().longValue());
//            SzmCBrand szmCBrand = szmCBrandMapper.selectByBrandId(Long.parseLong(szmCProduct.getR3()));
        shopSpuDeatil.setBrandName(szmBBranddeatil.getBrandname());
//            if (null != szmCBrand){
//
//            }
        //商品详情
//        shopSpuDeatil.setDetails(szmCProduct.getProductDescribe());
        shopSpuDeatil.setDetailsUrl(szmCProduct.getR4());
        // 处理商品详情图集合
        if (StringUtils.isNotEmpty(szmCProduct.getR4())) {
            List<String> detailImages = new ArrayList<>();
            String[] imageUrls = szmCProduct.getR4().split(",");
            for (String imageUrl : imageUrls) {
                if (StringUtils.isNotEmpty(imageUrl.trim())) {
                    detailImages.add(imageUrl.trim());
                }
            }
            shopSpuDeatil.setDetailImages(detailImages);
        }
//        shopSpuDeatil.setManufactor(szmCProduct.getR2());
        shopSpuDeatil.setCommodityName(szmCProduct.getProductTitle());
        shopSpuDeatil.setImgLogo(szmCProduct.getR1());
        List<AddShopSkuList> list = new ArrayList<>();
        for (SzmCProductModelNew szmCProductModel : szmCProductModels) {
            ResultBean resultBean1 = selectShopSkuDeatil(szmCProductModel.getProductModelId());
            if (1 == resultBean1.getCode()) {
                AddShopSkuList addShopSkuList = (AddShopSkuList) resultBean1.getData();
                if (null == addShopSkuList.getFlag()) {
                    shopSpuDeatil.setFlag(0);
                }
                shopSpuDeatil.setFlag(1);
                list.add(addShopSkuList);
            }
        }
        List<Long> list1 = waterProductMapper.selectNewWaterId(spuId);
        for (Long storeId : list1) {
            WaterWorks waterWorks = waterWorksMapper.selectByPrimaryKey(storeId);
            Map<String, Object> map = new HashMap<>();
            map.put("waterId", waterWorks.getWaterWorksId());
            map.put("waterName", waterWorks.getWaterTitle());
            map.put("img", waterWorks.getWaterLogo());
            WaterProduct waterProduct = waterProductMapper.selectProductBySpuId(storeId, spuId);
            map.put("video", waterProduct.getR5());
            map.put("name", waterWorks.getWaterTitle());
            shopSpuDeatil.getWaterlist().add(map);
        }
        shopSpuDeatil.setAddShopSkuLists(list);
        shopSpuDeatil.setProductDescribe(szmCProduct.getProductDescribe());
        return resultBean.success(shopSpuDeatil);
    }


    /**
     * 查看商品的详细信息
     *
     * @param spuId
     * @param source
     * @return
     */
    @Override
    public ResultBean newSelectShopSpu(Long spuId, Integer source) {
        ResultBean resultBean = new ResultBean();
        if (null == spuId) {
            return resultBean.error("商品id不能为空");
        }
        ShopSpuDeatil shopSpuDeatil = new ShopSpuDeatil();
        if (0 == source) {
            List<SzmCProductModelNew> szmCProductModels = szmCProductModelNewMapper.selectByProductId(spuId);
            if (szmCProductModels.size() <= 0) {
                return resultBean.error("暂无数据");
            }

            SzmCProductNew szmCProduct = szmCProductNewMapper.selectByPrimaryKey(spuId);
            if (null == szmCProduct) {
                return resultBean.error("暂无数据");
            }
            shopSpuDeatil.setSpuId(szmCProduct.getProductId());
            shopSpuDeatil.setType(0);
            //类别id
            shopSpuDeatil.setId(szmCProduct.getProductClassifyId());
            ClassifyInfo classifyInfo = classifyInfoMapper.selectByPrimaryKey(szmCProduct.getProductClassifyId());
            if (null != classifyInfo) {
                //类别名称
                shopSpuDeatil.setClassName(classifyInfo.getClassifyName());
            }
            //品牌id
            SzmBBranddeatil szmBBranddeatil = szmBBranddeatilMapper.selectByPrimaryKey(Integer.parseInt(szmCProduct.getR3()));
            shopSpuDeatil.setBrandId(szmBBranddeatil.getBrandid().longValue());
//            SzmCBrand szmCBrand = szmCBrandMapper.selectByBrandId(Long.parseLong(szmCProduct.getR3()));
            shopSpuDeatil.setBrandName(szmBBranddeatil.getBrandname());
//            if (null != szmCBrand){
//
//            }
            //商品详情
//        shopSpuDeatil.setDetails(szmCProduct.getProductDescribe());
            shopSpuDeatil.setDetailsUrl(szmCProduct.getR4());
            // 处理商品详情图集合
            if (StringUtils.isNotEmpty(szmCProduct.getR4())) {
                List<String> detailImages = new ArrayList<>();
                String[] imageUrls = szmCProduct.getR4().split(",");
                for (String imageUrl : imageUrls) {
                    if (StringUtils.isNotEmpty(imageUrl.trim())) {
                        detailImages.add(imageUrl.trim());
                    }
                }
                shopSpuDeatil.setDetailImages(detailImages);
            }
//        shopSpuDeatil.setManufactor(szmCProduct.getR2());
            shopSpuDeatil.setCommodityName(szmCProduct.getProductTitle());
            shopSpuDeatil.setImgLogo(szmCProduct.getR1());
            List<AddShopSkuList> list = new ArrayList<>();
            for (SzmCProductModelNew szmCProductModel : szmCProductModels) {
                ResultBean resultBean1 = selectShopSkuDeatil(szmCProductModel.getProductModelId());
                if (1 == resultBean1.getCode()) {
                    AddShopSkuList addShopSkuList = (AddShopSkuList) resultBean1.getData();
                    if (null == addShopSkuList.getFlag()) {
                        shopSpuDeatil.setFlag(0);
                    }
                    shopSpuDeatil.setFlag(1);
                    list.add(addShopSkuList);
                }
            }
            shopSpuDeatil.setAddShopSkuLists(list);

            return resultBean.success(shopSpuDeatil);
        } else {
            List<WaterProductModel> waterProductModels = waterProductModelMapper.selectAll(spuId.intValue());
            if (waterProductModels.size() <= 0) {
                return resultBean.error("暂无数据");
            }
            WaterProduct waterProduct = waterProductMapper.selectByPrimaryKey(spuId);
            if (null == waterProduct) {
                return resultBean.error("暂无数据");
            }

            shopSpuDeatil.setSpuId(waterProduct.getProductId());
            shopSpuDeatil.setType(0);
            //类别id
            shopSpuDeatil.setId(waterProduct.getProductClassifyId());
            WaterProductClassify waterProductClassify = waterProductClassifyMapper.selectByPrimaryKey(waterProduct.getProductClassifyId());
            if (null != waterProductClassify) {
                //类别名称
                shopSpuDeatil.setClassName(waterProductClassify.getProductClassifyName());
            }
            //品牌id
            WaterBrand waterBrand = waterBrandMapper.selectByPrimaryKey(Long.parseLong(waterProduct.getR3()));
            shopSpuDeatil.setBrandId(waterBrand.getBrandId());
//            SzmCBrand szmCBrand = szmCBrandMapper.selectByBrandId(Long.parseLong(szmCProduct.getR3()));
            shopSpuDeatil.setBrandName(waterBrand.getBrandName());
//            if (null != szmCBrand){
//
//            }
            //商品详情
//        shopSpuDeatil.setDetails(szmCProduct.getProductDescribe());
            shopSpuDeatil.setDetailsUrl(waterProduct.getR4());
            // 处理商品详情图集合
            if (StringUtils.isNotEmpty(waterProduct.getR4())) {
                List<String> detailImages = new ArrayList<>();
                String[] imageUrls = waterProduct.getR4().split(",");
                for (String imageUrl : imageUrls) {
                    if (StringUtils.isNotEmpty(imageUrl.trim())) {
                        detailImages.add(imageUrl.trim());
                    }
                }
                shopSpuDeatil.setDetailImages(detailImages);
            }
//        shopSpuDeatil.setManufactor(szmCProduct.getR2());
            shopSpuDeatil.setCommodityName(waterProduct.getProductTitle());
            shopSpuDeatil.setImgLogo(waterBrand.getR1());
            List<AddShopSkuList> list = new ArrayList<>();
            for (WaterProductModel szmCProductModel : waterProductModels) {
                ResultBean resultBean1 = selectWaterShopSkuDeatil(szmCProductModel.getProductModelId());
                if (1 == resultBean1.getCode()) {
                    AddShopSkuList addShopSkuList = (AddShopSkuList) resultBean1.getData();
                    if (null == addShopSkuList.getFlag()) {
                        shopSpuDeatil.setFlag(0);
                    }
                    shopSpuDeatil.setFlag(1);
                    list.add(addShopSkuList);
                }
            }
            shopSpuDeatil.setAddShopSkuLists(list);

            return resultBean.success(shopSpuDeatil);
        }
    }


    /**
     * 查看商品的sku的具体信息
     * skuId
     */
    @Override
    public ResultBean selectShopSkuDeatil(Long skuId) {
        ResultBean resultBean = new ResultBean();
        if (null == skuId) {
            return resultBean.error("参数不能为空");
        }
        SzmCProductModelNew szmCProductModel = szmCProductModelNewMapper.selectByPrimaryKey(skuId);
        if (null == szmCProductModel) {
            return resultBean.error("暂无数据");
        }

        AddShopSkuList addShopSkuList = new AddShopSkuList();
        addShopSkuList.setSkuId(szmCProductModel.getProductModelId());
        addShopSkuList.setImgUrl(szmCProductModel.getR1());
        Map map = szmCProductModel == null ? new HashMap<>() : JSON.parseObject(szmCProductModel.getSpecificationsDescribe(), Map.class);
        SzmCProductNew szmCProduct1 = szmCProductNewMapper.selectByPrimaryKey(szmCProductModel.getProductId());

        StringBuffer stringBuffer = new StringBuffer();
//                stringBuffer.append(szmCProduct1.getProductTitle());
        for (Object obj : map.keySet()) {
//                                        stringBuffer.append(obj.toString());
            stringBuffer.append(map.get(obj) + "");
        }
        addShopSkuList.setGroupImg(szmCProductModel.getProductCode());
        List<String> imgList = addShopSkuList.getImgList();
        imgList.add(szmCProduct1.getR1());
        imgList.add(szmCProductModel.getR1());
        imgList.add(szmCProduct1.getR4());
        imgList.add(szmCProductModel.getProductCode());
        addShopSkuList.setImgList(imgList);
        addShopSkuList.setDetails(stringBuffer.toString());
        addShopSkuList.setAnotherName(szmCProductModel.getProductModelName());
        return resultBean.success(addShopSkuList);
    }

    /**
     * 查看厂家的商品详情
     *
     * @param skuId
     * @return
     */
    public ResultBean selectWaterShopSkuDeatil(Long skuId) {
        ResultBean resultBean = new ResultBean();
        if (null == skuId) {
            return resultBean.error("参数不能为空");
        }
        WaterProductModel szmCProductModel = waterProductModelMapper.selectByPrimaryKey(skuId);
        if (null == szmCProductModel) {
            return resultBean.error("暂无数据");
        }

        AddShopSkuList addShopSkuList = new AddShopSkuList();
        addShopSkuList.setSkuId(szmCProductModel.getProductModelId());
        addShopSkuList.setImgUrl(szmCProductModel.getR1());
        Map map = szmCProductModel == null ? new HashMap<>() : JSON.parseObject(szmCProductModel.getSpecificationsDescribe(), Map.class);
        WaterProduct waterProduct = waterProductMapper.selectByPrimaryKey(szmCProductModel.getProductId());

        StringBuffer stringBuffer = new StringBuffer();
//                stringBuffer.append(szmCProduct1.getProductTitle());
        for (Object obj : map.keySet()) {
//                                        stringBuffer.append(obj.toString());
            stringBuffer.append(map.get(obj) + "");
        }
        addShopSkuList.setGroupImg(szmCProductModel == null ? "" : szmCProductModel.getProductCode());
        List<String> imgList = addShopSkuList.getImgList();
        imgList.add(waterProduct.getR1());
        imgList.add(szmCProductModel == null ? "" : szmCProductModel.getR1());
        imgList.add(waterProduct.getR4());
        imgList.add(szmCProductModel == null ? "" : szmCProductModel.getProductCode());
        addShopSkuList.setImgList(imgList);
        addShopSkuList.setDetails(stringBuffer.toString());
        return resultBean.success(addShopSkuList);
    }

    /**
     * 查看商品的信息
     *
     * @param productName
     * @return
     */
    @Override
    public ResultBean selectListProduct(String productName) {
        ResultBean resultBean = new ResultBean();
        if (null == productName || "".equals(productName)) {
            return resultBean.error("商品名称不能为空");
        }
        List<SzmCProductNew> szmCProductNews = szmCProductNewMapper.selectByName(productName);
        if (szmCProductNews.size() > 0) {
            List<ShopContent> skuShop = getSkuShop(szmCProductNews);
            return resultBean.success(skuShop);
        } else {
            return resultBean.error("暂无数据");
        }
    }

    /**
     * 查询子商品
     *
     * @param classId
     * @param name
     * @return
     */
    @Override
    public ResultBean selectListProductModel(Long classId, String name, Long storeId) {
        ResultBean resultBean = new ResultBean();
        if (null == classId) {
            return resultBean.error("分类不能为空");
        }
        if (null == name || "".equals(name)) {
            return resultBean.error("名称不能为空");
        }
        List<SzmCProduct> list = szmCProductMapper.selectALLbySpu(storeId.toString());
        List<SzmCProductNew> szmCProductNews = new ArrayList<>();
        if (0 == classId) {
            szmCProductNews = szmCProductNewMapper.selectByName(name);

        } else {
            szmCProductNews = szmCProductNewMapper.selectByClassName(classId, name);

        }
        if (szmCProductNews.size() <= 0) {
            return resultBean.error("暂无数据");
        }
        List<ShopContent> skuShop = getSkuShop(szmCProductNews);
        for (ShopContent shopContent : skuShop) {
            if (list.size() > 0) {
                for (SzmCProduct szmCProduct : list) {
                    if (szmCProduct.getProductTitle().equals(shopContent.getCommodityName())) {
                        shopContent.setStoreState(1);
                        shopContent.setSpuId(szmCProduct.getProductId().intValue());
                        List<Map<String, Object>> lists = shopContent.getLists();
                        for (Map<String, Object> map : lists) {
                            Object name1 = map.get("skuName");
                            Map<String, String> map1 = new HashMap<>();
                            map1.put("rule", name1.toString());
                            String string = JSON.toJSONString(map1);
                            SzmCProductModel szmCProductModel = szmCProductModelMapper.
                                    selectByStoreId(szmCProduct.getProductId(), string);
                            if (null == szmCProductModel) {
                                continue;
                            }
                            map.put("cost", szmCProductModel.getR2());
                            map.put("retail", szmCProductModel.getProductOriginalPrice());
                            map.put("stock", szmCProductModel.getProductInventory());
                            map.put("virtualStock", szmCProductModel.getVirtualInventory());
                        }
                    }
                }
            }
        }
        return resultBean.success(skuShop);
    }


    /**
     * 分类查看商品
     *
     * @param classId
     * @param name
     * @param storeId
     * @return
     */
    @Override
    public ResultBean selectListByProductName(Long classId, String name, Long storeId) {
        ResultBean resultBean = new ResultBean();
        if (null == classId) {
            return resultBean.error("分类编号不能为空");
        }
        if (null == name || "".equals(name)) {
            return resultBean.error("名称不能为空");
        }
        if (null == storeId || 0 == storeId) {
            return resultBean.error("店铺编号不能为空");
        }
        List<SzmCProduct> list = new ArrayList<>();
        if (0 == classId) {
            list = szmCProductMapper.selectByStoreIdAndName(storeId, name);
        } else {
            list = szmCProductMapper.selectByClassIdAndName(storeId, name, classId);
        }
        if (list.size() <= 0) {
            return resultBean.error("暂无数据");
        }

        List<ShopContent> skuShop = szmbStoreShopClassService.getSkuShop(list, storeId);
        Collections.sort(skuShop, new Comparator<ShopContent>() {
            @Override
            public int compare(ShopContent o1, ShopContent o2) {
                try {
                    if (o1.getInventoryState() > o2.getInventoryState()) {
                        return 1;
                    } else if (o1.getInventoryState() < o2.getInventoryState()) {
                        return -1;
                    } else {
                        return 0;
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                return 0;
            }
        });
        Collections.reverse(skuShop);
        return resultBean.success(skuShop);
    }


    /**
     * 进货调整
     *
     * @param storeId
     * @param shopClassId
     * @param state
     * @return
     */
    @Override
    public ResultBean selectProductListInventory(Long storeId, Integer shopClassId, Integer state) {
        ResultBean resultBean = szmbStoreShopClassService.selectStoreShopClass(storeId, shopClassId, state);
        if (resultBean.getCode() != 1) {
            return resultBean;
        }
        ShopAll shopAll = (ShopAll) resultBean.getData();
        List<ShopContent> shopContents = shopAll.getShopContents();
        Collections.sort(shopContents, new Comparator<ShopContent>() {
            @Override
            public int compare(ShopContent o1, ShopContent o2) {
                try {
                    if (o1.getInventoryState() > o2.getInventoryState()) {
                        return 1;
                    } else if (o1.getInventoryState() < o2.getInventoryState()) {
                        return -1;
                    } else {
                        return 0;
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                return 0;
            }
        });
        Collections.reverse(shopContents);
        return resultBean;
    }

    /**
     * 进货调整(PC)
     *
     * @param storeId
     * @param shopClassId
     * @param brandId
     * @param productName
     * @return
     */
    @Override
    public ResultBean selectProductListInventoryPc(Long storeId, Long shopClassId, Long brandId, String productName, Integer pageNo, Integer pageSize) {
        ResultBean resultBean = new ResultBean();
        if (Validator.isEmpty(productName)) {
            productName = null;
        }
        List<SzmCProduct> szmCProducts = szmCProductMapper.selProductListPC(storeId, shopClassId, brandId, productName);
        if (szmCProducts.size() < 1) {
            return resultBean.error("暂无数据");
        }
        List<Stock> list = new ArrayList<>();
        for (int i = 0; i < szmCProducts.size(); i++) {
            Stock stock = new Stock();
            if (Validator.isEmpty(szmCProducts)) {
                continue;
            }
            List<SzmCProductModel> szmCProductModels = szmCProductModelMapper.selectAllByStoreId(szmCProducts.get(i).getProductId());
            SzmCProductClassify szmCProductClassify = szmCProductClassifyMapper.selectByPrimaryKey(szmCProducts.get(i).getProductClassifyId());
            if (Validator.isEmpty(szmCProductClassify)){
                szmCProductClassify = new SzmCProductClassify();
            }
            for (int j = 0; j < szmCProductModels.size(); j++) {
                if (szmCProductClassify.getR5() != null) {
                    if (Integer.parseInt(szmCProductClassify.getR5()) >= szmCProductModels.get(j).getProductInventory()) {
                        stock.setInventoryWarning("库存预警");
                    }
                }
                stock.setInventory(szmCProductModels.get(j).getProductInventory());
                stock.setRetailPrice(szmCProductModels.get(j).getProductOriginalPrice());
                stock.setCost(Double.parseDouble(szmCProductModels.get(j).getR2()));
                stock.setGrossMargin(szmCProductModels.get(j).getProductOriginalPrice() - Double.parseDouble(szmCProductModels.get(j).getR2()));
                stock.setSkuId(szmCProductModels.get(j).getProductModelId());
            }
            stock.setImg(szmCProducts.get(i).getR1());
            stock.setProductName(szmCProducts.get(i).getProductTitle());
            stock.setBrandName(szmCProducts.get(i).getBrandName());
            if (szmCProductClassify.getR4() == null) {
                stock.setClassId(100);
            } else {
                stock.setClassId(Integer.parseInt(szmCProductClassify.getR4()));
            }

            stock.setBrandId(szmCProducts.get(i).getR3());
            list.add(stock);
        }
        Collections.sort(list, new Comparator<Stock>() {
            @Override
            public int compare(Stock u1, Stock u2) {
                int diff = u1.getClassId() - u2.getClassId();
                if (diff > 0) {
                    return 1;
                } else if (diff < 0) {
                    return -1;
                }
                return 0; //相等为0
            }
        });
        Map<String, List<Stock>> collect = list.stream().collect(Collectors.groupingBy(Stock::getBrandId, LinkedHashMap::new, Collectors.toList()));
        List list1 = new ArrayList();
        for (String s : collect.keySet()) {
            Map map = new HashMap();
            map.put("brandId", s);
            SzmCBrand szmCBrand = szmCBrandMapper.selectByBrandId(Long.parseLong(s));
            map.put("brandName", szmCBrand.getBrandName());
            BuckInventory buckInventory = buckInventoryMapper.selBrandIdAndBrandName(Long.parseLong(s), szmCBrand.getBrandName(), storeId);
            if (buckInventory == null) {
                map.put("emptyBuck", -1);
            } else {
                map.put("emptyBuck", buckInventory.getInventory());
            }
            map.put("productList", collect.get(s));
            map.put("state", 0);
            map.put("abc", "");
            list1.add(map);
        }
        return resultBean.success(list1);
    }

    /**
     * 查询该店铺分类和品牌
     *
     * @param storeId
     * @return
     */
    @Override
    public ResultBean selectStoreClassAndBrand(Long storeId) {
        ResultBean resultBean = new ResultBean();
        List<SzmCProductClassify> szmCProductClassifies = szmCProductClassifyMapper.selectExceptGroup(storeId);//分类
        List<SzmCBrand> list = szmCBrandMapper.selectWaterBackList(storeId,null);
        Map map = new HashMap();
        map.put("class", szmCProductClassifies);
        SzmCBrand szmCBrand = new SzmCBrand();
        szmCBrand.setBrandId(0l);
        szmCBrand.setBrandName("杂牌桶");
        list.add(szmCBrand);
        map.put("brand", list);
        return resultBean.success(map);
    }

    /**
     * 库存管理-库存盘点(PC)
     *
     * @param storeId
     * @param shopClassId
     * @param brandId
     * @param productName
     * @param pageNo
     * @param pageSize
     * @return
     */
    @Override
    public ResultBean selectProductInventoryPdPc(Long storeId, Long shopClassId, Long brandId, String productName, Integer pageNo, Integer pageSize) {
        ResultBean resultBean = new ResultBean();
        if (Validator.isEmpty(productName)) {
            productName = null;
        }
        List<SzmCProduct> szmCProducts = szmCProductMapper.selProductListPC(storeId, shopClassId, brandId, productName);
        List<Stock> list = new ArrayList<>();
        for (int i = 0; i < szmCProducts.size(); i++) {
            Stock stock = new Stock();
            if (Validator.isEmpty(szmCProducts)) {
                continue;
            }
            List<SzmCProductModel> szmCProductModels = szmCProductModelMapper.selectAllByStoreId(szmCProducts.get(i).getProductId());
            SzmCProductClassify szmCProductClassify = szmCProductClassifyMapper.selectByPrimaryKey(szmCProducts.get(i).getProductClassifyId());
            if (Validator.isEmpty(szmCProductClassify)){
                szmCProductClassify = new SzmCProductClassify();
            }
            for (int j = 0; j < szmCProductModels.size(); j++) {
                if (szmCProductClassify.getR5() != null) {
                    if (Integer.parseInt(szmCProductClassify.getR5()) >= szmCProductModels.get(j).getProductInventory()) {
                        stock.setInventoryWarning("库存预警");
                    }
                }
                stock.setInventory(szmCProductModels.get(j).getProductInventory());
                if (szmCProductModels.get(j).getProductModelName() == null || szmCProductModels.get(j).getProductModelName().equals("")) {
                    stock.setBuckClass("-");
                } else if (szmCProductModels.get(j).getProductModelName().equals("0")) {
                    stock.setBuckClass("常规桶");
                } else {
                    stock.setBuckClass("一次性桶");
                }
                stock.setRetailPrice(szmCProductModels.get(j).getProductOriginalPrice());
                stock.setCost(Double.parseDouble(szmCProductModels.get(j).getR2()));
                stock.setGrossMargin(szmCProductModels.get(j).getProductOriginalPrice() - Double.parseDouble(szmCProductModels.get(j).getR2()));
                stock.setSkuId(szmCProductModels.get(j).getProductModelId());
            }
            stock.setImg(szmCProducts.get(i).getR1());
            stock.setProductName(szmCProducts.get(i).getProductTitle());
            stock.setBrandName(szmCProducts.get(i).getBrandName());
            if (szmCProductClassify.getR4() == null) {
                stock.setClassId(100);
            } else {
                stock.setClassId(Integer.parseInt(szmCProductClassify.getR4()));
            }
            stock.setBrandId(szmCProducts.get(i).getR3());
            list.add(stock);
        }
        if ((brandId==0 || brandId==-1) && shopClassId == -1 && productName == null) {
            Stock stock = new Stock();
            stock.setClassId(100000);
            stock.setBrandId("0");
            list.add(stock);
        }
        Collections.sort(list, new Comparator<Stock>() {
            @Override
            public int compare(Stock u1, Stock u2) {
                int diff = u1.getClassId() - u2.getClassId();
                if (diff > 0) {
                    return 1;
                } else if (diff < 0) {
                    return -1;
                }
                return 0; //相等为0
            }
        });
        Map<String, List<Stock>> collect = list.stream().collect(Collectors.groupingBy(Stock::getBrandId, LinkedHashMap::new, Collectors.toList()));
        List list1 = new ArrayList();
        BuckInventory buckInventory1 = buckInventoryMapper.selBrandIdAndBrandName(0l, "杂牌桶", storeId);
        Integer inventory = 0;
        if (buckInventory1 !=null) {
            inventory = buckInventory1.getInventory();
        }
        if (brandId==0 && shopClassId == -1 && productName == null) {
            Map map = new HashMap();
            map.put("brandName", "杂牌桶");
            map.put("emptyBuck", inventory);
            map.put("totalInventory", inventory);
            map.put("watreInventory", "-");
            map.put("productList", new ArrayList<>());
            list1.add(map);
        } else {
            for (String s : collect.keySet()) {
                Map map = new HashMap();
                map.put("brandId", s);
                if (s.equals("0")) {
                    map.put("brandName", "杂牌桶");
                    map.put("emptyBuck", inventory);
                    map.put("totalInventory", inventory);
                    map.put("watreInventory", 0);
                    map.put("productList", new ArrayList<>());
                } else {
                    SzmCBrand szmCBrand = szmCBrandMapper.selectByBrandId(Long.parseLong(s));
                    map.put("brandName", szmCBrand.getBrandName());
                    BuckInventory buckInventory = buckInventoryMapper.selBrandIdAndBrandName(Long.parseLong(s), szmCBrand.getBrandName(), storeId);
                    Integer integer = szmCProductModelMapper.selectBrandNumber(Long.parseLong(s), storeId);
                    if (integer==null) {
                        integer = 0;
                        map.put("watreInventory", -1);
                    }else {
                        map.put("watreInventory", integer);
                    }
                    if (buckInventory == null) {
                        map.put("emptyBuck", 0);
                        map.put("totalInventory", integer);
                    } else {
                        map.put("emptyBuck", buckInventory.getInventory());
                        map.put("totalInventory", buckInventory.getInventory() + integer);
                    }
                    map.put("productList", collect.get(s));
                }
                list1.add(map);
            }
        }
        return resultBean.success(list1);
    }

    /**
     * 库存管理-库存统计(PC)
     *
     * @param storeId
     * @param shopClassId
     * @param brandId
     * @param productName
     * @param pageNo
     * @param pageSize
     * @return
     */
    @Override
    public ResultBean selectProductInventoryTjPc(Long storeId, Long shopClassId, Long brandId, String productName, Integer pageNo, Integer pageSize) {
        ResultBean resultBean = new ResultBean();
        if (Validator.isEmpty(productName)) {
            productName = null;
        }
        List<SzmCProduct> szmCProducts = szmCProductMapper.selProductListPC(storeId, shopClassId, brandId, productName);
        if (szmCProducts.size() < 1) {
            return resultBean.error("暂无数据");
        }
        List<Stock> list = new ArrayList<>();
        for (int i = 0; i < szmCProducts.size(); i++) {
            Stock stock = new Stock();
            if (Validator.isEmpty(szmCProducts)){
                continue;
            }
            List<SzmCProductModel> szmCProductModels = szmCProductModelMapper.selectAllByStoreId(szmCProducts.get(i).getProductId());
            SzmCProductClassify szmCProductClassify = szmCProductClassifyMapper.selectByPrimaryKey(szmCProducts.get(i).getProductClassifyId());
            if (Validator.isEmpty(szmCProductClassify)){
                szmCProductClassify = new SzmCProductClassify();
            }
            for (int j = 0; j < szmCProductModels.size(); j++) {
                if (szmCProductClassify.getR5() != null) {
                    if (Integer.parseInt(szmCProductClassify.getR5()) >= szmCProductModels.get(j).getProductInventory()) {
                        stock.setInventoryWarning("库存预警");
                    }
                }
                stock.setInventory(szmCProductModels.get(j).getProductInventory());
                if (szmCProductModels.get(j).getProductModelName() == null || szmCProductModels.get(j).getProductModelName().equals("")) {
                    stock.setBuckClass("-");
                } else if (szmCProductModels.get(j).getProductModelName().equals("0")) {
                    stock.setBuckClass("常规桶");
                } else {
                    stock.setBuckClass("一次性桶");
                }
                stock.setRetailPrice(szmCProductModels.get(j).getProductOriginalPrice());
                stock.setCost(Double.parseDouble(szmCProductModels.get(j).getR2()));
                stock.setGrossMargin(szmCProductModels.get(j).getProductOriginalPrice() - Double.parseDouble(szmCProductModels.get(j).getR2()));
                stock.setSkuId(szmCProductModels.get(j).getProductModelId());
            }
            stock.setImg(szmCProducts.get(i).getR1());
            stock.setProductName(szmCProducts.get(i).getProductTitle());
            stock.setBrandName(szmCProducts.get(i).getBrandName());
            if (szmCProductClassify.getR4() == null) {
                stock.setClassId(100);
            } else {
                stock.setClassId(Integer.parseInt(szmCProductClassify.getR4()));
            }
            stock.setBrandId(szmCProducts.get(i).getR3());
            list.add(stock);
        }
        Collections.sort(list, new Comparator<Stock>() {
            @Override
            public int compare(Stock u1, Stock u2) {
                int diff = u1.getClassId() - u2.getClassId();
                if (diff > 0) {
                    return 1;
                } else if (diff < 0) {
                    return -1;
                }
                return 0; //相等为0
            }
        });
        Map<String, List<Stock>> collect = list.stream().collect(Collectors.groupingBy(Stock::getBrandId, LinkedHashMap::new, Collectors.toList()));
        List list1 = new ArrayList();
        for (String s : collect.keySet()) {
            Map map = new HashMap();
            map.put("brandId", s);
            SzmCBrand szmCBrand = szmCBrandMapper.selectByBrandId(Long.parseLong(s));
            map.put("brandName", szmCBrand.getBrandName());
            BuckInventory buckInventory = buckInventoryMapper.selBrandIdAndBrandName(Long.parseLong(s), szmCBrand.getBrandName(), storeId);
            Integer integer = szmCProductModelMapper.selectBrandNumber(Long.parseLong(s), storeId);
            if (integer==null) {
                integer = 0;
                map.put("watreInventory", -1);
            }else {
                map.put("watreInventory", integer);
            }
            if (buckInventory == null) {
                map.put("emptyBuck", 0);
                map.put("totalInventory", integer);
            } else {
                map.put("emptyBuck", buckInventory.getInventory());
                map.put("totalInventory", buckInventory.getInventory() + integer);
            }
            map.put("productList", collect.get(s));
            list1.add(map);
        }
        Map map = new HashMap();
        Integer integer1 = szmCProductModelMapper.selectClassNumber(1l, storeId);
        Integer integer2 = szmCProductModelMapper.selectClassNumber(2l, storeId);
        map.put("buckNum", integer1);
        map.put("boxNum", integer2);
        map.put("list", list1);
        return resultBean.success(map);
    }


    /**
     * 查看商品标签
     *
     * @return
     */
    @Override
    public ResultBean selectProductLable() {
        ResultBean resultBean = new ResultBean();
        List<StoreLable> storeLables = storeLableMapper.selectAll();
        return resultBean.success(storeLables);
    }

    /**
     * 查询该店铺的分类和品牌
     *
     * @param storeId
     * @return
     */
    @Override
    public ResultBean selectStoreAllBrand(Long storeId) {
        ResultBean resultBean = new ResultBean();
        List<SzmCProductClassify> szmCProductClassifies = szmCProductClassifyMapper.selectExceptGroup(storeId);//分类
        List<SzmCBrand> list = szmCBrandMapper.selectAllBrandByStoreId(storeId);
        Map map = new HashMap();
        map.put("class", szmCProductClassifies);
        map.put("brand", list);
        return resultBean.success(map);
    }


    /**
     * 查看产品库信息pc
     *
     * @param classId
     * @param pageindex
     * @param name
     * @param state
     * @return
     */
    public ResultBean selectProductPc(Long classId, Integer pageindex, String name, Integer state, Long storeId, Long brandId) {
        ResultBean resultBean = new ResultBean();
        List<ClassifyInfo> classifyInfos = classifyInfoMapper.selectAll();
        ShopAll shopAll = new ShopAll();
        List<ShopClass> classes = new ArrayList<>();
        //返回分类信息
        for (ClassifyInfo szmCProductClassify : classifyInfos) {
            ShopClass shopClass = new ShopClass();
            shopClass.setId(szmCProductClassify.getClassifyId());
            shopClass.setName(szmCProductClassify.getClassifyName());
            classes.add(shopClass);
        }
        PageHelper.startPage(pageindex, 10);
        List<SzmCProductNew> szmCProductNews = new ArrayList<>();

        // 处理多关键词搜索
        List<String> keywords = null;
        if (StringUtils.isNotEmpty(name) && name.contains(" ")) {
            String[] split = name.split(" ");
            keywords = Arrays.asList(split);
        }

        // 统一调用，让XML根据keywords参数判断使用哪种搜索模式
        if (0 == state) {
            szmCProductNews = szmCProductNewMapper.selectProductListAll(classId, name, state, storeId, brandId, keywords);
        } else if (1 == state) {
            szmCProductNews = szmCProductNewMapper.selectProductList(classId, name, state, storeId, brandId, keywords);
        } else {
            szmCProductNews = szmCProductNewMapper.selectProductListCs(classId, name, state, storeId, brandId, keywords);
        }
        PageInfo<SzmCProductNew> pageInfo = new PageInfo<>(szmCProductNews);
        if (pageindex + 1 > pageInfo.getPages()) {
            shopAll.setPageState(0);
        } else {
            shopAll.setPageState(1);
        }
        shopAll.setPageCount(pageInfo.getTotal());
        List<ShopContent> skuShop = getSkuShop(szmCProductNews);
        shopAll.setShopContents(skuShop);

        List<ShopContent> todayList = new ArrayList<>();
        List<ShopContent> addList = new ArrayList<>();

        List<ShopContent> shopContents2 = shopAll.getShopContents();
        //统计今天的时间
        Date todayStartTime = DateUtil.getTodayStartTime();
        Date todayEndTime = DateUtil.getTodayEndTime();
        for (ShopContent shopContent : shopContents2) {
            int i = todayStartTime.compareTo(shopContent.getDate());
            int i1 = todayEndTime.compareTo(shopContent.getDate());
            if (i <= 0 && i1 >= 0) {
                todayList.add(shopContent);
            } else {
                addList.add(shopContent);
            }
        }
        Collections.sort(todayList, new Comparator<ShopContent>() {
            @Override
            public int compare(ShopContent o1, ShopContent o2) {
                try {
                    if (o1.getDate().getTime() > o2.getDate().getTime()) {
                        return 1;
                    } else if (o1.getDate().getTime() < o2.getDate().getTime()) {
                        return -1;
                    } else {
                        return 0;
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                return 0;
            }
        });
        Collections.reverse(todayList);
        //排序
        Map<Integer, String> map = new HashMap<>();
        for (int i = 0; i < addList.size(); i++) {
            map.put(i, addList.get(i).getCommodityName());
        }
        shopAll.setTodayList(todayList);
        Map<String, List<String>> pinyin = getPinyin(map);
        List<ShopList> getshuoindex = getshuoindex(pinyin);
        List<ShopMapList> mapLists = new ArrayList<>();
        for (ShopList shopList : getshuoindex) {
            List<ShopContent> lists = new ArrayList<>();
            List<Integer> list1 = shopList.getList();
            for (Integer index : list1) {
                ShopContent shopContent = addList.get(index);
                lists.add(shopContent);
            }
            ShopMapList shopMapList = new ShopMapList();
            shopMapList.setZm(shopList.getHanzi());
            shopMapList.setList(lists);
            mapLists.add(shopMapList);

        }
        shopAll.setRemap(mapLists);
        return resultBean.success(shopAll);
    }


}
