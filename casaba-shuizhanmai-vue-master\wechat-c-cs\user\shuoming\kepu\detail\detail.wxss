/* Detail Container */
.detail-container {
  min-height: 100vh;
  background: #f8f9fa;
}

/* Header Section */
.detail-header {
  background: white;
  padding: 40rpx 30rpx 30rpx;
  border-bottom: 1rpx solid #e9ecef;
}

.article-title {
  font-size: 36rpx;
  font-weight: 600;
  line-height: 1.4;
  color: #333;
  margin-bottom: 20rpx;
}

.article-meta {
  display: flex;
  align-items: center;
}

.meta-text {
  font-size: 24rpx;
  color: #666;
}

/* Content Section */
.content-section {
  background: white;
  padding: 30rpx;
}

.content-image {
  width: 100%;
  border-radius: 8rpx;
}

/* 简洁样式完成 */