(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["driverManage~purchaseedit"],{8376:function(a,t,o){},"8ac6":function(a,t,o){"use strict";o("8376")},"91ce":function(a,t,o){"use strict";var s=function(){var a=this,t=a._self._c;return t("div",[t("el-dialog",{staticClass:"new-dialog-css",attrs:{visible:a.comDialogVisible,width:"38%",center:"","before-close":a.handleClose},on:{"update:visible":function(t){a.comDialogVisible=t}}},[t("div",{staticClass:"new-dialog-title",attrs:{slot:"title"},slot:"title"},[a._v("\n      "+a._s(a.comDialogTitle[0])+"\n    ")]),t("div",{staticClass:"new-dialog-two-title"},[a._v(a._s(a.comDialogTitle[1]))]),t("div",{staticClass:"new-dialog-body"},[t("el-form",{staticClass:"ticheng-info",attrs:{"label-width":"100px",size:"mini"}},[t("el-form-item",{attrs:{label:"联系人："}},[t("span",[a._v(a._s(a.comDialogData.userName))])]),t("el-form-item",{attrs:{label:"手机号："}},[t("span",[a._v(a._s(a.comDialogData.userPhone))]),a._v("    "),t("span",[a._v(a._s(a.comDialogData.fixedLline))])]),t("el-form-item",{attrs:{label:"地址："}},[t("span",[a._v(a._s(a.comDialogData.address))])]),t("el-form-item",{attrs:{label:"买家留言："}},[t("span",{staticClass:"color-red"},[a._v(a._s(a.comDialogData.userLeaveWord))])])],1)],1),t("div",{staticClass:"new-dialog-two-title"},[a._v(a._s(a.comDialogTitle[2]))]),t("div",{staticClass:"new-dialog-body"},[t("div",{staticClass:"goods-infos"},[a._l(a.comDialogData.specList,(function(o,s){return t("div",{key:o.goodsName+s,staticClass:"goods-item"},[t("img",{attrs:{src:o.pic}}),t("div",{staticClass:"item-box"},[t("div",{staticClass:"item-name"},[a._v(a._s(o.goodsName))]),t("el-tag",{staticClass:"item-flag",attrs:{type:"info"}},[a._v(a._s(o.specName))]),t("div",{staticClass:"item-money-num"},[o.price?[t("span",{staticClass:"money-item"},[a._v("￥"+a._s(o.price)),t("span",[a._v("￥"+a._s(o.doller))])])]:[t("span",{staticClass:"money-item"},[a._v("￥"+a._s(o.doller))])],t("span",{staticClass:"money-num"},[a._v("x "+a._s(o.standardNumber))])],2)],1)])})),a.comDialogData.shopGroupList?a._l(a.comDialogData.shopGroupList,(function(o,s){return t("div",{key:s,staticClass:"goods-item"},[t("img",{attrs:{src:o.pic}}),t("div",{staticClass:"item-box set-meal-box"},[t("div",{staticClass:"item-name"},[a._v(a._s(o.goodsName))]),t("div",{staticClass:"item-money-num"},[o.price?[t("span",{staticClass:"money-item"},[a._v("￥"+a._s(o.price)),t("span",[a._v("￥"+a._s(o.doller))])])]:[t("span",{staticClass:"money-item"},[a._v("￥"+a._s(o.doller))])],t("span",{staticClass:"money-num"},[a._v("x "+a._s(o.standardNumber))])],2),a._l(o.specList,(function(o,s){return t("div",{key:s,staticClass:"item-money-num"},[t("span",[a._v(a._s(o.goodsName))]),t("span",[a._v("x "+a._s(o.standardNumber))])])}))],2)])})):a._e(),t("el-form",{staticClass:"goods-msg",attrs:{"label-width":"155px",size:"mini",inline:!0,"label-position":"left"}},[t("div",{staticClass:"info-flex"},[t("div",{staticClass:"goods-msg-left"},[t("el-form-item",{attrs:{label:"商品合计："}},[t("span",[a._v("+￥"+a._s(a.comDialogData.productTotalPrice))])]),"0"!=a.comDialogData.groupDiscounts&&"0.00"!=a.comDialogData.groupDiscounts?t("el-form-item",{attrs:{label:"套餐优惠："}},[t("span",{staticClass:"color-red"},[a._v("-￥"+a._s(a.comDialogData.groupDiscounts))])]):a._e(),"0"!=a.comDialogData.productDiscounts&&"0.00"!=a.comDialogData.productDiscounts?t("el-form-item",{attrs:{label:"商品优惠："}},[t("span",{staticClass:"color-red"},[a._v("-￥"+a._s(a.comDialogData.productDiscounts))])]):a._e(),"0"!=a.comDialogData.buckPrice&&"0.00"!=a.comDialogData.buckPrice?t("el-form-item",{attrs:{label:"押桶金合计："}},[t("span",[a._v("+￥"+a._s(a.comDialogData.buckPrice))])]):a._e(),"0"!=a.comDialogData.upPriceTotal&&"0.00"!=a.comDialogData.upPriceTotal?t("el-form-item",{attrs:{label:"客户付上楼费总额："}},[t("span",[a._v("+￥"+a._s(a.comDialogData.upPriceTotal))])]):a._e()],1),t("div",{staticClass:"goods-msg-right"},[t("el-form-item",{attrs:{label:"订单总金额："}},[t("span",{staticClass:"color-red"},[a._v("+￥"+a._s(a.comDialogData.orderMoney))])]),"0"!=a.comDialogData.waterDeduct&&"0.00"!=a.comDialogData.waterDeduct?t("el-form-item",{attrs:{label:"其中 水票抵扣："}},[t("span",[a._v("-￥"+a._s(a.comDialogData.waterDeduct))])]):a._e(),"0"!=a.comDialogData.total&&"0.00"!=a.comDialogData.total?t("el-form-item",{staticClass:"order-pay",attrs:{label:"订单实付金额："}},[t("span",[a._v("+￥"+a._s(a.comDialogData.total))])]):a._e()],1)])])],2)]),a.comDialogData.buckLists?[t("div",{staticClass:"new-dialog-two-title"},[a._v(a._s(a.comDialogTitle[3]))]),t("div",{staticClass:"new-dialog-body"},[a._l(a.comDialogData.buckLists,(function(o,s){return t("div",{key:s,staticClass:"yt-infos"},[t("div",{staticClass:"yt-div1"},[t("span",[a._v("押桶品牌")]),t("span",[a._v(a._s(o.brandName))])]),t("div",{staticClass:"yt-div2"},[t("span",[a._v("桶单价")]),t("span",[a._v("￥"+a._s(o.buckMoney)+"/个")])]),t("div",{staticClass:"yt-div3"},[t("span",[a._v("押桶数量")]),t("span",[a._v(a._s(o.buckNumber)+"个")])])])})),t("div",{staticClass:"yt-total-money"},[t("span",{staticClass:"color-E6A23C"},[a._v("押桶金合计金额：￥"+a._s(a.comDialogData.buckPrice))]),t("span",{staticClass:"color-E6A23C"},[a._v("押桶金支付方式："+a._s(a.comDialogData.buckPayMent))]),t("span",[a._v("押桶金支付状态：商家"+a._s(1==a.comDialogData.buckAffirm?"已确认":"未确认"))])])],2)]:a._e(),t("div",{staticClass:"new-dialog-two-title"},[a._v(a._s(a.comDialogTitle[4]))]),t("div",{staticClass:"new-dialog-body"},[t("div",{staticClass:"goods-infos"},[t("el-form",{staticClass:"goods-msg",attrs:{"label-width":"110px",size:"mini",inline:!0,"label-position":"left"}},[t("el-form-item",{attrs:{label:"订单编号:"}},[t("span",[a._v(a._s(a.comDialogData.orderNumber))])]),t("el-form-item",{attrs:{label:"商品数量:"}},[t("span",[a._v("共"+a._s(a.comDialogData.totalPieces)+"件商品")])]),t("el-form-item",{attrs:{label:"下单时间:"}},[t("span",[a._v(a._s(a.comDialogData.orderDate))])]),t("el-form-item",{staticClass:"order-payment",attrs:{label:"订单支付方式:"}},[t("span",{staticClass:"color-E6A23C"},[a._v(a._s(a.comDialogData.payment)+"（￥"+a._s(a.comDialogData.payMentPrice)+"）")])])],1)],1)]),"0"!=a.comDialogData.totalMoeny&&"0.00"!=a.comDialogData.totalMoeny?[t("div",{staticClass:"new-dialog-two-title"},[a._v(a._s(a.comDialogTitle[5]))]),t("div",{staticClass:"new-dialog-body"},[t("div",{staticClass:"goods-infos"},[t("el-form",{staticClass:"ticheng-info",attrs:{"label-width":"120px",size:"mini","label-position":"left"}},[t("el-form-item",{attrs:{label:"配送提成合计:"}},[t("span",[a._v("￥"+a._s(a.comDialogData.totalMoeny))])]),t("el-form-item",{attrs:{label:"明细："}},[t("div",["0"!=a.comDialogData.shopMoney&&"0.00"!=a.comDialogData.shopMoney?[a._v("\n                  配送单件提成：¥"+a._s(a.comDialogData.shopMoney?a.comDialogData.shopMoney:"0.00")+"元/件\n                ")]:a._e(),"0"!=a.comDialogData.shopMoney&&"0.00"!=a.comDialogData.shopMoney&&"0"!=a.comDialogData.deliveryMoney&&"0.00"!=a.comDialogData.deliveryMoney?[a._v("\n                   | \n                ")]:a._e(),"0"!=a.comDialogData.deliveryMoney&&"0.00"!=a.comDialogData.deliveryMoney?[a._v("\n                  配送业务提成：¥"+a._s(a.comDialogData.deliveryMoney?a.comDialogData.deliveryMoney:"0.00")+"元/件\n                ")]:a._e()],2),t("div",["0"!=a.comDialogData.distanceMoney&&"0.00"!=a.comDialogData.distanceMoney?[a._v("\n                  配送距离提成：¥"+a._s(a.comDialogData.distanceMoney?a.comDialogData.distanceMoney:"0.00")+"元/件\n                ")]:a._e(),"0"!=a.comDialogData.floorMonety&&"0.00"!=a.comDialogData.floorMonety&&"0"!=a.comDialogData.distanceMoney&&"0.00"!=a.comDialogData.distanceMoney?[a._v("\n                   | \n                ")]:a._e(),"0"!=a.comDialogData.floorMonety&&"0.00"!=a.comDialogData.floorMonety?[a._v("\n                  水站付上楼提成：¥"+a._s(a.comDialogData.floorMonety?a.comDialogData.floorMonety:"0.00")+"元/件\n                ")]:a._e()],2),"0"!=a.comDialogData.upPrice&&"0.00"!=a.comDialogData.upPrice?t("div",[a._v("客户付上楼费：¥"+a._s(a.comDialogData.upPrice?a.comDialogData.upPrice:"0.00")+"元/件 ")]):a._e()])],1)],1)])]:a._e(),""!=a.comDialogData.delivery?[t("div",{staticClass:"new-dialog-two-title"},[a._v(a._s(a.comDialogTitle[6]))]),t("div",{staticClass:"new-dialog-body"},[t("div",{staticClass:"goods-infos"},[t("el-form",{staticClass:"goods-msg",attrs:{"label-width":"90px",size:"mini",inline:!0,"label-position":"left"}},[t("el-form-item",{attrs:{label:"送水员:"}},[t("span",[a._v(a._s(a.comDialogData.name))])]),t("el-form-item",{attrs:{label:"联系方式:"}},[t("span",[a._v(a._s(a.comDialogData.phone))])]),t("el-form-item",{attrs:{label:"配送状态:"}},[t("span",[a._v(a._s(a.comDialogData.delivery))])])],1)],1)])]:a._e()],2)],1)},i=[],l={props:{comDialogVisible:{type:Boolean,default:!0},comDialogTitle:{type:Array},comDialogData:{type:Object}},data:function(){return{}},computed:{},created:function(){},mounted:function(){},watch:{},methods:{handleClose:function(){this.$emit("close-com-online-dialog")}},components:{}},e=l,c=(o("edee"),o("0c7c")),n=Object(c["a"])(e,s,i,!1,null,"fa9aa4e6",null);t["a"]=n.exports},bbe6:function(a,t,o){"use strict";var s=function(){var a=this,t=a._self._c;return t("div",[t("el-dialog",{staticClass:"new-dialog-css",attrs:{visible:a.comDialogVisible,width:"38%",center:"","before-close":a.handleClose},on:{"update:visible":function(t){a.comDialogVisible=t}}},[t("div",{staticClass:"new-dialog-title",attrs:{slot:"title"},slot:"title"},[a._v("\n      "+a._s(a.comDialogTitle[0])+"\n    ")]),t("div",{staticClass:"new-dialog-two-title"},[a._v(a._s(a.comDialogTitle[1]))]),t("div",{staticClass:"new-dialog-body"},[t("el-form",{staticClass:"ticheng-info",attrs:{"label-width":"100px",size:"mini"}},[t("el-form-item",{attrs:{label:"联系人："}},[t("span",[a._v(a._s(a.comDialogData.userName))])]),t("el-form-item",{attrs:{label:"手机号："}},[t("span",[a._v(a._s(a.comDialogData.phone))]),a._v("    "),t("span",[a._v(a._s(a.comDialogData.landLine))])]),t("el-form-item",{attrs:{label:"地址："}},[t("span",[a._v(a._s(a.comDialogData.addressAll))])]),t("el-form-item",{attrs:{label:"买家留言："}},[t("span",{staticClass:"color-red"},[a._v(a._s(a.comDialogData.buyerMessage))])])],1)],1),t("div",{staticClass:"new-dialog-two-title"},[a._v(a._s(a.comDialogTitle[2]))]),t("div",{staticClass:"new-dialog-body"},[t("div",{staticClass:"goods-infos"},[a._l(a.comDialogData.skuList,(function(o){return t("div",{key:o.goodsName,staticClass:"goods-item"},[t("img",{attrs:{src:o.content}}),t("div",{staticClass:"item-box"},[t("div",{staticClass:"item-name"},[a._v(a._s(o.spuName))]),t("el-tag",{staticClass:"item-flag",attrs:{type:"info"}},[a._v(a._s(o.skuName))]),t("div",{staticClass:"item-money-num"},[t("span",{staticClass:"money-item"},[a._v("￥"+a._s(o.price))]),t("span",{staticClass:"money-num"},[a._v("x "+a._s(o.num))])])],1)])})),t("el-form",{staticClass:"goods-msg",attrs:{"label-width":"155px",size:"mini",inline:!0,"label-position":"left"}},[t("div",{staticClass:"info-flex"},[t("div",{staticClass:"goods-msg-left"},[t("el-form-item",{attrs:{label:"商品合计："}},[t("span",[a._v("+￥"+a._s(a.comDialogData.allPrice))])]),"0"!=a.comDialogData.buckPrice&&"0.00"!=a.comDialogData.buckPrice?t("el-form-item",{attrs:{label:"押桶金合计："}},[t("span",[a._v("+￥"+a._s(a.comDialogData.buckPrice))])]):a._e(),"0"!=a.comDialogData.totalUpPrice&&"0.00"!=a.comDialogData.totalUpPrice?t("el-form-item",{attrs:{label:"客户付上楼费总额："}},[t("span",[a._v("+￥"+a._s(a.comDialogData.totalUpPrice))])]):a._e()],1),t("div",{staticClass:"goods-msg-right"},[t("el-form-item",{attrs:{label:"订单总金额："}},[t("span",{staticClass:"color-red"},[a._v("+￥"+a._s(a.comDialogData.orderTotal))])]),"0"!=a.comDialogData.orderTotal&&"0.00"!=a.comDialogData.orderTotal?t("el-form-item",{staticClass:"order-pay",attrs:{label:"订单实付金额："}},[t("span",[a._v("+￥"+a._s(a.comDialogData.orderTotal))])]):a._e()],1)])])],2)]),0!=a.comDialogData.list?[t("div",{staticClass:"new-dialog-two-title"},[a._v(a._s(a.comDialogTitle[3]))]),t("div",{staticClass:"new-dialog-body"},[a._l(a.comDialogData.list,(function(o,s){return t("div",{key:s,staticClass:"yt-infos"},[t("div",{staticClass:"yt-div1"},[t("span",[a._v("押桶品牌")]),t("span",[a._v(a._s(o.brandName))])]),t("div",{staticClass:"yt-div2"},[t("span",[a._v("桶单价")]),t("span",[a._v("￥"+a._s(o.buckMoney)+"/个")])]),t("div",{staticClass:"yt-div3"},[t("span",[a._v("押桶数量")]),t("span",[a._v(a._s(o.buckNumber)+"个")])])])})),t("div",{staticClass:"yt-total-money"},[t("span",{staticClass:"color-E6A23C"},[a._v("押桶金合计金额：￥"+a._s(a.comDialogData.buckPrice))]),t("span",{staticClass:"color-E6A23C"},[a._v("押桶金支付方式："+a._s(a.comDialogData.buckPayMent))]),t("span",[a._v("押桶金支付状态：商家"+a._s(1==a.comDialogData.buckAffirm?"已确认":"未确认"))])])],2)]:a._e(),t("div",{staticClass:"new-dialog-two-title"},[a._v(a._s(a.comDialogTitle[4]))]),t("div",{staticClass:"new-dialog-body"},[t("div",{staticClass:"goods-infos"},[t("el-form",{staticClass:"goods-msg",attrs:{"label-width":"115px",size:"mini",inline:!0,"label-position":"left"}},[t("el-form-item",{attrs:{label:"订单编号:"}},[t("span",[a._v(a._s(a.comDialogData.orderNumber))])]),t("el-form-item",{attrs:{label:"商品数量:"}},[t("span",[a._v("共"+a._s(a.comDialogData.orderNum)+"件商品")])]),t("el-form-item",{attrs:{label:"下单时间:"}},[t("span",[a._v(a._s(a.comDialogData.orderPayTime))])]),t("el-form-item",{staticClass:"order-payment",attrs:{label:"订单支付方式:"}},[t("span",{staticClass:"color-E6A23C"},[a._v(a._s(a.comDialogData.spuType)+"（￥"+a._s(a.comDialogData.payMentPrice)+"）")])])],1)],1)]),"0"!=a.comDialogData.totalMoeny&&"0.00"!=a.comDialogData.totalMoeny?[t("div",{staticClass:"new-dialog-two-title"},[a._v(a._s(a.comDialogTitle[5]))]),t("div",{staticClass:"new-dialog-body"},[t("div",{staticClass:"goods-infos"},[t("el-form",{staticClass:"ticheng-info",attrs:{"label-width":"120px",size:"mini","label-position":"left"}},[t("el-form-item",{attrs:{label:"配送提成合计:"}},[t("span",[a._v("￥"+a._s(a.comDialogData.totalMoeny))])]),t("el-form-item",{attrs:{label:"明细："}},[t("div",["0"!=a.comDialogData.shopMoney&&"0.00"!=a.comDialogData.shopMoney?[a._v("\n                  配送单件提成：¥"+a._s(a.comDialogData.shopMoney?a.comDialogData.shopMoney:"0.00")+"元/件\n                ")]:a._e(),"0"!=a.comDialogData.shopMoney&&"0.00"!=a.comDialogData.shopMoney&&"0"!=a.comDialogData.deliveryMoney&&"0.00"!=a.comDialogData.deliveryMoney?[a._v("\n                   | \n                ")]:a._e(),"0"!=a.comDialogData.deliveryMoney&&"0.00"!=a.comDialogData.deliveryMoney?[a._v("\n                  配送业务提成：¥"+a._s(a.comDialogData.deliveryMoney?a.comDialogData.deliveryMoney:"0.00")+"元/件\n                ")]:a._e()],2),t("div",["0"!=a.comDialogData.distanceMoney&&"0.00"!=a.comDialogData.distanceMoney?[a._v("\n                  配送距离提成：¥"+a._s(a.comDialogData.distanceMoney?a.comDialogData.distanceMoney:"0.00")+"元/件\n                ")]:a._e(),"0"!=a.comDialogData.floorMoney&&"0.00"!=a.comDialogData.floorMoney&&"0"!=a.comDialogData.distanceMoney&&"0.00"!=a.comDialogData.distanceMoney?[a._v("\n                   | \n                ")]:a._e(),"0"!=a.comDialogData.floorMoney&&"0.00"!=a.comDialogData.floorMoney?[a._v("\n                  水站付上楼提成：¥"+a._s(a.comDialogData.floorMoney?a.comDialogData.floorMoney:"0.00")+"元/件\n                ")]:a._e()],2),"0"!=a.comDialogData.upPrice&&"0.00"!=a.comDialogData.upPrice?t("div",[a._v("客户付上楼费：¥"+a._s(a.comDialogData.upPrice?a.comDialogData.upPrice:"0.00")+"元/件 ")]):a._e()])],1)],1)])]:a._e(),t("div",{staticClass:"new-dialog-two-title"},[a._v(a._s(a.comDialogTitle[6]))]),t("div",{staticClass:"new-dialog-body"},[t("div",{staticClass:"goods-infos"},[t("el-form",{staticClass:"goods-msg",attrs:{"label-width":"90px",size:"mini",inline:!0,"label-position":"left"}},[t("el-form-item",{attrs:{label:"送水员:"}},[t("span",[a._v(a._s(a.comDialogData.deliveryName))])]),t("el-form-item",{attrs:{label:"联系方式:"}},[t("span",[a._v(a._s(a.comDialogData.deliveryPhone))])]),t("el-form-item",{attrs:{label:"配送状态:"}},[t("span",[a._v(a._s(a.comDialogData.deliveryState))])])],1)],1)])],2)],1)},i=[],l={props:{comDialogVisible:{type:Boolean,default:!0},comDialogTitle:{type:Array},comDialogData:{type:Object}},data:function(){return{}},computed:{},created:function(){},mounted:function(){},watch:{},methods:{handleClose:function(){this.$emit("close-com-dialog")}},components:{}},e=l,c=(o("8ac6"),o("0c7c")),n=Object(c["a"])(e,s,i,!1,null,"7a574e68",null);t["a"]=n.exports},d1b3:function(a,t,o){},edee:function(a,t,o){"use strict";o("d1b3")}}]);