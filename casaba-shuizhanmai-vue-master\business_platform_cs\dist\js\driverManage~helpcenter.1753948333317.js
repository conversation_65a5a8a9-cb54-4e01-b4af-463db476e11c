(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["driverManage~helpcenter"],{"0898":function(e,t,n){"use strict";n("a38c")},3740:function(e,t,n){"use strict";function i(e){return new Promise((function(t,n){if(window.T)return console.log("天地图API已加载，直接返回"),void t(window.T);var i=document.createElement("script");i.type="text/javascript",i.src="https://api.tianditu.gov.cn/api?v=4.0&tk=".concat(e,"&callback=initTiandituMap"),window.initTiandituMap=function(){console.log("天地图API加载完成，回调函数执行"),window.T?(t(window.T),s&&clearTimeout(s)):n(new Error("天地图API加载异常：window.T 对象不存在"))},i.onerror=function(e){console.error("天地图API加载出错:",e),n(new Error("天地图API加载失败，请检查网络连接和TK密钥是否有效"))};var s=setTimeout((function(){console.error("天地图API加载超时"),n(new Error("天地图API加载超时，请检查网络连接"))}),3e3);document.head.appendChild(i),console.log("天地图API脚本标签已添加到文档")}))}n.d(t,"a",(function(){return i}))},"96d3":function(e,t,n){"use strict";t["a"]={baiduMapAK:"2Bwy6imlC7EL5Ij23TGlMX4NGOM6utY3",tiandituTK:"c356b7527391300668b99fe267d94985",tiandituTKBackup:"c356b7527391300668b99fe267d94985",geofence:{defaultRadius:500,defaultDenoise:30,defaultOffsetDistance:200,defaultMonitorType:"3",defaultAlarmType:["1"]}}},a38c:function(e,t,n){},bd82:function(e,t,n){"use strict";n.r(t);var i=function(){var e=this,t=e._self._c;return t("div",{staticClass:"page-container"},[t("div",{staticClass:"map-container"},[t("div",{attrs:{id:"container"}}),t("div",{staticClass:"map-controls-container"},[t("div",{staticClass:"controls-title"},[e._v("送水员列表")]),t("div",{staticClass:"map-controls"},[t("el-tooltip",{attrs:{content:"地区搜索",placement:"right"}},[t("el-button",{class:{"active-control":e.searchDrawerVisible},attrs:{type:"primary",icon:"el-icon-search",circle:""},on:{click:e.toggleSearchDrawer}})],1),t("el-tooltip",{attrs:{content:"围栏列表",placement:"right"}},[t("el-button",{class:{"active-control":e.fenceDrawerVisible},attrs:{type:"primary",icon:"el-icon-menu",circle:""},on:{click:e.toggleFenceDrawer}})],1)],1)]),-1!==e.currentFenceIndex||e.isCreatingNew?t("div",{staticClass:"map-edit-buttons"},[t("el-button",{attrs:{type:"success",size:"small"},on:{click:e.openSaveFenceDialog}},[e._v("保存围栏")]),t("el-button",{attrs:{type:"info",size:"small"},on:{click:e.cancelEdit}},[e._v("取消编辑")])],1):e._e()]),t("el-drawer",{attrs:{title:"地区搜索",visible:e.searchDrawerVisible,direction:"ltr",size:"350px",modal:!1,"with-header":!0,"custom-class":"map-drawer"},on:{"update:visible":function(t){e.searchDrawerVisible=t},open:e.handleDrawerChange,close:e.handleDrawerChange}},[t("div",{staticClass:"drawer-content"},[t("el-form",{staticClass:"fence-form",attrs:{model:e.dataForm,"label-width":"80px"}},[t("el-form-item",{attrs:{label:"地址搜索"}},[t("div",{staticStyle:{display:"flex"}},[t("el-input",{staticStyle:{flex:"1","margin-right":"10px"},attrs:{placeholder:"输入地址、地标、商圈等"},model:{value:e.searchAddress,callback:function(t){e.searchAddress=t},expression:"searchAddress"}}),t("el-button",{attrs:{type:"primary"},on:{click:e.searchLocation},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.searchLocation.apply(null,arguments)}}},[e._v("搜索")])],1),t("div",{staticStyle:{"margin-top":"10px",display:"flex","flex-wrap":"wrap"}},[t("span",{staticStyle:{"margin-right":"5px","font-size":"13px",color:"#666"}},[e._v("热门城市：")]),e._l(e.hotCities,(function(n){return t("el-button",{key:n,staticStyle:{padding:"0 5px",margin:"0 5px"},attrs:{type:"text"},on:{click:function(t){return e.quickLocateCity(n)}}},[e._v(e._s(n))])}))],2)])],1)],1)]),t("el-drawer",{attrs:{title:"围栏列表",visible:e.fenceDrawerVisible,direction:"rtl",size:"350px",modal:!1,"with-header":!0,"custom-class":"map-drawer"},on:{"update:visible":function(t){e.fenceDrawerVisible=t},open:e.handleDrawerChange,close:e.handleDrawerChange}},[t("div",{staticClass:"drawer-content"},[t("div",{staticClass:"fence-info"},[t("div",{staticClass:"store-selector"},[t("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"选择送水员",filterable:"",clearable:"",size:"small"},on:{change:e.handleDeliveryUserChange},model:{value:e.selectDeliveryUserId,callback:function(t){e.selectDeliveryUserId=t},expression:"selectDeliveryUserId"}},e._l(e.driverList,(function(e){return t("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1),t("div",{staticClass:"fence-header"},[t("h3",{staticClass:"fence-title"},[e._v("送水员围栏列表")]),t("div",{directives:[{name:"show",rawName:"v-show",value:e.selectedFences.length>0,expression:"selectedFences.length > 0"}],staticClass:"batch-actions"},[t("el-button",{attrs:{type:"text",size:"small"},on:{click:e.showBatchUpdateDialog}},[e._v("批量修改")])],1)]),t("div",{staticClass:"fence-filter"},[t("el-radio-group",{attrs:{size:"small"},on:{change:e.toggleFenceListFilter},model:{value:e.fenceFilterStatus,callback:function(t){e.fenceFilterStatus=t},expression:"fenceFilterStatus"}},[t("el-radio-button",{attrs:{label:-1}},[e._v("全部")]),t("el-radio-button",{attrs:{label:1}},[e._v("启用")]),t("el-radio-button",{attrs:{label:0}},[e._v("禁用")])],1),t("span",{staticClass:"filter-info"},[e._v("地图显示全部围栏，列表可筛选")])],1),e.fenceList&&e.fenceList.length>0?t("div",{staticClass:"fence-list-container"},e._l(e.fenceList,(function(n,i){return t("div",{key:n.id,staticClass:"fence-list-item",class:{active:e.currentFenceIndex===i}},[t("el-checkbox",{staticClass:"fence-checkbox",on:{change:function(t){return e.handleFenceCheckboxChange(n)}},model:{value:n.selected,callback:function(t){e.$set(n,"selected",t)},expression:"fence.selected"}}),t("div",{staticClass:"fence-item-content",on:{click:function(t){return e.selectFence(n,i)}}},[t("div",{style:{color:n.color||"#333"}},[e._v(e._s(n.name))]),t("div",{staticClass:"fence-status-row"},[t("span",{staticClass:"fence-status",class:{"status-enabled":1===n.status,"status-disabled":0===n.status}},[e._v("\n                  "+e._s(1===n.status?"启用":"禁用")+"\n                ")]),void 0!==n.paixu?t("span",{staticClass:"fence-priority"},[e._v("优先级: "+e._s(n.paixu||0))]):e._e()])]),t("div",{staticClass:"fence-item-actions"},[t("el-button",{attrs:{type:"text",size:"mini",icon:1===n.status?"el-icon-close":"el-icon-check",title:1===n.status?"禁用":"启用"},on:{click:function(t){return t.stopPropagation(),e.toggleFenceStatus(n,i)}}}),t("el-button",{attrs:{type:"text",size:"mini",icon:"el-icon-edit"},on:{click:function(t){return t.stopPropagation(),e.openEditInfoDialog(n,i)}}}),t("el-button",{attrs:{type:"text",size:"mini",icon:"el-icon-delete"},on:{click:function(t){return t.stopPropagation(),e.deleteFence(n,i)}}})],1)],1)})),0):t("div",{staticClass:"empty-tip"},[t("span",[e._v("暂无围栏数据")])]),t("div",{staticClass:"fence-actions"},[t("el-button",{attrs:{size:"small",type:"primary"},on:{click:e.createNewFence}},[e._v("新建围栏")]),t("el-button",{attrs:{size:"small",type:"success",disabled:!e.canSaveCurrentFence()},on:{click:e.openSaveFenceDialog}},[e._v("保存")])],1)])])]),t("el-dialog",{attrs:{title:"编辑围栏信息",visible:e.editInfoDialogVisible,width:"30%","close-on-click-modal":!1},on:{"update:visible":function(t){e.editInfoDialogVisible=t}}},[t("el-form",{ref:"editInfoFormRef",attrs:{model:e.editInfoForm,"label-width":"80px",rules:e.editFormRules}},[t("el-form-item",{attrs:{label:"送水员",prop:"deliveryUserId"}},[t("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"选择送水员",filterable:"",clearable:""},model:{value:e.editInfoForm.deliveryUserId,callback:function(t){e.$set(e.editInfoForm,"deliveryUserId",t)},expression:"editInfoForm.deliveryUserId"}},e._l(e.driverList,(function(e){return t("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1),t("el-form-item",{attrs:{label:"围栏名称",prop:"name"}},[t("el-input",{attrs:{placeholder:"请输入围栏名称"},model:{value:e.editInfoForm.name,callback:function(t){e.$set(e.editInfoForm,"name",t)},expression:"editInfoForm.name"}})],1),t("el-form-item",{attrs:{label:"围栏颜色",prop:"color"}},[t("div",{staticClass:"preset-colors"},e._l(e.presetColors,(function(n){return t("div",{key:n,staticClass:"color-item",class:{active:e.editInfoForm.color===n},style:{backgroundColor:n},on:{click:function(t){e.editInfoForm.color=n}}})})),0)]),t("el-form-item",{attrs:{label:"优先级",prop:"paixu"}},[t("el-input-number",{attrs:{min:0,max:999,placeholder:"请输入优先级"},model:{value:e.editInfoForm.paixu,callback:function(t){e.$set(e.editInfoForm,"paixu",t)},expression:"editInfoForm.paixu"}})],1),t("el-form-item",{attrs:{label:"状态",prop:"status"}},[t("el-radio-group",{model:{value:e.editInfoForm.status,callback:function(t){e.$set(e.editInfoForm,"status",t)},expression:"editInfoForm.status"}},[t("el-radio",{attrs:{label:1}},[e._v("启用")]),t("el-radio",{attrs:{label:0}},[e._v("禁用")])],1)],1)],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.editInfoDialogVisible=!1}}},[e._v("取 消")]),t("el-button",{attrs:{type:"primary"},on:{click:e.confirmEditFenceInfo}},[e._v("确 定")])],1)],1),t("el-dialog",{attrs:{title:"批量更新选中围栏",visible:e.batchUpdateDialogVisible,width:"30%","close-on-click-modal":!1},on:{"update:visible":function(t){e.batchUpdateDialogVisible=t}}},[t("el-form",{ref:"batchUpdateFormRef",attrs:{model:e.batchUpdateForm,"label-width":"80px"}},[t("el-form-item",{attrs:{label:"送水员",prop:"deliveryUserId"}},[t("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"选择送水员",filterable:"",clearable:""},model:{value:e.batchUpdateForm.deliveryUserId,callback:function(t){e.$set(e.batchUpdateForm,"deliveryUserId",t)},expression:"batchUpdateForm.deliveryUserId"}},e._l(e.driverList,(function(e){return t("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1),t("el-form-item",{attrs:{label:"统一颜色",prop:"color"}},[t("div",{staticClass:"preset-colors"},[e._l(e.presetColors,(function(n){return t("div",{key:n,staticClass:"color-item",class:{active:e.batchUpdateForm.color===n},style:{backgroundColor:n},on:{click:function(t){e.batchUpdateForm.color=n}}})})),t("div",{staticClass:"color-item color-none",class:{active:null===e.batchUpdateForm.color},on:{click:function(t){e.batchUpdateForm.color=null}}},[t("i",{staticClass:"el-icon-close"})])],2),t("div",{staticClass:"color-hint"},[e._v("不选择则保持原有颜色")])]),t("div",{staticClass:"selected-fences-info"},[e._v("\n        已选择 "+e._s(e.selectedFences.length)+" 个围栏进行批量更新。\n      ")])],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.batchUpdateDialogVisible=!1}}},[e._v("取 消")]),t("el-button",{attrs:{type:"primary"},on:{click:e.confirmBatchUpdate}},[e._v("确 定")])],1)],1),t("el-dialog",{attrs:{title:e.isCreatingNew?"新建围栏信息":"保存围栏信息",visible:e.saveFenceDialogVisible,width:"30%","close-on-click-modal":!1},on:{"update:visible":function(t){e.saveFenceDialogVisible=t}}},[t("el-form",{ref:"saveFenceFormRef",attrs:{model:e.saveFenceForm,"label-width":"80px",rules:e.fenceFormRules}},[t("el-form-item",{attrs:{label:"送水员",prop:"deliveryUserId"}},[t("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"选择送水员",filterable:"",clearable:""},model:{value:e.saveFenceForm.deliveryUserId,callback:function(t){e.$set(e.saveFenceForm,"deliveryUserId",t)},expression:"saveFenceForm.deliveryUserId"}},e._l(e.driverList,(function(e){return t("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1),t("el-form-item",{attrs:{label:"围栏名称",prop:"name"}},[t("el-input",{attrs:{placeholder:"请输入围栏名称"},model:{value:e.saveFenceForm.name,callback:function(t){e.$set(e.saveFenceForm,"name",t)},expression:"saveFenceForm.name"}})],1),t("el-form-item",{attrs:{label:"围栏颜色",prop:"color"}},[t("div",{staticClass:"preset-colors"},e._l(e.presetColors,(function(n){return t("div",{key:n,staticClass:"color-item",class:{active:e.saveFenceForm.color===n},style:{backgroundColor:n},on:{click:function(t){e.saveFenceForm.color=n}}})})),0)]),t("el-form-item",{attrs:{label:"优先级",prop:"paixu"}},[t("el-input-number",{attrs:{min:0,max:999,placeholder:"请输入优先级"},model:{value:e.saveFenceForm.paixu,callback:function(t){e.$set(e.saveFenceForm,"paixu",t)},expression:"saveFenceForm.paixu"}})],1),t("el-form-item",{attrs:{label:"状态",prop:"status"}},[t("el-radio-group",{model:{value:e.saveFenceForm.status,callback:function(t){e.$set(e.saveFenceForm,"status",t)},expression:"saveFenceForm.status"}},[t("el-radio",{attrs:{label:1}},[e._v("启用")]),t("el-radio",{attrs:{label:0}},[e._v("禁用")])],1)],1)],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.saveFenceDialogVisible=!1}}},[e._v("取 消")]),t("el-button",{attrs:{type:"primary"},on:{click:e.confirmSaveFenceDialog}},[e._v("确 定")])],1)],1)],1)},s=[],a=n("3740"),r=n("96d3"),o=n("a78e"),l=n.n(o);function c(e){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},c(e)}function d(e,t){return f(e)||h(e,t)||m(e,t)||u()}function u(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function h(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var i,s,a,r,o=[],l=!0,c=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(i=a.call(n)).done)&&(o.push(i.value),o.length!==t);l=!0);}catch(e){c=!0,s=e}finally{try{if(!l&&null!=n.return&&(r=n.return(),Object(r)!==r))return}finally{if(c)throw s}}return o}}function f(e){if(Array.isArray(e))return e}function p(e){return y(e)||v(e)||m(e)||g()}function g(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function m(e,t){if(e){if("string"==typeof e)return b(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?b(e,t):void 0}}function v(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function y(e){if(Array.isArray(e))return b(e)}function b(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,i=Array(t);n<t;n++)i[n]=e[n];return i}var F={data:function(){return{selectDeliveryUserId:null,storeId:null,TMap:null,T:null,primaryColor:"#409EFF",storeColor:"#67C23A",otherDriverColor:"#909399",addMarkerPos:null,fenceAssembly:{TMapMarker:[],latlngArr:[],latlngStr:"",polygon:null,points:[],virtualMarkers:[]},onePoint:{marker:null,lat:null,lng:null},storeFence:{polygons:[],points:[],markers:[],lats:[],lngs:[],labels:[]},mapInitialized:!1,listeners:{resize:null,visibilitychange:null},editInfoDialogVisible:!1,editInfoForm:{id:"",deliveryUserId:"",name:"",color:"",paixu:0,status:1},editFormRules:{deliveryUserId:[{required:!0,message:"请选择送水员",trigger:"change"}],name:[{required:!0,message:"请输入围栏名称",trigger:"blur"}],color:[{required:!0,message:"请选择围栏颜色",trigger:"change"}],paixu:[{type:"integer",min:0,message:"优先级必须为正整数",trigger:"blur"}]},batchUpdateDialogVisible:!1,batchUpdateForm:{deliveryUserId:"",color:null},saveFenceDialogVisible:!1,saveFenceForm:{deliveryUserId:"",name:"",color:"",paixu:0,status:1},fenceFormRules:{deliveryUserId:[{required:!0,message:"请选择送水员",trigger:"change"}],paixu:[{type:"integer",min:0,message:"优先级必须为正整数",trigger:"blur"}]},fenceList:[],currentFenceIndex:-1,currentFence:null,isCreatingNew:!1,selectedFences:[],driverList:[],presetColors:["#409EFF","#67C23A","#E6A23C","#F56C6C","#909399","#545454"],mapRelatedDataToSave:null,mapClickListeners:{click:null,contextmenu:null},lastDragEndTime:0,rightClickMenu:{visible:!1,x:0,y:0,latlng:null},savedState:{currentFence:null,currentFenceIndex:-1,fenceAssembly:null,onePoint:null,isCreatingNew:!1},dataForm:{id:"",storeId:null},searchAddress:"",localSearch:null,searchResultMarkers:[],hotCities:["北京","上海","广州","深圳","杭州","南京","武汉","成都","重庆","西安","厦门","青岛"],searchDrawerVisible:!1,fenceDrawerVisible:!1,allFenceData:[],fenceFilterStatus:-1,snapConfig:{enabled:!0,threshold:3e-4,snapMarkers:[],snapLineMarker:null,snapPointHighlight:null}}},created:function(){this.storeId=l.a.get("storeId"),this.dataForm.storeId=this.storeId,this.listeners.visibilitychange=this.handleVisibilityChange.bind(this),document.addEventListener("visibilitychange",this.listeners.visibilitychange),this.searchDrawerVisible=!1,this.fenceDrawerVisible=!1},mounted:function(){var e=this;this.setMapContainerHeight(),this.$nextTick((function(){e.initTMap(),e.listeners.resize=e.handleResize.bind(e),window.addEventListener("resize",e.listeners.resize),e.getDriverList(),setTimeout((function(){e.TMap&&e.TMap.checkResize()}),1e3)}))},beforeDestroy:function(){this.listeners.resize&&window.removeEventListener("resize",this.listeners.resize),this.listeners.visibilitychange&&document.removeEventListener("visibilitychange",this.listeners.visibilitychange),this.cleanupMap()},methods:{handleDeliveryUserChange:function(e){if(console.log("切换送水员:",e),this.currentFence=null,this.currentFenceIndex=-1,this.resetFenceAssemblyAndOnePoint(),this.selectDeliveryUserId=e,!e&&this.allFenceData&&this.allFenceData.length>0)return-1!==this.fenceFilterStatus?this.filterFenceList({status:this.fenceFilterStatus}):this.fenceList=p(this.allFenceData),void this.redrawMapWithCurrentSelection();this.getDriverFenceList()},filterFenceList:function(e){var t=this;if(e&&this.allFenceData&&0!==this.allFenceData.length){console.log("筛选围栏列表，条件:",e);var n=p(this.allFenceData);if(this.selectDeliveryUserId&&(n=n.filter((function(e){return e.deliveryUserId===t.selectDeliveryUserId}))),void 0!==e.status)this.fenceList=n.filter((function(t){return t.status===e.status}));else if(void 0!==e.deliveryUserId)this.fenceList=n.filter((function(t){return t.deliveryUserId===e.deliveryUserId}));else if(e.keyword){var i=e.keyword.toLowerCase();this.fenceList=n.filter((function(e){return e.name.toLowerCase().includes(i)||e.deliveryUserName&&e.deliveryUserName.toLowerCase().includes(i)}))}else this.fenceList=n;if(console.log("筛选后的围栏列表数量: ".concat(this.fenceList.length)),-1!==this.currentFenceIndex&&this.currentFence){var s=this.fenceList.some((function(e){return e.id===t.currentFence.id}));s?this.currentFenceIndex=this.fenceList.findIndex((function(e){return e.id===t.currentFence.id})):(console.log("当前选中的围栏不在筛选结果中，重置选择状态"),this.currentFenceIndex=-1,this.currentFence=null,this.resetFenceAssemblyAndOnePoint())}}},toggleFenceListFilter:function(e){var t=this;this.fenceFilterStatus=e,-1===e?this.selectDeliveryUserId?(this.fenceList=this.allFenceData.filter((function(e){return e.deliveryUserId===t.selectDeliveryUserId})),console.log("显示送水员ID为 ".concat(this.selectDeliveryUserId," 的所有围栏，数量:"),this.fenceList.length)):(this.fenceList=p(this.allFenceData),console.log("显示全部围栏，数量:",this.fenceList.length)):this.filterFenceList({status:e})},toggleSearchDrawer:function(){this.searchDrawerVisible=!this.searchDrawerVisible},toggleFenceDrawer:function(){this.fenceDrawerVisible=!this.fenceDrawerVisible},handleDrawerChange:function(){var e=this;this.TMap&&setTimeout((function(){e.TMap.checkResize()}),300)},cancelEdit:function(){if(this.isCreatingNew)this.isCreatingNew=!1,this.resetFenceAssemblyAndOnePoint(),this.savedState&&this.savedState.currentFence?this.restoreSavedState():(this.currentFence=null,this.currentFenceIndex=-1),this.redrawMapWithCurrentSelection(),this.$message.info("已取消新建围栏");else if(-1!==this.currentFenceIndex){var e=this.fenceList[this.currentFenceIndex];this.selectFence(e,this.currentFenceIndex),this.$message.info("已取消编辑，恢复原始数据")}},searchLocation:function(){var e=this;if(this.searchAddress){if(console.log("开始搜索地址:",this.searchAddress),!this.TMap||!this.T)return console.log("地图对象不可用"),void this.$message.error("地图组件未加载完成，请稍后再试");this.$message.info("正在搜索地址...");var t={"北京":{lng:116.404,lat:39.915},"上海":{lng:121.473,lat:31.233},"广州":{lng:113.264,lat:23.129},"深圳":{lng:114.057,lat:22.543},"杭州":{lng:120.157,lat:30.286},"南京":{lng:118.796,lat:32.058},"武汉":{lng:114.305,lat:30.593},"成都":{lng:104.066,lat:30.659},"重庆":{lng:106.551,lat:29.563},"西安":{lng:108.939,lat:34.343},"厦门":{lng:118.11,lat:24.49},"青岛":{lng:120.355,lat:36.083}};if(t[this.searchAddress])return console.log("使用预设城市坐标:",t[this.searchAddress]),void this.showSearchResult(t[this.searchAddress].lng,t[this.searchAddress].lat,this.searchAddress);this.clearSearchResults(),this.$post("/util/geocode/geocoder",{address:this.searchAddress}).then((function(t){if(console.log("后端搜索结果:",t),1===t.code&&t.data){var n,i,s=t.data;if(s.longitude&&s.latitude)n=parseFloat(s.longitude),i=parseFloat(s.latitude);else if(s.location)n=parseFloat(s.location.lng||s.location.longitude),i=parseFloat(s.location.lat||s.location.latitude);else{if(!s.result||!s.result.location)return console.warn("未知的返回数据格式:",s),void e.$message.warning("搜索结果格式异常");n=parseFloat(s.result.location.lng),i=parseFloat(s.result.location.lat)}n&&i&&!isNaN(n)&&!isNaN(i)?(console.log("搜索成功 - 经度: ".concat(n,", 纬度: ").concat(i)),e.showSearchResult(n,i,e.searchAddress),e.$message.success("已定位到: ".concat(e.searchAddress))):(console.error("坐标数据无效:",{longitude:n,latitude:i}),e.$message.warning("获取到的坐标数据无效"))}else console.error("搜索失败:",t.msg||"未知错误"),e.$message.warning(t.msg||"未找到相关位置")})).catch((function(t){console.error("搜索请求失败:",t),e.$message.error("搜索失败: "+(t.message||"网络错误"))}))}else this.$message.warning("请输入搜索地址")},showSearchResult:function(e,t,n){if(this.TMap&&this.T)try{var i=new this.T.LngLat(e,t);this.TMap.centerAndZoom(i,14);var s=new this.T.Marker(i);this.TMap.addOverLay(s),this.searchResultMarkers.push(s);var a=new this.T.InfoWindow(n);s.addEventListener("click",(function(){s.openInfoWindow(a)})),s.openInfoWindow(a),console.log("地图已定位到: ".concat(n," (").concat(e,", ").concat(t,")"))}catch(r){console.error("显示搜索结果失败:",r),this.$message.error("地图定位失败: "+r.message)}},clearSearchResults:function(){var e=this;console.log("清除搜索结果标记"),this.searchResultMarkers&&this.searchResultMarkers.length>0&&(this.searchResultMarkers.forEach((function(t){t&&e.TMap.removeOverLay(t)})),this.searchResultMarkers=[])},quickLocateCity:function(e){console.log("快速定位到城市:",e),this.TMap&&this.T?(this.clearSearchResults(),this.searchAddress=e,this.searchLocation()):this.$message.error("地图组件未加载完成，请稍后再试")},canSaveCurrentFence:function(){return this.isCreatingNew?this.fenceAssembly.latlngArr.length>0||this.onePoint.lat&&this.onePoint.lng:!(-1===this.currentFenceIndex||!this.currentFence)&&!(this.fenceAssembly.latlngArr.length>0&&this.fenceAssembly.latlngArr.length<3)},handleResize:function(){this.TMap&&this.mapInitialized&&this.TMap.checkResize()},handleVisibilityChange:function(){"visible"===document.visibilityState&&this.TMap&&this.mapInitialized&&(this.TMap.checkResize(),this.forceDragEnable())},cleanupMap:function(){if(this.TMap)try{this.TMap.clearOverLays(),this.removeExistingMapListeners(),this.TMap.disableDrag(),this.TMap.disableScrollWheelZoom(),this.TMap=null,this.T=null,this.mapInitialized=!1}catch(t){console.error("清理地图资源时发生错误:",t)}var e=document.getElementById("container");e&&(e.innerHTML="")},initTMap:function(){var e=this;console.log("initTMap: 开始加载天地图API, 使用TK:",r["a"].tiandituTK);var t=document.getElementById("container");if(!t)return console.error("initTMap: 地图容器元素不存在，初始化失败"),void this.$message.error("地图初始化失败：容器元素不存在");t.innerHTML="",Object(a["a"])(r["a"].tiandituTK).then((function(t){console.log("initTMap: 天地图API加载成功 (主TK)"),e.T=t,window.T=t,e.initMapInstance(t)})).catch((function(t){console.error("initTMap: 天地图API加载失败 (主TK):",t),r["a"].tiandituTKBackup?(e.$message.warning("initTMap: 尝试使用备用密钥加载地图..."),Object(a["a"])(r["a"].tiandituTKBackup).then((function(t){console.log("initTMap: 天地图API加载成功 (备用TK)"),e.T=t,window.T=t,e.initMapInstance(t)})).catch((function(t){console.error("initTMap: 天地图API加载失败 (备用TK):",t),e.$message.error("地图加载失败: 请检查网络和天地图密钥配置")}))):e.$message.error("天地图加载失败: "+t.message)}))},initMapInstance:function(e){var t=this;try{var n=document.getElementById("container");if(n.querySelector(".T-map")&&(n.innerHTML=""),this.TMap=new e.Map("container",{projection:"EPSG:4326",minZoom:4,maxZoom:18,attributionControl:!1,crossOrigin:"anonymous"}),!this.TMap)return console.error("initMapInstance: 地图对象创建失败"),void this.$message.error("地图初始化失败：无法创建地图对象");var i=new e.LngLat(116.404,39.915);this.TMap.centerAndZoom(i,12),this.TMap.enableDrag(),this.TMap.enableScrollWheelZoom(!0),this.TMap.enableDoubleClickZoom(),this.TMap.enableKeyboard(),this.TMap.enableInertia(),this.TMap.addControl(new e.Control.Zoom),this.TMap.addControl(new e.Control.Scale),this.TMap.addControl(new e.Control.MapType),console.log("initMapInstance: 地图和控件初始化完成"),this.mapInitialized=!0,this.getStoreFenceData((function(){t.getDriverFenceList()})),this.resetMapEventsToDefaultClick(),setTimeout((function(){t.TMap&&t.mapInitialized&&(window.dispatchEvent(new Event("resize")),t.TMap.checkResize(),t.forceDragEnable())}),500)}catch(s){console.error("initMapInstance: 地图实例化失败:",s),this.$message.error("地图实例化失败: "+s.message)}},forceDragEnable:function(){var e=this;if(this.TMap&&this.mapInitialized)try{this.TMap.disableDrag(),setTimeout((function(){e.TMap&&e.mapInitialized&&(e.TMap.enableDrag(),"function"===typeof e.TMap.setDraggable&&e.TMap.setDraggable(!0),e.TMap.checkResize())}),100)}catch(t){console.error("forceDragEnable: 启用拖拽功能失败:",t)}},getStoreFenceData:function(e){var t=this;if(!this.storeId)return console.warn("getStoreFenceData: storeId is missing."),void("function"===typeof e&&e());this.$get("storeGeofence/findByStoreId",{storeId:this.storeId}).then((function(n){if(t.storeFence={polygons:[],points:[],markers:[],lats:[],lngs:[],labels:[]},1==n.code&&n.data){var i=Array.isArray(n.data)?n.data:[n.data];i.forEach((function(e,n){if(e.onePoint)try{var i=JSON.parse(e.onePoint);if(i&&i.latitude&&i.longitude){t.storeFence.lats.push(i.latitude),t.storeFence.lngs.push(i.longitude);var s=new t.T.LngLat(i.longitude,i.latitude),a=new t.T.Icon({iconUrl:"https://mpjoy.oss-cn-beijing.aliyuncs.com/20250515/21552a4358b04c15ba8f99ce956ee18e.png",iconSize:new t.T.Point(23,25),iconAnchor:new t.T.Point(10,25)}),r=new t.T.Marker(s,{icon:a});t.TMap.addOverLay(r);var o=new t.T.InfoWindow(e.storeName||"店铺".concat(n+1));r.addEventListener("click",(function(){return r.openInfoWindow(o)})),t.storeFence.markers.push(r)}}catch(g){console.error("getStoreFenceData: 解析店铺定位点失败:",g)}if(2==e.fenceType&&e.points)try{var l=JSON.parse(e.points);if(l&&l.length>=3){var c=l.map((function(e){return new t.T.LngLat(e.longitude,e.latitude)})),d=e.color||t.storeColor,u=new t.T.Polygon(c,{color:d,weight:2,opacity:.8,fillColor:d,fillOpacity:.2});t.TMap.addOverLay(u),t.storeFence.polygons.push(u),t.storeFence.points.push(c);var h=t.calculatePolygonCenter(c),f=e.fenceName||e.storeName||"店铺围栏".concat(n+1),p=new t.T.Label({position:h,text:f,offset:new t.T.Point(-12*f.length/2,-10)});t.TMap.addOverLay(p),t.storeFence.labels.push(p)}}catch(g){console.error("getStoreFenceData: 解析店铺围栏失败:",g)}}))}"function"===typeof e&&e()})).catch((function(t){console.error("getStoreFenceData: 获取店铺围栏数据API请求失败:",t),"function"===typeof e&&e()}))},redrawMapWithCurrentSelection:function(){var e=this;this.TMap&&this.mapInitialized?(console.log("redrawMapWithCurrentSelection: Clearing all overlays and redrawing based on current state."),this.TMap.clearOverLays(),this.getStoreFenceData((function(){console.log("redrawMapWithCurrentSelection: Store fences rendered. Proceeding with driver fences.");var t=e.allFenceData&&e.allFenceData.length>0?e.allFenceData:e.fenceList;t.forEach((function(t,n){var i=e.fenceList.findIndex((function(e){return e.id===t.id}));i!==e.currentFenceIndex||e.isCreatingNew?(console.log("redrawMapWithCurrentSelection: Rendering fence '".concat(t.name,"' as reference.")),e.renderSingleFenceAsReference(t,i)):(console.log("redrawMapWithCurrentSelection: Rendering fence '".concat(t.name,"' (index ").concat(i,") as editable.")),e.operateTMap())})),e.isCreatingNew&&(console.log("redrawMapWithCurrentSelection: In creating new mode, calling operateTMap for potential onePoint display."),e.operateTMap()),e.adjustViewportAndModeAfterRedraw()}))):console.warn("redrawMapWithCurrentSelection: Map not ready.")},adjustViewportAndModeAfterRedraw:function(){var e=this;if(this.TMap&&this.mapInitialized){var t=[];-1!==this.currentFenceIndex&&!this.isCreatingNew&&this.fenceAssembly.points.length>0?t.push.apply(t,p(this.fenceAssembly.points)):this.isCreatingNew&&this.fenceAssembly.latlngArr.length>0&&t.push.apply(t,p(this.fenceAssembly.latlngArr.map((function(t){var n=t.split(","),i=d(n,2),s=i[0],a=i[1];return new e.T.LngLat(a,s)})))),this.onePoint.lat&&this.onePoint.lng&&t.push(new this.T.LngLat(this.onePoint.lng,this.onePoint.lat)),0===t.length&&this.fenceList.length>0&&this.fenceList.forEach((function(n){if(n.points)try{var i=JSON.parse(n.points);i&&i.length>0&&t.push.apply(t,p(i.map((function(t){return new e.T.LngLat(t.longitude,t.latitude)}))))}catch(s){}})),0===t.length&&(this.storeFence.points.forEach((function(e){return t.push.apply(t,p(e))})),this.storeFence.markers.forEach((function(e){return t.push(e.getLngLat())}))),t.length>0?this.TMap.setViewport(t):this.TMap.getZoom()<5&&this.TMap.centerAndZoom(new this.T.LngLat(116.404,39.915),12),this.isCreatingNew?this.setupMapClickForNewFence():-1!==this.currentFenceIndex?this.setupEditModeInteractions():this.resetMapEventsToDefaultClick(),setTimeout((function(){return e.forceDragEnable()}),250)}},renderSingleFenceAsReference:function(e,t){var n=this;if(this.TMap&&this.mapInitialized){var i=e.color||this.otherDriverColor;if(e.points)try{var s=JSON.parse(e.points);if(s&&s.length>=3){var a=s.map((function(e){return new n.T.LngLat(e.longitude,e.latitude)})),r=new this.T.Polygon(a,{color:i,weight:2,opacity:.7,fillColor:i,fillOpacity:.15,clickable:!0});this.TMap.addOverLay(r),r.addEventListener("click",(function(){return n.selectFence(e,t)}));var o=this.calculatePolygonCenter(a),l=e.name||e.deliveryUserName||"围栏".concat(t+1),c=new this.T.Label({position:o,text:l,offset:new this.T.Point(-12*l.length/2,-10)});this.TMap.addOverLay(c)}}catch(p){console.error("renderSingleFenceAsReference: 渲染参考围栏 '".concat(e.name,"' 多边形失败:"),p)}if(e.onePoint)try{var d=JSON.parse(e.onePoint);if(d&&d.latitude&&d.longitude){var u=new this.T.Icon({iconUrl:"https://mpjoy.oss-cn-beijing.aliyuncs.com/20250515/2e161a33861f4c5c924a257526ab6a4f.png",iconSize:new this.T.Point(23,25),iconAnchor:new this.T.Point(10,25)}),h=new this.T.LngLat(d.longitude,d.latitude),f=new this.T.Marker(h,{icon:u,clickable:!0});this.TMap.addOverLay(f),f.addEventListener("click",(function(){return n.selectFence(e,t)}))}}catch(p){console.error("renderSingleFenceAsReference: 渲染参考围栏 '".concat(e.name,"' 定位点失败:"),p)}}},operateTMap:function(){var e=this;if(this.TMap&&this.mapInitialized){console.log("operateTMap: Drawing/updating editable fence based on fenceAssembly and onePoint."),this.clearCurrentFenceElements(),this.clearSnapHelpers();var t=this.fenceAssembly.latlngArr;if(t&&t.length>0){this.fenceAssembly.TMapMarker=[],t.forEach((function(t,n){var i=t.split(","),s=d(i,2),a=s[0],r=s[1],o=new e.T.LngLat(r,a);e.generateTextImage(n+1,(function(t){var i=new e.T.Icon({iconUrl:t,iconAnchor:new e.T.Point(10,10)}),s=new e.T.Marker(o,{icon:i,draggable:!0});s.addEventListener("dragstart",(function(t){e.lastDragEndTime=Date.now(),s._originalPosition=t.lnglat,e.snapConfig.enabled&&e.collectSnapPoints(n)})),s.addEventListener("dragging",(function(t){if(e.snapConfig.enabled&&e.snapPoints&&e.snapPoints.length>0){var n=t.lnglat,i=e.findNearestSnapPoint(n);i?e.showSnapVisualHint(n,i.point):e.clearSnapHelpers()}})),s.addEventListener("dragend",(function(t){e.lastDragEndTime=Date.now();var i=t.lnglat.lat,s=t.lnglat.lng;if(e.snapConfig.enabled&&e.snapPoints&&e.snapPoints.length>0){var a=e.findNearestSnapPoint(t.lnglat);a&&(i=a.point.lat,s=a.point.lng,console.log("吸附到附近点: [".concat(i,", ").concat(s,"], 距离: ").concat(a.distance.toFixed(6))))}e.clearSnapHelpers(),e.fenceAssembly.latlngArr[n]="".concat(i,",").concat(s),e.fenceAssembly.latlngStr=e.fenceAssembly.latlngArr.join(";"),e.operateTMapNoDeal()})),s.addEventListener("contextmenu",(function(t){e.lastDragEndTime=Date.now(),e.fenceAssembly.latlngArr.length<=3&&e.fenceAssembly.polygon?e.$message.warning("封闭的多边形至少需要三个点"):(e.fenceAssembly.latlngArr.splice(n,1),e.fenceAssembly.latlngStr=e.fenceAssembly.latlngArr.join(";"),e.operateTMapNoDeal(),e.$message.success("已删除坐标点"),t.domEvent&&t.domEvent.preventDefault())})),e.TMap.addOverLay(s),e.fenceAssembly.TMapMarker.push(s)}))}));var n=t.map((function(t){var n=t.split(","),i=d(n,2),s=i[0],a=i[1];return new e.T.LngLat(a,s)}));if(this.fenceAssembly.points=n,n.length>=3){var i=new this.T.Polygon(n,{color:this.primaryColor,weight:3,opacity:.8,fillColor:this.primaryColor,fillOpacity:.3});this.TMap.addOverLay(i),this.fenceAssembly.polygon=i,this.addVirtualPoints(n)}else this.fenceAssembly.polygon=null,this.fenceAssembly.virtualMarkers=[]}else this.fenceAssembly.points=[],this.fenceAssembly.polygon=null,this.fenceAssembly.virtualMarkers=[],this.fenceAssembly.TMapMarker=[],console.log("operateTMap: No polygon points. Polygon and its markers cleared.");this.onePoint.lat&&this.onePoint.lng&&this.renderAndBindOnePointMarker()}else console.warn("operateTMap: Map not ready.")},collectSnapPoints:function(e){var t=this;this.snapPoints=[],this.fenceAssembly.latlngArr.forEach((function(n,i){if(i!==e){var s=n.split(","),a=d(s,2),r=a[0],o=a[1];t.snapPoints.push({lat:parseFloat(r),lng:parseFloat(o),source:"current",index:i})}})),this.allFenceData.forEach((function(e,n){if((-1===t.currentFenceIndex||e.id!==t.currentFence.id)&&e.points)try{var i=JSON.parse(e.points);Array.isArray(i)&&i.length>0&&i.forEach((function(i,s){void 0!==i.latitude&&void 0!==i.longitude&&t.snapPoints.push({lat:i.latitude,lng:i.longitude,source:"fence",fenceId:e.id,fenceIndex:n,pointIndex:s})}))}catch(s){console.error("解析围栏点数据失败",s)}})),console.log("收集到 ".concat(this.snapPoints.length," 个潜在吸附点"))},findNearestSnapPoint:function(e){var t=this;if(!this.snapPoints||0===this.snapPoints.length)return null;var n=null,i=this.snapConfig.threshold;return this.snapPoints.forEach((function(s){var a=t.calculateDistance(e.lat,e.lng,s.lat,s.lng);a<i&&(i=a,n=s)})),n?{point:n,distance:i}:null},calculateDistance:function(e,t,n,i){var s=t-i,a=e-n;return Math.sqrt(s*s+a*a)},showSnapVisualHint:function(e,t){this.clearSnapHelpers();var n=new this.T.Icon({iconUrl:"https://api.map.baidu.com/images/marker_red_sprite.png",iconSize:new this.T.Point(20,20),iconAnchor:new this.T.Point(10,10)}),i=new this.T.LngLat(t.lng,t.lat),s=new this.T.Marker(i,{icon:n});this.TMap.addOverLay(s),this.snapConfig.snapPointHighlight=s;var a=new this.T.LngLat(e.lng,e.lat),r=[a,i],o=new this.T.Polyline(r,{color:"#FF4500",weight:2,opacity:.8,lineStyle:"dashed"});this.TMap.addOverLay(o),this.snapConfig.snapLineMarker=o},clearSnapHelpers:function(){var e=this;this.snapConfig.snapPointHighlight&&(this.TMap.removeOverLay(this.snapConfig.snapPointHighlight),this.snapConfig.snapPointHighlight=null),this.snapConfig.snapLineMarker&&(this.TMap.removeOverLay(this.snapConfig.snapLineMarker),this.snapConfig.snapLineMarker=null),this.snapConfig.snapMarkers&&this.snapConfig.snapMarkers.length>0&&(this.snapConfig.snapMarkers.forEach((function(t){t&&e.TMap.removeOverLay(t)})),this.snapConfig.snapMarkers=[])},operateTMapNoDeal:function(){var e=this;if(this.TMap&&this.mapInitialized){console.log("operateTMap: Drawing/updating editable fence based on fenceAssembly and onePoint."),this.clearCurrentFenceElements();var t=this.fenceAssembly.latlngArr;if(t&&t.length>0){this.fenceAssembly.TMapMarker=[],t.forEach((function(t,n){var i=t.split(","),s=d(i,2),a=s[0],r=s[1],o=new e.T.LngLat(r,a);e.generateTextImage(n+1,(function(t){var i=new e.T.Icon({iconUrl:t,iconAnchor:new e.T.Point(10,10)}),s=new e.T.Marker(o,{icon:i,draggable:!0});s.addEventListener("dragstart",(function(t){e.lastDragEndTime=Date.now(),s._originalPosition=t.lnglat,e.snapConfig.enabled&&e.collectSnapPoints(n)})),s.addEventListener("dragging",(function(t){if(e.snapConfig.enabled&&e.snapPoints&&e.snapPoints.length>0){var n=t.lnglat,i=e.findNearestSnapPoint(n);i?e.showSnapVisualHint(n,i.point):e.clearSnapHelpers()}})),s.addEventListener("dragend",(function(t){e.lastDragEndTime=Date.now();var i=t.lnglat.lat,s=t.lnglat.lng;if(e.snapConfig.enabled&&e.snapPoints&&e.snapPoints.length>0){var a=e.findNearestSnapPoint(t.lnglat);a&&(i=a.point.lat,s=a.point.lng,console.log("吸附到附近点: [".concat(i,", ").concat(s,"], 距离: ").concat(a.distance.toFixed(6))))}e.clearSnapHelpers(),e.fenceAssembly.latlngArr[n]="".concat(i,",").concat(s),e.fenceAssembly.latlngStr=e.fenceAssembly.latlngArr.join(";"),e.operateTMapNoDeal()})),s.addEventListener("contextmenu",(function(t){e.lastDragEndTime=Date.now(),e.fenceAssembly.latlngArr.length<=3&&e.fenceAssembly.polygon?e.$message.warning("封闭的多边形至少需要三个点"):(e.fenceAssembly.latlngArr.splice(n,1),e.fenceAssembly.latlngStr=e.fenceAssembly.latlngArr.join(";"),e.operateTMapNoDeal(),e.$message.success("已删除坐标点"),t.domEvent&&t.domEvent.preventDefault())})),e.TMap.addOverLay(s),e.fenceAssembly.TMapMarker.push(s)}))}));var n=t.map((function(t){var n=t.split(","),i=d(n,2),s=i[0],a=i[1];return new e.T.LngLat(a,s)}));if(this.fenceAssembly.points=n,n.length>=3){var i=new this.T.Polygon(n,{color:this.primaryColor,weight:3,opacity:.8,fillColor:this.primaryColor,fillOpacity:.3});this.TMap.addOverLay(i),this.fenceAssembly.polygon=i,this.addVirtualPoints(n)}else this.fenceAssembly.polygon=null,this.fenceAssembly.virtualMarkers=[]}else this.fenceAssembly.points=[],this.fenceAssembly.polygon=null,this.fenceAssembly.virtualMarkers=[],this.fenceAssembly.TMapMarker=[],console.log("operateTMap: No polygon points. Polygon and its markers cleared.");this.onePoint.lat&&this.onePoint.lng&&this.renderAndBindOnePointMarker()}else console.warn("operateTMap: Map not ready.")},renderAndBindOnePointMarker:function(){var e=this;if(this.TMap&&this.mapInitialized&&this.onePoint.lat&&this.onePoint.lng){var t=new this.T.Icon({iconUrl:"https://mpjoy.oss-cn-beijing.aliyuncs.com/20250515/2e161a33861f4c5c924a257526ab6a4f.png",iconSize:new this.T.Point(23,25),iconAnchor:new this.T.Point(10,25)}),n=new this.T.LngLat(this.onePoint.lng,this.onePoint.lat),i=new this.T.Marker(n,{icon:t,draggable:!0});i.addEventListener("dragstart",(function(t){e.lastDragEndTime=Date.now(),e.snapConfig.enabled&&e.collectSnapPoints(-1)})),i.addEventListener("dragging",(function(t){if(e.snapConfig.enabled&&e.snapPoints&&e.snapPoints.length>0){var n=t.lnglat,i=e.findNearestSnapPoint(n);i?e.showSnapVisualHint(n,i.point):e.clearSnapHelpers()}})),i.addEventListener("dragend",(function(t){e.lastDragEndTime=Date.now();var n=t.lnglat.lat,i=t.lnglat.lng;if(e.snapConfig.enabled&&e.snapPoints&&e.snapPoints.length>0){var s=e.findNearestSnapPoint(t.lnglat);s&&(n=s.point.lat,i=s.point.lng,console.log("送水员定位点吸附到附近点: [".concat(n,", ").concat(i,"], 距离: ").concat(s.distance.toFixed(6))))}e.clearSnapHelpers(),e.onePoint.lat=n,e.onePoint.lng=i,e.operateTMapNoDeal()})),i.addEventListener("contextmenu",(function(t){e.lastDragEndTime=Date.now(),e.$confirm("是否要删除此送水员定位点？","删除定位点",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.onePoint.lat=null,e.onePoint.lng=null,e.operateTMapNoDeal(),e.$message.success("送水员定位点已删除")})).catch((function(){})),t.domEvent&&t.domEvent.preventDefault()}));var s=new this.T.InfoWindow("送水员定位点");i.addEventListener("click",(function(){i.openInfoWindow(s)})),this.TMap.addOverLay(i),this.onePoint.marker=i}},clearCurrentFenceElements:function(){var e=this;this.TMap&&(console.log("clearCurrentFenceElements: Clearing elements for current editable fence (fenceAssembly & onePoint)."),this.fenceAssembly.polygon&&(this.TMap.removeOverLay(this.fenceAssembly.polygon),this.fenceAssembly.polygon=null),this.fenceAssembly.TMapMarker.forEach((function(t){t&&e.TMap.removeOverLay(t)})),this.fenceAssembly.TMapMarker=[],this.fenceAssembly.virtualMarkers.forEach((function(t){t&&e.TMap.removeOverLay(t)})),this.fenceAssembly.virtualMarkers=[],this.onePoint.marker&&(this.TMap.removeOverLay(this.onePoint.marker),this.onePoint.marker=null))},addMarkerForNewFenceCreation:function(){if(this.TMap&&this.addMarkerPos&&this.isCreatingNew)if(this.fenceAssembly.latlngArr.length>=50)this.$message.warning("围栏最多允许添加50个坐标点！");else{var e=this.addMarkerPos,t=e.lat,n=e.lng;this.fenceAssembly.latlngArr.push("".concat(t,",").concat(n)),this.fenceAssembly.latlngStr=this.fenceAssembly.latlngArr.join(";"),this.redrawMapWithCurrentSelection()}},generateTextImage:function(e,t){var n=document.createElement("canvas"),i=n.getContext("2d");n.width=20,n.height=20,i.beginPath(),i.arc(10,10,10,0,2*Math.PI),i.fillStyle=this.primaryColor,i.fill(),i.fillStyle="#ffffff",i.font="bold 12px Arial",i.textAlign="center",i.textBaseline="middle",i.fillText(String(e),n.width/2,n.height/2+1),t(n.toDataURL("image/png"))},resetPolygon:function(){this.resetFenceAssemblyAndOnePoint(),this.redrawMapWithCurrentSelection(),this.$message.info("当前编辑的围栏几何形状已重置")},resetFenceAssemblyAndOnePoint:function(){this.clearCurrentFenceElements(),this.fenceAssembly={TMapMarker:[],latlngArr:[],latlngStr:"",polygon:null,points:[],virtualMarkers:[]},this.onePoint={marker:null,lat:null,lng:null}},showBatchUpdateDialog:function(){0!==this.selectedFences.length?(this.batchUpdateDialogVisible=!0,this.batchUpdateForm={deliveryUserId:"",color:null}):this.$message.warning("请先在列表中勾选要批量修改的围栏")},handleFenceCheckboxChange:function(e){var t=this.selectedFences.findIndex((function(t){return t.id===e.id}));e.selected?-1===t&&this.selectedFences.push(e):-1!==t&&this.selectedFences.splice(t,1)},selectFence:function(e,t){if(this.TMap&&this.mapInitialized)if(this.currentFenceIndex!==t||this.isCreatingNew){if(console.log("selectFence: User selected fence '".concat(e.name,"' (index ").concat(t,").")),console.log("选中的围栏数据:",JSON.stringify(e)),this.currentFence=JSON.parse(JSON.stringify(e)),this.currentFenceIndex=t,this.isCreatingNew=!1,this.primaryColor=this.currentFence.color||this.presetColors[0],this.resetFenceAssemblyAndOnePoint(),this.currentFence.points)try{var n=JSON.parse(this.currentFence.points);console.log("解析围栏points数据:",n),Array.isArray(n)&&n.length>0?(this.fenceAssembly.latlngArr=n.map((function(e){return"".concat(e.latitude,",").concat(e.longitude)})),this.fenceAssembly.latlngStr=this.fenceAssembly.latlngArr.join(";"),console.log("设置围栏顶点数据成功，点数:",this.fenceAssembly.latlngArr.length)):console.warn("围栏points数据为空数组或格式不正确")}catch(s){console.error("selectFence: 解析选中围栏的点数据失败:",s,"原始数据:",this.currentFence.points),this.fenceAssembly.latlngArr=[],this.fenceAssembly.latlngStr=""}else console.log("选中的围栏没有points数据");if(this.currentFence.onePoint)try{var i=JSON.parse(this.currentFence.onePoint);console.log("解析围栏onePoint数据:",i),i&&"number"===typeof i.latitude&&"number"===typeof i.longitude?(this.onePoint.lat=i.latitude,this.onePoint.lng=i.longitude,console.log("设置围栏定位点成功:",this.onePoint.lat,this.onePoint.lng)):console.warn("围栏onePoint数据格式不正确")}catch(s){console.error("selectFence: 解析选中围栏的定位点数据失败:",s,"原始数据:",this.currentFence.onePoint),this.onePoint.lat=null,this.onePoint.lng=null}else console.log("选中的围栏没有onePoint数据");this.redrawMapWithCurrentSelection(),this.$message.success('"'.concat(this.currentFence.name,'" 已选中，可拖动顶点修改'))}else this.fenceAssembly.points&&this.fenceAssembly.points.length>0?this.TMap.setViewport(this.fenceAssembly.points):this.onePoint.marker&&this.TMap.setViewport([this.onePoint.marker.getLngLat()])},calculatePolygonCenter:function(e){if(!e||0===e.length)return null;var t=0,n=0;return e.forEach((function(e){t+=e.lat,n+=e.lng})),new this.T.LngLat(n/e.length,t/e.length)},createNewFence:function(){var e=this;this.currentFence||this.fenceAssembly.latlngArr&&this.fenceAssembly.latlngArr.length>0?this.$confirm("切换到新建模式将丢失当前未保存的编辑，是否继续？","提示",{confirmButtonText:"继续",cancelButtonText:"取消",type:"warning"}).then((function(){e.startNewFenceCreation()})).catch((function(){})):this.startNewFenceCreation()},setupMapClickForNewFence:function(){var e=this;this.TMap&&this.mapInitialized&&(this.removeExistingMapListeners(),console.log("setupMapClickForNewFence: Setting up map listeners for new fence creation."),this.mapClickListeners.click=function(t){Date.now()-e.lastDragEndTime<200||(e.addMarkerPos=t.lnglat,e.addMarkerForNewFenceCreation())},this.mapClickListeners.contextmenu=function(t){if(!(Date.now()-e.lastDragEndTime<200)){e.addMarkerPos=t.lnglat;var n=!1;if(e.fenceAssembly.TMapMarker&&e.fenceAssembly.TMapMarker.length>0)for(var i=0;i<e.fenceAssembly.TMapMarker.length;i++){var s=e.fenceAssembly.TMapMarker[i],a=s.getLngLat();if(Math.abs(a.lng-t.lnglat.lng)<1e-4&&Math.abs(a.lat-t.lnglat.lat)<1e-4){n=!0,e.fenceAssembly.latlngArr.length<=3&&e.fenceAssembly.polygon?e.$message.warning("封闭的多边形至少需要三个点"):(e.fenceAssembly.latlngArr.splice(i,1),e.fenceAssembly.latlngStr=e.fenceAssembly.latlngArr.join(";"),e.redrawMapWithCurrentSelection(),e.$message.success("已删除坐标点"));break}}n||(e.onePoint.lat=e.addMarkerPos.lat,e.onePoint.lng=e.addMarkerPos.lng,e.redrawMapWithCurrentSelection(),e.$message.success("送水员定位点已设置")),t.domEvent&&t.domEvent.preventDefault()}},this.TMap.addEventListener("click",this.mapClickListeners.click),this.TMap.addEventListener("contextmenu",this.mapClickListeners.contextmenu))},setupEditModeInteractions:function(){var e=this;this.TMap&&this.mapInitialized&&(this.removeExistingMapListeners(),console.log("setupEditModeInteractions: Map interaction set for editing (drag vertices/midpoints)."),this.mapClickListeners.contextmenu=function(t){if(!(Date.now()-e.lastDragEndTime<200)){e.addMarkerPos=t.lnglat;var n=!1;if(e.fenceAssembly.TMapMarker&&e.fenceAssembly.TMapMarker.length>0)for(var i=0;i<e.fenceAssembly.TMapMarker.length;i++){var s=e.fenceAssembly.TMapMarker[i],a=s.getLngLat();if(Math.abs(a.lng-t.lnglat.lng)<1e-4&&Math.abs(a.lat-t.lnglat.lat)<1e-4){n=!0;break}}n||e.showRightClickMenu(t),t.domEvent&&t.domEvent.preventDefault()}},this.TMap.addEventListener("contextmenu",this.mapClickListeners.contextmenu),this.mapClickListeners.click=function(){e.hideRightClickMenu()},this.TMap.addEventListener("click",this.mapClickListeners.click))},resetMapEventsToDefaultClick:function(){var e=this;this.TMap&&this.mapInitialized&&(this.removeExistingMapListeners(),console.log("resetMapEventsToDefaultClick: Map interaction reset to default (no custom click/contextmenu)."),this.mapClickListeners.contextmenu=function(t){Date.now()-e.lastDragEndTime<200||(e.addMarkerPos=t.lnglat,e.showRightClickMenu(t),t.domEvent&&t.domEvent.preventDefault())},this.TMap.addEventListener("contextmenu",this.mapClickListeners.contextmenu),this.mapClickListeners.click=function(){e.hideRightClickMenu()},this.TMap.addEventListener("click",this.mapClickListeners.click))},showRightClickMenu:function(e){var t=this,n=Date.now();if(n-this.lastDragEndTime<500)console.log("忽略拖动后的右键事件");else{this.rightClickMenu.latlng=e.lnglat;var i,s,a=document.getElementById("container").getBoundingClientRect();if(e.domEvent&&void 0!==e.domEvent.clientX)i=e.domEvent.clientX,s=e.domEvent.clientY;else if(e.pixel){var r=document.getElementById("container"),o=r.getBoundingClientRect();i=o.left+e.pixel.x,s=o.top+e.pixel.y}else if(e.point){var l=document.getElementById("container"),c=l.getBoundingClientRect();i=c.left+e.point.x,s=c.top+e.point.y}else i=window.event?window.event.clientX:0,s=window.event?window.event.clientY:0,console.warn("无法从事件对象中获取精确坐标，使用备用方法");var d=i-a.left,u=s-a.top;this.rightClickMenu.x=d,this.rightClickMenu.y=u,this.rightClickMenu.visible=!0,this.$nextTick((function(){t.createRightClickMenuDOM()}))}},hideRightClickMenu:function(){this.rightClickMenu.visible=!1;var e=document.getElementById("map-right-click-menu");e&&e.parentNode.removeChild(e)},createRightClickMenuDOM:function(){var e=this,t=document.getElementById("map-right-click-menu");t&&t.parentNode.removeChild(t);var n=document.createElement("div");n.id="map-right-click-menu",n.className="map-right-click-menu",n.style.position="absolute",n.style.left="".concat(this.rightClickMenu.x,"px"),n.style.top="".concat(this.rightClickMenu.y,"px"),n.style.backgroundColor="#fff",n.style.boxShadow="0 2px 12px 0 rgba(0,0,0,.1)",n.style.borderRadius="4px",n.style.padding="5px 0",n.style.zIndex="1000";var i=function(t,n,i){var s=document.createElement("div");if(s.className="right-click-menu-item",s.style.padding="8px 16px",s.style.cursor="pointer",s.style.fontSize="14px",s.style.display="flex",s.style.alignItems="center",s.style.color="#606266",n){var a=document.createElement("i");a.className=n,a.style.marginRight="5px",a.style.fontSize="16px",s.appendChild(a)}var r=document.createElement("span");return r.textContent=t,s.appendChild(r),s.addEventListener("mouseover",(function(){s.style.backgroundColor="#f5f7fa"})),s.addEventListener("mouseout",(function(){s.style.backgroundColor="transparent"})),s.addEventListener("click",(function(t){t.stopPropagation(),e.hideRightClickMenu(),i()})),s},s=i("设置送水员定位点","el-icon-location",(function(){e.setOnePointAtRightClick()}));n.appendChild(s);var a=i("创建新围栏","el-icon-plus",(function(){e.createNewFenceAtRightClick()}));n.appendChild(a),document.getElementById("container").appendChild(n),document.addEventListener("click",this.hideRightClickMenu,{once:!0})},setOnePointAtRightClick:function(){var e=this;this.rightClickMenu.latlng&&this.$confirm("是否要将此位置设为送水员定位点？","设置定位点",{confirmButtonText:"确定",cancelButtonText:"取消",type:"info"}).then((function(){e.onePoint.lat=e.rightClickMenu.latlng.lat,e.onePoint.lng=e.rightClickMenu.latlng.lng,e.redrawMapWithCurrentSelection(),e.$message.success("送水员定位点已设置")})).catch((function(){}))},createNewFenceAtRightClick:function(){var e=this;this.rightClickMenu.latlng&&this.$confirm("是否要在此位置创建新围栏？这将退出当前编辑状态。","创建新围栏",{confirmButtonText:"确定",cancelButtonText:"取消",type:"info"}).then((function(){e.saveCurrentState(),e.isCreatingNew=!0,e.currentFence=null,e.currentFenceIndex=-1,e.resetFenceAssemblyAndOnePoint();var t=e.rightClickMenu.latlng.lat,n=e.rightClickMenu.latlng.lng,i=.001;e.fenceAssembly.latlngArr=["".concat(t,",").concat(n),"".concat(t+i,",").concat(n-i),"".concat(t-i,",").concat(n-i)],e.fenceAssembly.latlngStr=e.fenceAssembly.latlngArr.join(";"),e.redrawMapWithCurrentSelection(),e.setupMapClickForNewFence(),e.$message.success('已创建新围栏，请拖动顶点调整形状，完成后点击"保存"')})).catch((function(){}))},saveCurrentState:function(){this.savedState={currentFence:this.currentFence?JSON.parse(JSON.stringify(this.currentFence)):null,currentFenceIndex:this.currentFenceIndex,fenceAssembly:{latlngArr:p(this.fenceAssembly.latlngArr),latlngStr:this.fenceAssembly.latlngStr},onePoint:{lat:this.onePoint.lat,lng:this.onePoint.lng},isCreatingNew:this.isCreatingNew},console.log("当前状态已保存:",this.savedState)},restoreSavedState:function(){return!!this.savedState&&(this.isCreatingNew=this.savedState.isCreatingNew,this.currentFenceIndex=this.savedState.currentFenceIndex,this.currentFence=this.savedState.currentFence,this.resetFenceAssemblyAndOnePoint(),this.savedState.fenceAssembly&&(this.fenceAssembly.latlngArr=p(this.savedState.fenceAssembly.latlngArr),this.fenceAssembly.latlngStr=this.savedState.fenceAssembly.latlngStr),this.savedState.onePoint&&(this.onePoint.lat=this.savedState.onePoint.lat,this.onePoint.lng=this.savedState.onePoint.lng),this.redrawMapWithCurrentSelection(),console.log("已恢复保存的状态"),!0)},startNewFenceCreation:function(){console.log("createNewFence: User initiated new fence creation."),this.isCreatingNew=!0,this.currentFence=null,this.currentFenceIndex=-1,this.primaryColor=this.presetColors[0],this.resetFenceAssemblyAndOnePoint(),this.redrawMapWithCurrentSelection(),this.$message.info('请在地图上操作：左键添加顶点，右键顶点删除，右键空白处设置送水员定位。完成后点"保存"。')},removeExistingMapListeners:function(){this.TMap&&(this.mapClickListeners.click&&(this.TMap.removeEventListener("click",this.mapClickListeners.click),this.mapClickListeners.click=null),this.mapClickListeners.contextmenu&&(this.TMap.removeEventListener("contextmenu",this.mapClickListeners.contextmenu),this.mapClickListeners.contextmenu=null))},openEditInfoDialog:function(e,t){e&&(this.currentFence=e,this.currentFenceIndex=t,this.editInfoForm={id:e.id,deliveryUserId:e.deliveryUserId,name:e.fenceName,color:e.color||this.presetColors[0],paixu:e.paixu||0,status:void 0!==e.status?e.status:1},console.log(this.editInfoForm),this.editInfoDialogVisible=!0)},confirmEditFenceInfo:function(){var e=this;this.$refs.editInfoFormRef.validate((function(t){if(t){var n=e.editInfoForm.id,i={points:e.currentFence&&e.currentFence.points?e.currentFence.points:null,onePoint:e.currentFence&&e.currentFence.onePoint?e.currentFence.onePoint:null};console.log("编辑围栏信息前保存几何数据:",i);var s={id:e.editInfoForm.id,deliveryUserId:e.editInfoForm.deliveryUserId,fenceName:e.editInfoForm.name,color:e.editInfoForm.color,paixu:e.editInfoForm.paixu,status:e.editInfoForm.status,header:"json"};e.$post("/deliveryGeofence/update",s).then((function(t){1==t.code?(e.$message.success("围栏信息更新成功"),e.editInfoDialogVisible=!1,e.getDriverFenceList((function(){var t=e.fenceList.find((function(e){return e.id===n}));if(t)t.points&&"[]"!==t.points||!i.points||(console.log("恢复围栏points数据:",i.points),t.points=i.points),!t.onePoint&&i.onePoint&&(console.log("恢复围栏onePoint数据:",i.onePoint),t.onePoint=i.onePoint),e.selectFence(t,e.fenceList.indexOf(t)),console.log("围栏已编辑并选中:",t);else if(console.warn("编辑的围栏在列表中未找到，ID:",n),e.currentFence&&e.currentFence.id===n){e.currentFence.name=e.editInfoForm.name,e.currentFence.deliveryUserId=e.editInfoForm.deliveryUserId,e.currentFence.color=e.editInfoForm.color,e.currentFence.paixu=e.editInfoForm.paixu,e.currentFence.status=e.editInfoForm.status,!e.currentFence.points&&i.points&&(e.currentFence.points=i.points),!e.currentFence.onePoint&&i.onePoint&&(e.currentFence.onePoint=i.onePoint);var s=e.currentFenceIndex;e.currentFenceIndex=-1,e.selectFence(e.currentFence,s),console.log("使用本地数据恢复围栏:",e.currentFence)}else e.currentFence=null,e.currentFenceIndex=-1,e.resetFenceAssemblyAndOnePoint(),e.redrawMapWithCurrentSelection(),setTimeout((function(){e.getDriverFenceList()}),1e3)}))):e.$message.error(t.msg||"更新失败")})).catch((function(t){console.error("confirmEditFenceInfo: 编辑围栏信息失败:",t),e.$message.error("编辑失败: "+t.message)}))}}))},deleteFence:function(e,t){var n=this;this.$confirm('确定要删除围栏 "'.concat(e.name,'" 吗？'),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){n.$post("/deliveryGeofence/delete",{id:e.id}).then((function(t){1==t.code?(n.$message.success("删除成功"),n.currentFence&&n.currentFence.id===e.id&&(n.currentFence=null,n.currentFenceIndex=-1,n.isCreatingNew=!1,n.resetFenceAssemblyAndOnePoint()),n.getDriverFenceList()):n.$message.error(t.msg||"删除失败")})).catch((function(e){console.error("deleteFence: 删除围栏API请求失败:",e),n.$message.error("删除失败: "+e.message)}))})).catch((function(){}))},openSaveFenceDialog:function(){if(this.isCreatingNew){if(0===this.fenceAssembly.latlngArr.length&&(!this.onePoint.lat||!this.onePoint.lng))return void this.$message.error("请先在地图上绘制围栏或设置定位点");if(this.fenceAssembly.latlngArr.length>0&&this.fenceAssembly.latlngArr.length<3)return void this.$message.error("新建的多边形围栏至少需要3个点")}else{if(-1===this.currentFenceIndex||!this.currentFence)return void this.$message.warning('没有正在编辑的围栏，如需新建请点"新建围栏"');if(this.fenceAssembly.latlngArr.length>0&&this.fenceAssembly.latlngArr.length<3)return void this.$message.error("修改后的多边形围栏至少需要3个点，或清空所有点")}var e=this.fenceAssembly.latlngArr.map((function(e){var t=e.split(","),n=d(t,2),i=n[0],s=n[1];return{latitude:parseFloat(i),longitude:parseFloat(s)}}));this.mapRelatedDataToSave={points:e&&e.length>0?e:null,onePoint:this.onePoint.lat&&this.onePoint.lng?{latitude:this.onePoint.lat,longitude:this.onePoint.lng}:null},console.log("准备保存的几何数据:",JSON.stringify(this.mapRelatedDataToSave)),this.isCreatingNew?this.saveFenceForm={deliveryUserId:"",name:"",color:this.primaryColor,paixu:0,status:1}:this.currentFence&&(this.saveFenceForm={deliveryUserId:this.currentFence.deliveryUserId,name:this.currentFence.fenceName,color:this.currentFence.color||this.primaryColor,paixu:this.currentFence.paixu||0,status:void 0!==this.currentFence.status?this.currentFence.status:1}),this.saveFenceDialogVisible=!0},confirmSaveFenceDialog:function(){var e=this;this.$refs.saveFenceFormRef.validate((function(t){if(t&&e.mapRelatedDataToSave){var n=e.mapRelatedDataToSave,i=n.points,s=n.onePoint,a={id:e.isCreatingNew?"":e.currentFence?e.currentFence.id:"",deliveryUserId:e.saveFenceForm.deliveryUserId,storeId:e.storeId,points:i&&i.length>0?JSON.stringify(i):null,onePoint:s?JSON.stringify(s):null,fenceName:e.saveFenceForm.name,color:e.saveFenceForm.color,paixu:e.saveFenceForm.paixu,status:e.saveFenceForm.status,header:"json"};if(!a.points&&!a.onePoint&&!e.isCreatingNew&&e.currentFence)return void e.$confirm("围栏的几何形状和定位点都为空，保存后将清除该围栏的地图信息。是否继续？","警告",{confirmButtonText:"继续保存",cancelButtonText:"取消",type:"warning"}).then((function(){e.submitFenceData(a)})).catch((function(){}));e.submitFenceData(a)}}))},submitFenceData:function(e){var t=this,n=this.isCreatingNew||!e.id?"/deliveryGeofence/save":"/deliveryGeofence/update",i=e.id;this.$post(n,e).then((function(e){if(1==e.code){t.$message.success("围栏保存成功"),t.saveFenceDialogVisible=!1;var n=null;e.data&&("object"===c(e.data)&&e.data.id?n=e.data.id:"string"!==typeof e.data&&"number"!==typeof e.data||(n=e.data)),!n&&i&&(n=i),t.isCreatingNew=!1;var s={points:t.mapRelatedDataToSave?JSON.stringify(t.mapRelatedDataToSave.points||[]):null,onePoint:t.mapRelatedDataToSave&&t.mapRelatedDataToSave.onePoint?JSON.stringify(t.mapRelatedDataToSave.onePoint):null};t.getDriverFenceList((function(){if(n){var e=t.fenceList.find((function(e){return e.id===n}));e?(e.points&&"[]"!==e.points||!s.points||(e.points=s.points),!e.onePoint&&s.onePoint&&(e.onePoint=s.onePoint),t.selectFence(e,t.fenceList.indexOf(e)),console.log("围栏已保存并选中:",e)):(console.warn("保存的围栏在列表中未找到，ID:",n),t.currentFence=null,t.currentFenceIndex=-1,t.resetFenceAssemblyAndOnePoint(),t.redrawMapWithCurrentSelection(),setTimeout((function(){t.getDriverFenceList()}),1e3))}else t.currentFence=null,t.currentFenceIndex=-1,t.resetFenceAssemblyAndOnePoint(),t.redrawMapWithCurrentSelection()}))}else t.$message.error(e.msg||"保存失败")})).catch((function(e){console.error("submitFenceData: 保存围栏API请求失败:",e),t.$message.error("保存失败: "+e.message)}))},confirmBatchUpdate:function(){var e=this;this.$refs.batchUpdateFormRef.validate((function(t){if(t){if(!e.selectedFences.length)return void e.$message.warning("请先选择要更新的围栏");var n={deliveryId:e.batchUpdateForm.deliveryUserId||void 0,color:e.batchUpdateForm.color,deliveryGeofenceIds:e.selectedFences.map((function(e){return e.id})).join(",")};Object.keys(n).forEach((function(e){return(void 0===n[e]||null===n[e])&&delete n[e]})),e.$get("/deliveryGeofence/updateBatchDeliveryId",n).then((function(t){1==t.code?(e.$message.success("批量更新成功"),e.batchUpdateDialogVisible=!1,e.selectedFences.forEach((function(e){e.selected=!1})),e.selectedFences=[],e.getDriverFenceList()):e.$message.error(t.msg||"批量更新失败")})).catch((function(t){console.error("confirmBatchUpdate: 批量更新API请求失败:",t),e.$message.error("批量更新失败: "+t.message)}))}}))},getDriverList:function(){var e=this;this.$post("/szmb/szmsendmembercontroller/selectdelectstate",{storeId:this.storeId}).then((function(t){e.driverList=1===t.code&&t.data?t.data:[]})).catch((function(e){console.error("getDriverList: 获取送水员列表失败:",e)}))},getDriverFenceList:function(e){var t=this;this.$get("/deliveryGeofence/findDeliveryGeofence",{storeId:this.storeId,deliveryId:""}).then((function(n){if(1===n.code&&n.data){var i=(Array.isArray(n.data)?n.data:[n.data]).map((function(e){return{id:e.id,deliveryUserId:e.deliveryUserId,name:e.fenceName?e.fenceName+"-"+e.deliveryUserName:e.deliveryUserName||"围栏".concat(e.id),fenceName:e.fenceName,deliveryUserName:e.deliveryUserName,color:e.color,paixu:e.paixu||0,points:e.points,onePoint:e.onePoint,selected:!1,status:void 0!==e.status?e.status:1}}));t.allFenceData=i,t.selectDeliveryUserId?(t.fenceList=i.filter((function(e){return e.deliveryUserId===t.selectDeliveryUserId})),-1!==t.fenceFilterStatus&&(t.fenceList=t.fenceList.filter((function(e){return e.status===t.fenceFilterStatus}))),console.log("已筛选送水员ID为 ".concat(t.selectDeliveryUserId," 的围栏，共 ").concat(t.fenceList.length," 个"))):-1!==t.fenceFilterStatus?t.fenceList=i.filter((function(e){return e.status===t.fenceFilterStatus})):t.fenceList=p(i),console.log("加载了 ".concat(t.allFenceData.length," 个围栏数据，筛选后显示 ").concat(t.fenceList.length," 个")),t.fenceList.forEach((function(e,t){console.log("围栏[".concat(t,"]: ID=").concat(e.id,", 名称=").concat(e.name,", 送水员ID=").concat(e.deliveryUserId,", 状态=").concat(e.status))}))}else console.warn("获取围栏列表返回空数据或错误码:",n),t.fenceList=[],t.allFenceData=[];t.$nextTick((function(){t.mapInitialized&&(t.currentFence=null,t.currentFenceIndex=-1,t.resetFenceAssemblyAndOnePoint(),t.redrawMapWithCurrentSelection()),"function"===typeof e&&e()}))})).catch((function(n){console.error("getDriverFenceList: 获取送水员围栏列表失败:",n),t.$message.error("获取送水员围栏列表失败"),t.fenceList=[],t.allFenceData=[],t.$nextTick((function(){t.mapInitialized&&t.redrawMapWithCurrentSelection(),"function"===typeof e&&e()}))}))},setMapContainerHeight:function(){var e=document.querySelector(".map-container"),t=document.getElementById("container");if(e&&t){var n=window.innerHeight,i=(document.querySelector(".fence-header")&&document.querySelector(".fence-header").offsetHeight,document.querySelector(".fence-actions")&&document.querySelector(".fence-actions").offsetHeight,40),s=60+i,a=n-s;a<400&&(a=400),e.style.height="".concat(a,"px"),t.style.height="".concat(a,"px"),console.log("setMapContainerHeight: Map container height set to",a)}},addVirtualPoints:function(e){var t=this;if(this.TMap&&this.mapInitialized&&e&&!(e.length<2))for(var n=this.createVirtualPointIcon(),i=function(){var i=e[s],a=e[(s+1)%e.length],r=new t.T.LngLat((i.lng+a.lng)/2,(i.lat+a.lat)/2),o=new t.T.Marker(r,{icon:n,draggable:!0});o.originalEdgeStartIndex=s,o.addEventListener("dragstart",(function(e){t.lastDragEndTime=Date.now(),t.snapConfig.enabled&&t.collectSnapPoints(-1)})),o.addEventListener("dragging",(function(e){if(t.snapConfig.enabled&&t.snapPoints&&t.snapPoints.length>0){var n=e.lnglat,i=t.findNearestSnapPoint(n);i?t.showSnapVisualHint(n,i.point):t.clearSnapHelpers()}})),o.addEventListener("dragend",(function(e){t.lastDragEndTime=Date.now();var n=e.lnglat.lat,i=e.lnglat.lng;if(t.snapConfig.enabled&&t.snapPoints&&t.snapPoints.length>0){var s=t.findNearestSnapPoint(e.lnglat);s&&(n=s.point.lat,i=s.point.lng,console.log("中点吸附到附近点: [".concat(n,", ").concat(i,"], 距离: ").concat(s.distance.toFixed(6))))}t.clearSnapHelpers();var a="".concat(n,",").concat(i),r=o.originalEdgeStartIndex+1;t.fenceAssembly.latlngArr.splice(r,0,a),t.fenceAssembly.latlngStr=t.fenceAssembly.latlngArr.join(";"),t.operateTMapNoDeal()})),t.TMap.addOverLay(o),t.fenceAssembly.virtualMarkers.push(o)},s=0;s<e.length;s++)i()},createVirtualPointIcon:function(){var e=document.createElement("canvas"),t=e.getContext("2d");return e.width=16,e.height=16,t.beginPath(),t.arc(8,8,6,0,2*Math.PI),t.fillStyle="#ffffff",t.fill(),t.strokeStyle=this.primaryColor,t.lineWidth=2,t.stroke(),new this.T.Icon({iconUrl:e.toDataURL("image/png"),iconSize:new this.T.Point(16,16),iconAnchor:new this.T.Point(8,8)})},toggleFenceStatus:function(e,t){var n=this,i=1===e.status?0:1,s=1===i?"启用":"禁用";this.$confirm("确定要".concat(s,"该围栏吗？"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){n.$post("/deliveryGeofence/update",{id:e.id,status:i,header:"json"}).then((function(a){if(1==a.code){n.$message.success("".concat(s,"成功")),e.status=i;var r=n.allFenceData.findIndex((function(t){return t.id===e.id}));-1!==r&&(n.allFenceData[r].status=i),0===i&&t===n.currentFenceIndex&&(n.currentFence=null,n.currentFenceIndex=-1,n.resetFenceAssemblyAndOnePoint()),n.redrawMapWithCurrentSelection()}else n.$message.error(a.msg||"".concat(s,"失败"))})).catch((function(e){console.error("".concat(s,"围栏失败:"),e),n.$message.error("".concat(s,"失败: ")+e.message)}))})).catch((function(){n.$message.info("已取消".concat(s))}))}}},w=F,M=(n("0898"),n("0c7c")),T=Object(M["a"])(w,i,s,!1,null,null,null);t["default"]=T.exports}}]);