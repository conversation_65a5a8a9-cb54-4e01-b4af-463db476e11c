(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["setAdmin"],{"02ef":function(t,e,a){"use strict";a("6f2c")},"26ee":function(t,e,a){"use strict";a("3c19")},"3c19":function(t,e,a){},"4588e":function(t,e,a){},"5af7":function(t,e,a){"use strict";a("4588e")},"6f2c":function(t,e,a){},8737:function(t,e,a){"use strict";a.r(e);var o=function(){var t=this,e=t._self._c;return e("div",{staticClass:"mod-config"},[e("el-form",{attrs:{inline:!0,model:t.dataForm},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.onSearch()}}},[e("el-form-item",[e("el-input",{attrs:{placeholder:"来源名称",clearable:""},model:{value:t.dataForm.name,callback:function(e){t.$set(t.dataForm,"name",e)},expression:"dataForm.name"}})],1),e("el-form-item",[e("el-select",{attrs:{placeholder:"状态",clearable:""},model:{value:t.dataForm.status,callback:function(e){t.$set(t.dataForm,"status",e)},expression:"dataForm.status"}},[e("el-option",{attrs:{label:"全部",value:""}}),e("el-option",{attrs:{label:"启用",value:1}}),e("el-option",{attrs:{label:"禁用",value:0}})],1)],1),e("el-form-item",[e("el-button",{on:{click:function(e){return t.onSearch()}}},[t._v("查询")]),e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.orderSourceAddOrUpdate()}}},[t._v("新增")]),e("el-button",{attrs:{type:"success"},on:{click:function(e){return t.initDefaultData()}}},[t._v("初始化默认数据")])],1)],1),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.dataListLoading,expression:"dataListLoading"}],staticStyle:{width:"100%"},attrs:{data:t.dataList,border:""},on:{"selection-change":t.selectionChangeHandle}},[e("el-table-column",{attrs:{prop:"image","header-align":"center",align:"center",label:"图标",width:"80"},scopedSlots:t._u([{key:"default",fn:function(a){return e("div",{},[t.isImageUrl(a.row.image)?e("img",{staticClass:"image-sm",attrs:{src:a.row.image}}):e("span",[t._v(t._s(a.row.image))])])}}])}),e("el-table-column",{attrs:{prop:"name","header-align":"center",align:"center",label:"来源名称"}}),e("el-table-column",{attrs:{prop:"status","header-align":"center",align:"center",label:"状态",width:"100"},scopedSlots:t._u([{key:"default",fn:function(a){return e("div",{},[e("el-tag",{attrs:{type:1===a.row.status?"success":"danger"}},[t._v("\n                    "+t._s(1===a.row.status?"启用":"禁用")+"\n                ")])],1)}}])}),e("el-table-column",{attrs:{prop:"sortOrder","header-align":"center",align:"center",label:"排序",width:"80"}}),e("el-table-column",{attrs:{prop:"settlementCycle","header-align":"center",align:"center",label:"结算周期",width:"120"}}),e("el-table-column",{attrs:{prop:"remark","header-align":"center",align:"center",label:"备注"}}),e("el-table-column",{attrs:{prop:"createTime","header-align":"center",align:"center",label:"创建时间",width:"160"},scopedSlots:t._u([{key:"default",fn:function(a){return e("div",{},[t._v("\n                "+t._s(t.formatDate(a.row.createTime))+"\n            ")])}}])}),e("el-table-column",{attrs:{prop:"操作","header-align":"center",align:"center",width:"200",label:"操作"},scopedSlots:t._u([{key:"default",fn:function(a){return e("div",{},[e("el-button",{staticStyle:{color:"blue"},attrs:{type:"text",size:"small"},on:{click:function(e){return t.orderSourceAddOrUpdate(a.row.id)}}},[t._v("修改")]),e("el-button",{style:1===a.row.status?"color: orange;":"color: green;",attrs:{type:"text",size:"small"},on:{click:function(e){return t.updateStatus(a.row.id,1===a.row.status?0:1)}}},[t._v("\n                    "+t._s(1===a.row.status?"禁用":"启用")+"\n                ")]),e("el-button",{staticStyle:{color:"red"},attrs:{type:"text",size:"small"},on:{click:function(e){return t.deleteHandle(a.row.id)}}},[t._v("删除")])],1)}}])})],1),t.orderSourceAddOrUpdateVisible?e("orderSourceAddOrUpdate",{ref:"orderSourceAddOrUpdate",on:{refreshDataList:t.getDataList}}):t._e()],1)},r=[],i=function(){var t=this,e=t._self._c;return e("div",[e("el-dialog",{attrs:{title:t.dataForm.id?"修改订单来源":"新增订单来源","close-on-click-modal":!1,visible:t.visible},on:{"update:visible":function(e){t.visible=e}}},[e("el-form",{ref:"dataForm",attrs:{model:t.dataForm,rules:t.dataRule,"label-width":"120px"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.dataFormSubmit()}}},[e("el-form-item",{attrs:{label:"来源名称",prop:"name"}},[e("el-input",{attrs:{placeholder:"请输入来源名称"},model:{value:t.dataForm.name,callback:function(e){t.$set(t.dataForm,"name",e)},expression:"dataForm.name"}})],1),e("el-form-item",{attrs:{label:"来源图标",prop:"image"}},[e("el-upload",{attrs:{action:"https://szmsh.waterstation.com.cn/szm/uploadcontroller/upload",accept:".png,.jpg,.jpeg,.gif","list-type":"picture-card","show-file-list":!1,"on-success":t.uploadImage,"before-upload":t.beforeUpload}},[t.dataForm.image?e("img",{staticClass:"avatar",attrs:{src:t.dataForm.image}}):e("i",{staticClass:"el-icon-plus"})]),e("div",{staticClass:"upload-tip"},[t._v("建议上传尺寸为64x64像素的图片")])],1),e("el-form-item",{attrs:{label:"状态",prop:"status"}},[e("el-radio-group",{model:{value:t.dataForm.status,callback:function(e){t.$set(t.dataForm,"status",e)},expression:"dataForm.status"}},[e("el-radio",{attrs:{label:1}},[t._v("启用")]),e("el-radio",{attrs:{label:0}},[t._v("禁用")])],1)],1),e("el-form-item",{attrs:{label:"排序",prop:"sortOrder"}},[e("el-input-number",{attrs:{min:0,max:999,placeholder:"排序值，越小越靠前"},model:{value:t.dataForm.sortOrder,callback:function(e){t.$set(t.dataForm,"sortOrder",e)},expression:"dataForm.sortOrder"}}),e("div",{staticClass:"form-tip"},[t._v("数值越小排序越靠前")])],1),e("el-form-item",{attrs:{label:"结算周期",prop:"settlementCycle"}},[e("el-input",{attrs:{placeholder:"请输入结算周期（如：月结、周结等）"},model:{value:t.dataForm.settlementCycle,callback:function(e){t.$set(t.dataForm,"settlementCycle",e)},expression:"dataForm.settlementCycle"}})],1),e("el-form-item",{attrs:{label:"备注",prop:"remark"}},[e("el-input",{attrs:{type:"textarea",rows:3,placeholder:"请输入备注信息（可选）"},model:{value:t.dataForm.remark,callback:function(e){t.$set(t.dataForm,"remark",e)},expression:"dataForm.remark"}})],1)],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.visible=!1}}},[t._v("取消")]),e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.dataFormSubmit()}}},[t._v("确定")])],1)],1)],1)},n=[],c={data:function(){return{visible:!1,dataForm:{id:0,name:"",image:"",status:1,sortOrder:0,settlementCycle:"",remark:""},dataRule:{name:[{required:!0,message:"来源名称不能为空",trigger:"blur"},{min:1,max:50,message:"来源名称长度在1到50个字符",trigger:"blur"}],image:[{required:!0,message:"来源图标不能为空",trigger:"blur"}],status:[{required:!0,message:"请选择状态",trigger:"change"}]}}},methods:{init:function(t){var e=this;this.dataForm.id=t||0,this.visible=!0,this.$nextTick((function(){e.$refs["dataForm"].resetFields(),e.dataForm.id?e.$get("/szmb/order-source/detail/"+t,{}).then((function(t){1===t.code?(e.dataForm.name=t.data.name,e.dataForm.image=t.data.image,e.dataForm.status=t.data.status,e.dataForm.sortOrder=t.data.sortOrder||0,e.dataForm.settlementCycle=t.data.settlementCycle||"",e.dataForm.remark=t.data.remark||""):e.$message.error(t.data||"获取数据失败")})):(e.dataForm.name="",e.dataForm.image="",e.dataForm.status=1,e.dataForm.sortOrder=0,e.dataForm.settlementCycle="",e.dataForm.remark="")}))},uploadImage:function(t,e){1===t.code?(this.dataForm.image=t.data,this.$message.success("图片上传成功")):this.$message.error(t.data||"图片上传失败")},beforeUpload:function(t){var e=/^image\//.test(t.type),a=t.size/1024/1024<2;return e?!!a||(this.$message.error("上传图片大小不能超过 2MB!"),!1):(this.$message.error("只能上传图片文件!"),!1)},dataFormSubmit:function(){var t=this;this.$refs["dataForm"].validate((function(e){if(e){var a=t.dataForm.id?"/szmb/order-source/update":"/szmb/order-source/add";t.dataForm.header="json",t.$post(a,t.dataForm).then((function(e){1===e.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){t.visible=!1,t.$emit("refreshDataList")}}):t.$message.error(e.data||"操作失败")}))}}))}}},d=c,s=(a("02ef"),a("0c7c")),l=Object(s["a"])(d,i,n,!1,null,"f210e568",null),u=l.exports,p={data:function(){return{dataForm:{name:"",status:""},dataList:[],dataListLoading:!1,dataListSelections:[],orderSourceAddOrUpdateVisible:!1}},components:{orderSourceAddOrUpdate:u},mounted:function(){this.getDataList()},methods:{onSearch:function(){this.getDataList()},getDataList:function(){var t=this;this.dataListLoading=!0,this.$get("/szmb/order-source/all",{}).then((function(e){if(t.dataListLoading=!1,1===e.code){var a=e.data||[];t.dataForm.name&&(a=a.filter((function(e){return e.name&&e.name.toLowerCase().includes(t.dataForm.name.toLowerCase())}))),""!==t.dataForm.status&&(a=a.filter((function(e){return e.status===t.dataForm.status}))),a.sort((function(t,e){return t.sortOrder!==e.sortOrder?(t.sortOrder||999)-(e.sortOrder||999):new Date(e.createTime||0)-new Date(t.createTime||0)})),t.dataList=a}else t.dataList=[],t.$message.error(e.data||"获取数据失败")})).catch((function(){t.dataListLoading=!1,t.dataList=[],t.$message.error("网络错误，请稍后重试")}))},selectionChangeHandle:function(t){this.dataListSelections=t},orderSourceAddOrUpdate:function(t){var e=this;this.orderSourceAddOrUpdateVisible=!0,this.$nextTick((function(){e.$refs.orderSourceAddOrUpdate.init(t)}))},deleteHandle:function(t){var e=this;this.$confirm("确定要删除该订单来源吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.$post("/szmb/order-source/delete/"+t,{}).then((function(t){1===t.code?e.$message({message:"删除成功",type:"success",duration:1500,onClose:function(){e.getDataList()}}):e.$message.error(t.data||"删除失败")}))})).catch((function(){}))},updateStatus:function(t,e){var a=this,o=1===e?"启用":"禁用";this.$confirm("确定要".concat(o,"该订单来源吗？"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){a.$post("/szmb/order-source/status",{id:t,status:e}).then((function(t){1===t.code?a.$message({message:o+"成功",type:"success",duration:1500,onClose:function(){a.getDataList()}}):a.$message.error(t.data||o+"失败")}))})).catch((function(){}))},initDefaultData:function(){var t=this;this.$confirm("确定要初始化默认订单来源数据吗？这将添加系统预设的订单来源。","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.$post("/szmb/order-source/init",{}).then((function(e){1===e.code?t.$message({message:"初始化成功",type:"success",duration:1500,onClose:function(){t.getDataList()}}):t.$message.error(e.data||"初始化失败")}))})).catch((function(){}))},isImageUrl:function(t){return!!t&&(/\.(jpg|jpeg|png|gif|bmp|webp)$/i.test(t)||t.startsWith("http"))},formatDate:function(t){if(!t)return"";var e=new Date(t);return isNaN(e.getTime())?t:e.getFullYear()+"-"+String(e.getMonth()+1).padStart(2,"0")+"-"+String(e.getDate()).padStart(2,"0")+" "+String(e.getHours()).padStart(2,"0")+":"+String(e.getMinutes()).padStart(2,"0")}}},m=p,g=(a("5af7"),Object(s["a"])(m,o,r,!1,null,"125b8ce6",null));e["default"]=g.exports},a4b7:function(t,e,a){"use strict";a.r(e);var o=function(){var t=this,e=t._self._c;return e("div",{staticClass:"mod-config"},[e("el-form",{attrs:{inline:!0,model:t.dataForm},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.onSearch()}}},[e("el-form-item",[e("el-input",{attrs:{placeholder:"唯一标识(skuId)",clearable:""},model:{value:t.dataForm.unionCode,callback:function(e){t.$set(t.dataForm,"unionCode",e)},expression:"dataForm.unionCode"}})],1),e("el-form-item",[e("el-input",{attrs:{placeholder:"名称（支持多关键词搜索，用空格分隔，任一匹配即可）",clearable:""},model:{value:t.dataForm.name,callback:function(e){t.$set(t.dataForm,"name",e)},expression:"dataForm.name"}})],1),e("el-form-item",[e("el-select",{attrs:{placeholder:"订单来源",clearable:""},model:{value:t.dataForm.orderSourceId,callback:function(e){t.$set(t.dataForm,"orderSourceId",e)},expression:"dataForm.orderSourceId"}},[e("el-option",{attrs:{label:"全部",value:""}}),t._l(t.orderSourceList,(function(t){return e("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})}))],2)],1),e("el-form-item",[e("el-select",{attrs:{placeholder:"配置状态",clearable:""},model:{value:t.dataForm.configStatus,callback:function(e){t.$set(t.dataForm,"configStatus",e)},expression:"dataForm.configStatus"}},[e("el-option",{attrs:{label:"全部",value:""}}),e("el-option",{attrs:{label:"已配置",value:"configured"}}),e("el-option",{attrs:{label:"未配置",value:"unconfigured"}})],1)],1),e("el-form-item",[e("el-button",{on:{click:function(e){return t.onSearch()}}},[t._v("查询")]),e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.orderSourceConnectAddOrUpdate()}}},[t._v("新增")]),e("el-button",{attrs:{type:"warning"},on:{click:function(e){return t.getUnConfiguredList()}}},[t._v("查看未配置")]),e("el-button",{attrs:{type:"info",disabled:0===t.dataListSelections.length},on:{click:function(e){return t.batchConfigureProducts()}}},[t._v("批量配置")]),e("el-button",{attrs:{type:"success",icon:"el-icon-download"},on:{click:function(e){return t.exportExcel()}}},[t._v("导出")])],1)],1),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.dataListLoading,expression:"dataListLoading"}],staticStyle:{width:"100%"},attrs:{data:t.dataList,border:""},on:{"selection-change":t.selectionChangeHandle}},[e("el-table-column",{attrs:{type:"selection","header-align":"center",align:"center",width:"50"}}),e("el-table-column",{attrs:{prop:"unionCode","header-align":"center",align:"center",label:"唯一标识(skuId)",width:"120"}}),e("el-table-column",{attrs:{prop:"name","header-align":"center",align:"center",label:"名称"}}),e("el-table-column",{attrs:{prop:"orderSourceName","header-align":"center",align:"center",label:"订单来源",width:"120"}}),e("el-table-column",{attrs:{prop:"productNewId","header-align":"center",align:"center",label:"产品ID",width:"100"},scopedSlots:t._u([{key:"default",fn:function(a){return e("div",{},[a.row.productNewId?e("span",[t._v(t._s(a.row.productNewId))]):e("el-tag",{attrs:{type:"danger",size:"mini"}},[t._v("未配置")])],1)}}])}),e("el-table-column",{attrs:{prop:"productName","header-align":"center",align:"center",label:"产品名称"},scopedSlots:t._u([{key:"default",fn:function(a){return e("div",{},[a.row.productName?e("span",[t._v(t._s(a.row.productName))]):e("span",{staticStyle:{color:"#999"}},[t._v("-")])])}}])}),e("el-table-column",{attrs:{prop:"createTime","header-align":"center",align:"center",label:"创建时间",width:"160"},scopedSlots:t._u([{key:"default",fn:function(a){return e("div",{},[t._v("\n                "+t._s(t.formatDate(a.row.createTime))+"\n            ")])}}])}),e("el-table-column",{attrs:{prop:"操作","header-align":"center",align:"center",width:"200",label:"操作"},scopedSlots:t._u([{key:"default",fn:function(a){return e("div",{},[e("el-button",{staticStyle:{color:"blue"},attrs:{type:"text",size:"small"},on:{click:function(e){return t.orderSourceConnectAddOrUpdate(a.row.id)}}},[t._v("修改")]),a.row.productNewId?t._e():e("el-button",{staticStyle:{color:"green"},attrs:{type:"text",size:"small"},on:{click:function(e){return t.configureProduct(a.row)}}},[t._v("配置产品")]),e("el-button",{staticStyle:{color:"red"},attrs:{type:"text",size:"small"},on:{click:function(e){return t.deleteHandle(a.row.id)}}},[t._v("删除")])],1)}}])})],1),e("el-pagination",{attrs:{"current-page":t.pageIndex,"page-sizes":[10,20,50,100],"page-size":t.pageSize,total:t.totalPage,layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":t.sizeChangeHandle,"current-change":t.currentChangeHandle}}),t.orderSourceConnectAddOrUpdateVisible?e("orderSourceConnectAddOrUpdate",{ref:"orderSourceConnectAddOrUpdate",on:{refreshDataList:t.getDataList}}):t._e(),e("el-dialog",{attrs:{title:"批量配置产品",visible:t.batchConfigVisible,width:"800px","close-on-click-modal":!1},on:{"update:visible":function(e){t.batchConfigVisible=e}}},[e("div",{staticStyle:{"margin-bottom":"20px"}},[e("span",{staticStyle:{color:"#666"}},[t._v("已选择 "+t._s(t.dataListSelections.length)+" 个项目进行批量配置")])]),e("el-form",{attrs:{model:t.batchConfigForm,"label-width":"100px"}},[e("el-form-item",{attrs:{label:"选择产品",required:""}},[e("div",{staticStyle:{display:"flex",gap:"10px","align-items":"flex-start"}},[e("el-select",{staticStyle:{flex:"1"},attrs:{placeholder:"请选择要配置的产品",filterable:"",remote:"","remote-method":t.searchBatchProducts,loading:t.batchProductLoading,clearable:""},model:{value:t.batchConfigForm.productNewId,callback:function(e){t.$set(t.batchConfigForm,"productNewId",e)},expression:"batchConfigForm.productNewId"}},t._l(t.batchProductList,(function(a){return e("el-option",{key:a.productId,attrs:{label:"".concat(a.productTitle," ").concat(a.brandName?"("+a.brandName+")":""," - ¥").concat(a.productPrice),value:a.productId}},[e("div",{staticStyle:{display:"flex","justify-content":"space-between","align-items":"center"}},[e("span",[t._v(t._s(a.productTitle))]),e("span",{staticStyle:{color:"#999","font-size":"12px"}},[t._v("¥"+t._s(a.productPrice))])])])})),1),e("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.openBatchProductDialog}},[t._v("浏览选择")])],1)])],1),e("div",{staticStyle:{"margin-top":"20px"}},[e("h4",[t._v("将要配置的项目：")]),e("el-table",{staticStyle:{"margin-top":"10px"},attrs:{data:t.dataListSelections,border:"","max-height":"300"}},[e("el-table-column",{attrs:{prop:"unionCode",label:"唯一标识",width:"120"}}),e("el-table-column",{attrs:{prop:"name",label:"名称"}}),e("el-table-column",{attrs:{prop:"orderSourceName",label:"订单来源",width:"120"}}),e("el-table-column",{attrs:{label:"当前状态",width:"100"},scopedSlots:t._u([{key:"default",fn:function(a){return e("div",{},[a.row.productNewId?e("el-tag",{attrs:{type:"success",size:"mini"}},[t._v("已配置")]):e("el-tag",{attrs:{type:"danger",size:"mini"}},[t._v("未配置")])],1)}}])})],1)],1),e("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.batchConfigVisible=!1}}},[t._v("取消")]),e("el-button",{attrs:{type:"primary",disabled:!t.batchConfigForm.productNewId,loading:t.batchConfigLoading},on:{click:t.confirmBatchConfig}},[t._v("确定配置")])],1)],1),e("el-dialog",{attrs:{title:"选择产品",visible:t.batchProductDialogVisible,width:"1000px","close-on-click-modal":!1},on:{"update:visible":function(e){t.batchProductDialogVisible=e}}},[e("el-form",{staticStyle:{"margin-bottom":"20px"},attrs:{inline:!0}},[e("el-form-item",{attrs:{label:"关键词"}},[e("el-input",{staticStyle:{width:"200px"},attrs:{placeholder:"请输入产品名称",clearable:""},model:{value:t.batchProductSearch.keyword,callback:function(e){t.$set(t.batchProductSearch,"keyword",e)},expression:"batchProductSearch.keyword"}})],1),e("el-form-item",[e("el-button",{attrs:{type:"primary"},on:{click:t.loadBatchDialogProducts}},[t._v("搜索")])],1)],1),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.batchDialogProductLoading,expression:"batchDialogProductLoading"}],attrs:{data:t.batchDialogProductList,border:"","highlight-current-row":""},on:{"current-change":t.handleBatchProductSelect}},[e("el-table-column",{attrs:{label:"产品图片",width:"80"},scopedSlots:t._u([{key:"default",fn:function(a){return e("div",{},[a.row.r1?e("img",{staticStyle:{width:"50px",height:"50px","object-fit":"cover"},attrs:{src:a.row.r1}}):e("div",{staticStyle:{width:"50px",height:"50px",background:"#f5f5f5",display:"flex","align-items":"center","justify-content":"center","font-size":"12px",color:"#999"}},[t._v("无图")])])}}])}),e("el-table-column",{attrs:{prop:"productTitle",label:"产品名称"}}),e("el-table-column",{attrs:{prop:"price",label:"零售价",width:"100"},scopedSlots:t._u([{key:"default",fn:function(a){return e("div",{},[t._v("¥"+t._s(a.row.price||0))])}}])})],1),e("el-pagination",{staticStyle:{"margin-top":"20px","text-align":"center"},attrs:{"current-page":t.batchProductSearch.page,"page-size":t.batchProductSearch.pageSize,total:t.batchProductSearch.total,layout:"total, prev, pager, next"},on:{"current-change":t.handleBatchProductPageChange}}),e("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.batchProductDialogVisible=!1}}},[t._v("取消")]),e("el-button",{attrs:{type:"primary",disabled:!t.batchSelectedProduct},on:{click:t.confirmBatchProductSelect}},[t._v("确定选择")])],1)],1)],1)},r=[],i=function(){var t=this,e=t._self._c;return e("div",[e("el-dialog",{attrs:{title:t.dataForm.id?"修改订单来源关联":"新增订单来源关联","close-on-click-modal":!1,visible:t.visible,width:"600px"},on:{"update:visible":function(e){t.visible=e}}},[e("el-form",{ref:"dataForm",attrs:{model:t.dataForm,rules:t.dataRule,"label-width":"120px"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.dataFormSubmit()}}},[e("el-form-item",{attrs:{label:"唯一标识",prop:"unionCode"}},[e("el-input",{attrs:{placeholder:"请输入唯一标识(通常为skuId)"},model:{value:t.dataForm.unionCode,callback:function(e){t.$set(t.dataForm,"unionCode",e)},expression:"dataForm.unionCode"}}),e("div",{staticClass:"form-tip"},[t._v("通常为商品的skuId，用于关联订单商品")])],1),e("el-form-item",{attrs:{label:"名称",prop:"name"}},[e("el-input",{attrs:{placeholder:"请输入关联关系名称"},model:{value:t.dataForm.name,callback:function(e){t.$set(t.dataForm,"name",e)},expression:"dataForm.name"}}),e("div",{staticClass:"form-tip"},[t._v("便于识别的名称，如：商品名称")])],1),e("el-form-item",{attrs:{label:"订单来源",prop:"orderSourceId"}},[e("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择订单来源"},model:{value:t.dataForm.orderSourceId,callback:function(e){t.$set(t.dataForm,"orderSourceId",e)},expression:"dataForm.orderSourceId"}},t._l(t.orderSourceList,(function(t){return e("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1),e("el-form-item",{attrs:{label:"关联产品",prop:"productNewId"}},[e("div",{staticStyle:{display:"flex",gap:"10px","align-items":"flex-start"}},[e("el-select",{staticStyle:{flex:"1"},attrs:{placeholder:"请选择关联的产品",filterable:"",remote:"","remote-method":t.searchProducts,loading:t.productLoading,clearable:""},model:{value:t.dataForm.productNewId,callback:function(e){t.$set(t.dataForm,"productNewId",e)},expression:"dataForm.productNewId"}},t._l(t.productList,(function(a){return e("el-option",{key:a.productId,attrs:{label:"".concat(a.productTitle," ").concat(a.brandName?"("+a.brandName+")":""," - ¥").concat(a.productPrice),value:a.productId}},[e("div",{staticStyle:{display:"flex","justify-content":"space-between","align-items":"center"}},[e("span",[t._v(t._s(a.productTitle))]),e("span",{staticStyle:{color:"#999","font-size":"12px"}},[t._v("\n                  "+t._s(a.brandName?a.brandName+" - ":"")+"¥"+t._s(a.productPrice)+"\n                ")])])])})),1),e("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.openProductDialog}},[t._v("选择产品")])],1),e("div",{staticClass:"form-tip"},[t._v("选择关联的产品，用于价格计算。可留空后续配置。支持搜索产品名称")])])],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.visible=!1}}},[t._v("取消")]),e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.dataFormSubmit()}}},[t._v("确定")])],1)],1),e("el-dialog",{attrs:{title:"选择产品",visible:t.productDialogVisible,width:"800px","close-on-click-modal":!1},on:{"update:visible":function(e){t.productDialogVisible=e}}},[e("div",{staticStyle:{"margin-bottom":"20px"}},[e("el-row",{attrs:{gutter:10}},[e("el-col",{attrs:{span:8}},[e("el-input",{attrs:{placeholder:"输入产品名称搜索",clearable:""},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.loadDialogProducts.apply(null,arguments)}},model:{value:t.productSearch.keyword,callback:function(e){t.$set(t.productSearch,"keyword",e)},expression:"productSearch.keyword"}})],1),e("el-col",{attrs:{span:4}},[e("el-button",{attrs:{type:"primary"},on:{click:t.loadDialogProducts}},[t._v("搜索")])],1)],1)],1),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.dialogProductLoading,expression:"dialogProductLoading"}],attrs:{data:t.dialogProductList,height:"400","highlight-current-row":""},on:{"row-click":t.selectProduct}},[e("el-table-column",{attrs:{prop:"productTitle",label:"产品名称","min-width":"200"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("div",{staticStyle:{display:"flex","align-items":"center"}},[a.row.r1?e("img",{staticStyle:{width:"40px",height:"40px","margin-right":"10px","border-radius":"4px"},attrs:{src:a.row.r1}}):t._e(),e("div",[e("div",[t._v(t._s(a.row.productTitle))])])])]}}])}),e("el-table-column",{attrs:{label:"零售价",width:"100"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",{staticStyle:{color:"#f56c6c","font-weight":"bold"}},[t._v("\n            ¥"+t._s(a.row.sellprice||0)+"\n          ")])]}}])}),e("el-table-column",{attrs:{label:"成本价",width:"100"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",[t._v("¥"+t._s(a.row.price||0))])]}}])}),e("el-table-column",{attrs:{label:"操作",width:"80"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(e){return t.selectProduct(a.row)}}},[t._v("选择")])]}}])})],1),e("div",{staticStyle:{"margin-top":"20px","text-align":"center"}},[e("el-pagination",{attrs:{"current-page":t.productSearch.page,"page-size":t.productSearch.pageSize,total:t.productSearch.total,layout:"total, prev, pager, next"},on:{"current-change":t.handleDialogPageChange}})],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.productDialogVisible=!1}}},[t._v("取消")])],1)],1)],1)},n=[],c={data:function(){return{visible:!1,dataForm:{id:0,unionCode:"",name:"",orderSourceId:null,productNewId:null},dataRule:{unionCode:[{required:!0,message:"唯一标识不能为空",trigger:"blur"},{min:1,max:100,message:"唯一标识长度在1到100个字符",trigger:"blur"}],name:[{required:!0,message:"名称不能为空",trigger:"blur"},{min:1,max:255,message:"名称长度在1到255个字符",trigger:"blur"}],orderSourceId:[{required:!0,message:"请选择订单来源",trigger:"change"}]},orderSourceList:[],productList:[],productLoading:!1,productDialogVisible:!1,dialogProductList:[],dialogProductLoading:!1,classifyOptions:[],brandOptions:[],productSearch:{classId:null,brandId:null,keyword:"",page:1,pageSize:10,total:0}}},methods:{init:function(t){var e=this;this.dataForm.id=t||0,this.visible=!0,this.getOrderSourceList(),this.$nextTick((function(){e.$refs["dataForm"].resetFields(),e.dataForm.id?e.$get("/szmb/orderSourceConnect/detail/"+t,{}).then((function(t){1===t.code?(e.dataForm.unionCode=t.data.unionCode,e.dataForm.name=t.data.name,e.dataForm.orderSourceId=t.data.orderSourceId,e.dataForm.productNewId=t.data.productNewId,t.data.productNewId&&e.loadProductById(t.data.productNewId)):e.$message.error(t.data||"获取数据失败")})):(e.dataForm.unionCode="",e.dataForm.name="",e.dataForm.orderSourceId=null,e.dataForm.productNewId=null,e.productList=[])}))},getOrderSourceList:function(){var t=this;this.$get("/szmb/order-source/all",{}).then((function(e){1===e.code&&(t.orderSourceList=e.data||[])}))},searchProducts:function(t){var e=this;""!==t?(this.productLoading=!0,this.$post("/szmb/orderSourceConnect/product/search",{storeId:this.Cookies.get("storeId"),keyword:t,pageNo:1,pageSize:20,status:0}).then((function(t){e.productLoading=!1,1===t.code&&t.data&&t.data.list&&t.data.list.length>0?e.productList=t.data.list.map((function(t){return{productId:t.productId,productTitle:t.productName,productPrice:t.retailPrice||0,productImage:t.productImage||"",brandName:t.brandName||"",classifyName:t.categoryName||"",skuId:t.skuId}})):e.productList=[]})).catch((function(){e.productLoading=!1,e.productList=[],e.$message.error("搜索产品失败，请稍后重试")}))):this.productList=[]},loadProductById:function(t){var e=this;this.$get("/szmb/orderSourceConnect/product/detail/"+t,{storeId:this.Cookies.get("storeId")}).then((function(a){if(1===a.code&&a.data){var o={productId:a.data.productId||t,productTitle:a.data.productName||"产品".concat(t),productPrice:a.data.retailPrice||0,productImage:a.data.productImage||"",brandName:a.data.brandName||"",classifyName:a.data.categoryName||"",skuId:a.data.skuId};e.productList=[o]}else{var r={productId:t,productTitle:"产品".concat(t),productPrice:0,productImage:"",brandName:"",classifyName:"",skuId:null};e.productList=[r]}})).catch((function(){var a={productId:t,productTitle:"产品".concat(t),productPrice:0,productImage:"",brandName:"",classifyName:"",skuId:null};e.productList=[a]}))},openProductDialog:function(){this.productDialogVisible=!0,this.loadClassifyOptions(),this.loadBrandOptions(),this.loadDialogProducts()},loadClassifyOptions:function(){var t=this;this.$get("/szmb/orderSourceConnect/product/categories").then((function(e){1===e.code&&e.data&&(t.classifyOptions=e.data)})).catch((function(){t.classifyOptions=[]}))},loadBrandOptions:function(){var t=this;this.$get("/szmb/orderSourceConnect/product/brands",{storeId:this.Cookies.get("storeId")}).then((function(e){1===e.code&&e.data&&(t.brandOptions=e.data)})).catch((function(){t.brandOptions=[]}))},loadDialogProducts:function(){var t=this;this.dialogProductLoading=!0,this.$post("/szmb/orderSourceConnect/product/search",{storeId:this.Cookies.get("storeId"),categoryId:this.productSearch.classId,brandId:this.productSearch.brandId,pageNo:this.productSearch.page,pageSize:this.productSearch.pageSize,keyword:this.productSearch.keyword,status:0}).then((function(e){t.dialogProductLoading=!1,1===e.code&&e.data?(t.dialogProductList=e.data.list||[],t.productSearch.total=e.data.total||0):(t.dialogProductList=[],t.productSearch.total=0)})).catch((function(){t.dialogProductLoading=!1,t.dialogProductList=[],t.productSearch.total=0,t.$message.error("加载产品列表失败")}))},handleDialogPageChange:function(t){this.productSearch.page=t,this.loadDialogProducts()},selectProduct:function(t){this.dataForm.productNewId=t.productId;var e={productId:t.productId,productTitle:t.productTitle,productPrice:t.sellprice||0,productImage:t.r1||""};this.productList=[e],this.productDialogVisible=!1,this.$message.success("产品选择成功")},dataFormSubmit:function(){var t=this;this.$refs["dataForm"].validate((function(e){if(e){var a=t.dataForm.id?"/szmb/orderSourceConnect/update":"/szmb/orderSourceConnect/add";t.dataForm.header="json",t.$post(a,t.dataForm).then((function(e){1===e.code?t.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){t.visible=!1,t.$emit("refreshDataList")}}):t.$message.error(e.data||"操作失败")}))}}))}}},d=c,s=(a("e46d"),a("0c7c")),l=Object(s["a"])(d,i,n,!1,null,"95c53886",null),u=l.exports,p=a("e143"),m={data:function(){return{dataForm:{unionCode:"",name:"",orderSourceId:"",configStatus:""},dataList:[],dataListLoading:!1,dataListSelections:[],orderSourceConnectAddOrUpdateVisible:!1,orderSourceList:[],pageIndex:1,pageSize:20,totalPage:0,batchConfigVisible:!1,batchConfigLoading:!1,batchConfigForm:{productNewId:null},batchProductList:[],batchProductLoading:!1,batchProductDialogVisible:!1,batchDialogProductList:[],batchDialogProductLoading:!1,batchSelectedProduct:null,batchProductSearch:{keyword:"",classId:null,brandId:null,page:1,pageSize:10,total:0},batchClassifyOptions:[],batchBrandOptions:[]}},components:{orderSourceConnectAddOrUpdate:u},mounted:function(){this.getOrderSourceList()},methods:{onSearch:function(){this.pageIndex=1,this.getDataList()},getOrderSourceList:function(){var t=this;this.$get("/szmb/order-source/all",{}).then((function(e){1===e.code?(t.orderSourceList=e.data||[],t.getDataList()):(t.$message.error("获取订单来源列表失败"),t.getDataList())})).catch((function(){t.$message.error("获取订单来源列表失败"),t.getDataList()}))},getDataList:function(){var t=this;this.dataListLoading=!0;var e={pageNo:this.pageIndex,pageSize:this.pageSize};this.dataForm.unionCode&&(e.unionCode=this.dataForm.unionCode),this.dataForm.name&&(e.name=this.dataForm.name),this.dataForm.orderSourceId&&(e.orderSourceId=this.dataForm.orderSourceId),this.dataForm.configStatus&&(e.configStatus=this.dataForm.configStatus),this.$get("/szmb/orderSourceConnect/page",e).then((function(e){if(t.dataListLoading=!1,1===e.code&&e.data){var a=e.data,o=a.list||[];o.forEach((function(e){var a=t.orderSourceList.find((function(t){return t.id==e.orderSourceId}));e.orderSourceName=a?a.name:"未知来源"})),t.dataList=o,t.totalPage=a.total||0}else t.dataList=[],t.totalPage=0,t.$message.error(e.data||"获取数据失败")})).catch((function(){t.dataListLoading=!1,t.dataList=[],t.totalPage=0,t.$message.error("网络错误，请稍后重试")}))},selectionChangeHandle:function(t){this.dataListSelections=t},sizeChangeHandle:function(t){this.pageSize=t,this.pageIndex=1,this.getDataList()},currentChangeHandle:function(t){this.pageIndex=t,this.getDataList()},orderSourceConnectAddOrUpdate:function(t){var e=this;this.orderSourceConnectAddOrUpdateVisible=!0,this.$nextTick((function(){e.$refs.orderSourceConnectAddOrUpdate.init(t)}))},configureProduct:function(t){this.orderSourceConnectAddOrUpdate(t.id)},deleteHandle:function(t){var e=this;this.$confirm("确定要删除该关联关系吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.$post("/szmb/orderSourceConnect/delete/"+t,{}).then((function(t){1===t.code?e.$message({message:"删除成功",type:"success",duration:1500,onClose:function(){e.getDataList()}}):e.$message.error(t.data||"删除失败")}))})).catch((function(){}))},getUnConfiguredList:function(){this.dataForm.configStatus="unconfigured",this.onSearch()},formatDate:function(t){if(!t)return"";var e=new Date(t);return isNaN(e.getTime())?t:e.getFullYear()+"-"+String(e.getMonth()+1).padStart(2,"0")+"-"+String(e.getDate()).padStart(2,"0")+" "+String(e.getHours()).padStart(2,"0")+":"+String(e.getMinutes()).padStart(2,"0")},batchConfigureProducts:function(){0!==this.dataListSelections.length?(this.batchConfigVisible=!0,this.batchConfigForm.productNewId=null,this.batchProductList=[]):this.$message.warning("请先选择要配置的项目")},searchBatchProducts:function(t){var e=this;""!==t?(this.batchProductLoading=!0,this.$post("/szmb/orderSourceConnect/product/search",{storeId:this.Cookies.get("storeId"),keyword:t,pageNo:1,pageSize:20,status:0}).then((function(t){e.batchProductLoading=!1,1===t.code&&t.data&&t.data.list&&t.data.list.length>0?e.batchProductList=t.data.list.map((function(t){return{productId:t.productId,productTitle:t.productName,productPrice:t.retailPrice||0,productImage:t.productImage||"",brandName:t.brandName||"",classifyName:t.categoryName||"",skuId:t.skuId}})):e.batchProductList=[]})).catch((function(){e.batchProductLoading=!1,e.batchProductList=[],e.$message.error("搜索产品失败")}))):this.batchProductList=[]},confirmBatchConfig:function(){var t=this;this.batchConfigForm.productNewId?this.$confirm("确定要将选中的 ".concat(this.dataListSelections.length," 个项目都配置为同一个产品吗？"),"批量配置确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.batchConfigLoading=!0;var e=t.dataListSelections.map((function(e){return t.$post("/szmb/orderSourceConnect/update",{id:e.id,unionCode:e.unionCode,name:e.name,orderSourceId:e.orderSourceId,productNewId:t.batchConfigForm.productNewId,header:"json"})}));Promise.all(e).then((function(e){t.batchConfigLoading=!1;var a=e.filter((function(t){return 1===t.code})).length,o=e.length-a;0===o?t.$message.success("批量配置成功！共配置了 ".concat(a," 个项目")):t.$message.warning("批量配置完成！成功 ".concat(a," 个，失败 ").concat(o," 个")),t.batchConfigVisible=!1,t.getDataList()})).catch((function(){t.batchConfigLoading=!1,t.$message.error("批量配置失败，请稍后重试")}))})).catch((function(){})):this.$message.warning("请选择要配置的产品")},exportExcel:function(){this.getAllDataForExport()},getAllDataForExport:function(){var t=this,e={pageNo:1,pageSize:1e4};this.dataForm.unionCode&&(e.unionCode=this.dataForm.unionCode),this.dataForm.name&&(e.name=this.dataForm.name),this.dataForm.orderSourceId&&(e.orderSourceId=this.dataForm.orderSourceId),this.dataForm.configStatus&&(e.configStatus=this.dataForm.configStatus),this.$get("/szmb/orderSourceConnect/page",e).then((function(e){if(1===e.code&&e.data){var a=e.data.list||[];a.forEach((function(e){var a=t.orderSourceList.find((function(t){return t.id==e.orderSourceId}));e.orderSourceName=a?a.name:"未知来源"})),t.handleExportData(a)}else t.$message.error(e.data||"获取数据失败")})).catch((function(){t.$message.error("网络错误，请稍后重试")}))},handleExportData:function(t){var e=this;if(0!==t.length){var a=t.map((function(t){return{unionCode:t.unionCode||"",name:t.name||"",orderSourceName:t.orderSourceName||"",productNewId:t.productNewId||"未配置",productName:t.productName||"-",configStatus:t.productNewId?"已配置":"未配置",createTime:e.formatDate(t.createTime)}})),o=[{tHeader:["唯一标识(skuId)","名称","订单来源","产品ID","产品名称","配置状态","创建时间"],filterVal:["unionCode","name","orderSourceName","productNewId","productName","configStatus","createTime"],tableDatas:a,sheetName:"订单来源关联数据"}],r="订单来源关联数据_".concat((new Date).getFullYear()).concat(String((new Date).getMonth()+1).padStart(2,"0")).concat(String((new Date).getDate()).padStart(2,"0"),"_").concat(String((new Date).getHours()).padStart(2,"0")).concat(String((new Date).getMinutes()).padStart(2,"0"));Object(p["c"])(o,r,!0,"xlsx"),this.$message.success("成功导出 ".concat(t.length," 条数据"))}else this.$message.warning("没有数据可以导出")},openBatchProductDialog:function(){this.batchProductDialogVisible=!0,this.batchSelectedProduct=null,this.loadBatchClassifyOptions(),this.loadBatchBrandOptions(),this.loadBatchDialogProducts()},loadBatchClassifyOptions:function(){var t=this;this.$get("/szmb/orderSourceConnect/product/categories").then((function(e){1===e.code&&e.data&&(t.batchClassifyOptions=e.data)})).catch((function(){t.batchClassifyOptions=[]}))},loadBatchBrandOptions:function(){var t=this;this.$get("/szmb/orderSourceConnect/product/brands",{storeId:this.Cookies.get("storeId")}).then((function(e){1===e.code&&e.data&&(t.batchBrandOptions=e.data)})).catch((function(){t.batchBrandOptions=[]}))},loadBatchDialogProducts:function(){var t=this;this.batchDialogProductLoading=!0,this.$post("/szmb/orderSourceConnect/product/search",{storeId:this.Cookies.get("storeId"),categoryId:this.batchProductSearch.classId,brandId:this.batchProductSearch.brandId,pageNo:this.batchProductSearch.page,pageSize:this.batchProductSearch.pageSize,keyword:this.batchProductSearch.keyword,status:0}).then((function(e){t.batchDialogProductLoading=!1,1===e.code&&e.data?(t.batchDialogProductList=e.data.list||[],t.batchProductSearch.total=e.data.total||0):(t.batchDialogProductList=[],t.batchProductSearch.total=0)})).catch((function(){t.batchDialogProductLoading=!1,t.batchDialogProductList=[],t.batchProductSearch.total=0,t.$message.error("加载产品列表失败")}))},handleBatchProductSelect:function(t){this.batchSelectedProduct=t},handleBatchProductPageChange:function(t){this.batchProductSearch.page=t,this.loadBatchDialogProducts()},confirmBatchProductSelect:function(){if(this.batchSelectedProduct){this.batchConfigForm.productNewId=this.batchSelectedProduct.productId;var t={productId:this.batchSelectedProduct.productId,productTitle:this.batchSelectedProduct.productName,productPrice:this.batchSelectedProduct.retailPrice||0,productImage:this.batchSelectedProduct.productImage||"",brandName:this.batchSelectedProduct.brandName||"",classifyName:this.batchSelectedProduct.categoryName||"",skuId:this.batchSelectedProduct.skuId};this.batchProductList=[t],this.batchProductDialogVisible=!1,this.$message.success("产品选择成功")}else this.$message.warning("请选择一个产品")}}},g=m,h=(a("26ee"),Object(s["a"])(g,o,r,!1,null,"a6e2d090",null));e["default"]=h.exports},e46d:function(t,e,a){"use strict";a("e7c6")},e7c6:function(t,e,a){}}]);