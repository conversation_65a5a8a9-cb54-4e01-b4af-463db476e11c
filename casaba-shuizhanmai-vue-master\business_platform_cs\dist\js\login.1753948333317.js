(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["login"],{"16b4":function(e,o,s){"use strict";s("c002")},"395a":function(e,o,s){"use strict";s.r(o);var t=function(){var e=this,o=e._self._c;return o("div",{staticClass:"login-box"},[o("div",{staticClass:"login-form"},[o("p",{staticClass:"login-title"},[e._v("水站登录")]),o("p",{staticClass:"login-en"},[e._v("USER LOGIN")]),o("el-form",{ref:"ruleForm",staticClass:"ms-content",attrs:{model:e.ruleForm,rules:e.rules,"label-width":"0px"}},[o("div",{staticStyle:{display:"flex","align-items":"center","justify-content":"space-around",height:"60px"}},[o("div",{class:0==e.active?"active123":"",on:{click:function(o){e.active=0}}},[e._v("验证码登录")]),o("div",{class:1==e.active?"active123":"",on:{click:function(o){e.active=1}}},[e._v("密码登录")])]),o("el-form-item",{attrs:{prop:"username"}},[o("el-input",{attrs:{placeholder:"请输入手机号",size:"large","suffix-icon":"el-icon-phone"},nativeOn:{keyup:function(o){return!o.type.indexOf("key")&&e._k(o.keyCode,"enter",13,o.key,"Enter")?null:e.submitForm("ruleForm")}},model:{value:e.ruleForm.username,callback:function(o){e.$set(e.ruleForm,"username",o)},expression:"ruleForm.username"}})],1),0==e.active?o("el-form-item",{staticClass:"sen-msg",attrs:{prop:"code"}},[o("el-input",{attrs:{oninput:"value=value.replace(/[^\\d]/g,'')"},model:{value:e.ruleForm.code,callback:function(o){e.$set(e.ruleForm,"code",o)},expression:"ruleForm.code"}},[o("template",{slot:"append"},[o("el-button",{staticClass:"codeClass",attrs:{type:"primary",loading:"发送验证码"!=e.ruleForm.codeText},on:{click:e.sendCode}},[e._v(e._s(e.ruleForm.codeText))])],1)],2)],1):e._e(),1==e.active?o("el-form-item",{attrs:{prop:"password"}},[o("el-input",{attrs:{type:"password",placeholder:"请输入密码","suffix-icon":"iconfont icon-mima",size:"large","show-password":""},nativeOn:{keyup:function(o){return!o.type.indexOf("key")&&e._k(o.keyCode,"enter",13,o.key,"Enter")?null:e.submitForm("ruleForm")}},model:{value:e.ruleForm.password,callback:function(o){e.$set(e.ruleForm,"password",o)},expression:"ruleForm.password"}})],1):e._e(),o("el-form-item",[o("el-checkbox",{model:{value:e.ruleForm.savepwd,callback:function(o){e.$set(e.ruleForm,"savepwd",o)},expression:"ruleForm.savepwd"}},[e._v("记住密码")]),o("div",{staticClass:"float-right"},[o("el-button",{attrs:{type:"text"},on:{click:e.toFindPsd}},[e._v("忘记密码")]),o("span",{staticStyle:{color:"#1693fd",padding:"0 10px","font-size":"12px"}},[e._v("|")]),o("el-button",{attrs:{type:"text"},on:{click:e.toRegister}},[e._v("我要注册")])],1)],1),o("div",{staticClass:"login-btn"},[o("el-button",{attrs:{type:"primary",loading:e.loginOff},on:{click:function(o){return e.submitForm("ruleForm")}}},[e._v(e._s(e.loginTxt))])],1)],1)],1)])},r=[],a={data:function(){var e=this,o=function(o,s,t){console.log(s,"phone");var r=e.$util.checkPhone(s);r?t():t(new Error("请输入有效的手机号码"))};return{imgcode:"",inputImgcode:"",imgCodeKey:"",active:1,loginOff:!1,loginTxt:"登录",ruleForm:{code:"",codeText:"发送验证码",username:"",password:"",savepwd:!1},rules:{username:[{validator:o,trigger:"change"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:1,max:25,message:"长度在 1 到 25 个字符",trigger:"blur"}]}}},methods:{sendCode:function(){var e=this;if(this.ruleForm.username){var o=60;e.ruleForm.codeText=o+"s";var s="/szmcuercontroller/sendcodeonly",t={mobilPhone:e.ruleForm.username,imgCode:e.inputImgcode,imgCodeKey:e.imgCodeKey};e.$post(s,t).then((function(s){console.log(s),1==s.code?(e.$message({message:"发送成功",type:"success"}),e.ruleForm.dTime=setInterval((function(){o--,o<0?(e.ruleForm.codeText="发送验证码",clearInterval(e.ruleForm.dTime)):e.ruleForm.codeText=o+"s"}),1e3)):(e.ruleForm.codeText="发送验证码",e.$message({message:s.msg,type:"error"}))}))}else this.$message({message:"请输入手机号",type:"warning"})},getUrlParam:function(e){var o=new RegExp("(^|&)"+e+"=([^&]*)(&|$)"),s=window.location.search.substr(1).match(o);return null!=s?decodeURIComponent(s[2]):null},submitForm:function(e){var o=this,s=this;this.$refs[e].validate((function(e){if(!e)return o.$message({showClose:!0,center:!0,message:"请检查输入是否正确",type:"error"}),!1;o.loginOff=!0,o.loginTxt="登录中...";var t=0==o.active?"/szmb/szmbstorecontroller/storeloginbycode":"/szmb/szmbstorecontroller/storelogin",r={phone:s.ruleForm.username,password:s.ruleForm.password,code:s.ruleForm.code,sign:"pc"};s.$post(t,r).then((function(e){if(console.log(e),o.loginOff=!1,1==e.code)if(o.ruleForm.savepwd?o.Cookies.set("savePw",{username:o.ruleForm.username,password:o.$base64.encode(o.ruleForm.password),savepwd:o.ruleForm.savepwd},{expires:s.$expires}):o.Cookies.set("savePw",""),o.Cookies.set("userToken",e.data.token,{expires:s.$expires}),o.Cookies.set("userId",e.data.userId?e.data.userId:"",{expires:s.$expires}),o.Cookies.set("userRole",e.data.userRole?e.data.userRole:99999999,{expires:s.$expires}),o.Cookies.set("storeLocal",e.data.storeLocal,{expires:s.$expires}),o.Cookies.set("storeId",e.data.storeId,{expires:s.$expires}),o.Cookies.set("storeStatus",e.data.identity,{expires:s.$expires}),"0"==e.data.identity)console.log("注册",e.data),o.$router.push({name:"registerStore"});else{console.log("登录成功",1231231);var t="/szmb/storeapplyfor/selectbyid",r={storeId:s.Cookies.get("storeId")};s.$post(t,r).then((function(e){if(console.log(e),1==e.code){s.Cookies.set("storeInfo",e.data,{expires:s.$expires}),s.Cookies.set("adminStoreInfo",e.data,{expires:s.$expires});var t=e.data.jurisdiction,r={};t.forEach((function(e){"设置优惠价格"==e.moduleName&&(r.yhjg=e.state),"组合套餐"==e.moduleName&&(r.zhtc=e.state),"派单管理"==e.moduleName&&(r.pdgl=e.state),"库存盘点"==e.moduleName&&(r.kcpd=e.state),"消费提醒"==e.moduleName&&(r.xftx=e.state),"收支简表"==e.moduleName&&(r.szjb=e.state)})),s.Cookies.set("moduleList",r,{expires:s.$expires}),o.$router.push({name:"home",params:{from:"login"}})}}))}else s.$message.error(e.data),o.loginOff=!1,o.loginTxt="登录"}))}))},toRegister:function(){var e=this;e.$router.push({name:"register",params:{name:"陈卓",age:"18"}})},toFindPsd:function(){console.log("find");var e=this;e.$router.push({name:"findPassword",params:{name:"陈卓",age:"18"}})}},created:function(){},mounted:function(){this.remPw=this.Cookies.get("savePw"),this.remPw?(console.log("记住"),this.remForm=JSON.parse(this.remPw),this.active=1,this.ruleForm={code:"",codeText:"发送验证码",username:this.remForm.username,password:this.$base64.decode(this.remForm.password),savepwd:this.remForm.savepwd}):(this.active=0,console.log("忘记"),this.ruleForm={code:"",codeText:"发送验证码",username:"",password:"",savepwd:!1})}},i=a,n=(s("16b4"),s("0c7c")),l=Object(n["a"])(i,t,r,!1,null,"7b4e9a19",null);o["default"]=l.exports},c002:function(e,o,s){}}]);