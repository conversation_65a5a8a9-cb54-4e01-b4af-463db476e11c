(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["menumanage"],{2914:function(e,t,a){"use strict";a.r(t);var o=function(){var e=this,t=e._self._c;return t("div",[t("div",[t("div",{staticClass:"user-app-case"},[t("el-table",{staticStyle:{width:"100%"},attrs:{data:e.user<PERSON>ing<PERSON>a,"tooltip-effect":"dark",size:"medium",stripe:"","max-height":e.tableHeight+115,"header-cell-style":{"text-align":"center",background:"#ededed",color:"#000000",height:"50px"},"cell-style":{"text-align":"center","font-size":"14px",color:"#333C48"}}},[t("el-table-column",{attrs:{label:"头像",prop:"pic"},scopedSlots:e._u([{key:"default",fn:function(e){return[t("img",{staticStyle:{width:"75px",height:"75px"},attrs:{src:e.row.pic,alt:""}})]}}])}),t("el-table-column",{attrs:{label:"客户名称",prop:"userName"}}),t("el-table-column",{attrs:{label:"客户联系方式",prop:"mobile"}}),t("el-table-column",{attrs:{label:"下单人联系方式",prop:"orderPhone"}}),t("el-table-column",{attrs:{label:"评价时间",prop:"evaluationDate"}}),t("el-table-column",{attrs:{label:"下单地址",prop:"orderAddress"}}),t("el-table-column",{attrs:{prop:"nickName",label:"客户备注"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("span",[e._v(e._s(a.row.nickName?a.row.nickName:"-"))])]}}])}),t("el-table-column",{attrs:{label:"商品名称",prop:"formName"},scopedSlots:e._u([{key:"default",fn:function(a){return e._l(a.row.formName,(function(a,o){return t("p",{key:o,staticStyle:{margin:"3px 0"}},[e._v("\n                "+e._s(a)+"\n              ")])}))}}])}),t("el-table-column",{attrs:{label:"配送服务",prop:"dissstar"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-rate",{staticStyle:{},attrs:{disabled:"","text-color":"#ff9900"},model:{value:a.row.dissstar,callback:function(t){e.$set(a.row,"dissstar",t)},expression:"scope.row.dissstar"}})]}}])}),t("el-table-column",{attrs:{label:"服务态度",prop:"service"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-rate",{staticStyle:{},attrs:{disabled:"","text-color":"#ff9900"},model:{value:a.row.service,callback:function(t){e.$set(a.row,"service",t)},expression:"scope.row.service"}})]}}])}),t("el-table-column",{attrs:{label:"评价内容",prop:"evaluate"}})],1),t("div",{staticClass:"pages-box flex align-items-center justify-content-center"},[t("el-pagination",{attrs:{layout:"prev, pager, next",background:"","current-page":e.pagesData.currentPage,"page-size":e.pagesData.pageSize,total:e.pagesData.pageTotal},on:{"current-change":e.handleCurrentChange}})],1)],1)])])},s=[],r={props:{},data:function(){return{imgUri:this.$imgUri,userPingjia:[],pagesData:{pageTotal:0,currentPage:1,pageSize:10}}},computed:{tableHeight:function(){console.log(this.$store.getters.getGlobalHeight);var e=Number(this.$store.getters.getGlobalHeight)-300;return e>=300?e:300},inTableHeight:function(){var e=this.$store.getters.getGlobalHeight;return e>=400?parseInt(this.$util.mul(e,.5)):400}},created:function(){},mounted:function(){this.requestUserAppraise()},watch:{},methods:{handleCurrentChange:function(e){this.pagesData.currentPage=e,this.requestUserAppraise()},requestUserAppraise:function(){var e=this,t=this;console.log(this.userMsg),t.$post("/szmb/orderevaluate/selectbystoreid",{storeId:t.Cookies.get("storeId"),index:t.pagesData.currentPage}).then((function(a){1===a.code?(t.userPingjia=a.data.evaluatelist,t.pagesData.pageTotal=a.data.count):0===a.code?t.$message({type:"warning",message:a.data}):(console.log(a.msg),e.$message.error(a.data))})).catch((function(e){console.log(e)}))}}},i=r,l=(a("4d40"),a("0c7c")),n=Object(l["a"])(i,o,s,!1,null,"438f4d54",null);t["default"]=n.exports},"2e18":function(e,t,a){"use strict";a.r(t);var o=function(){var e=this,t=e._self._c;return t("div",{staticClass:"padding-bottom-30"},[t("div",[t("div",{staticClass:"headBox",staticStyle:{"padding-top":"0"}},[t("div",{staticClass:"optionBox color-red"},[e._v("系统的分类名不可修改")]),t("el-button",{attrs:{type:"success",icon:"el-icon-plus"},on:{click:e.addClassNew}},[e._v("新增分类名")])],1),t("div",{staticClass:"tableBox"},[t("el-table",{staticStyle:{width:"100%"},attrs:{data:e.classifyList,"max-height":e.tableHeight+100,border:"","header-cell-style":{"text-align":"center",background:"#EFF2F7",color:"#3D4C66"},"cell-style":{"text-align":"center","font-size":"16px",color:"#3D4C66"},"empty-text":"暂无数据，请切换分类或重新输入重试",size:"medium"}},[t("el-table-column",{attrs:{width:"100",label:"序号",type:"index"}}),t("el-table-column",{attrs:{label:"分类名称",prop:"classifyName"}}),t("el-table-column",{attrs:{label:"排序(小靠前)",prop:"paixu"}}),t("el-table-column",{attrs:{prop:"r1","header-align":"center",align:"center",label:"是否显示"},scopedSlots:e._u([{key:"default",fn:function(a){return t("div",{},[t("div",{attrs:{type:"primary"}},[e._v(e._s(null==a.row.state?"不显示":e.showShow[a.row.state].value))])])}}])}),t("el-table-column",{attrs:{label:"首页描述",prop:"biref"}}),t("el-table-column",{attrs:{label:"商品个数",prop:"totalProduct"}}),t("el-table-column",{attrs:{label:"操作"},scopedSlots:e._u([{key:"default",fn:function(a){return[1!=a.row.systemClassifyFlag||"13816541474"==e.adminStoreInfo.phone?t("el-button",{attrs:{type:"text",size:"medium"},on:{click:function(t){return e.editClass(a.row)}}},[e._v("编辑")]):e._e(),t("el-button",{staticStyle:{color:"red"},attrs:{type:"text",size:"medium"},on:{click:function(t){return e.deleteClass(a.row)}}},[e._v("删除")])]}}])})],1)],1)]),t("el-dialog",{staticClass:"new-dialog-css",attrs:{visible:e.newAddClassVisible,width:"30%",center:""},on:{"update:visible":function(t){e.newAddClassVisible=t}}},[t("div",{staticClass:"new-dialog-title",attrs:{slot:"title"},slot:"title"},[e._v(e._s(e.titleClass))]),t("div",{staticClass:"new-dialog-two-title"},[e._v("分类信息")]),t("div",{staticClass:"new-dialog-body"},[t("el-form",{staticStyle:{padding:"0 30px"}},[t("el-form-item",[t("el-input",{attrs:{placeholder:"请输入分类名"},model:{value:e.dialogValue,callback:function(t){e.dialogValue=t},expression:"dialogValue"}})],1),t("el-form-item",{attrs:{label:"排序"}},[t("el-input",{attrs:{placeholder:"请输入排序编号"},model:{value:e.paixu,callback:function(t){e.paixu=t},expression:"paixu"}})],1),t("el-form-item",{attrs:{label:"是否显示",prop:"state"}},[t("el-radio-group",{model:{value:e.state,callback:function(t){e.state=t},expression:"state"}},e._l(e.showShow,(function(a){return t("el-radio",{key:a.key,attrs:{label:a.key}},[e._v(e._s(a.value))])})),1)],1),t("el-form-item",{attrs:{label:"首页描述"}},[t("el-input",{attrs:{placeholder:"首页描述"},model:{value:e.biref,callback:function(t){e.biref=t},expression:"biref"}})],1),e.newclassType?e._e():t("div",{staticClass:"color-red"},[e._v("该分类中包含"+e._s(e.classGoodsNum)+"个商品，编辑修改分类名保存后，包含商品的所属分类都会修改。")])],1)],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:e.cancelBtn}},[e._v("取 消")]),e.newclassType?t("el-button",{attrs:{type:"primary"},on:{click:e.newClassBtn}},[e._v("保 存")]):e._e(),e.newclassType?e._e():t("el-button",{attrs:{type:"primary"},on:{click:e.editClassBtn}},[e._v("保 存")])],1)])],1)},s=[],r=a("7de9"),i={props:{},data:function(){return{adminStoreInfo:{},yesOrNo:r["b"],imgUri:this.$imgUri,classifyList:[],newAddClassVisible:!1,titleClass:"新增分类",newclassType:!0,dialogValue:"",biref:"",paixu:0,state:0,r1:"0",classGoodsNum:0,editClassId:"",showIndex:[{key:"0",value:"显示到主页"},{key:"1",value:"不显示到主页"}],showShow:[{key:0,value:"显示"},{key:1,value:"不显示"}]}},computed:{tableHeight:function(){console.log(this.$store.getters.getGlobalHeight);var e=Number(this.$store.getters.getGlobalHeight)-300;return e>=300?e:300},inTableHeight:function(){var e=this.$store.getters.getGlobalHeight;return e>=400?parseInt(this.$util.mul(e,.5)):400}},created:function(){this.requestClassifyList(),this.adminStoreInfo=JSON.parse(this.Cookies.get("adminStoreInfo"))},mounted:function(){},watch:{},methods:{addClassNew:function(){this.titleClass="新增分类",this.newclassType=!0,this.newAddClassVisible=!0},editClass:function(e){this.titleClass="编辑分类",this.newclassType=!1,this.classGoodsNum=e.totalProduct,this.dialogValue=e.classifyName,this.r1=e.r1,this.state=e.state,this.biref=e.biref,this.paixu=e.paixu,this.editClassId=e.productClassifyId,this.newAddClassVisible=!0},cancelBtn:function(){this.newAddClassVisible=!1,this.classGoodsNum=0,this.dialogValue="",this.paixu=0,this.r1=0,this.state=0,this.biref="",this.editClassId=""},newClassBtn:function(){var e=this;if(""!=e.dialogValue){var t={storeId:e.Cookies.get("storeId"),className:e.dialogValue,paixu:e.paixu,r1:e.r1,state:e.state,biref:e.biref};this.$post("/szmcproductclassifycontroller/addProductClassifyName",t).then((function(t){1===t.code?(e.$message({type:"success",message:"新增成功"}),e.newAddClassVisible=!1,e.dialogValue="",e.paixu=0,e.r1=0,e.state=0,e.biref="",e.requestClassifyList()):("5005"==t.data?e.$message.error("系统已存在组合套餐分类，无需重复添加。想要添加组合套餐商品，请到【商品管理-商品维护-新增套餐】中添加"):e.$message({type:"warning",message:t.data}),console.log(t.msg))})).catch((function(e){console.log(e)}))}else e.$message.error("分类名不能为空")},editClassBtn:function(){var e=this;if(""!=e.dialogValue){var t={storeId:e.Cookies.get("storeId"),className:e.dialogValue,paixu:e.paixu,r1:e.r1,state:e.state,biref:e.biref,productClassifyId:e.editClassId};this.$post("/szmcproductclassifycontroller/editOptionClassify",t).then((function(t){1===t.code?(e.$message({type:"success",message:"修改成功"}),e.cancelBtn(),e.requestClassifyList()):(e.$message({type:"warning",message:t.data}),console.log(t.msg))})).catch((function(e){console.log(e)}))}else e.$message.error("分类名不能为空")},deleteClass:function(e){var t=this,a=this;this.$confirm("确认删除该分类","提示",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning"}).then((function(){var t={storeId:a.Cookies.get("storeId"),productClassifyId:e.productClassifyId};a.$post("/szmcproductclassifycontroller/deleteProductClassifyName",t).then((function(e){1===e.code?(a.$message({type:"success",message:"删除成功"}),a.requestClassifyList()):a.$message.error(e.data)})).catch((function(e){console.log(e)}))})).catch((function(){t.$message({type:"info",message:"已取消删除"})}))},requestClassifyList:function(){var e=this;this.$get("/szmcproductclassifycontroller/getClassifyList",{storeId:e.Cookies.get("storeId")}).then((function(t){1===t.code?e.classifyList=t.data:e.$message.error(t.data)})).catch((function(e){console.log(e)}))}},filters:{},components:{}},l=i,n=(a("4d86"),a("0c7c")),c=Object(n["a"])(l,o,s,!1,null,"047557b4",null);t["default"]=c.exports},"2fe1":function(e,t,a){"use strict";a.r(t);var o=function(){var e=this,t=e._self._c;return t("div",{staticClass:"padding-bottom-30",staticStyle:{"min-width":"1651px","box-sizing":"border-box",margin:"0 auto"}},[t("div",{staticClass:"headBox margin-bottom-20"},[t("div",{staticClass:"flex algin-items-center"},[t("div",{staticClass:"optionBox flex align-items-center"},[t("el-select",{staticStyle:{width:"170px"},attrs:{clearable:"",filterable:"",placeholder:"按商品分类筛选"},model:{value:e.selectData.classId,callback:function(t){e.$set(e.selectData,"classId",t)},expression:"selectData.classId"}},e._l(e.classifyOptions,(function(e){return t("el-option",{key:e.id,attrs:{label:e.classifyName,value:e.productClassifyId}})})),1),t("el-select",{staticStyle:{width:"170px","margin-left":"10px"},attrs:{clearable:"",filterable:"",placeholder:"按商品品牌筛选"},model:{value:e.selectData.brandId,callback:function(t){e.$set(e.selectData,"brandId",t)},expression:"selectData.brandId"}},e._l(e.bucketBrandList,(function(e){return t("el-option",{key:e.id,attrs:{label:e.brandName,value:e.brandId}})})),1),t("el-select",{staticStyle:{width:"170px","margin-left":"10px"},attrs:{clearable:"",filterable:"",placeholder:"按商品上架状态筛选"},model:{value:e.selectData.shelfState,callback:function(t){e.$set(e.selectData,"shelfState",t)},expression:"selectData.shelfState"}},e._l(e.shelfList,(function(e){return t("el-option",{key:e.id,attrs:{label:e.name,value:e.value}})})),1),t("el-select",{staticStyle:{width:"170px","margin-left":"10px"},attrs:{clearable:"",filterable:"",placeholder:"按商品推荐状态筛选"},model:{value:e.selectData.state,callback:function(t){e.$set(e.selectData,"state",t)},expression:"selectData.state"}},e._l(e.recommendList,(function(e){return t("el-option",{key:e.id,attrs:{label:e.name,value:e.value}})})),1),t("div",{staticClass:"flex align-items-center margin-left-10"},[t("el-input",{attrs:{placeholder:"请输入商品名称筛选",clearable:""},model:{value:e.selectData.isKey,callback:function(t){e.$set(e.selectData,"isKey",t)},expression:"selectData.isKey"}}),t("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary"},on:{click:e.toSearch}},[e._v("查询")]),t("el-button",{staticStyle:{"margin-left":"10px"},on:{click:e.clearSearch}},[e._v("清空筛选条件")])],1)],1)]),t("div",[1==e.moduleList.zhtc?t("el-button",{attrs:{type:"primary",icon:"el-icon-box"},on:{click:e.addNewGroup}},[e._v("新增套餐")]):e._e(),t("el-button",{attrs:{type:"success",icon:"el-icon-help"},on:{click:e.addNewGoods}},[e._v("新增商品")])],1)]),t("div",{staticClass:"tableBox"},[t("div",{staticClass:"color-red",staticStyle:{"font-size":"14px","padding-bottom":"10px"}},[e._v("\n        成本价仅由水站自己可见，零售价是客户可见的价格。系统可自动根据设置的价格库存信息进行营业分析。\n      ")]),t("el-table",{staticStyle:{width:"100%"},attrs:{data:e.list,"header-cell-style":{"text-align":"center","background-color":"rgba(239,242,247,1)"},"cell-style":{"text-align":"center","font-size":"13px",color:"#333C48"},"max-height":e.tableHeight+90,stripe:"",border:""}},[t("el-table-column",{attrs:{prop:"",label:"商品图",width:"100"},scopedSlots:e._u([{key:"default",fn:function(e){return[t("img",{staticStyle:{width:"75px",height:"75px"},attrs:{src:e.row.img,alt:""}})]}}])}),t("el-table-column",{attrs:{prop:"",label:"商品名"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("div",[1==a.row.flag?t("el-tag",{attrs:{type:"danger"}},[e._v("限")]):e._e()],1),t("div",[e._v(e._s(a.row.commodityName))])]}}])}),t("el-table-column",{attrs:{label:"桶类型"},scopedSlots:e._u([{key:"default",fn:function(t){return[0!=t.row.lists?[e._v("\n              "+e._s("0"==t.row.lists[0].buckState?"常规桶":"1"==t.row.lists[0].buckState?"一次性桶":"-")+"\n            ")]:[e._v("-")]]}}])}),t("el-table-column",{attrs:{prop:"className",label:"分类名"}}),t("el-table-column",{attrs:{prop:"brandName",label:"品牌名"}}),t("el-table-column",{attrs:{prop:"unitPrice",label:"零售价"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("span",[e._v(e._s(a.row.unitPrice)+"元")])]}}])}),t("el-table-column",{attrs:{label:"成本价"},scopedSlots:e._u([{key:"default",fn:function(a){return[0!=a.row.lists?t("span",[e._v(e._s(a.row.lists[0].cost)+"元")]):t("span",[e._v("-")])]}}])}),t("el-table-column",{attrs:{prop:"productOrderNum",label:"排序(越小越靠前)"},scopedSlots:e._u([{key:"default",fn:function(a){return t("div",{},[t("div",{on:{click:function(t){e.paixuShow=!0,e.paixuForm.productId=a.row.commodityId,e.paixuForm.productOrderNum=a.row.productOrderNum}}},[e._v(e._s(a.row.productOrderNum))])])}}])}),t("el-table-column",{attrs:{prop:"date",label:"上架时间"}}),t("el-table-column",{attrs:{prop:"",label:"上架状态"},scopedSlots:e._u([{key:"default",fn:function(a){return[1==a.row.upperShelf?t("span",{staticClass:"color-green"},[e._v("已上架")]):t("span",{staticClass:"color-red"},[e._v("未上架")])]}}])}),t("el-table-column",{attrs:{prop:"",label:"推荐状态"},scopedSlots:e._u([{key:"default",fn:function(a){return[1==a.row.recommend?t("span",{staticClass:"color-green"},[e._v("已推荐")]):t("span",{staticClass:"color-red"},[e._v("未推荐")])]}}])}),t("el-table-column",{attrs:{prop:"",label:"操作",width:"180"},scopedSlots:e._u([{key:"default",fn:function(a){return[2==e.storeId?t("el-button",{attrs:{type:"text"},on:{click:function(t){return e.goodsdiscount(a.row.commodityId)}}},[e._v("优惠设置")]):e._e(),t("el-button",{attrs:{type:"text"},on:{click:function(t){return e.editIt(a.row)}}},[e._v("编辑")]),1!=a.row.recommend?t("el-button",{attrs:{type:"text"},on:{click:function(t){return e.upShow(a.row)}}},[e._v("推荐")]):e._e(),1==a.row.recommend?t("el-button",{attrs:{type:"text"},on:{click:function(t){return e.upShow(a.row)}}},[e._v("取消推荐")]):e._e(),1!=a.row.upperShelf?t("el-button",{attrs:{type:"text"},on:{click:function(t){return e.upShelf(a.row)}}},[e._v("上架")]):e._e(),1==a.row.upperShelf?t("el-button",{attrs:{type:"text"},on:{click:function(t){return e.upShelf(a.row)}}},[e._v("下架")]):e._e(),1!=a.row.upperShelf&&6!=a.row.classMastId?t("el-button",{attrs:{type:"text"},on:{click:function(t){return e.deleteGoods(a.row)}}},[e._v("删除")]):e._e(),6==a.row.classMastId?t("el-button",{attrs:{type:"text"},on:{click:function(t){return e.delGroup(a.row)}}},[e._v("删除")]):e._e()]}}])})],1),t("div",{staticClass:"flex align-items-center justify-content-center"},[t("el-pagination",{staticStyle:{padding:"15px"},attrs:{background:"",layout:"prev, pager, next",total:e.pageCount,"current-page":e.page},on:{"current-change":e.changePage}})],1)],1),t("el-dialog",{attrs:{visible:e.addGoodsModal,width:"35%","before-close":e.handleClose},on:{"update:visible":function(t){e.addGoodsModal=t}}},[t("div",{staticClass:"text-align-center font-size-30 padding-tb-20 border-bottom"},[e._v("\n        新增商品\n      ")]),t("div",{staticClass:"text-align-center font-size-16 padding-tb-10 margin-top-20 margin-bottom-20",staticStyle:{"background-color":"#F5F5F5"}},[e._v("\n        基本信息\n      ")]),t("div",[t("el-form",{ref:"addGoodsElement",attrs:{model:e.formData,"label-width":"120px"}},[t("el-form-item",{attrs:{label:"商品分类",prop:"classifyName"}},[t("el-select",{attrs:{placeholder:"商品分类"},on:{change:e.changeName},model:{value:e.formData.classifyName,callback:function(t){e.$set(e.formData,"classifyName",t)},expression:"formData.classifyName"}},e._l(e.classifyOptions,(function(e){return t("el-option",{key:e.id,attrs:{label:e.classifyName,value:e.classifyName}})})),1)],1),t("el-form-item",{attrs:{label:"商品品牌",prop:"brandId"}},[t("el-select",{attrs:{placeholder:"商品品牌",filterable:""},on:{change:e.brandChange},model:{value:e.formData.brandId,callback:function(t){e.$set(e.formData,"brandId",t)},expression:"formData.brandId"}},e._l(e.bucketBrandList,(function(e){return t("el-option",{key:e.brandId,attrs:{label:e.brandName,value:e.brandId}})})),1)],1),t("el-form-item",{attrs:{label:"创建水票",prop:"isWater"}},[t("el-select",{attrs:{placeholder:"创建水票"},model:{value:e.formData.isWater,callback:function(t){e.$set(e.formData,"isWater",t)},expression:"formData.isWater"}},[t("el-option",{attrs:{label:"不创建",value:0}}),t("el-option",{attrs:{label:"创建",value:1}})],1)],1),t("el-form-item",{attrs:{label:"商品名称",prop:"ggoodsName"}},[t("el-input",{model:{value:e.formData.ggoodsName,callback:function(t){e.$set(e.formData,"ggoodsName",t)},expression:"formData.ggoodsName"}})],1),t("el-form-item",{attrs:{label:"商品规格",prop:"specName"}},[t("el-input",{model:{value:e.formData.specName,callback:function(t){e.$set(e.formData,"specName",t)},expression:"formData.specName"}})],1),"桶装水"==e.formData.classifyName?t("el-form-item",{attrs:{label:"桶类型"}},[t("div",{staticClass:"tabBox flex align-items-center"},[t("div",{class:[0===e.formData.bucketType?"actTabBox":""],on:{click:function(t){return e.chooseBucketType(0,"add")}}},[e._v("\n                常规桶\n              ")]),t("div",{class:[1==e.formData.bucketType?"actTabBox":""],on:{click:function(t){return e.chooseBucketType(1,"add")}}},[e._v("\n                一次性桶\n              ")])])]):e._e(),t("el-form-item",{attrs:{label:"商品图片",prop:"imgList"}},[t("el-upload",{ref:"upload1",attrs:{action:"https://szmsh.waterstation.com.cn/szm/uploadcontroller/upload","list-type":"picture-card","on-preview":e.handlePictureCardPreview1,"on-remove":e.handleRemove1,"on-success":e.uploadImg1,"on-error":e.uploadError,"on-exceed":e.uploadExceed,multiple:!0,accept:".jpg, .png",limit:4}},[t("i",{staticClass:"el-icon-plus"})]),t("el-dialog",{attrs:{visible:e.specImgsModal1},on:{"update:visible":function(t){e.specImgsModal1=t}}},[t("img",{attrs:{width:"100%",src:e.dialogImageUrl,alt:""}})])],1),t("el-form-item",{attrs:{label:"商品标签",prop:"cost"}},[t("div",{staticClass:"tabBox flex align-items-center wrap"},e._l(e.goodsLabelList,(function(a,o){return t("div",{key:o,class:[e.formData.goodsLabel==a.storeLable?"actTabBox":""],on:{click:function(t){return e.chooseGoodsLabel(a.storeLable,"add")}}},[e._v("\n                "+e._s(a.lableName)+"\n              ")])})),0)]),t("el-form-item",{attrs:{label:"商品描述",prop:"productDescribess"}},[t("el-input",{attrs:{type:"textarea",placeholder:"请输入内容",maxlength:"17","show-word-limit":""},model:{value:e.formData.productDescribess,callback:function(t){e.$set(e.formData,"productDescribess",t)},expression:"formData.productDescribess"}})],1),t("el-form-item",{attrs:{label:"返现金返还比例"}},[t("el-input",{attrs:{placeholder:"请输入排序"},model:{value:e.formData.returnrate,callback:function(t){e.$set(e.formData,"returnrate",t)},expression:"formData.returnrate"}})],1),t("div",{staticClass:"text-align-center font-size-16 padding-tb-10 margin-top-20 margin-bottom-20",staticStyle:{"background-color":"#F5F5F5"}},[e._v("\n            价格库存\n          ")]),t("div",{staticClass:"color-red",staticStyle:{"font-size":"14px","padding-bottom":"10px"}},[e._v("\n            成本价仅由水站自己可见，零售价是客户可见的价格。系统可自动根据设置的价格库存信息进行营业分析。\n          ")]),t("div",[t("div",{staticClass:"flex align-items-center justify-content-between margin-bottom-20"},[t("div",{staticClass:"flex align-items-center"},[t("span",{staticClass:"box-sizing padding-right-10 text-align-right",staticStyle:{width:"90px"}},[e._v("零售价")]),t("el-input",{nativeOn:{keyup:function(t){e.formData.retail=e.oninputPrice(e.formData.retail,2)}},model:{value:e.formData.retail,callback:function(t){e.$set(e.formData,"retail","string"===typeof t?t.trim():t)},expression:"formData.retail"}})],1),t("div",{staticClass:"flex align-items-center"},[t("span",{staticClass:"box-sizing padding-right-10 text-align-right",staticStyle:{width:"90px"}},[e._v("成本价")]),t("el-input",{nativeOn:{keyup:function(t){e.formData.cost=e.oninputPrice(e.formData.cost,2)}},model:{value:e.formData.cost,callback:function(t){e.$set(e.formData,"cost",t)},expression:"formData.cost"}})],1)]),t("div",{staticClass:"flex align-items-center justify-content-between margin-top-20"},[t("el-form-item",{staticStyle:{width:"45%"},attrs:{label:"毛利"}},[Number(e.formData.retail)-Number(e.formData.cost)>=0?t("span",{staticClass:"color-green"},[e._v(e._s(Number(e.formData.retail)-Number(e.formData.cost)))]):t("span",{staticClass:"color-red"},[e._v(e._s(Number(e.formData.retail)-Number(e.formData.cost)))])])],1),t("div",{staticClass:"flex align-items-center justify-content-between margin-top-20"},[t("div",{staticClass:"flex align-items-center"},[t("span",{staticClass:"box-sizing padding-right-10 text-align-right",staticStyle:{width:"90px"}},[e._v("实际库存")]),t("el-input",{attrs:{"οnkeyup":"value=(value.replace(/\\D/g,'')==''?'':parseInt(value))"},on:{input:e.countGoodsVirtualStock},model:{value:e.formData.stock,callback:function(t){e.$set(e.formData,"stock",t)},expression:"formData.stock"}})],1),t("div",{staticClass:"flex align-items-center"},[t("span",{staticClass:"box-sizing padding-right-10 text-align-right",staticStyle:{width:"90px"}},[e._v("虚拟库存")]),t("el-input",{attrs:{"οnkeyup":"value=(value.replace(/\\D/g,'')==''?'':parseInt(value))"},model:{value:e.formData.virtualStock,callback:function(t){e.$set(e.formData,"virtualStock",t)},expression:"formData.virtualStock"}})],1)])])],1)],1),t("div",{staticClass:"dialog-footer text-align-center",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.addGoodsModal=!1}}},[e._v("取 消")]),t("el-button",{attrs:{type:"primary"},on:{click:e.specSubmit}},[e._v("确 定")])],1)]),t("el-dialog",{attrs:{visible:e.editModal,width:"700px","before-close":e.handleClose},on:{"update:visible":function(t){e.editModal=t}}},[t("div",{staticClass:"text-align-center font-size-30 padding-tb-20 border-bottom"},[e._v("\n        商品编辑\n      ")]),t("div",{staticClass:"text-align-center font-size-16 padding-tb-10 margin-top-20 margin-bottom-20",staticStyle:{"background-color":"#F5F5F5"}},[e._v("\n        基本信息\n      ")]),t("div",[t("el-form",{ref:"form",attrs:{model:e.editFormData,"label-width":"120px"}},[t("div",{staticStyle:{color:"red","font-weight":"600",height:"50px","line-height":"50px","margin-left":"20px"}},[e._v("如果存在水票，对应的水票也会更新为对应修改的商品")]),t("el-form-item",{attrs:{label:"商品分类"}},[t("el-select",{attrs:{placeholder:"商品分类"},model:{value:e.editFormData.classifyId,callback:function(t){e.$set(e.editFormData,"classifyId",t)},expression:"editFormData.classifyId"}},e._l(e.classifyOptions,(function(e){return t("el-option",{key:e.id,attrs:{label:e.classifyName,value:e.productClassifyId}})})),1)],1),t("el-form-item",{attrs:{label:"商品品牌",prop:"brandId"}},[t("el-select",{attrs:{placeholder:"商品品牌",filterable:""},on:{change:e.brandChange},model:{value:e.editFormData.brandId,callback:function(t){e.$set(e.editFormData,"brandId",t)},expression:"editFormData.brandId"}},e._l(e.bucketBrandList,(function(e){return t("el-option",{key:e.brandId,attrs:{label:e.brandName,value:e.brandId}})})),1)],1),t("el-form-item",{attrs:{label:"创建水票",prop:"isWater"}},[t("el-select",{attrs:{placeholder:"创建水票"},model:{value:e.editFormData.isWater,callback:function(t){e.$set(e.editFormData,"isWater",t)},expression:"editFormData.isWater"}},[t("el-option",{attrs:{label:"不创建",value:0}}),t("el-option",{attrs:{label:"创建",value:1}})],1)],1),t("el-form-item",{attrs:{label:"商品名称"}},[t("el-input",{model:{value:e.editFormData.commodityName,callback:function(t){e.$set(e.editFormData,"commodityName",t)},expression:"editFormData.commodityName"}})],1),t("el-form-item",{attrs:{label:"商品规格"}},[t("el-input",{model:{value:e.editFormData.specTag,callback:function(t){e.$set(e.editFormData,"specTag",t)},expression:"editFormData.specTag"}})],1),"桶装水"==e.editFormData.classifyName?t("el-form-item",{attrs:{label:"桶类型"}},[0==e.editFormData.spuType?[t("div",{staticClass:"tabBox flex align-items-center"},[t("div",{class:[0==e.editFormData.bucketType?"actTabBox":""],on:{click:function(t){return e.chooseBucketType(0,"edit")}}},[e._v("\n                  常规桶\n                ")]),t("div",{class:[1==e.editFormData.bucketType?"actTabBox":""],on:{click:function(t){return e.chooseBucketType(1,"edit")}}},[e._v("\n                  一次性桶\n                ")])])]:[t("span",[e._v("\n                "+e._s(1==e.editFormData.bucketType?"一次性桶":"常规桶")+"\n              ")])]],2):e._e(),t("el-form-item",{attrs:{label:"商品图片"}},[t("el-upload",{attrs:{action:"https://szmsh.waterstation.com.cn/szm/uploadcontroller/upload","list-type":"picture-card","file-list":e.editFormData.specImgs,"on-success":e.uploadImg2,"on-remove":e.handleRemove2,"on-error":e.uploadError,"on-exceed":e.uploadExceed,multiple:!0,limit:4}},[t("i",{staticClass:"el-icon-plus"})]),t("el-dialog",{attrs:{visible:e.specImgsModal2},on:{"update:visible":function(t){e.specImgsModal2=t}}},[t("img",{attrs:{width:"100%",src:e.editFormData.specImgs,alt:""}})])],1),t("el-form-item",{attrs:{label:"商品标签",prop:"cost"}},[t("div",{staticClass:"tabBox flex align-items-center wrap"},e._l(e.goodsLabelList,(function(a,o){return t("div",{key:o,class:[e.editFormData.goodsLabel==a.storeLable?"actTabBox":""],on:{click:function(t){return e.chooseGoodsLabel(a.storeLable,"edit")}}},[e._v("\n                "+e._s(a.lableName)+"\n              ")])})),0)]),t("el-form-item",{attrs:{label:"排序"}},[t("el-input",{attrs:{placeholder:"请输入排序"},model:{value:e.editFormData.productOrderNum,callback:function(t){e.$set(e.editFormData,"productOrderNum",t)},expression:"editFormData.productOrderNum"}})],1),t("el-form-item",{attrs:{label:"商品描述"}},[t("el-input",{attrs:{type:"textarea",placeholder:"请输入内容",maxlength:"17","show-word-limit":""},model:{value:e.editFormData.goodsDescribe,callback:function(t){e.$set(e.editFormData,"goodsDescribe",t)},expression:"editFormData.goodsDescribe"}})],1),t("el-form-item",{attrs:{label:"水票优惠描述"}},[t("el-input",{attrs:{type:"textarea",placeholder:"请输入内容",maxlength:"17","show-word-limit":""},model:{value:e.editFormData.waterRemarks,callback:function(t){e.$set(e.editFormData,"waterRemarks",t)},expression:"editFormData.waterRemarks"}})],1),t("el-form-item",{attrs:{label:"返现金返还比例"}},[t("el-input",{attrs:{placeholder:"请输入排序"},model:{value:e.editFormData.returnrate,callback:function(t){e.$set(e.editFormData,"returnrate",t)},expression:"editFormData.returnrate"}})],1),t("div",{staticClass:"text-align-center font-size-16 padding-tb-10 margin-top-20 margin-bottom-20",staticStyle:{"background-color":"#F5F5F5"}},[e._v("\n            价格库存\n          ")]),t("div",{staticClass:"color-red",staticStyle:{"font-size":"14px","padding-bottom":"10px"}},[e._v("\n            成本价仅由水站自己可见，零售价是客户可见的价格。系统可自动根据设置的价格库存信息进行营业分析。\n          ")]),t("div",{staticClass:"flex align-items-center justify-content-between margin-bottom-20"},[t("div",{staticClass:"flex align-items-center"},[t("span",{staticClass:"box-sizing padding-right-10 text-align-right",staticStyle:{width:"90px"}},[e._v("零售价格")]),t("el-input",{on:{input:e.getRetail2},model:{value:e.editFormData.retail,callback:function(t){e.$set(e.editFormData,"retail",t)},expression:"editFormData.retail"}})],1),t("div",{staticClass:"flex align-items-center"},[t("span",{staticClass:"box-sizing padding-right-10 text-align-right",staticStyle:{width:"90px"}},[e._v("成本价格")]),t("el-input",{on:{input:e.getCost2},model:{value:e.editFormData.cost,callback:function(t){e.$set(e.editFormData,"cost",t)},expression:"editFormData.cost"}})],1)]),t("div",{staticClass:"flex align-items-center justify-content-between margin-bottom-20"},[t("el-form-item",{staticStyle:{width:"45%"},attrs:{label:"毛利"}},[e.editFormData.profit>=0?t("span",{staticClass:"color-green"},[e._v("\n                "+e._s(e.editFormData.profit)+"\n              ")]):t("span",{staticClass:"color-red"},[e._v(e._s(e.editFormData.profit))])])],1),t("div",{staticClass:"flex align-items-center justify-content-between margin-bottom-20"},[t("div",{staticClass:"flex align-items-center"},[t("span",{staticClass:"box-sizing padding-right-10 text-align-right",staticStyle:{width:"90px"}},[e._v("实际库存")]),t("el-input",{attrs:{oninput:"value=value.replace(/[^\\d]/g,'')"},on:{input:e.countEditGoodsvirtualStock},model:{value:e.editFormData.stock,callback:function(t){e.$set(e.editFormData,"stock",t)},expression:"editFormData.stock"}})],1),t("div",{staticClass:"flex align-items-center"},[t("span",{staticClass:"box-sizing padding-right-10 text-align-right",staticStyle:{width:"90px"}},[e._v("虚拟库存")]),t("el-input",{attrs:{oninput:"value=value.replace(/[^\\d]/g,'')"},model:{value:e.editFormData.virtualStock,callback:function(t){e.$set(e.editFormData,"virtualStock",t)},expression:"editFormData.virtualStock"}})],1)])],1)],1),t("span",{staticClass:"dialog-footer text-align-center",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.editModal=!1}}},[e._v("取 消")]),t("el-button",{attrs:{type:"primary"},on:{click:e.editSubmit}},[e._v("确 定")])],1)]),t("el-dialog",{attrs:{title:"商品编辑",visible:e.serviceModal,width:"30%","before-close":e.handleClose},on:{"update:visible":function(t){e.serviceModal=t}}},[t("div",[t("el-form",{ref:"form",attrs:{model:e.serviceFormData,"label-width":"120px"}},[t("el-form-item",{attrs:{label:"服务详情图"}},[t("el-upload",{attrs:{action:"https://szmsh.waterstation.com.cn/szm/uploadcontroller/upload","list-type":"picture-card","file-list":e.serviceFormData.serviceImgs,"on-success":e.uploadImg3,"on-remove":e.handleRemove3,"on-error":e.uploadError,"on-exceed":e.uploadExceed,multiple:!0,limit:3}},[t("i",{staticClass:"el-icon-plus"})]),t("el-dialog",{attrs:{visible:e.serviceImgPreview},on:{"update:visible":function(t){e.serviceImgPreview=t}}},[t("img",{attrs:{width:"100%",src:e.serviceFormData.imgList,alt:""}})])],1),t("el-form-item",{attrs:{label:"服务名称"}},[t("el-input",{model:{value:e.serviceFormData.name,callback:function(t){e.$set(e.serviceFormData,"name",t)},expression:"serviceFormData.name"}})],1),t("el-form-item",{attrs:{label:"服务价格"}},[t("el-input",{model:{value:e.serviceFormData.price,callback:function(t){e.$set(e.serviceFormData,"price",t)},expression:"serviceFormData.price"}})],1)],1)],1),t("span",{staticClass:"dialog-footer text-align-center",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.serviceModal=!1}}},[e._v("取 消")]),t("el-button",{attrs:{type:"primary"},on:{click:e.serviceSubmit}},[e._v("确 定")])],1)]),t("el-dialog",{attrs:{visible:e.groupModal,width:"30%","before-close":e.handleClose},on:{"update:visible":function(t){e.groupModal=t}}},[t("div",{staticClass:"text-align-center font-size-30 padding-tb-20 border-bottom"},[e._v("\n        "+e._s(e.groupFormData.id?"编辑套餐":"新增套餐")+"\n      ")]),t("div",{staticClass:"text-align-center font-size-16 padding-tb-10 margin-top-20 margin-bottom-20",staticStyle:{"background-color":"#F5F5F5"}},[e._v("\n        基本信息\n      ")]),t("div",[t("el-form",{ref:"groupFormDataElement",attrs:{model:e.groupFormData,"label-width":"120px"}},[t("el-form-item",{attrs:{label:"套餐名称",prop:"name"}},[t("el-input",{model:{value:e.groupFormData.name,callback:function(t){e.$set(e.groupFormData,"name",t)},expression:"groupFormData.name"}})],1),t("el-form-item",{attrs:{label:"排序(越小靠前)",prop:"productOrderNum"}},[t("el-input",{model:{value:e.groupFormData.productOrderNum,callback:function(t){e.$set(e.groupFormData,"productOrderNum",t)},expression:"groupFormData.productOrderNum"}})],1),t("el-form-item",{attrs:{prop:"list",label:"套餐商品"}},[t("div",{staticClass:"imgList flex align-items-center justify-content-between cursor-pointer",on:{click:e.chooseProduct}},[t("div",[0==e.groupFormData.list.length?e._l(4,(function(a){return t("el-badge",{key:a,staticClass:"imgBadge",attrs:{value:0}},[t("el-image",{attrs:{src:e.imgUri+"/images/defaultMeal.png",fit:"contain"}})],1)})):e._l(e.groupFormData.list,(function(e){return t("el-badge",{key:e.skuId,staticClass:"imgBadge",attrs:{value:e.num}},[t("el-image",{attrs:{src:e.content,fit:"contain"}})],1)}))],2),t("div",[e._v("\n                请选择\n                "),t("i",{staticClass:"el-icon-arrow-right"})])])]),t("el-form-item",{attrs:{label:"商品图"}},[t("div",{staticClass:"flex align-items-center"},[t("div",[0!=e.groupFormData.list.length?t("canvas",{ref:"myCanvas",attrs:{width:"112",height:"112"}}):t("el-image",{staticClass:"myCanvas",attrs:{src:e.imgUri+"/images/defaultImg.png",fit:"fill"}})],1),t("div",[t("span",{staticClass:"color-grey",staticStyle:{"margin-left":"30px"}},[e._v("选择好要组合的商品后，自动生成组合的商品图")])])])]),t("div",{staticClass:"text-align-center font-size-16 padding-tb-10 margin-top-20 margin-bottom-20",staticStyle:{"background-color":"#F5F5F5"}},[e._v("\n            价格库存\n          ")]),t("div",{staticClass:"flex align-items-center margin-bottom-20"},[t("div",[t("span",[e._v("总成本：")]),t("span",{staticClass:"margin-left-30"},[e._v(e._s(e.groupFormData.goodsCost)+"元")])]),t("div",{staticStyle:{"margin-left":"200px"}},[t("span",[e._v("总零售：")]),t("span",{staticClass:"margin-left-30"},[e._v(e._s(e.groupFormData.goodsRetail)+"元")])])]),t("el-form-item",{attrs:{label:"套餐价格",prop:"retail"}},[t("el-input",{attrs:{oninput:"value=value.replace(/[^\\d.]/g,'')"},model:{value:e.groupFormData.retail,callback:function(t){e.$set(e.groupFormData,"retail",t)},expression:"groupFormData.retail"}})],1),t("el-form-item",{directives:[{name:"show",rawName:"v-show",value:e.groupFormData.retail,expression:"groupFormData.retail"}]},[e.countGroupProfit>=0?t("span",{staticClass:"color-blue bold"},[e._v("盈利："+e._s(e.countGroupProfit)+" 元")]):t("span",{staticClass:"color-blue bold"},[e._v("亏损："+e._s(e.countGroupProfit)+" 元")])])],1)],1),t("el-dialog",{attrs:{width:"50%",title:"选择商品",visible:e.productModal,"append-to-body":""},on:{"update:visible":function(t){e.productModal=t}}},[t("div",{staticClass:"flex align-items-center"},[t("el-input",{staticStyle:{width:"200px","margin-right":"10px"},attrs:{placeholder:"请输入商品或规格名称",clearable:""},on:{clear:e.chooseProduct},model:{value:e.productSearchKey,callback:function(t){e.productSearchKey=t},expression:"productSearchKey"}}),t("el-button",{attrs:{type:"primary"},on:{click:e.toSearchProduct}},[e._v("搜索")])],1),t("el-table",{ref:"productElement",staticClass:"productList",staticStyle:{width:"100%"},attrs:{data:e.productList,"max-height":e.inTableHeight,"header-cell-style":{"text-align":"center"},"cell-style":{"text-align":"center","font-size":"13px",color:"#333C48"}},on:{select:e.productCheck,"selection-change":e.changeSelect}},[t("el-table-column",{attrs:{type:"selection",width:"54"}}),t("el-table-column",{attrs:{prop:"",label:"商品图",width:"100"},scopedSlots:e._u([{key:"default",fn:function(e){return[t("img",{staticStyle:{width:"75px",height:"75px"},attrs:{src:e.row.content,alt:""}})]}}])}),t("el-table-column",{attrs:{prop:"spuName",label:"名称"}}),t("el-table-column",{attrs:{prop:"skuName",label:"规格"}}),t("el-table-column",{attrs:{prop:"price",label:"成本价"}}),t("el-table-column",{attrs:{prop:"newPrice",label:"零售价"}}),t("el-table-column",{attrs:{prop:"inventory",label:"库存"}}),t("el-table-column",{attrs:{prop:"num",label:"数量",width:"150"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-input-number",{attrs:{min:1,label:"输入数量"},model:{value:a.row.num,callback:function(t){e.$set(a.row,"num",t)},expression:"scope.row.num"}})]}}])})],1),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.productModal=!1}}},[e._v("取 消")]),t("el-button",{attrs:{type:"primary"},on:{click:e.productSubmit}},[e._v("确 定")])],1)],1),t("div",{staticClass:"dialog-footer text-align-center",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.groupModal=!1}}},[e._v("取 消")]),t("el-button",{directives:[{name:"loading",rawName:"v-loading",value:e.groupSubmitLoading,expression:"groupSubmitLoading"}],attrs:{type:"primary"},on:{click:e.groupSubmit}},[e._v("确 定")])],1)],1),t("el-dialog",{attrs:{title:"押桶管理",visible:e.bucketDialog,width:"40%"},on:{"update:visible":function(t){e.bucketDialog=t}}},[t("div",[t("el-table",{staticStyle:{width:"100%"},attrs:{data:e.bucketList,"header-cell-style":{"text-align":"center"},"cell-style":{"text-align":"center","font-size":"13px",color:"#333C48"},"max-height":e.inTableHeight}},[t("el-table-column",{attrs:{prop:"",label:"商品图",width:"100"},scopedSlots:e._u([{key:"default",fn:function(e){return[t("img",{staticStyle:{width:"75px",height:"75px"},attrs:{src:e.row.img,alt:""}})]}}])}),t("el-table-column",{attrs:{prop:"brandName",label:"桶品牌"}}),t("el-table-column",{attrs:{prop:"",label:"押桶金额",width:"180"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-input",{attrs:{placeholder:"请输入押桶金额"},model:{value:a.row.money,callback:function(t){e.$set(a.row,"money",e._n(t))},expression:"scope.row.money"}})]}}])})],1)],1),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.bucketDialog=!1}}},[e._v("取 消")]),t("el-button",{attrs:{type:"primary"},on:{click:e.bucketSetSubmit}},[e._v("确 定")])],1)]),t("el-dialog",{attrs:{title:"排序",visible:e.paixuShow,width:"40%"},on:{"update:visible":function(t){e.paixuShow=t}}},[t("el-form",{ref:"paixuForm",attrs:{model:e.paixuForm,"label-width":"120px"}},[t("el-form-item",{attrs:{label:"排序(越小越靠前)",prop:"productOrderNum"}},[t("el-input",{model:{value:e.paixuForm.productOrderNum,callback:function(t){e.$set(e.paixuForm,"productOrderNum",t)},expression:"paixuForm.productOrderNum"}})],1)],1),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.paixuShow=!1}}},[e._v("取 消")]),t("el-button",{attrs:{type:"primary"},on:{click:e.paixuSubmit}},[e._v("确 定")])],1)],1),e.goodsdiscountVisible?t("goodsdiscount",{ref:"goodsdiscount",on:{refreshDataList:e.load}}):e._e()],1)},s=[],r=function(){var e=this,t=e._self._c;return t("el-dialog",{attrs:{title:"修改状态","close-on-click-modal":!1,visible:e.visible},on:{"update:visible":function(t){e.visible=t}}},[t("div",{staticClass:"operation-row"},[t("el-button",{attrs:{type:"primary"},on:{click:e.handleAdd}},[e._v("添加区间")])],1),t("el-form",{directives:[{name:"show",rawName:"v-show",value:e.dialogVisible,expression:"dialogVisible"}],ref:"discountForm",attrs:{model:e.currentDiscount,rules:e.rules,inline:""}},[t("el-form-item",{attrs:{label:"满",prop:"start"}},[t("el-input-number",{model:{value:e.currentDiscount.start,callback:function(t){e.$set(e.currentDiscount,"start",t)},expression:"currentDiscount.start"}}),e._v("件\n  ")],1),t("el-form-item",{attrs:{label:"售价",prop:"end"}},[t("el-input-number",{attrs:{precision:2},model:{value:e.currentDiscount.end,callback:function(t){e.$set(e.currentDiscount,"end",t)},expression:"currentDiscount.end"}}),e._v("%\n  ")],1),t("el-form-item",[t("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v(e._s(-1===e.editIndex?"添加":"更新"))])],1)],1),t("el-table",{attrs:{data:e.discount,border:""}},[t("el-table-column",{attrs:{label:"满",prop:"0"}}),t("el-table-column",{attrs:{label:"售价的%",prop:"1"}}),t("el-table-column",{attrs:{label:"操作",width:"150"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleEdit(a.row,a.$index)}}},[e._v("编辑")]),t("el-button",{staticStyle:{color:"red"},attrs:{type:"text"},on:{click:function(t){return e.handleDelete(a.$index)}}},[e._v("删除")])]}}])})],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.visible=!1}}},[e._v("取消")]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.dataFormSubmit()}}},[e._v("确定")])],1)],1)},i=[],l=a("81d3"),n={data:function(){return{discount:[],productId:"",verifyStatus:l["b"],visible:!1,product:{repeatToken:"",productId:"",verify:"",discount:"",szmCProductModel:{},header:"json"},dataRule:{shangjia:[{required:!0,message:"商家抽佣必须填写",trigger:"blur"},{validator:function(e,t,a){var o=/^0|([1-9]\d?)(\.\d{1,2})?$/;o.test(t)?a():a(new Error("请输入0到99之间的数值，最多保留两位小数"))},trigger:["blur","change"]}]},dialogVisible:!1,dialogTitle:"添加区间",currentDiscount:{start:0,end:0},editIndex:-1,rules:{start:[{required:!0,message:"请输入数量",trigger:"blur"},{validator:function(e,t,a){var o=/^[1-9]\d*$/;o.test(t)?a():a(new Error("请输入数字"))},trigger:["blur","change"]}],end:[{required:!0,message:"请输入折扣",trigger:"blur"},{validator:function(e,t,a){var o=/^0|([1-9]\d?)(\.\d{1,2})?$/;o.test(t)?a():a(new Error("请输入0到99之间的数值，最多保留两位小数"))},trigger:["blur","change"]}]}}},methods:{init:function(e){var t=this;this.productId=e||0,this.visible=!0,this.$nextTick((function(){t.discount=[],t.product={repeatToken:"",productId:"",discount:"",szmCProductModel:{},header:"json"},t.$get("szmcproduct/info",{id:t.productId}).then((function(e){1===e.code&&(t.product=e.data,t.product.discount&&(t.discount=t.product.discount.split(";"),t.discount=t.discount.map((function(e){return[e.split(",")[0],e.split(",")[1]]}))))}))}))},getToken:function(){var e=this;this.$http({url:this.$http.adornUrl("/common/createToken"),method:"get",params:this.$http.adornParams()}).then((function(t){var a=t.data;a&&200===a.code&&(e.dataForm.repeatToken=a.result)}))},dataFormSubmit:function(){var e=this;this.product.header="json",this.product.discount=this.discount.map((function(e){return e.join(",")})).join(";"),this.$post("szmcproduct/update",this.product).then((function(t){1===t.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.visible=!1,e.$emit("refreshDataList")}}):e.$message.error(t.data)}))},handleAdd:function(){this.dialogTitle="添加区间",this.currentDiscount={start:0,end:0},this.editIndex=-1,this.dialogVisible=!0},handleEdit:function(e,t){this.dialogTitle="编辑区间",this.currentDiscount={start:e[0],end:e[1]},this.editIndex=t,this.dialogVisible=!0},handleDelete:function(e){var t=this;this.$confirm("确认删除该区间?","提示",{type:"warning"}).then((function(){t.discount.splice(e,1),t.$message.success("删除成功")})).catch((function(){}))},submitForm:function(){var e=this;this.$refs.discountForm.validate((function(t){if(t){var a=e.currentDiscount,o=a.start,s=a.end;if(o>=s)return void e.$message.error("起始值必须小于结束值");var r=[o,s];-1===e.editIndex?e.discount.push(r):e.discount.splice(e.editIndex,1,r),e.discount.sort((function(e,t){return e[0]-t[0]})),e.dialogVisible=!1,e.$message.success("保存成功")}}))}}},c=n,d=a("0c7c"),u=Object(d["a"])(c,r,i,!1,null,null,null),m=u.exports,p={props:{},data:function(){return{storeId:"",goodsdiscountVisible:!1,paixuShow:!1,paixuForm:{productId:"",productOrderNum:""},imgUri:this.$imgUri,list:[],page:1,pageSize:10,pageCount:0,bucketBrandList:[],classifyOptions:[],goodsLabelList:[],shelfList:[{name:"已上架",value:1},{name:"未上架",value:2}],recommendList:[{name:"已推荐",value:1},{name:"未推荐",value:2}],selectData:{classId:"",brandId:"",state:"",shelfState:"",isKey:""},addGoodsModal:!1,formData:{classifyName:"",brandName:"",specName:"",ggoodsName:"",productDescribess:"",bucketType:"",bucketTypeFlag:!1,imgList:"",goodsLabel:"",retail:"",crossedPrice:"",cost:"",stock:"",virtualStock:"",brandId:"",isWater:0,returnrate:0,usercomeUpFee:0,comeUpFee:0,numberFee:0,tudiFee:0,yewuFee:0},dialogImageUrl:"",specImgsModal1:!1,editModal:!1,editFormData:{classifyName:"",specImgs:[],spuType:0,imgList:[],brandsName:"",specTag:"",retail:"",cost:"",crossedPrice:"",profit:"0.00",stock:"",virtualStock:"",isWater:0,isJifen:"",jifen:"",source:"",skuId:"",brandId:"",returnrate:0,usercomeUpFee:0,comeUpFee:0,numberFee:0,tudiFee:0,yewuFee:0},specImgsModal2:!1,serviceModal:!1,serviceImgPreview:!1,serviceFormData:{serviceImgs:[],imgList:[],name:"",price:"",productServiceId:0},groupModal:!1,groupFormData:{id:0,list:[],img:"",name:"",goodsCost:0,goodsRetail:0,productOrderNum:0,retail:""},groupSubmitLoading:!1,productModal:!1,productList:[],productSearchKey:"",checkList:[],moduleList:{},bucketDialog:!1,bucketList:[],restaurants:[],canvasList:[],canvasImgSize:{}}},computed:{countGroupProfit:function(){var e=this.groupFormData.goodsCost,t=this.groupFormData.retail;if(""!=t){var a=this.$util.sub(t,e);return a}return""},tableHeight:function(){console.log(this.$store.getters.getGlobalHeight);var e=Number(this.$store.getters.getGlobalHeight)-300;return e>=300?e:300},inTableHeight:function(){var e=this.$store.getters.getGlobalHeight;return e>=400?parseInt(this.$util.mul(e,.5)):400},canvasImgSizeCom:function(){var e=this.canvasImgSize,t=0;return this.groupFormData.list.forEach((function(e){e.num>2?t++:t+=e.num})),1==t?(e.width=100,e.fontSize=30):t>=2&&t<=4?(e.width=50,e.fontSize=20):t>4&&(e.width=25,e.fontSize=16),e}},created:function(){},mounted:function(){var e=this;e.storeId=e.Cookies.get("storeId"),e.load(),e.lookUpBrand(),e.lookUpClassify(),e.lookUpGoodsLabel();var t=JSON.parse(this.Cookies.get("moduleList"));e.moduleList=t,e.loadAll()},watch:{groupModal:function(e){e||(this.checkList=[])}},methods:{oninputPrice:function(e,t){var a=e,o=a.substr(0,1),s=a.substr(1,1);if(a.length>1&&0==o&&"."!=s&&(a=a.substr(1,1)),"."==o&&(a=""),-1!=a.indexOf(".")){var r=a.substr(a.indexOf(".")+1);-1!=r.indexOf(".")&&(a=a.substr(0,a.indexOf(".")+r.indexOf(".")+1))}return a=a.replace(/[^\d^\.]+/g,""),a=t/1===1?a.replace(/^\D*([0-9]\d*\.?\d{0,1})?.*$/,"$1"):a.replace(/^\D*([0-9]\d*\.?\d{0,2})?.*$/,"$1"),a},querySearch:function(e,t){var a=this.restaurants,o=e?a.filter(this.createFilter(e)):a;t(o)},createFilter:function(e){return function(t){return console.log("ddd",t),0===t.value.toLowerCase().indexOf(e.toLowerCase())}},loadAll:function(){var e=this;this.$get("szmcproductclassifycontroller/getAllClassifys",{storeId:e.Cookies.get("storeId")}).then((function(t){if(1===t.code){var a=t.data,o=[];a.forEach((function(e){o.push({value:e.className})})),e.restaurants=o}else e.$message.error(t.data)})).catch((function(e){console.log(e)}))},lookUpBrand:function(){var e=this,t="szmb/szmbrandcontroller/selectallbrandbystore",a={storeId:e.Cookies.get("storeId")};e.$post(t,a).then((function(t){1==t.code?e.bucketBrandList=t.data:e.$message.error(t.data)}))},lookUpClassify:function(){var e=this;this.$get("szmcproductclassifycontroller/getClassifyList",{storeId:e.Cookies.get("storeId")}).then((function(t){1===t.code?e.classifyOptions=t.data:e.$message.error(t.data)})).catch((function(e){console.log(e)}))},lookUpGoodsLabel:function(){var e=this,t="/szmb/newinsertproductcontroller/selectstorelable";e.$post(t,{}).then((function(t){1==t.code&&(e.goodsLabelList=t.data)}))},clearSearch:function(){var e=this;e.selectData={classId:"",brandId:"",state:"",shelfState:"",isKey:""},e.page=1,e.load()},changePage:function(e){var t=this;t.page=e,t.load()},chooseBucketType:function(e,t){var a=this;"add"==t?a.$set(a.formData,"bucketType",e):a.$set(a.editFormData,"bucketType",e)},chooseGoodsLabel:function(e,t){var a=this;console.log(e,t),"add"==t?a.formData.goodsLabel==e?a.$set(a.formData,"goodsLabel",""):a.$set(a.formData,"goodsLabel",e):a.editFormData.goodsLabel==e?a.$set(a.editFormData,"goodsLabel",""):a.$set(a.editFormData,"goodsLabel",e)},checkClassify:function(){var e=this;console.log("check");var t="/szmb/newinsertproductcontroller/selectclasssname",a={storeId:e.Cookies.get("storeId"),className:e.formData.classifyName};e.$post(t,a).then((function(t){1==t.code&&e.$set(e.formData,"bucketTypeFlag",t.data)}))},load:function(){var e=this,t="/szmb/szmstoreshopcontroller/selectallshoppc",a={storeId:e.Cookies.get("storeId"),classId:e.selectData.classId?e.selectData.classId:0,brandId:e.selectData.brandId?e.selectData.brandId:0,recommendId:e.selectData.state?e.selectData.state:0,state:e.selectData.shelfState?e.selectData.shelfState:0,name:e.selectData.isKey,index:e.page,pageSize:e.pageSize};e.$post(t,a).then((function(t){console.log(t),1===t.code?(e.list=t.data.shopContents,e.pageCount=t.data.pageCount):(e.list=[],e.pageCount=0)}))},toSearch:function(){var e=this;e.page=1,e.load()},upShelf:function(e){console.log(e);var t=this,a=e.classMastId,o=e.commodityId,s=e.upperShelf,r=e.productOrderNum,i=[{commodityId:o,productOrderNum:r,recommend:1==s?0:1,classId:a}],l="/szmb/szmstoreshopcontroller/updateshopstate",n={storeId:t.Cookies.get("storeId"),list:i,header:"json"};t.$post(l,n).then((function(e){console.log(e),1===e.code?(t.$message({message:"操作成功！",type:"success"}),t.load()):t.$message.error(e.data)}))},paixuSubmit:function(){var e=this;if(null==this.paixuForm.productOrderNum||""==this.paixuForm.productOrderNum)return e.$message({message:"请输入序号",type:"error"}),!1;e.$get("/szmb/szmstoreshopcontroller/updatepaixu",this.paixuForm).then((function(t){console.log(t),1===t.code?(e.$message({message:"操作成功！",type:"success"}),e.paixuShow=!1,e.load()):e.$message.error(t.data)}))},upShow:function(e){var t=this,a=e.classMastId,o=e.commodityId,s=e.recommend,r=e.flag;if(r)t.$message({message:"限时商品不能被推荐",type:"warning"});else{var i=[{commodityId:o,recommend:1==s?0:1,classId:a}],l="/szmb/szmstoreshopcontroller/updateshoprecommend",n={storeId:t.Cookies.get("storeId"),list:i,header:"json"};t.$post(l,n).then((function(e){console.log(e),1===e.code?(t.$message({message:"操作成功！",type:"success"}),t.load()):t.$message.error(e.data)}))}},delGroup:function(e){var t=this,a=e.upperShelf;1!=a?t.$confirm("确认要删除该套餐?","温馨提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){var a="/szmb/szmshopgroupcontroller/delectshopgroup",o={id:e.commodityId,storeId:t.Cookies.get("storeId")};t.$post(a,o).then((function(e){1==e.code?(t.$message({message:"删除成功!",type:"success"}),t.load()):t.$message.error("删除失败")}))})).catch((function(){t.$message({type:"info",message:"已取消删除"})})):t.$message.error("已上架商品不能删除，请先下架~")},addNewGoods:function(){var e=this;e.formData={classifyName:"",brandName:"",specName:"",bucketType:"",bucketTypeFlag:!1,imgList:"",goodsLabel:"",retail:"",cost:"",crossedPrice:"",stock:"",virtualStock:"",brandId:"",isWater:0,returnrate:0,usercomeUpFee:0,comeUpFee:0,numberFee:0,tudiFee:0,yewuFee:0},e.loadAll(),e.addGoodsModal=!0,e.$nextTick((function(){e.$refs.upload1.clearFiles()}))},handleClose:function(e){this.$confirm("确认关闭？").then((function(t){e()})).catch((function(e){}))},editIt:function(e){console.log(e);var t=this,a=e.commodityId,o=(e.upperShelf,e.classId);if(7==e.classMastId){var s=e.imgList,r=s.map((function(e,t){var a={name:0==t?"主图":1==t?"规格图":2==t?"详情图":"",url:e};return a}));t.serviceFormData={serviceImgs:r,imgList:[],name:e.commodityName,price:e.unitPrice,productServiceId:e.commodityId},t.serviceModal=!0}if(6==e.classMastId){var i=e.commodityId;t.groupLoad(i)}else{var l="/szmb/szmstoreshopcontroller/selectspu",n={spuId:a,classId:o};t.$post(l,n).then((function(e){if(console.log(e),1==e.code){var a=e.data.addShopSkuLists[0].imgList;console.log(a);var o=a.filter((function(e){return e})).map((function(e,t){var a={name:0==t?"主图":1==t?"规格图":2==t?"详情图":"",url:e};return a}));t.editFormData={classifyName:e.data.className,bucketType:e.data.addShopSkuLists[0].buckType?e.data.addShopSkuLists[0].buckType:"",goodsLabel:e.data.addShopSkuLists[0].labelId,goodsDescribe:e.data.details,productOrderNum:e.data.productOrderNum,waterRemarks:e.data.waterRemarks,specImgs:o,spuType:e.data.spuType,imgList:[],specTag:e.data.addShopSkuLists[0].details,retail:e.data.addShopSkuLists[0].retail,cost:e.data.addShopSkuLists[0].cost,crossedPrice:e.data.addShopSkuLists[0].marketPrice,profit:t.$util.sub(e.data.addShopSkuLists[0].retail,e.data.addShopSkuLists[0].cost).toFixed(2),stock:e.data.addShopSkuLists[0].stock,virtualStock:e.data.addShopSkuLists[0].virtualStock,isWater:e.data.addShopSkuLists[0].isWater,isJifen:!e.data.addShopSkuLists[0].integralChecked,jifen:e.data.addShopSkuLists[0].malIntegral,source:e.data.addShopSkuLists[0].source,skuId:e.data.addShopSkuLists[0].skuId,brandId:e.data.brandId,brandName:e.data.brandName,classifyId:e.data.id,commodityName:e.data.commodityName,spuId:e.data.spuId,returnrate:e.data.returnrate,usercomeUpFee:e.data.usercomeUpFee,comeUpFee:e.data.comeUpFee,numberFee:e.data.numberFee,yewuFee:e.data.yewuFee,tudiFee:e.data.tudiFee},t.editModal=!0}else t.$message.error(e.data)}))}},getRetail2:function(e){e=e.replace(/[^\d.]/g,"");var t=this;t.editFormData.retail=e;var a=t.editFormData.retail,o=t.editFormData.cost;a&&o&&(t.editFormData.profit=t.$util.sub(a,o).toFixed(2))},getCost2:function(e){var t=this;e=e.replace(/[^\d.]/g,""),t.editFormData.cost=e;var a=t.editFormData.retail,o=t.editFormData.cost;a&&o&&(t.editFormData.profit=t.$util.sub(a,o).toFixed(2))},handleRemove2:function(e,t){var a=this;console.log(t),a.editFormData.imgList=t},uploadImg2:function(e,t,a){console.log(a);var o=this;o.editFormData.imgList=a},handlePictureCardPreview2:function(e){this.dialogImageUrl=e.url,this.specImgsModal2=!0},editSubmit:function(){var e=this,t=e.editFormData,a=[];a=t.imgList.length?t.imgList:t.specImgs;var o=a.map((function(e){var t=e.url.indexOf("https://waterstation.com.cn");return-1==t?"https://waterstation.com.cn/szm"+e.response.data:e.url})),s=[{imgList:o,details:t.specTag,cost:t.cost,retail:t.retail,stock:t.stock,marketPrice:t.crossedPrice,virtualStock:t.virtualStock,isWater:t.isWater,malIntegral:t.jifen,integralChecked:t.isJifen?0:1,source:t.source,skuId:t.skuId,buckDeatil:JSON.stringify({buckType:t.bucketType,label:t.goodsLabel})}];if(t.commodityName)if(o.length)if(t.specTag)if(t.cost)if(t.retail)if(t.stock)if(t.virtualStock)if(!t.isJifen||t.jifen)if(!t.returnrate||/^([1-9]\d?|0)(\.\d{1,2})?$/.test(t.returnrate)){var r={brandId:t.brandId,brandName:t.brandName,commodityName:t.commodityName,productDescribe:t.goodsDescribe,productOrderNum:t.productOrderNum,waterRemarks:t.waterRemarks,className:t.classifyName,id:t.classifyId,spuId:t.spuId,skuId:t.skuId,returnrate:t.returnrate,usercomeUpFee:t.usercomeUpFee,comeUpFee:t.comeUpFee,numberFee:t.numberFee,tudiFee:t.tudiFee,yewuFee:t.yewuFee,storeId:e.Cookies.get("storeId"),type:0,lists:s,header:"json"},i="/szmb/szmstoreshopcontroller/insertstoreshop";e.$post(i,r).then((function(t){console.log(t),1==t.code?(e.$message({message:"修改成功！",type:"success"}),e.editModal=!1,e.load()):3==t.code?e.$confirm(t.data,"提示",{confirmButtonText:"确定",showCancelButton:!1,type:"warning"}).then((function(){e.editModal=!1,e.load()})).catch((function(){e.editModal=!1,e.load()})):e.$message.error(t.data)}))}else e.$message.error("请填写返回消费金");else e.$message.error("请填写所需积分");else e.$message.error("请填写虚拟库存");else e.$message.error("请填写实际库存");else e.$message.error("请填写零售价格");else e.$message.error("请填写成本价格");else e.$message.error("请填写规格标签");else e.$message.error("至少上传一张商品图");else e.$message.error("请填写商品名称")},uploadError:function(){var e=this;e.$message.error("图片上传失败!")},uploadExceed:function(){var e=this;e.$message.error("图片数量已超上限!")},handleRemove1:function(e,t){console.log(e,t);var a=this,o=t.map((function(e){return"https://waterstation.com.cn/szm"+e.response.data}));a.formData.imgList=o},uploadImg1:function(e,t,a){console.log(a);var o=this,s=a.map((function(e){return"https://waterstation.com.cn/szm"+e.response.data}));o.formData.imgList=s},handlePictureCardPreview1:function(e){this.dialogImageUrl=e.url,this.specImgsModal1=!0},countGoodsVirtualStock:function(e){var t=this;e=e.replace(/[^\d]/g,""),t.formData.stock=e,t.formData.virtualStock=2*e},countEditGoodsvirtualStock:function(e){var t=this;e=e.replace(/[^\d]/g,""),t.editFormData.stock=e,t.editFormData.virtualStock=2*e},specSubmit:function(e){var t=this,a=this,o="1";if(a.formData.classifyName.length<2)a.$message.error("商品分类长度不能少于2位");else if(a.formData.brandId)if(a.formData.ggoodsName)if(a.formData.specName)if(1!=a.formData.bucketTypeFlag||""!==a.formData.bucketType)if(0!==a.formData.imgList.length)if(a.formData.cost)if(a.formData.classifyName)if("组合套餐"==a.formData.classifyName&&(o="2"),a.formData.retail)if(a.formData.stock)if(!a.formData.returnrate||/^([1-9]\d?|0)(\.\d{1,2})?$/.test(a.formData.returnrate)){var s="/szmb/szmstoreshopcontroller/insertstoreshop",r={storeId:a.Cookies.get("storeId"),id:0,classNmae:a.formData.classifyName,brandName:a.formData.brandName,brandId:a.formData.brandId,commodityName:a.formData.ggoodsName,details:a.formData.specName,spuId:0,type:0,productDescribe:a.formData.productDescribess,source:o,returnrate:a.formData.returnrate,usercomeUpFee:a.formData.usercomeUpFee,comeUpFee:a.formData.comeUpFee,numberFee:a.formData.numberFee,tudiFee:a.formData.tudiFee,yewuFee:a.formData.yewuFee,lists:[{imgList:a.formData.imgList,skuId:0,cost:a.formData.cost,marketPrice:a.formData.crossedPrice,retail:a.formData.retail,stock:a.formData.stock,virtualStock:a.formData.virtualStock,source:0,details:a.formData.specName,isWater:a.formData.isWater,integralChecked:1,buckDeatil:JSON.stringify({buckType:a.formData.bucketType,label:a.formData.goodsLabel})}],header:"json"};Number(a.formData.cost)>Number(a.formData.retail)?(this.$confirm("成本价格大于零售价格, 是否继续?","温馨提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){a.$post(s,r).then((function(e){console.log(e),1===e.code?(a.$message({message:"添加成功",type:"success"}),a.addGoodsModal=!1):"5005"==e.data?a.$message.error("新增商品页面不能添加组合套餐商品，请点击【新增套餐】去添加"):a.$message.error(e.data)}))})).catch((function(){t.$message({type:"info",message:"已取消"})})),a.$refs.addGoodsElement.resetFields(),a.load(),a.addGoodsModal=!1):a.$post(s,r).then((function(e){console.log(e),1===e.code?(a.$message({message:"添加成功",type:"success"}),a.$refs.addGoodsElement.resetFields(),a.load(),a.loadAll(),a.addGoodsModal=!1):"5005"==e.data?a.$message.error("新增商品页面不能添加组合套餐商品，请点击【新增套餐】去添加"):a.$message.error(e.data)}))}else a.$message.error("请填写返回消费金");else a.$message.error("请填写库存数量");else a.$message.error("请填写零售价");else a.$message.error("请填写商品分类");else a.$message.error("请填写成本价格");else a.$message.error("请选择至少1张图片");else a.$message.error("请选择桶类型");else a.$message.error("请填写规格名称");else a.$message.error("请输入商品名称");else a.$message.error("请输入商品品牌")},handleRemove3:function(e,t){var a=this;console.log(t),a.serviceFormData.imgList=t},uploadImg3:function(e,t,a){console.log(a);var o=this;o.serviceFormData.imgList=a},handlePictureCardPreview3:function(e){this.dialogImageUrl=e.url,this.serviceImgPreview=!0},serviceSubmit:function(){var e=this,t=e.serviceFormData,a=[];a=t.imgList.length?t.imgList:t.serviceImgs;var o,s,r=a.map((function(e){var t=e.url.indexOf("https://waterstation.com.cn");return-1==t?"https://waterstation.com.cn/szm"+e.response.data:e.url}));1==r.length?(o=r[0],s=r[0]+","+r[0]):2==r.length?(o=r[1],s=r[0]+","+r[1]):3==r.length&&(o=r[1],s=r[0]+","+r[2]);var i="";if(e.classifyOptions.forEach((function(e){7==e.classId&&(i=e.id)})),r.length)if(t.name)if(t.price){var l="/szmb/szmshopservicecontroller/insertservice",n={r3:o,serviceIntroduction:s,productServiceName:t.name,productPrice:t.price,r2:e.Cookies.get("storeId"),productClassifyId:i,productServiceId:t.productServiceId,header:"json"};e.$post(l,n).then((function(t){console.log(t),1==t.code?(e.$message({message:"操作成功！",type:"success"}),e.serviceModal=!1,e.load()):e.$message.error(t.data)}))}else e.$message.error("请输入服务价格");else e.$message.error("请输入服务名称");else e.$message.error("至少上传一张服务详情图")},addNewGroup:function(){var e=this;e.groupFormData={id:0,list:[],img:"",name:"",goodsCost:0,goodsRetail:0,productOrderNum:0,retail:""},e.groupModal=!0},groupSubmit:function(){var e=this,t=e.groupFormData;if(0==t.list.length&&e.$message.error("请选择需要组合的商品"),t.name)if(t.retail)if(Number(t.retail)>Number(t.goodsRetail))e.$message.error("套餐价不能大于总零售价");else{var a="/szmb/szmshopgroupcontroller/insertshopgroup",o=[],s=[];t.list.forEach((function(e){var t={skuId:e.skuId,num:e.num};s.push(t),o.push(e.groupImg)}));var r=e.dataURLtoFile(e.groupFormData.img),i=new FormData;i.append("file",r),e.$post("https://szmsh.waterstation.com.cn/szm/uploadcontroller/upload",i).then((function(r){if(console.log(r),1==r.code){var i="https://waterstation.com.cn/szm/"+r.data,l={list:s,price:t.retail,setmeal:t.name,imgList:o,specificationsId:t.id,storeId:e.Cookies.get("storeId"),type:"",url:i,productOrderNum:t.productOrderNum,header:"json"};e.$post(a,l).then((function(t){console.log(t),1==t.code?(e.$message({message:"保存成功",type:"success"}),e.$refs.groupFormDataElement.resetFields(),e.checkList=[],e.groupModal=!1,e.load()):e.$message.error(t.data)}))}else e.$message.error(r.data)}))}else e.$message.error("请填写组合商品的价格");else e.$message.error("请填写组合商品的名称")},groupLoad:function(e){var t=this,a="/szmb/szmshopgroupcontroller/selectshopgroupdeatil",o={shopGroupId:e};t.$post(a,o).then((function(a){if(console.log(a),1==a.code){var o=a.data.list,s=o.map((function(e){return e.shopGroupSkuAll})),r="",i="",l=[],n=0;o.forEach((function(e){r=t.$util.add(t.$util.mul(e.price,e.num),r),i=t.$util.add(t.$util.mul(e.newPrice,e.num),i),l.push(e),2==e.num?n+=2:n++})),t.groupFormData.list=s,t.checkList=s,t.groupFormData.goodsCost=r,t.groupFormData.goodsRetail=i,t.groupFormData.retail=a.data.price,t.groupFormData.name=a.data.setmeal,t.groupFormData.img=a.data.url,t.groupFormData.productOrderNum=a.data.productOrderNum,t.groupFormData.id=e,t.groupModal=!0,l.sort(t.compare("num")),t.canvasList=l,t.$nextTick((function(){t.makeImgCanvas(n)}))}else t.$message.error(a.data)}))},chooseProduct:function(){var e=this,t=this,a="/szmb/szmshopgroupcontroller/selectskulistall",o={storeId:t.Cookies.get("storeId")};t.$post(a,o).then((function(a){1==a.code?(t.productList=a.data,e.$nextTick((function(){var e=a.data,o=t.checkList;0!=o.length&&e.forEach((function(e){o.forEach((function(a){e.skuId==a.skuId&&(t.$refs.productElement.toggleRowSelection(e,!0),e.num=a.num)}))}))})),t.productModal=!0):t.$message.error(a.data)}))},changeSelect:function(e){var t=this;if(e.length>4)return t.$message({message:"最多只能组合四种商品!",type:"warning"}),!1},productCheck:function(e,t){var a=this;console.log(e);var o=-1!=e.indexOf(t);o?a.checkList.length>=4?(a.$message({message:"最多只能组合四种商品!",type:"warning"}),a.$refs.productElement.toggleRowSelection(t,!1)):a.checkList.push(t):a.checkList=a.$util.arrRemoveJson(a.checkList,"skuId",t.skuId)},productSubmit:function(){var e=this;e.groupSubmitLoading=!0;var t=e.productList,a=e.checkList,o=[];if(t.forEach((function(e){a.forEach((function(t){e.skuId==t.skuId&&o.push(e)}))})),e.groupFormData.list=o,e.productModal=!1,e.groupFormData.list.length){var s=0,r=0,i=[],l=0;e.groupFormData.list.forEach((function(t){s=e.$util.add(e.$util.mul(t.num,t.price),s).toFixed(2),r=e.$util.add(e.$util.mul(t.num,t.newPrice),r).toFixed(2),i.push(t),2==t.num?l+=2:l++})),i.sort(e.compare("num")),e.canvasList=i,e.groupFormData.goodsCost=s,e.groupFormData.goodsRetail=r,e.$nextTick((function(){e.makeImgCanvas(l)}))}},makeImgCanvas:function(e){var t=this,a=t.$refs.myCanvas,o=a.getContext("2d");o.clearRect(0,0,112,112),o.fillStyle="#ffffff",o.fillRect(0,0,112,112),o.strokeStyle="#dddddd",o.lineWidth="1",o.strokeRect(0,0,112,112);var s=t.canvasList,r=t.groupFormData.list.length,i=0;switch(1==e?(i=100,o.font="30px 微软雅黑"):e>=2&&e<=4?(i=50,o.font="26px 微软雅黑"):e>4&&(i=25,o.font="14px 微软雅黑"),e){case 1:var l=t.newCanvasImage(s[0].content);l.onload=function(){o.drawImage(l,10,10,i,i),s[0].num>2&&s[0].num<10?t.canvasToFillText(41,100,"×"+s[0].num,o):s[0].num>=10&&t.canvasToFillText(35,100,"×"+s[0].num,o)};break;case 2:if(1==r){var n=t.newCanvasImage(s[0].content);n.onload=function(){o.drawImage(n,6,31,i,i),o.drawImage(n,56,31,i,i)}}else{var c=t.newCanvasImage(s[0].content);c.onload=function(){o.drawImage(c,6,31,i,i),s[0].num>2&&s[0].num<10?t.canvasToFillText(10,81,"×"+s[0].num,o):s[0].num>=10&&t.canvasToFillText(5,81,"×"+s[0].num,o)};var d=t.newCanvasImage(s[1].content);d.onload=function(){o.drawImage(d,56,31,i,i),s[1].num>2&&s[1].num<10?t.canvasToFillText(64,81,"×"+s[1].num,o):s[1].num>=10&&t.canvasToFillText(59,81,"×"+s[0].num,o)}}break;case 3:if(2==r)s.forEach((function(e){var a=t.newCanvasImage(e.content);a.onload=function(){2==e.num?(o.drawImage(a,6,56,i,i),o.drawImage(a,56,56,i,i)):(o.drawImage(a,31,6,i,i),e.num>2&&e.num<10?t.canvasToFillText(41,56,"×"+e.num,o):e.num>=10&&t.canvasToFillText(46,56,"×"+e.num,o))}}));else{var u=t.newCanvasImage(s[0].content);u.onload=function(){o.drawImage(u,6,56,i,i),s[0].num>2&&s[0].num<10?t.canvasToFillText(10,106,"×"+s[0].num,o):s[0].num>=10&&t.canvasToFillText(5,106,"×"+s[0].num,o)};var m=t.newCanvasImage(s[1].content);m.onload=function(){o.drawImage(m,56,56,i,i),s[1].num>2&&s[1].num<10?t.canvasToFillText(63,106,"×"+s[1].num,o):s[1].num>=10&&t.canvasToFillText(58,106,"×"+s[1].num,o)};var p=t.newCanvasImage(s[2].content);p.onload=function(){o.drawImage(p,31,6,i,i),s[2].num>2&&s[2].num<10?t.canvasToFillText(39,56,"×"+s[2].num,o):s[2].num>=10&&t.canvasToFillText(54,56,"×"+s[2].num,o)}}break;case 4:if(2==r){var f=t.newCanvasImage(s[0].content);f.onload=function(){o.drawImage(f,6,6,i,i),o.drawImage(f,56,6,i,i)};var g=t.newCanvasImage(s[1].content);g.onload=function(){o.drawImage(g,6,56,i,i),o.drawImage(g,56,56,i,i)},console.log("img",f,g)}else if(3==r){var b=0;s.forEach((function(e){var a=t.newCanvasImage(e.content);a.onload=function(){2==e.num?(o.drawImage(a,6,6,i,i),o.drawImage(a,56,6,i,i)):(0==b?(o.drawImage(a,6,56,i,i),e.num>2&&e.num<10?t.canvasToFillText(10,106,"×"+e.num,o):e.num>=10&&t.canvasToFillText(5,106,"×"+e.num,o)):(o.drawImage(a,56,56,i,i),e.num>2&&e.num<10?t.canvasToFillText(63,106,"×"+e.num,o):e.num>=10&&t.canvasToFillText(58,106,"×"+e.num,o)),b++)}}))}else if(4==r){var h=t.newCanvasImage(s[0].content);h.onload=function(){o.drawImage(h,6,6,i,i),s[0].num>2&&s[0].num<10?t.canvasToFillText(10,56,"×"+s[0].num,o):s[0].num>=10&&t.canvasToFillText(5,56,"×"+s[0].num,o)};var v=t.newCanvasImage(s[1].content);v.onload=function(){o.drawImage(v,56,6,i,i),s[1].num>2&&s[1].num<10?t.canvasToFillText(63,56,"×"+s[1].num,o):s[1].num>=10&&t.canvasToFillText(58,56,"×"+s[1].num,o)};var y=t.newCanvasImage(s[2].content);y.onload=function(){o.drawImage(y,6,56,i,i),s[2].num>2&&s[2].num<10?t.canvasToFillText(10,106,"×"+s[2].num,o):s[2].num>=10&&t.canvasToFillText(5,106,"×"+s[2].num,o)};var k=t.newCanvasImage(s[3].content);k.onload=function(){o.drawImage(k,56,56,i,i),s[3].num>2&&s[3].num<10?t.canvasToFillText(63,106,"×"+s[3].num,o):s[3].num>=10&&t.canvasToFillText(58,106,"×"+s[3].num,o)}}break;case 5:var x=0;3==r?s.forEach((function(e){var a=t.newCanvasImage(e.content);a.onload=function(){2==e.num?(0==x?(o.drawImage(a,6,56,i,i),o.drawImage(a,31,56,i,i)):(o.drawImage(a,56,56,i,i),o.drawImage(a,81,56,i,i)),x++):(o.drawImage(a,43,31,i,i),e.num>2&&e.num<10?t.canvasToFillText(50,56,"×"+e.num,o):e.num>=10&&t.canvasToFillText(48,56,"×"+e.num,o))}})):4==r&&s.forEach((function(e){var a=t.newCanvasImage(e.content);a.onload=function(){2==e.num?(o.drawImage(a,31,31,i,i),o.drawImage(a,56,31,i,i)):(0==x?(o.drawImage(a,18,56,i,i),e.num>2&&e.num<10?t.canvasToFillText(20,81,"×"+e.num,o):e.num>=10&&t.canvasToFillText(18,81,"×"+e.num,o)):1==x?(o.drawImage(a,43,56,i,i),e.num>2&&e.num<10?t.canvasToFillText(45,81,"×"+e.num,o):e.num>=10&&t.canvasToFillText(43,81,"×"+e.num,o)):(o.drawImage(a,68,56,i,i),e.num>2&&e.num<10?t.canvasToFillText(70,81,"×"+e.num,o):e.num>=10&&t.canvasToFillText(68,81,"×"+e.num,o)),x++)}}));break;case 6:var w=0,I=0;3==r?s.forEach((function(e){var a=t.newCanvasImage(e.content);a.onload=function(){0==w?(o.drawImage(a,6,56,i,i),o.drawImage(a,31,56,i,i)):1==w?(o.drawImage(a,56,56,i,i),o.drawImage(a,81,56,i,i)):2==w&&(o.drawImage(a,31,31,i,i),o.drawImage(a,56,31,i,i)),w++}})):4==r&&s.forEach((function(e){var a=t.newCanvasImage(e.content);a.onload=function(){2==e.num?(0==w?(o.drawImage(a,6,56,i,i),o.drawImage(a,31,56,i,i)):1==w&&(o.drawImage(a,56,56,i,i),o.drawImage(a,81,56,i,i)),w++):(0==I?(o.drawImage(a,31,31,i,i),e.num>2&&e.num<10?t.canvasToFillText(33,56,"×"+e.num,o):e.num>=10&&t.canvasToFillText(31,56,"×"+e.num,o)):1==I&&(o.drawImage(a,56,31,i,i),e.num>2&&e.num<10?t.canvasToFillText(58,56,"×"+e.num,o):e.num>=10&&t.canvasToFillText(56,56,"×"+e.num,o)),I++)}}));break;case 7:var D=0;s.forEach((function(e){var a=t.newCanvasImage(e.content);a.onload=function(){2==e.num?(0==D?(o.drawImage(a,43,31,i,i),o.drawImage(a,68,31,i,i)):1==D?(o.drawImage(a,6,56,i,i),o.drawImage(a,31,56,i,i)):2==D&&(o.drawImage(a,56,56,i,i),o.drawImage(a,81,56,i,i)),D++):(o.drawImage(a,18,31,i,i),e.num>2&&e.num<10?t.canvasToFillText(20,56,"×"+e.num,o):e.num>=10&&t.canvasToFillText(18,56,"×"+e.num,o))}}));break;case 8:var C=0;s.forEach((function(e){var a=t.newCanvasImage(e.content);a.onload=function(){0==C?(o.drawImage(a,6,31,i,i),o.drawImage(a,31,31,i,i)):1==C?(o.drawImage(a,56,31,i,i),o.drawImage(a,81,31,i,i)):2==C?(o.drawImage(a,6,56,i,i),o.drawImage(a,31,56,i,i)):3==C&&(o.drawImage(a,56,56,i,i),o.drawImage(a,81,56,i,i)),C++}}));break}t.canvasToTempFilePathFun(a)},newCanvasImage:function(e){var t=new Image;return t.crossOrigin="anonymous",t.src=e.replace(/:10000/g,""),t},canvasToFillText:function(e,t,a,o){o.fillStyle="red",o.fillText(a,e,t),o.fillStyle="white"},canvasToTempFilePathFun:function(e){var t=this;setTimeout((function(){var a=e.toDataURL();t.groupFormData.img=a,t.groupSubmitLoading=!1}),500)},dataURLtoFile:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"file",a=e.split(","),o=a[0].match(/:(.*?);/)[1],s=o.split("/")[1],r=atob(a[1]),i=r.length,l=new Uint8Array(i);while(i--)l[i]=r.charCodeAt(i);return new File([l],"".concat(t,".").concat(s),{type:o})},compare:function(e){return function(t,a){var o=0,s=0;return o=2===t[e]?2:1,s=2===a[e]?2:1,isNaN(Number(o))||isNaN(Number(s))||(o=Number(o),s=Number(s)),o<s?1:o>s?-1:0}},toSearchProduct:function(){var e=this;if(e.productSearchKey){var t="/szmb/szmshopgroupcontroller/selectshopgroupname",a={storeId:e.Cookies.get("storeId"),shopGroupName:e.productSearchKey};e.$post(t,a).then((function(t){if(console.log(t),1==t.code){var a=t.data;a.length?(e.productList=a,e.$nextTick((function(){var t=e.checkList;console.log(e.checkList,"search-checkList"),a.forEach((function(a){t.forEach((function(t){a.skuId==t.skuId&&(e.$refs.productElement.toggleRowSelection(a,!0),a.num=t.num)}))}))}))):e.$message.error("未搜索到相关商品")}else e.$message.error(t.data)}))}else e.$message.error("请输入商品名称")},setBucketMoney:function(){var e=this,t="/szmb/szmbuserandstorebillcontroller/selectstorebuck",a={storeId:e.Cookies.get("storeId")};e.$post(t,a).then((function(t){console.log(t),1==t.code?(e.bucketList=t.data,e.bucketDialog=!0):e.$message.error(t.data)}))},bucketSetSubmit:function(){var e=this,t=e.bucketList,a=t.map((function(e){var t={brandId:e.brandId,money:e.money?e.money:0};return t})),o="/szmb/szmbuserandstorebillcontroller/insertstorebuck",s={storeId:e.Cookies.get("storeId"),brandList:JSON.stringify(a)};e.$post(o,s).then((function(t){console.log(t),1==t.code?(e.$message({message:"设置成功!",type:"success"}),e.bucketDialog=!1):e.$message.error(t.data)}))},deleteGoods:function(e){var t=this;console.log("123123",e),1==e.lists[0].isExistGroup?this.$confirm("该商品在系统中存在库存，您确认删除商品后，该商品在系统中的库存会清0；该商品关联的套餐商品也自动下架；请您前往【商品维护】中维护套餐信息。","提示",{confirmButtonText:"确定删除",cancelButtonText:"取消"}).then((function(){var a=t,o="/szmb/szmstoreshopcontroller/delectproduct",s={product:e.commodityId};a.$post(o,s).then((function(e){console.log(e),1==e.code?(a.load(),a.$message({message:"删除成功!",type:"success"})):a.$message.error(e.data)}))})).catch((function(){})):e.lists[0].stock>0?this.$confirm("该商品在系统中存在库存，您确认删除商品后，该商品在系统中的库存会清0。","提示",{confirmButtonText:"确定删除",cancelButtonText:"取消"}).then((function(){var a=t,o="/szmb/szmstoreshopcontroller/delectproduct",s={product:e.commodityId};a.$post(o,s).then((function(e){console.log(e),1==e.code?(a.load(),a.$message({message:"删除成功!",type:"success"})):a.$message.error(e.data)}))})).catch((function(){})):this.$confirm("确定删除此商品？","提示",{confirmButtonText:"确定删除",cancelButtonText:"取消"}).then((function(){var a=t,o="/szmb/szmstoreshopcontroller/delectproduct",s={product:e.commodityId};a.$post(o,s).then((function(e){console.log(e),1==e.code?(a.load(),a.$message({message:"删除成功!",type:"success"})):a.$message.error(e.data)}))})).catch((function(){}))},brandChange:function(e){this.formData.brandName=this.bucketBrandList.filter((function(t){return t.brandId==e}))[0].brandName,this.editFormData.brandName=this.bucketBrandList.filter((function(t){return t.brandId==e}))[0].brandName},goodsdiscount:function(e){var t=this;this.goodsdiscountVisible=!0,this.$nextTick((function(){t.$refs.goodsdiscount.init(e)}))}},components:{goodsdiscount:m}},f=p,g=(a("3194"),Object(d["a"])(f,o,s,!1,null,"f821b3fa",null));t["default"]=g.exports},3194:function(e,t,a){"use strict";a("71b6")},"3f50":function(e,t,a){},"4d40":function(e,t,a){"use strict";a("3f50")},"4d86":function(e,t,a){"use strict";a("7964")},"71b6":function(e,t,a){},7964:function(e,t,a){},"7de9":function(e,t,a){"use strict";a.d(t,"b",(function(){return o})),a.d(t,"a",(function(){return s}));var o=[{key:0,value:"否"},{key:1,value:"是"}],s=[{key:1,value:"京东"},{key:2,value:"饿了么"},{key:3,value:"美团"},{key:4,value:"抖音"},{key:7,value:"淘宝/天猫"},{key:8,value:"璞康"},{key:9,value:"微信小店"},{key:10,value:"拼多多"},{key:100,value:"自营",isShow:!1}]},"81d3":function(e,t,a){"use strict";a.d(t,"b",(function(){return o})),a.d(t,"a",(function(){return s}));var o=[{key:0,value:"未审核"},{key:1,value:"审核通过"},{key:2,value:"审核不通过"}],s=[{key:0,value:"重物桶装水"},{key:1,value:"重货箱装水"},{key:2,value:"轻货"}]},a38f:function(e,t,a){"use strict";a.r(t);var o=function(){var e=this,t=e._self._c;return t("div",{staticClass:"padding-bottom-30"},[t("div",[t("div",{staticClass:"headBox",staticStyle:{"padding-top":"0"}},[t("el-button",{attrs:{type:"success",icon:"el-icon-plus"},on:{click:e.addClassNew}},[e._v("新增品牌名")])],1),t("div",{staticClass:"tableBox"},[t("el-table",{staticStyle:{width:"100%"},attrs:{data:e.classifyList,"max-height":e.tableHeight+100,border:"","header-cell-style":{"text-align":"center",background:"#EFF2F7",color:"#3D4C66"},"cell-style":{"text-align":"center","font-size":"16px",color:"#3D4C66"},"empty-text":"暂无数据，请切换品牌或重新输入重试",size:"medium"}},[t("el-table-column",{attrs:{width:"100",label:"序号",type:"index"}}),t("el-table-column",{attrs:{label:"品牌名称",prop:"brandName"}}),t("el-table-column",{attrs:{label:"操作"},scopedSlots:e._u([{key:"default",fn:function(a){return[1!=a.row.systemClassifyFlag||"13816541474"==e.adminStoreInfo.phone?t("el-button",{attrs:{type:"text",size:"medium"},on:{click:function(t){return e.editClass(a.row)}}},[e._v("编辑")]):e._e(),t("el-button",{staticStyle:{color:"red"},attrs:{type:"text",size:"medium"},on:{click:function(t){return e.deleteClass(a.row)}}},[e._v("删除")])]}}])})],1)],1)]),t("el-dialog",{staticClass:"new-dialog-css",attrs:{visible:e.newAddClassVisible,width:"30%",center:""},on:{"update:visible":function(t){e.newAddClassVisible=t}}},[t("div",{staticClass:"new-dialog-title",attrs:{slot:"title"},slot:"title"},[e._v(e._s(e.titleClass))]),t("div",{staticClass:"new-dialog-two-title"},[e._v("品牌信息")]),t("div",{staticClass:"new-dialog-body"},[t("el-form",{staticStyle:{padding:"0 30px"}},[t("el-form-item",[t("el-input",{attrs:{placeholder:"请输入品牌名"},model:{value:e.dialogValue,callback:function(t){e.dialogValue=t},expression:"dialogValue"}})],1)],1)],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:e.cancelBtn}},[e._v("取 消")]),e.newclassType?t("el-button",{attrs:{type:"primary"},on:{click:e.newClassBtn}},[e._v("保 存")]):e._e(),e.newclassType?e._e():t("el-button",{attrs:{type:"primary"},on:{click:e.editClassBtn}},[e._v("保 存")])],1)])],1)},s=[],r=a("7de9");function i(e){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i(e)}function l(e,t,a){return(t=n(t))in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}function n(e){var t=c(e,"string");return"symbol"==i(t)?t:t+""}function c(e,t){if("object"!=i(e)||!e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var o=a.call(e,t||"default");if("object"!=i(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}var d={props:{},data:function(){return{adminStoreInfo:{},yesOrNo:r["b"],imgUri:this.$imgUri,classifyList:[],newAddClassVisible:!1,titleClass:"新增品牌",newclassType:!0,dialogValue:"",biref:"",paixu:0,state:0,r1:"0",classGoodsNum:0,editClassId:"",showIndex:[{key:"0",value:"显示到主页"},{key:"1",value:"不显示到主页"}],showShow:[{key:0,value:"显示"},{key:1,value:"不显示"}]}},computed:{tableHeight:function(){console.log(this.$store.getters.getGlobalHeight);var e=Number(this.$store.getters.getGlobalHeight)-300;return e>=300?e:300},inTableHeight:function(){var e=this.$store.getters.getGlobalHeight;return e>=400?parseInt(this.$util.mul(e,.5)):400}},created:function(){this.requestClassifyList(),this.adminStoreInfo=JSON.parse(this.Cookies.get("adminStoreInfo"))},mounted:function(){},watch:{},methods:{addClassNew:function(){this.titleClass="新增品牌",this.newclassType=!0,this.newAddClassVisible=!0},editClass:function(e){this.titleClass="编辑品牌",this.newclassType=!1,this.dialogValue=e.brandName,this.editClassId=e.brandId,this.newAddClassVisible=!0},cancelBtn:function(){this.newAddClassVisible=!1,this.classGoodsNum=0,this.dialogValue="",this.paixu=0,this.r1=0,this.state=0,this.biref="",this.editClassId=""},newClassBtn:function(){var e=this;if(""!=e.dialogValue){var t={storeId:e.Cookies.get("storeId"),brandName:e.dialogValue};this.$post("/szmb/szmbrandcontroller/insertbrand",t).then((function(t){1===t.code?(e.$message({type:"success",message:"新增成功"}),e.newAddClassVisible=!1,e.dialogValue="",e.paixu=0,e.r1="",e.state=0,e.biref="",e.requestClassifyList()):("5005"==t.data?e.$message.error("系统已存在组合套餐品牌，无需重复添加。想要添加组合套餐商品，请到【商品管理-商品维护-新增套餐】中添加"):e.$message({type:"warning",message:t.data}),console.log(t.msg))})).catch((function(e){console.log(e)}))}else e.$message.error("品牌名不能为空")},editClassBtn:function(){var e=this;if(""!=e.dialogValue){var t=l(l(l(l({r1:e.Cookies.get("storeId"),brandName:e.dialogValue,paixu:e.paixu},"r1",e.r1),"state",e.state),"biref",e.biref),"id",e.editClassId);this.$post("/szmb/szmbrandcontroller/updatebrandname",t).then((function(t){1===t.code?(e.$message({type:"success",message:"修改成功"}),e.cancelBtn(),e.requestClassifyList()):(e.$message({type:"warning",message:t.data}),console.log(t.msg))})).catch((function(e){console.log(e)}))}else e.$message.error("品牌名不能为空")},deleteClass:function(e){var t=this,a=this,o="该品牌中包含"+e.totalProduct+"个商品，删除品牌后，品牌中的商品也会被删除，请您仔细确认是否删除。";this.$confirm(o,"提示",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning"}).then((function(){var t={storeId:a.Cookies.get("storeId"),productClassifyId:e.productClassifyId};a.$post("/szmb/szmbrandcontroller/deleteProductClassifyName",t).then((function(e){1===e.code?(a.$message({type:"success",message:"删除成功"}),a.requestClassifyList()):console.log(e.msg)})).catch((function(e){console.log(e)}))})).catch((function(){t.$message({type:"info",message:"已取消删除"})}))},requestClassifyList:function(){var e=this;this.$post("/szmb/szmbrandcontroller/selectallbrandbystore",{storeId:e.Cookies.get("storeId")}).then((function(t){1===t.code?e.classifyList=t.data:e.$message.error(t.data)})).catch((function(e){console.log(e)}))}},filters:{},components:{}},u=d,m=(a("e897"),a("0c7c")),p=Object(m["a"])(u,o,s,!1,null,"ccfc72dc",null);t["default"]=p.exports},bb24:function(e,t,a){},e897:function(e,t,a){"use strict";a("bb24")}}]);