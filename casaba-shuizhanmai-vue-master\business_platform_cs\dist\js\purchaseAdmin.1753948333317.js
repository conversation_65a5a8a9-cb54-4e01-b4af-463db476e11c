(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["purchaseAdmin"],{1865:function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t._self._c;return e("div",{staticClass:"padding-bottom-30"},[e("div",[e("div",{staticClass:"headBox",staticStyle:{"padding-top":"0"}},[e("div",{staticClass:"optionBox"},[e("el-select",{staticClass:"select-salesman",attrs:{placeholder:"按商品分类筛选",filterable:"","no-data-text":"暂无商品分类"},model:{value:t.classifyState,callback:function(e){t.classifyState=e},expression:"classifyState"}},[e("el-option",{attrs:{label:"全部",value:-1}}),t._l(t.calssListData,(function(t,a){return[e("el-option",{key:a,attrs:{label:t.productClassifyName,value:t.productClassifyId}})]}))],2),t._v("\n         \n         \n        "),e("el-select",{staticClass:"select-salesman",attrs:{placeholder:"按商品品牌筛选",filterable:""},model:{value:t.brandNameState,callback:function(e){t.brandNameState=e},expression:"brandNameState"}},[e("el-option",{attrs:{label:"全部",value:-1}}),t._l(t.brandListData,(function(a,s){return[0!=a.brandId?e("el-option",{key:s,attrs:{label:a.brandName,value:a.brandId}}):t._e()]}))],2),e("el-input",{attrs:{placeholder:"输入商品名称筛选",clearable:""},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.changeStateSearch.apply(null,arguments)}},model:{value:t.productNameS,callback:function(e){t.productNameS=e},expression:"productNameS"}}),e("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary"},on:{click:t.changeStateSearch}},[t._v("查询")]),e("el-button",{on:{click:t.clearScreen}},[t._v("清空筛选条件")])],1)]),e("div",{staticClass:"top-msg"},[e("p",[e("span",[t._v("今日桶装水数量：")]),e("span",{staticClass:"color-red"},[t._v(t._s(t.todayTzs))])]),e("p",[e("span",[t._v("今日箱装水数量：")]),e("span",{staticClass:"color-red"},[t._v(t._s(t.todayXzs))])]),e("p",[t._v("其他分类请选择商品分类进行筛选查询")])]),e("div",{staticClass:"tableBox"},[t._m(0),e("div",{staticClass:"table-box-design",style:"height:"+t.tableHeight+"px"},[e("el-scrollbar",{attrs:{"wrap-class":"default-scrollbar__wrap"}},[0==t.goodsTableData.length?e("div",{staticClass:"empty-data"},[t._v("暂无数据")]):t._e(),t._l(t.goodsTableData,(function(a,s){return e("div",{key:s,staticClass:"table-item"},[e("div",{staticClass:"table-item-header"},[e("div",{staticClass:"table-item-bname"},[e("span",[t._v("商品品牌：")]),e("span",[t._v(t._s(a.brandName))])]),-1!=a.watreInventory?[e("span",{staticClass:"margin-left-20"},[t._v("空桶数量：")]),e("span",{staticClass:"color-red"},[t._v(t._s(a.emptyBuck))]),e("span",{staticClass:"margin-left-20"},[t._v("有水的桶数量：")]),e("span",{staticClass:"color-red"},[t._v(t._s(a.watreInventory))]),e("span",{staticClass:"margin-left-20"},[t._v("总空桶数：")]),e("span",{staticClass:"color-red"},[t._v(t._s(a.totalInventory))])]:t._e()],2),t._l(a.productList,(function(a,s){return[-1!=a.inventory?e("div",{key:s,staticClass:"table-body-line",staticStyle:{"text-align":"center"}},[e("div",{attrs:{"data-title":"商品图"}},[e("span",[e("img",{staticStyle:{width:"64px",height:"64px"},attrs:{src:a.img}})])]),e("div",{attrs:{"data-title":"商品名"}},[e("span",[t._v(t._s(a.productName))])]),e("div",{attrs:{"data-title":"桶类型"}},[e("span",[t._v(t._s(a.buckClass))])]),e("div",{attrs:{"data-title":"系统库存"}},[e("span",{class:{"color-red":"常规桶"==a.buckClass}},[t._v(t._s(a.inventory))])])]):t._e()]}))],2)}))],2)],1)])])])},i=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"table-head-box"},[e("div",[t._v("商品图")]),e("div",[t._v("商品名称")]),e("div",[t._v("桶类型")]),e("div",[t._v("实际库存")])])}],l={props:{},data:function(){return{imgUri:this.$imgUri,classifyState:"",brandNameState:"",calssListData:[],brandListData:[],productNameS:"",goodsTableData:[],todayTzs:0,todayXzs:0}},computed:{tableHeight:function(){console.log(this.$store.getters.getGlobalHeight);var t=Number(this.$store.getters.getGlobalHeight)-300;return t>=300?t:300},inTableHeight:function(){var t=this.$store.getters.getGlobalHeight;return t>=400?parseInt(this.$util.mul(t,.5)):400}},created:function(){this.requestClassApi(),this.requestGoodsListApi()},mounted:function(){},watch:{productNameS:function(t,e){t||this.requestGoodsListApi()}},methods:{requestClassApi:function(){var t=this,e=this;this.$post("/szmb/newinsertproductcontroller/selectstoreallbrand",{storeId:e.Cookies.get("storeId")}).then((function(a){1===a.code?(e.calssListData=a.data.class,e.brandListData=a.data.brand):0===a.code?(e.calssListData=[],e.brandListData=[]):(console.log(a.msg),t.$message.error(a.data))})).catch((function(t){console.log(t)}))},changeStateSearch:function(){this.requestGoodsListApi()},clearScreen:function(){this.classifyState="",this.brandNameState="",this.productNameS="",this.requestGoodsListApi()},requestGoodsListApi:function(){var t=this,e=this,a={storeId:e.Cookies.get("storeId"),shopClassId:""!=e.classifyState?e.classifyState:-1,brandId:""!=e.brandNameState?e.brandNameState:-1,productName:e.productNameS};this.$post("/szmb/newclasscontroller/selectProductInventorytjpc",a).then((function(a){1===a.code?(e.goodsTableData=a.data.list,e.todayTzs=a.data.buckNum,e.todayXzs=a.data.boxNum):0===a.code?(e.goodsTableData=[],e.todayTzs=0,e.todayXzs=0):(console.log(a.msg),t.$message.error(a.data))})).catch((function(t){console.log(t)}))},cancelNewAdd:function(t){console.log(t),t.state=0,t.abc=""},newAddEmptyBarrel:function(t){var e=this,a=this,s={number:Number(t.abc),storeId:a.Cookies.get("storeId"),brandId:t.brandId,brandName:t.brandName,header:"json"};a.$post("/szmb/inventory/insertemptybuckrecord",s).then((function(s){1===s.code?(a.$message({type:"success",message:s.data}),t.state=0,t.abc="",a.requestGoodsListApi()):(console.log(s.msg),e.$message.error(s.data))})).catch((function(t){console.log(t)}))},editPriceAndNum:function(t){t.childState=1},saveEditPrice:function(t){var e=this;console.log(t);var a=this,s=[{skuId:t.skuId,addNumber:Number(t.childAbc),money:t.cost,price:t.retailPrice}];a.$post("/szmb/szmstoreshopcontroller/updateskulist",{skuList:JSON.stringify(s)}).then((function(s){1===s.code?(a.$message({type:"success",message:s.data}),t.childState=0,t.childAbc="",a.requestGoodsListApi()):(console.log(s.msg),e.$message.error(s.data))})).catch((function(t){console.log(t)}))},cancelEditNum:function(t){t.childState=0,this.requestGoodsListApi()}},components:{}},n=l,r=(a("2474"),a("0c7c")),o=Object(r["a"])(n,s,i,!1,null,"ac28a628",null);e["default"]=o.exports},2474:function(t,e,a){"use strict";a("d61b")},"3eab":function(t,e,a){},6495:function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t._self._c;return e("div",{staticClass:"padding-bottom-30"},[e("div",[e("div",{staticClass:"tableBox"},[t._m(0),e("div",{staticClass:"table-box-design",style:"height:"+t.tableHeight+"px"},[e("el-scrollbar",{attrs:{"wrap-class":"default-scrollbar__wrap"}},[0==t.lists.length?e("div",{staticClass:"empty-data"},[t._v("暂无数据")]):t._l(t.lists,(function(a,s){return e("div",{key:s,staticClass:"table-body-line",staticStyle:{"text-align":"center"}},[e("div",{attrs:{"data-title":"商品图"}},[e("span",[e("img",{staticStyle:{width:"64px",height:"64px"},attrs:{src:a.img?a.img:"".concat(t.imgUri,"/images/otherBucket.png")}})])]),e("div",{attrs:{"data-title":"商品名"}},[e("span",[t._v(t._s(a.buckName))])]),e("div",{attrs:{"data-title":"空桶数量"}},[e("span",[t._v(t._s(a.inventory))])])])}))],2)],1)])])])},i=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"table-head-box"},[e("div",[t._v("商品图")]),e("div",[t._v("商品品牌")]),e("div",[t._v("空桶数量")])])}],l={props:{},data:function(){return{imgUri:this.$imgUri,lists:[]}},computed:{tableHeight:function(){console.log(this.$store.getters.getGlobalHeight);var t=Number(this.$store.getters.getGlobalHeight)-250;return t>=300?t:300}},created:function(){},mounted:function(){this.getEmptyBarrelStockLists()},methods:{getEmptyBarrelStockLists:function(){var t=this,e=this;this.$post("/szmb/inventory/selectemptybuck",{storeId:e.Cookies.get("storeId")}).then((function(a){1===a.code?e.lists=a.data.list:0===a.code?e.lists=[]:(console.log(a.msg),t.$message.error(a.data))})).catch((function(t){console.log(t)}))}},components:{}},n=l,r=(a("c04e"),a("0c7c")),o=Object(r["a"])(n,s,i,!1,null,"c8de05fa",null);e["default"]=o.exports},a117:function(t,e,a){"use strict";a("3eab")},c04e:function(t,e,a){"use strict";a("ee85")},c4c7:function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t._self._c;return e("div",{staticClass:"padding-bottom-30"},[e("div",[e("div",{staticClass:"headBox",staticStyle:{"padding-top":"0"}},[e("div",{staticClass:"optionBox"},[e("el-select",{staticClass:"select-salesman",attrs:{placeholder:"按商品分类筛选",filterable:"","no-data-text":"暂无商品分类"},model:{value:t.classifyState,callback:function(e){t.classifyState=e},expression:"classifyState"}},[e("el-option",{attrs:{label:"全部",value:-1}}),t._l(t.calssListData,(function(t,a){return[e("el-option",{key:a,attrs:{label:t.productClassifyName,value:t.productClassifyId}})]}))],2),t._v("\n         \n         \n        "),e("el-select",{staticClass:"select-salesman",attrs:{placeholder:"按商品品牌筛选",filterable:""},model:{value:t.brandNameState,callback:function(e){t.brandNameState=e},expression:"brandNameState"}},[e("el-option",{attrs:{label:"全部",value:-1}}),t._l(t.brandListData,(function(t,a){return[e("el-option",{key:a,attrs:{label:t.brandName,value:t.brandId}})]}))],2),e("el-input",{attrs:{placeholder:"输入商品名称筛选",clearable:""},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.changeStateSearch.apply(null,arguments)}},model:{value:t.productNameS,callback:function(e){t.productNameS=e},expression:"productNameS"}}),e("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary"},on:{click:t.changeStateSearch}},[t._v("查询")]),e("el-button",{on:{click:t.clearScreen}},[t._v("清空筛选条件")]),e("el-button",{attrs:{type:"success"},on:{click:function(e){return t.checkEmptyBarrelClick(!0)}}},[t._v("空桶盘点")])],1),e("div")]),e("div",{staticClass:"tableBox"},[t._m(0),e("div",{staticClass:"table-box-design",style:"height:"+t.tableHeight+"px"},[e("el-scrollbar",{attrs:{"wrap-class":"default-scrollbar__wrap"}},[0==t.goodsTableData.length?e("div",{staticClass:"empty-data"},[t._v("暂无数据")]):t._e(),t._l(t.goodsTableData,(function(a,s){return e("div",{key:s,staticClass:"table-item"},[e("div",{staticClass:"table-item-header"},[e("div",{staticClass:"table-item-bname"},[e("span",[t._v("商品品牌：")]),e("span",[t._v(t._s(a.brandName))])]),-1!=a.watreInventory?[e("span",{staticClass:"margin-left-20"},[t._v("空桶数量：")]),e("span",{staticClass:"color-red"},[t._v(t._s(a.emptyBuck))]),0!=a.brandId?[e("span",{staticClass:"margin-left-20"},[t._v("有水的桶数量：")]),e("span",{staticClass:"color-red"},[t._v(t._s(a.watreInventory))]),e("span",{staticClass:"margin-left-20"},[t._v("总空桶数：")]),e("span",{staticClass:"color-red"},[t._v(t._s(a.totalInventory))])]:t._e()]:t._e()],2),t._l(a.productList,(function(a,s){return e("div",{key:s,staticClass:"table-body-line",staticStyle:{"text-align":"center"}},[e("div",{attrs:{"data-title":"商品图"}},[e("span",[e("img",{staticStyle:{width:"64px",height:"64px"},attrs:{src:a.img}})])]),e("div",{attrs:{"data-title":"商品名"}},[e("span",[t._v(t._s(a.productName))])]),e("div",{attrs:{"data-title":"桶类型"}},[e("span",[t._v(t._s(a.buckClass))])]),e("div",{attrs:{"data-title":"系统库存"}},[e("span",{class:{"color-red":"常规桶"==a.buckClass}},[t._v(t._s(a.inventory))])]),e("div",{attrs:{"data-title":"盘点数量"}},[e("el-input",{attrs:{placeholder:"请输入实际库存",disabled:0==a.childState,onkeyup:"value=(value.replace(/\\D/g,'')==''?'':parseInt(value))"},model:{value:a.childAbc,callback:function(e){t.$set(a,"childAbc",e)},expression:"childItem.childAbc"}})],1),e("div",{attrs:{"data-title":"操作"}},[0==a.childState?[e("el-button",{staticClass:"save-item",attrs:{type:"text",size:"medium"},on:{click:function(e){return t.editPriceAndNum(a)}}},[t._v("编辑")])]:t._e(),1==a.childState?[e("el-button",{staticClass:"save-item",attrs:{type:"text",size:"medium"},on:{click:function(e){return t.saveEditPrice(a)}}},[t._v("保存")]),e("el-button",{staticClass:"save-item",staticStyle:{color:"#949FA8"},attrs:{type:"text",size:"medium"},on:{click:function(e){return t.cancelEditNum(a)}}},[t._v("取消")])]:t._e()],2)])}))],2)}))],2)],1)])]),e("el-dialog",{staticClass:"new-dialog-css",attrs:{visible:t.checkDialogVisible,width:"30%",center:""},on:{"update:visible":function(e){t.checkDialogVisible=e}}},[e("div",{staticClass:"new-dialog-title",attrs:{slot:"title"},slot:"title"},[t._v("\n      空桶盘点\n    ")]),e("div",{staticClass:"new-dialog-two-title"},[t._v("空桶盘点")]),e("div",{staticClass:"new-dialog-body"},[e("el-table",{attrs:{data:t.checkEmptyBarrelList,border:"","max-height":"500","header-cell-style":{"text-align":"center",background:"#EFF2F7",color:"#3D4C66",height:"36px","font-size":"16px",padding:"5px 0"},"cell-style":{"text-align":"center","font-size":"16px",color:"#3D4C66"},"empty-text":"暂无数据"}},[e("el-table-column",{attrs:{label:"桶品牌",prop:"buckName"}}),e("el-table-column",{attrs:{label:"空桶系统库存",prop:"inventory"}}),e("el-table-column",{attrs:{label:"空桶实际库存",prop:"abc"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-input",{attrs:{placeholder:"请输入",onkeyup:"value=(value.replace(/\\D/g,'')==''?'':parseInt(value))"},model:{value:a.row.abc,callback:function(e){t.$set(a.row,"abc",e)},expression:"scope.row.abc"}})]}}])})],1)],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){return t.checkEmptyBarrelClick(!1)}}},[t._v("取 消")]),e("el-button",{attrs:{type:"primary"},on:{click:t.saveBarrelSubmit}},[t._v("保 存")])],1)])],1)},i=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"table-head-box"},[e("div",[t._v("商品图")]),e("div",[t._v("商品名称")]),e("div",[t._v("桶类型")]),e("div",[t._v("实际库存")]),e("div",[t._v("盘点数量")]),e("div",[t._v("操作")])])}],l={props:{},data:function(){return{imgUri:this.$imgUri,classifyState:"",brandNameState:"",calssListData:[],brandListData:[],productNameS:"",goodsTableData:[],pagesData:{pageTotal:0,currentPage:1,currentPageSizes:[10,15,20],pageSize:10},checkDialogVisible:!1,checkEmptyBarrelList:[]}},computed:{tableHeight:function(){console.log(this.$store.getters.getGlobalHeight);var t=Number(this.$store.getters.getGlobalHeight)-300;return t>=300?t:300},inTableHeight:function(){var t=this.$store.getters.getGlobalHeight;return t>=400?parseInt(this.$util.mul(t,.5)):400}},created:function(){this.requestClassApi(),this.requestGoodsListApi()},mounted:function(){console.log("hdjskhhhhh",!0)},watch:{productNameS:function(t,e){t||this.requestGoodsListApi()}},methods:{checkEmptyBarrelClick:function(t){this.checkDialogVisible=t,this.requestAllKTapi()},requestAllKTapi:function(){var t=this,e=this;e.$post("/szmb/inventory/selectemptybuck",{storeId:e.Cookies.get("storeId")}).then((function(a){1===a.code?e.checkEmptyBarrelList=a.data.list:(console.log(a.msg),t.$message.error(a.data),e.checkEmptyBarrelList=[])})).catch((function(t){console.log(t)}))},saveBarrelSubmit:function(){var t=this,e=this,a=[];e.checkEmptyBarrelList.forEach((function(t){a.push({buckInventoryId:t.buckInventoryId,inventory:""!=t.abc?Number(t.abc):-1})})),e.$post("/szmb/inventory/updateemptybuck",{emptyBuck:JSON.stringify(a)}).then((function(a){1===a.code?(e.requestAllKTapi(),e.$confirm("修改成功，是否关闭?","提示",{confirmButtonText:"关闭",cancelButtonText:"继续修改",type:"warning"}).then((function(){e.checkDialogVisible=!1})).catch((function(){e.checkDialogVisible=!0}))):(console.log(a.msg),t.$message.error(a.data))})).catch((function(t){console.log(t)}))},requestClassApi:function(){var t=this,e=this;this.$post("/szmb/newinsertproductcontroller/selectstoreallbrand",{storeId:e.Cookies.get("storeId")}).then((function(a){1===a.code?(e.calssListData=a.data.class,e.brandListData=a.data.brand):0===a.code?(e.calssListData=[],e.brandListData=[]):(console.log(a.msg),t.$message.error(a.data))})).catch((function(t){console.log(t)}))},changeStateSearch:function(){this.requestGoodsListApi()},clearScreen:function(){this.classifyState="",this.brandNameState="",this.productNameS="",this.requestGoodsListApi()},requestGoodsListApi:function(){var t=this,e=this,a={storeId:e.Cookies.get("storeId"),shopClassId:""!=e.classifyState?e.classifyState:-1,brandId:""!=e.brandNameState.toString()?e.brandNameState:-1,productName:e.productNameS};this.$post("/szmb/newclasscontroller/selectProductInventorypdpc",a).then((function(a){1===a.code?e.goodsTableData=a.data:0===a.code?e.goodsTableData=[]:(console.log(a.msg),t.$message.error(a.data))})).catch((function(t){console.log(t)}))},editPriceAndNum:function(t){t.childState=1},saveEditPrice:function(t){var e=this;console.log(t);var a=this,s={skuId:t.skuId,inventory:Number(t.childAbc)};a.$post("/szmb/inventory/updateinventory",s).then((function(s){1===s.code?(a.$message({type:"success",message:"修改成功"}),t.childState=0,t.childAbc="",a.requestGoodsListApi()):(console.log(s.msg),e.$message.error(s.data))})).catch((function(t){console.log(t)}))},cancelEditNum:function(t){t.childState=0,this.requestGoodsListApi()}},components:{}},n=l,r=(a("a117"),a("0c7c")),o=Object(r["a"])(n,s,i,!1,null,"11523355",null);e["default"]=o.exports},d154:function(t,e,a){"use strict";a("db98")},d61b:function(t,e,a){},db98:function(t,e,a){},edf2:function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t._self._c;return e("div",{staticClass:"padding-bottom-30"},[e("div",{directives:[{name:"show",rawName:"v-show",value:t.newAddPageOff,expression:"newAddPageOff"}]},[e("div",{staticClass:"headBox",staticStyle:{"padding-top":"0"}},[e("div",{staticClass:"optionBox"},[e("el-select",{staticClass:"select-salesman",attrs:{placeholder:"按商品分类筛选",filterable:"","no-data-text":"暂无商品分类"},model:{value:t.classifyState,callback:function(e){t.classifyState=e},expression:"classifyState"}},[e("el-option",{attrs:{label:"全部",value:-1}}),t._l(t.calssListData,(function(t,a){return[e("el-option",{key:a,attrs:{label:t.productClassifyName,value:t.productClassifyId}})]}))],2),t._v("\n         \n         \n        "),e("el-select",{staticClass:"select-salesman",attrs:{placeholder:"按商品品牌筛选",filterable:"","no-data-text":"暂无商品品牌"},model:{value:t.brandNameState,callback:function(e){t.brandNameState=e},expression:"brandNameState"}},[e("el-option",{attrs:{label:"全部",value:-1}}),t._l(t.brandListData,(function(a,s){return[0!=a.brandId?e("el-option",{key:s,attrs:{label:a.brandName,value:a.brandId}}):t._e()]}))],2),e("el-input",{attrs:{placeholder:"输入商品名称筛选",clearable:""},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.changeStateSearch.apply(null,arguments)}},model:{value:t.productNameS,callback:function(e){t.productNameS=e},expression:"productNameS"}}),e("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary"},on:{click:t.changeStateSearch}},[t._v("查询")]),e("el-button",{on:{click:t.clearScreen}},[t._v("清空筛选条件")])],1),e("div",[e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.lookDetailPage(0)}}},[t._v("查看新进空桶记录")]),e("el-button",{attrs:{type:"success"},on:{click:function(e){return t.lookDetailPage(1)}}},[t._v("查看进货记录")])],1)]),e("div",{staticClass:"tableBox"},[t._m(0),e("div",{staticClass:"table-box-design",style:"height:"+t.tableHeight+"px"},[e("el-scrollbar",{attrs:{"wrap-class":"default-scrollbar__wrap"}},[0==t.goodsTableData.length?e("div",{staticClass:"empty-data"},[t._v("暂无数据")]):t._e(),t._l(t.goodsTableData,(function(a,s){return e("div",{key:s,staticClass:"table-item"},[e("div",{staticClass:"table-item-header"},[e("span",[t._v("商品品牌：")]),e("span",[t._v(t._s(a.brandName))]),-1!=a.emptyBuck?[e("span",{staticClass:"margin-left-20"},[t._v("空桶库存：")]),e("span",{staticClass:"color-red"},[t._v(t._s(a.emptyBuck))]),e("div",{staticClass:"table-header-input"},[0==a.state?e("el-button",{attrs:{type:"text",size:"medium"},on:{click:function(t){a.state=1}}},[t._v("新进空桶")]):t._e(),1==a.state?[e("el-input",{attrs:{placeholder:"请输入新进空桶数量",onkeyup:"value=value.replace(/^(0+)|[^\\d]+/g,'')"},model:{value:a.abc,callback:function(e){t.$set(a,"abc",e)},expression:"item.abc"}}),e("el-button",{staticClass:"save-item",attrs:{type:"text",size:"medium"},on:{click:function(e){return t.newAddEmptyBarrel(a)}}},[t._v("保存")]),e("el-button",{staticClass:"save-item",staticStyle:{color:"#949FA8"},attrs:{type:"text",size:"medium"},on:{click:function(e){return t.cancelNewAdd(a)}}},[t._v("取消")])]:t._e()],2)]:t._e()],2),t._l(a.productList,(function(a,s){return[-1!=a.inventory?e("div",{key:s,staticClass:"table-body-line",staticStyle:{"text-align":"center"}},[e("div",{attrs:{"data-title":"序号"}},[e("span",[t._v(t._s(s+1))])]),e("div",{attrs:{"data-title":"库存预警"}},[e("span",{staticClass:"color-red"},[t._v(t._s(a.inventoryWarning))])]),e("div",{attrs:{"data-title":"商品图"}},[e("span",[e("img",{staticStyle:{width:"64px",height:"64px"},attrs:{src:a.img}})])]),e("div",{attrs:{"data-title":"商品名"}},[e("span",[t._v(t._s(a.productName))])]),e("div",{attrs:{"data-title":"零售价"}},[e("el-input",{staticClass:"price-input",attrs:{placeholder:"输入零售价",disabled:0==a.childState,oninput:"value= value.match(/\\d+(\\.\\d{0,2})?/) ? value.match(/\\d+(\\.\\d{0,2})?/)[0] : ''"},model:{value:a.retailPrice,callback:function(e){t.$set(a,"retailPrice",e)},expression:"childItem.retailPrice"}})],1),e("div",{attrs:{"data-title":"成本价"}},[e("el-input",{staticClass:"price-input",attrs:{placeholder:"输入成本价",disabled:0==a.childState,oninput:"value= value.match(/\\d+(\\.\\d{0,2})?/) ? value.match(/\\d+(\\.\\d{0,2})?/)[0] : ''"},model:{value:a.cost,callback:function(e){t.$set(a,"cost",e)},expression:"childItem.cost"}})],1),e("div",{attrs:{"data-title":"毛利"}},[e("span",[t._v(t._s(a.grossMargin))])]),e("div",{attrs:{"data-title":"实际库存"}},[e("span",[t._v(t._s(a.inventory))])]),e("div",{attrs:{"data-title":"进货数量"}},[e("el-input",{attrs:{placeholder:"请输入进货数量",disabled:0==a.childState,onkeyup:"value=value.replace(/^(0+)|[^\\d]+/g,'')"},model:{value:a.childAbc,callback:function(e){t.$set(a,"childAbc",e)},expression:"childItem.childAbc"}})],1),e("div",{attrs:{"data-title":"操作"}},[0==a.childState?[e("el-button",{staticClass:"save-item",attrs:{type:"text",size:"medium"},on:{click:function(e){return t.editPriceAndNum(a)}}},[t._v("编辑")])]:t._e(),1==a.childState?[e("el-button",{staticClass:"save-item",attrs:{type:"text",size:"medium"},on:{click:function(e){return t.saveEditPrice(a)}}},[t._v("保存")]),e("el-button",{staticClass:"save-item",staticStyle:{color:"#949FA8"},attrs:{type:"text",size:"medium"},on:{click:function(e){return t.cancelEditNum(a)}}},[t._v("取消")])]:t._e()],2)]):t._e()]}))],2)}))],2)],1)])]),e("div",{directives:[{name:"show",rawName:"v-show",value:!t.newAddPageOff,expression:"!newAddPageOff"}],staticClass:"jinhuo-tt"},[e("el-page-header",{staticClass:"padding-bottom-10 iconfont icon-fanhui",on:{back:function(e){t.newAddPageOff=!0}}}),e("div",{staticClass:"content-box"},[e("div",{staticClass:"content-item1"},[e("div",{class:{"active-item":0==t.selectedKey},on:{click:function(e){return t.changeSelectedItem(0)}}},[t._v("新进空桶记录表")]),e("div",{class:{"active-item":1==t.selectedKey},on:{click:function(e){return t.changeSelectedItem(1)}}},[t._v("进货记录表")])])]),e("div",{staticClass:"cont-cent"},[0==t.selectedKey?[e("el-form",{attrs:{inline:!0}},[e("el-form-item",[e("el-select",{staticClass:"select-salesman",attrs:{placeholder:"按商品品牌筛选",filterable:""},on:{change:t.newBarrelSearch},model:{value:t.newBarrelBrand,callback:function(e){t.newBarrelBrand=e},expression:"newBarrelBrand"}},[e("el-option",{attrs:{label:"全部",value:-1}}),t._l(t.brandListData,(function(t,a){return[e("el-option",{key:a,attrs:{label:t.brandName,value:t.brandId}})]}))],2)],1),e("el-form-item",[e("el-date-picker",{attrs:{type:"daterange",align:"right","unlink-panels":"","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":t.pickerOptions,"value-format":"yyyy-MM-dd"},on:{change:t.newBarrelSearch},model:{value:t.newBarrelDateTime,callback:function(e){t.newBarrelDateTime=e},expression:"newBarrelDateTime"}})],1),e("el-form-item",{staticStyle:{"margin-left":"20px"}},[e("el-button",{attrs:{type:"primary"},on:{click:t.newBarrelSearch}},[t._v("查询")]),e("el-button",{on:{click:t.newBarrelresetClear}},[t._v("清空筛选条件")])],1)],1)]:t._e(),1==t.selectedKey?[e("el-form",{attrs:{inline:!0}},[e("el-form-item",[e("el-select",{staticClass:"select-salesman",attrs:{placeholder:"按商品品牌筛选",filterable:""},on:{change:t.jhSearch},model:{value:t.jhBrand,callback:function(e){t.jhBrand=e},expression:"jhBrand"}},[e("el-option",{attrs:{label:"全部",value:-1}}),t._l(t.brandListData,(function(t,a){return[e("el-option",{key:a,attrs:{label:t.brandName,value:t.brandId}})]}))],2)],1),e("el-form-item",[e("el-select",{staticClass:"select-salesman",attrs:{placeholder:"按商品类型筛选",filterable:""},on:{change:t.jhSearch},model:{value:t.jhClass,callback:function(e){t.jhClass=e},expression:"jhClass"}},[e("el-option",{attrs:{label:"全部",value:-1}}),t._l(t.calssListData,(function(t,a){return[e("el-option",{key:a,attrs:{label:t.productClassifyName,value:t.productClassifyId}})]}))],2)],1),e("el-form-item",[e("el-date-picker",{attrs:{type:"daterange",align:"right","unlink-panels":"","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":t.pickerOptions,"value-format":"yyyy-MM-dd"},on:{change:t.jhSearch},model:{value:t.jhDataTime,callback:function(e){t.jhDataTime=e},expression:"jhDataTime"}})],1),e("el-form-item",{staticStyle:{"margin-left":"20px"}},[e("el-button",{attrs:{type:"primary"},on:{click:t.jhSearch}},[t._v("查询")]),e("el-button",{on:{click:t.clearJhSearch}},[t._v("清空筛选条件")])],1)],1)]:t._e()],2),0==t.selectedKey?e("div",{staticClass:"royalty-cont"},[e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.newBarrelLoading,expression:"newBarrelLoading"}],attrs:{data:t.newBarrelTable,"max-height":t.tableHeight-100,border:"","header-cell-style":{"text-align":"center",background:"#EFF2F7",color:"#3D4C66"},"cell-style":{"text-align":"center","font-size":"16px",color:"#3D4C66"},"empty-text":"暂无数据，请切换筛选条件",size:"medium"}},[e("el-table-column",{attrs:{label:"序号",type:"index",width:"100"}}),e("el-table-column",{attrs:{label:"新进空桶时间",prop:"createTime"}}),e("el-table-column",{attrs:{label:"桶品牌",prop:"brandName"}}),e("el-table-column",{attrs:{label:"新进数量",prop:"number"}})],1),e("div",{staticClass:"total-box"},[e("div",[t._v("合计")]),e("div",[e("span",[t._v("新进空桶总数："+t._s(t.newBarrelTotal))])])]),e("div",{staticClass:"pages-box"},[e("el-pagination",{attrs:{layout:"total, prev, pager, next,sizes, jumper",background:"","current-page":t.pagesData1.currentPage1,"page-sizes":t.pagesData1.currentPageSizes1,"page-size":t.pagesData1.pageSize1,total:t.pagesData1.pageTotal1},on:{"size-change":t.handleSizeChange1,"current-change":t.handleCurrentChange1}})],1)],1):t._e(),1==t.selectedKey?e("div",{staticClass:"royalty-cont"},[e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.jhLoading,expression:"jhLoading"}],attrs:{data:t.jhTableList,"max-height":t.tableHeight-100,border:"","header-cell-style":{"text-align":"center",background:"#EFF2F7",color:"#3D4C66"},"cell-style":{"text-align":"center","font-size":"16px",color:"#3D4C66"},"empty-text":"暂无数据，请切换筛选条件",size:"medium"}},[e("el-table-column",{attrs:{label:"序号",type:"index",width:"100"}}),e("el-table-column",{attrs:{label:"进货时间",prop:"time"}}),e("el-table-column",{attrs:{label:"商品名称",prop:"name"}}),e("el-table-column",{attrs:{label:"进货数量",prop:"number"}})],1),e("div",{staticClass:"total-box"},[e("div",[t._v("合计")]),e("div",[e("span",[t._v("进货量总数："+t._s(t.jhTotal))])])]),e("div",{staticClass:"pages-box"},[e("el-pagination",{attrs:{layout:"total, prev, pager, next,sizes, jumper",background:"","current-page":t.pagesData2.currentPage2,"page-sizes":t.pagesData2.currentPageSizes2,"page-size":t.pagesData2.pageSize2,total:t.pagesData2.pageTotal2},on:{"size-change":t.handleSizeChange2,"current-change":t.handleCurrentChange2}})],1)],1):t._e()],1)])},i=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"table-head-box"},[e("div",[t._v("序号")]),e("div",[t._v("库存预警")]),e("div",[t._v("商品图")]),e("div",[t._v("商品名称")]),e("div",[t._v("零售价")]),e("div",[t._v("成本价")]),e("div",[t._v("毛利")]),e("div",[t._v("实际库存")]),e("div",[t._v("进货数量")]),e("div",[t._v("操作")])])}],l={props:{},data:function(){return{imgUri:this.$imgUri,classifyState:"",brandNameState:"",productNameS:"",calssListData:[],brandListData:[],goodsTableData:[],newAddPageOff:!0,selectedKey:0,pickerOptions:{disabledDate:function(t){return t.getTime()>Date.now()},shortcuts:[{text:"最近一周",onClick:function(t){var e=new Date,a=new Date;a.setTime(a.getTime()-6048e5),t.$emit("pick",[a,e])}},{text:"最近一个月",onClick:function(t){var e=new Date,a=new Date;a.setTime(a.getTime()-2592e6),t.$emit("pick",[a,e])}},{text:"最近三个月",onClick:function(t){var e=new Date,a=new Date;a.setTime(a.getTime()-7776e6),t.$emit("pick",[a,e])}}]},newBarrelBrand:"",newBarrelDateTime:"",newBarrelLoading:!0,newBarrelTable:[],newBarrelTotal:0,pagesData1:{pageTotal1:0,currentPage1:1,currentPageSizes1:[10,15,20],pageSize1:10},jhBrand:"",jhDataTime:"",jhClass:"",jhLoading:!1,jhTableList:[],jhTotal:0,pagesData2:{pageTotal2:0,currentPage2:1,currentPageSizes2:[10,15,20],pageSize2:10},pagesData:{pageTotal:0,currentPage:1,currentPageSizes:[10,15,20],pageSize:10}}},computed:{tableHeight:function(){console.log(this.$store.getters.getGlobalHeight);var t=Number(this.$store.getters.getGlobalHeight)-300;return t>=300?t:300},inTableHeight:function(){var t=this.$store.getters.getGlobalHeight;return t>=400?parseInt(this.$util.mul(t,.5)):400}},created:function(){this.requestClassApi(),this.requestGoodsListApi()},mounted:function(){},watch:{productNameS:function(t,e){t||this.requestGoodsListApi()},newAddPageOff:function(t){t&&(this.pagesData1.currentPage1=1,this.pagesData2.currentPage2=1,this.selectedKey=0,this.newBarrelresetClear(),this.clearJhSearch())}},methods:{requestClassApi:function(){var t=this,e=this;this.$post("/szmb/newinsertproductcontroller/selectstoreallbrand",{storeId:e.Cookies.get("storeId")}).then((function(a){1===a.code?(e.calssListData=a.data.class,e.brandListData=a.data.brand):0===a.code?(e.calssListData=[],e.brandListData=[]):(console.log(a.msg),t.$message.error(a.data))})).catch((function(t){console.log(t)}))},changeStateSearch:function(){this.requestGoodsListApi()},clearScreen:function(){this.classifyState="",this.brandNameState="",this.productNameS="",this.requestGoodsListApi()},requestGoodsListApi:function(){var t=this,e=this;this.$post("/szmb/newclasscontroller/selectproductlistinventorypc",{storeId:e.Cookies.get("storeId"),shopClassId:""!=e.classifyState?e.classifyState:-1,brandId:""!=e.brandNameState?e.brandNameState:-1,productName:e.productNameS}).then((function(a){1===a.code?e.goodsTableData=a.data:0===a.code?e.goodsTableData=[]:(console.log(a.msg),t.$message.error(a.data))})).catch((function(t){console.log(t)}))},cancelNewAdd:function(t){console.log(t),t.state=0,t.abc=""},newAddEmptyBarrel:function(t){var e=this,a=this,s={number:Number(t.abc),storeId:a.Cookies.get("storeId"),brandId:t.brandId,brandName:t.brandName,header:"json"};a.$post("/szmb/inventory/insertemptybuckrecord",s).then((function(s){1===s.code?(a.$message({type:"success",message:s.data}),t.state=0,t.abc="",a.requestGoodsListApi()):(console.log(s.msg),e.$message.error(s.data))})).catch((function(t){console.log(t)}))},editPriceAndNum:function(t){t.childState=1},saveEditPrice:function(t){var e=this;console.log(t);var a=this,s=[{skuId:t.skuId,addNumber:Number(t.childAbc),money:t.cost,price:t.retailPrice}];a.$post("/szmb/szmstoreshopcontroller/updateskulist",{skuList:JSON.stringify(s)}).then((function(s){1===s.code?(a.$message({type:"success",message:s.data}),t.childState=0,t.childAbc="",a.requestGoodsListApi()):(console.log(s.msg),e.$message.error(s.data))})).catch((function(t){console.log(t)}))},cancelEditNum:function(t){t.childState=0,this.requestGoodsListApi()},lookDetailPage:function(t){this.newAddPageOff=!1,this.selectedKey=t,this.requestEmptyBarrelList(),this.requestJhList()},changeSelectedItem:function(t){this.selectedKey=t},newBarrelSearch:function(){this.requestEmptyBarrelList()},newBarrelresetClear:function(){this.newBarrelDateTime="",this.newBarrelBrand="",this.requestEmptyBarrelList()},handleSizeChange1:function(t){this.pagesData1.pageSize1=t,this.requestEmptyBarrelList()},handleCurrentChange1:function(t){this.pagesData1.currentPage1=t,this.requestEmptyBarrelList()},requestEmptyBarrelList:function(){var t=this,e=this;e.newBarrelLoading=!0;var a={storeId:e.Cookies.get("storeId"),startTime:e.newBarrelDateTime?e.newBarrelDateTime[0]:"",endTime:e.newBarrelDateTime?e.newBarrelDateTime[1]:"",brandId:""==e.newBarrelBrand?-1:e.newBarrelBrand,pages:e.pagesData1.currentPage1,number:e.pagesData1.pageSize1};this.$post("/szmb/inventory/selemptybuckrecordPc",a).then((function(a){1===a.code?(e.newBarrelTable=a.data.list,e.pagesData1.pageTotal1=a.data.count,e.newBarrelTotal=a.data.total):0===a.code?(e.newBarrelTable=[],e.pagesData1.pageTotal1=0,e.newBarrelTotal=0):(console.log(a.msg),t.$message.error(a.data)),e.newBarrelLoading=!1})).catch((function(t){console.log(t)}))},jhSearch:function(){this.requestJhList()},clearJhSearch:function(){this.jhBrand="",this.jhClass="",this.jhDataTime="",this.requestJhList()},handleSizeChange2:function(t){this.pagesData2.pageSize2=t,this.requestJhList()},handleCurrentChange2:function(t){this.pagesData2.currentPage2=t,this.requestJhList()},requestJhList:function(){var t=this,e=this;e.jhLoading=!0;var a={storeId:e.Cookies.get("storeId"),startTime:e.jhDataTime?e.jhDataTime[0]:"",endTime:e.jhDataTime?e.jhDataTime[1]:"",brandId:""==e.jhBrand?-1:e.jhBrand,classId:""==e.jhClass?-1:e.jhClass,pageNo:e.pagesData2.currentPage2,pageSize:e.pagesData2.pageSize2};this.$post("/szmb/szmstoreshopcontroller/selcheckpc",a).then((function(a){1===a.code?(e.jhTableList=a.data.list,e.pagesData2.pageTotal2=a.data.count,e.jhTotal=a.data.total):0===a.code?(e.newBarrelTable=[],e.pagesData1.pageTotal1=0,e.newBarrelTotal=0):(console.log(a.msg),t.$message.error(a.data)),e.jhLoading=!1})).catch((function(t){console.log(t)}))}},components:{}},n=l,r=(a("d154"),a("0c7c")),o=Object(r["a"])(n,s,i,!1,null,"77027314",null);e["default"]=o.exports},ee85:function(t,e,a){}}]);