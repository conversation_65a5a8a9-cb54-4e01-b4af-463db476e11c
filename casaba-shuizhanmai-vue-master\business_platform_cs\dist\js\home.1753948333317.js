(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["home"],{"03e8":function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e._self._c;return t("div",{staticStyle:{"min-width":"1651px","box-sizing":"border-box",margin:"0 auto"}},[t("div",{staticClass:"flex align-items-center justify-content-between box-sizing font-size-14",staticStyle:{width:"100%",height:"37px"}},[t("div",{staticClass:"flex align-items-center cursor-pointer"},[t("div",{class:[0==e.tapMenuValue?"active_top_box":"","top-box position-relative"],on:{click:function(t){return t.stopPropagation(),e.tapMenu(0)}}},[e._v("\n        水站代客下单\n      ")]),t("div",{class:[1==e.tapMenuValue?"active_top_box":"","top-box position-relative"],on:{click:function(t){return t.stopPropagation(),e.tapMenu(1)}}},[e._v("\n        水站代客下单汇总表\n      ")]),t("div",{class:[2==e.tapMenuValue?"active_top_box":"","top-box position-relative"],on:{click:function(t){return t.stopPropagation(),e.tapMenu(2)}}},[e._v("\n        水站代客下单明细表\n      ")]),t("div",{class:[3==e.tapMenuValue?"active_top_box":"","top-box position-relative"],on:{click:function(t){return t.stopPropagation(),e.tapMenu(3)}}},[e._v("\n        押桶/退桶\n      ")]),t("div",{class:[5==e.tapMenuValue?"active_top_box":"","top-box position-relative"],staticStyle:{"border-right":"1px solid rgba(216, 220, 229, 1)"},on:{click:function(t){return t.stopPropagation(),e.tapMenu(5)}}},[e._v("\n        物资管理\n      ")])]),t("div",{staticClass:"text-align-right"},[1==e.tapMenuValue?t("span",[e._v("自提客户总数："),t("span",{staticClass:"color-red"},[e._v(e._s(e.orderRecord.customerCount))])]):e._e()])]),0!=e.tapMenuValue?t("div",{staticClass:"flex align-items-center justify-content-between",staticStyle:{padding:"20px",border:"1px solid rgba(216,220,229,1)"}},[t("div",[3==e.tapMenuValue||4==e.tapMenuValue?t("el-select",{staticStyle:{width:"148px","margin-right":"20px"},attrs:{clearable:"",placeholder:"按桶品牌筛选"},model:{value:e.orderSelect.brandId,callback:function(t){e.$set(e.orderSelect,"brandId",t)},expression:"orderSelect.brandId"}},e._l(e.bucketBrandList,(function(e,r){return t("el-option",{key:r,attrs:{label:e.brandName,value:e.brandId}})})),1):e._e(),1==e.tapMenuValue?t("el-select",{staticStyle:{width:"148px","margin-right":"20px"},attrs:{clearable:"",placeholder:"按客户来源"},model:{value:e.orderSelect.userType,callback:function(t){e.$set(e.orderSelect,"userType",t)},expression:"orderSelect.userType"}},e._l(e.userTypeList,(function(e,r){return t("el-option",{key:r,attrs:{label:e.name,value:e.value}})})),1):e._e(),1==e.tapMenuValue||2==e.tapMenuValue||3==e.tapMenuValue&&1==e.bucketTap||4==e.tapMenuValue&&1==e.bucketTap||5==e.tapMenuValue&&1==e.bucketTap?t("div",{staticStyle:{display:"inline-block","margin-right":"20px"}},[t("el-date-picker",{staticStyle:{width:"250px"},attrs:{"value-format":"yyyy-MM-dd",type:"daterange",align:"right","unlink-panels":"","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":e.dateSelect},model:{value:e.orderSelect.date,callback:function(t){e.$set(e.orderSelect,"date",t)},expression:"orderSelect.date"}})],1):e._e(),t("el-input",{staticStyle:{width:"282px","margin-right":"20px"},attrs:{placeholder:"输入订单号/手机号/联系方式/地址搜索"},model:{value:e.orderSelect.isKey,callback:function(t){e.$set(e.orderSelect,"isKey",t)},expression:"orderSelect.isKey"}}),t("el-button",{attrs:{type:"primary"},on:{click:e.toSearch}},[e._v("查询")]),t("el-button",{staticStyle:{"margin-left":"20px"},on:{click:e.clearSearch}},[e._v("清空筛选条件")])],1),t("div",[4==e.tapMenuValue&&1==e.bucketTap?t("el-button",{attrs:{type:"success"},on:{click:function(t){return e.backBucketRecord()}}},[e._v("查看回桶记录")]):e._e(),5==e.tapMenuValue&&1==e.bucketTap?t("el-button",{attrs:{type:"success"},on:{click:e.backThingsRecord}},[e._v("查看还物记录")]):e._e()],1)]):e._e(),t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loadLoading,expression:"loadLoading"}]},[t("div",{directives:[{name:"show",rawName:"v-show",value:0==e.tapMenuValue,expression:"tapMenuValue==0"}],staticClass:"margin-top-30 flex align-item-center justify-content-center padding-bottom-30"},[t("div",{staticClass:"box-shadow box-sizing",staticStyle:{width:"747px",padding:"30px"}},[t("div",[t("div",{staticClass:"text-align-center font-size-30 padding-tb-20 border-bottom"},[e._v("水站代客下单")]),t("div",{staticClass:"text-align-center font-size-16 padding-tb-10 margin-top-20",staticStyle:{"background-color":"#F5F5F5"}},[e._v("基本信息")]),t("div",{staticClass:"margin-top-30"},[t("div",[t("el-form",{ref:"selfFormElement",attrs:{model:e.selfForm,rules:e.selfRules,"label-width":"180px"}},[t("el-form-item",{attrs:{label:"客户类型:"}},[t("span",[t("el-radio-group",{on:{change:e.changeUserType},model:{value:e.userType,callback:function(t){e.userType=t},expression:"userType"}},[t("el-radio",{attrs:{label:"0"}},[e._v("自提客户")]),t("el-radio",{attrs:{label:"1"}},[e._v("线上客户")])],1)],1)]),t("el-form-item",{attrs:{label:"客户姓名:",prop:"name"}},[0==e.userType?t("div",[t("el-autocomplete",{ref:"nameInputElement",staticStyle:{width:"250px"},attrs:{"fetch-suggestions":e.querySearchName,placeholder:"请输入姓名"},on:{select:e.chooseDownUser},scopedSlots:e._u([{key:"default",fn:function(r){var a=r.item;return[t("div",{staticClass:"flex align-items-center justify-content-between"},[t("div",{staticClass:"name font-size-14"},[e._v(e._s(a.name))]),t("div",{staticClass:"name color-grey font-size-12"},[e._v(e._s(a.phone))])])]}}],null,!1,882444057),model:{value:e.selfForm.name,callback:function(t){e.$set(e.selfForm,"name",t)},expression:"selfForm.name"}})],1):t("div",[t("el-select",{attrs:{filterable:"",remote:"","reserve-keyword":"",placeholder:"请输入客户手机号(至少4位)","remote-method":e.suggestLoadByPhone,loading:e.loading},on:{change:e.chooseUser},model:{value:e.userNameIndex,callback:function(t){e.userNameIndex=t},expression:"userNameIndex"}},e._l(e.userNameList,(function(r){return t("el-option",{key:r.index,attrs:{label:r.name?r.name:"无地址客户",value:r.index}},[t("span",{staticStyle:{float:"left"}},[e._v(e._s(r.name?r.name:"无地址客户"))]),t("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[e._v(e._s(r.phone))])])})),1)],1)]),t("el-form-item",{attrs:{label:"客户手机号:",prop:"phone"}},[0==e.userType?t("div",[t("el-input",{staticStyle:{width:"250px"},attrs:{placeholder:"请输入手机号"},model:{value:e.selfForm.phone,callback:function(t){e.$set(e.selfForm,"phone",t)},expression:"selfForm.phone"}})],1):t("div",[t("el-input",{staticStyle:{width:"250px"},attrs:{disabled:"",placeholder:"请输入手机号"},model:{value:e.selfForm.phone,callback:function(t){e.$set(e.selfForm,"phone",t)},expression:"selfForm.phone"}})],1)]),t("el-form-item",{staticClass:"formPointer",attrs:{label:"请选择商品自提分类:"}},[t("el-checkbox-group",{model:{value:e.todaySelfData.goodsType,callback:function(t){e.$set(e.todaySelfData,"goodsType",t)},expression:"todaySelfData.goodsType"}},[t("div",[e.todaySelfData.goodsClass[0].state?t("el-checkbox",{attrs:{label:"1"}},[e._v("桶装水")]):e._e()],1),t("div",[e.todaySelfData.goodsClass[1].state?t("el-checkbox",{attrs:{label:"2"}},[e._v("箱装水")]):e._e()],1),t("div",[e.todaySelfData.goodsClass[2].state?t("el-checkbox",{attrs:{label:"3"}},[e._v("饮水机")]):e._e()],1)])],1),t("el-form-item",[t("el-button",{attrs:{type:"primary"},on:{click:e.chooseGoodsFirst}},[e._v("选 择 商 品")])],1)],1)],1)])])])]),t("div",{directives:[{name:"show",rawName:"v-show",value:1==e.tapMenuValue,expression:"tapMenuValue==1"}],staticClass:"margin-top-20"},[1==e.tapMenuValue?t("el-table",{staticClass:"productList",staticStyle:{width:"100%"},attrs:{data:e.orderRecord.list,"header-cell-style":{"text-align":"center","background-color":"#EFF2F7"},"cell-style":{"text-align":"center","font-size":"13px",color:"#333C48"},stripe:"",border:"","summary-method":e.getSummaries,"max-height":e.inTableHeight}},[t("el-table-column",{attrs:{prop:"userName",label:"客户名称"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n            "+e._s(t.row.userName?t.row.userName:"无地址客户")+"\n          ")]}}],null,!1,1898805245)}),t("el-table-column",{attrs:{prop:"nickName",label:"客户备注名"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("span",[e._v(e._s(r.row.nickName?r.row.nickName:"-"))])]}}],null,!1,3301648494)}),t("el-table-column",{attrs:{prop:"userPhone",label:"联系方式"}}),t("el-table-column",{attrs:{prop:"userSource",label:"客户来源"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("span",[e._v(e._s(1==r.row.userSource?"自提客户":"线上客户"))])]}}],null,!1,3108155793)}),t("el-table-column",{attrs:{prop:"orderCount",label:"下单总数"}}),t("el-table-column",{attrs:{prop:"money",label:"总销售额"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("span",[e._v("￥"+e._s(r.row.money))])]}}],null,!1,71814808)}),t("el-table-column",{attrs:{prop:"cost",label:"总成本"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("span",[e._v("￥"+e._s(r.row.cost))])]}}],null,!1,4278280291)}),t("el-table-column",{attrs:{prop:"profit",label:"总利润"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("span",[e._v("￥"+e._s(r.row.profit))])]}}],null,!1,3943368446)}),t("el-table-column",{attrs:{label:"操作"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("span",{staticClass:"cursor-pointer color-blue",on:{click:function(t){return e.lookSummaryDetail(r.row)}}},[e._v("查看明细")])]}}],null,!1,1962876206)})],1):e._e(),t("div",{staticClass:"flex align-items-center justify-content-center"},[t("el-pagination",{staticStyle:{padding:"15px"},attrs:{background:"",layout:"prev, pager, next","current-page":e.page,"page-size":e.pageSize,total:e.orderRecord.count},on:{"current-change":e.changeSummaryPage,"update:currentPage":function(t){e.page=t},"update:current-page":function(t){e.page=t}}})],1)],1),t("div",{directives:[{name:"show",rawName:"v-show",value:2==e.tapMenuValue,expression:"tapMenuValue==2"}],staticClass:"margin-top-20"},[2==e.tapMenuValue?t("el-table",{staticClass:"productList",staticStyle:{width:"100%"},attrs:{data:e.summaryDetail.info.list,"header-cell-style":{"text-align":"center","background-color":"#EFF2F7"},"cell-style":{"text-align":"center","font-size":"13px",color:"#333C48"},stripe:"","max-height":e.inTableHeight}},[t("el-table-column",{attrs:{prop:"userName",label:"客户信息"},scopedSlots:e._u([{key:"default",fn:function(r){return[r.row.name||r.row.nickName?t("div",[e._v(e._s(r.row.name)+" "),r.row.name&&r.row.nickName?[e._v("|")]:e._e(),e._v(" "+e._s(r.row.nickName))],2):t("div",[e._v("无地址客户")]),t("div",[e._v(e._s(r.row.phone))])]}}],null,!1,659173460)}),t("el-table-column",{attrs:{prop:"orderNumber",label:"订单编码"}}),t("el-table-column",{attrs:{prop:"product",label:"购买商品信息"},scopedSlots:e._u([{key:"default",fn:function(r){return e._l(r.row.product,(function(r,a){return t("div",{key:a},[e._v(e._s(r.spuName)+" "+e._s(r.skuName)+" x "+e._s(r.num))])}))}}],null,!1,4262674044)}),t("el-table-column",{attrs:{prop:"money",label:"商品销售额"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("span",[e._v("￥"+e._s(r.row.money))])]}}],null,!1,71814808)}),t("el-table-column",{attrs:{prop:"cost",label:"当前商品成本"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("span",[e._v("￥"+e._s(r.row.cost))])]}}],null,!1,4278280291)}),t("el-table-column",{attrs:{prop:"profit",label:"当前商品利润"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("span",[e._v("￥"+e._s(r.row.profit))])]}}],null,!1,3943368446)}),t("el-table-column",{attrs:{prop:"userName",label:"押桶信息"},scopedSlots:e._u([{key:"default",fn:function(r){return e._l(r.row.buckList,(function(r,a){return t("div",{key:a},[e._v(e._s(r.buckName)+" ￥"+e._s(r.buckMoney)+" x "+e._s(r.buckNum))])}))}}],null,!1,1188683196)}),t("el-table-column",{attrs:{prop:"buckMoney",label:"当前押桶金额"},scopedSlots:e._u([{key:"default",fn:function(r){return[r.row.buckMoney?t("span",[e._v("￥"+e._s(r.row.buckMoney))]):e._e()]}}],null,!1,464018967)}),t("el-table-column",{attrs:{prop:"repay",label:"当前回桶信息"},scopedSlots:e._u([{key:"default",fn:function(r){return e._l(r.row.repay,(function(r,a){return t("div",{key:a},[0==r.replace?[e._v("\n                "+e._s(r.replaceName)+" x "+e._s(r.replaceNumber)+"\n              ")]:e._e(),1==r.replace?[e._v("\n                "+e._s(r.replaceName)+" x "+e._s(r.replaceNumber)+" 抵扣 "+e._s(r.r3)+" x "+e._s(r.buckNumber)+"\n              ")]:e._e()],2)}))}}],null,!1,598360579)}),t("el-table-column",{attrs:{prop:"type",label:"支付方式"},scopedSlots:e._u([{key:"default",fn:function(r){return[0==r.row.type?t("span",[e._v("现金")]):e._e(),1==r.row.type?t("span",[e._v("线下微信")]):e._e(),2==r.row.type?t("span",[e._v("线下支付宝")]):e._e(),4==r.row.type?t("span",[e._v("月结付款")]):e._e(),3==r.row.type?t("span",[e._v("银行转账")]):e._e()]}}],null,!1,159489521)}),t("el-table-column",{attrs:{prop:"type",label:"收款状态"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("span",[e._v(e._s(1==r.row.delState?"已收款":"未收款"))])]}}],null,!1,2106485040)}),t("el-table-column",{attrs:{fixed:"right",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(r){return[1==r.row.repayState?t("el-button",{attrs:{type:"text"},on:{click:function(t){return e.backBucketFun(r.row)}}},[e._v("回桶")]):e._e(),0==r.row.delState&&3==r.row.type?t("el-button",{attrs:{type:"text"},on:{click:function(t){return e.receiptMoney(r.row)}}},[e._v("确认收款")]):e._e()]}}],null,!1,1331584114)})],1):e._e(),t("div",{staticClass:"flex align-items-center justify-content-center"},[t("el-pagination",{staticStyle:{padding:"15px"},attrs:{background:"",layout:"prev, pager, next","current-page":e.page,"page-size":e.pageSize,total:e.summaryDetail.info.total},on:{"current-change":e.changeSummaryDetailPage,"update:currentPage":function(t){e.page=t},"update:current-page":function(t){e.page=t}}})],1)],1),t("div",{directives:[{name:"show",rawName:"v-show",value:3==e.tapMenuValue,expression:"tapMenuValue==3"}]},[t("div",{staticClass:"flex-box-c"},[t("div",{staticClass:"tabMenu flex align-items-center cursor-pointer padding-bottom-20"},[t("div",{class:[0==e.bucketTap?"actTabMenu":""],on:{click:function(t){return t.stopPropagation(),e.changeBucketTap(0)}}},[e._v("\n            押桶/退桶汇总表\n          ")]),t("div",{class:[1==e.bucketTap?"actTabMenu":""],on:{click:function(t){return t.stopPropagation(),e.changeBucketTap(1)}}},[e._v("\n            押桶/退桶明细表\n          ")])]),t("el-button",{attrs:{type:"success"},on:{click:function(){return e.$router.push({name:"bucketInfo",params:{state:1,type:"selfGoods"}})}}},[e._v("查看欠桶汇总表")])],1),t("div",{directives:[{name:"show",rawName:"v-show",value:0==e.bucketTap,expression:"bucketTap==0"}]},[3==e.tapMenuValue?t("el-table",{staticClass:"productList",staticStyle:{width:"100%"},attrs:{data:e.pledgeBucket.list,"header-cell-style":{"text-align":"center","background-color":"#EFF2F7"},"cell-style":{"text-align":"center","font-size":"13px",color:"#333C48"},stripe:"","max-height":e.inTableHeight}},[t("el-table-column",{attrs:{type:"index",label:"序号"}}),t("el-table-column",{attrs:{prop:"userName",label:"客户姓名|备注名"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("span",[e._v(e._s(r.row.userName)+" "),r.row.userName&&r.row.userNickName?[e._v("|")]:e._e(),e._v(" "+e._s(r.row.userNickName))],2)]}}],null,!1,2961836400)}),t("el-table-column",{attrs:{prop:"phone",label:"手机号码"}}),t("el-table-column",{attrs:{prop:"brandName",label:"桶品牌"}}),t("el-table-column",{attrs:{prop:"pledgMoney",label:"桶单价"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("span",[e._v("￥"+e._s(r.row.pledgMoney))])]}}],null,!1,2491738242)}),t("el-table-column",{attrs:{prop:"pledgCount",label:"押桶总数"}}),t("el-table-column",{attrs:{prop:"redisCount",label:"剩余未退桶总数"}}),t("el-table-column",{attrs:{prop:"redisMoney",label:"剩余未退桶总金额"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("span",[e._v("￥"+e._s(r.row.redisMoney))])]}}],null,!1,3310407793)}),t("el-table-column",{attrs:{label:"操作"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("span",{staticClass:"cursor-pointer color-blue",on:{click:function(t){return e.goPledBucketDetail(r.row)}}},[e._v("查看明细")])]}}],null,!1,2913338590)})],1):e._e(),t("div",{staticClass:"padding flex align-items-center justify-content-between margin-top-20",staticStyle:{"background-color":"#EFF2F7"}},[t("div",[e._v("押桶总数："+e._s(e.pledgeBucket.info.pledgCount))]),t("div",{staticClass:"margin-left-30"},[e._v("退桶总数："+e._s(e.pledgeBucket.info.bucketCount))]),t("div",{staticClass:"margin-left-30"},[e._v("剩余未退总数："+e._s(e.pledgeBucket.info.redisPledgCount))]),t("div",{staticClass:"margin-left-30"},[e._v("剩余未退总金额：￥"+e._s(e.pledgeBucket.info.debtMoney))])]),t("div",{staticClass:"flex align-items-center justify-content-center"},[t("el-pagination",{staticStyle:{padding:"15px"},attrs:{background:"",layout:"prev, pager, next","current-page":e.page,"page-size":e.pageSize,total:e.pledgeBucket.count},on:{"current-change":e.clickPledgeBucketPage,"update:currentPage":function(t){e.page=t},"update:current-page":function(t){e.page=t}}})],1)],1),t("div",{directives:[{name:"show",rawName:"v-show",value:1==e.bucketTap,expression:"bucketTap==1"}]},[3==e.tapMenuValue?t("el-table",{staticClass:"productList",staticStyle:{width:"100%"},attrs:{data:e.pledgeBucketRecord.list,"header-cell-style":{"text-align":"center","background-color":"#EFF2F7"},"cell-style":{"text-align":"center","font-size":"13px",color:"#333C48"},stripe:"","max-height":e.inTableHeight}},[t("el-table-column",{attrs:{type:"index",label:"序号"}}),t("el-table-column",{attrs:{prop:"orderNumber",label:"订单编码"}}),t("el-table-column",{attrs:{prop:"time",label:"日期"}}),t("el-table-column",{attrs:{prop:"userName",label:"客户信息"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("div",[e._v(e._s(r.row.userName)+" "),r.row.userName&&r.row.nickName?[e._v("|")]:e._e(),e._v(" "+e._s(r.row.nickName))],2),t("div",[e._v(e._s(r.row.phone))])]}}],null,!1,2016435422)}),t("el-table-column",{attrs:{prop:"productName",label:"商品信息"},scopedSlots:e._u([{key:"default",fn:function(r){return e._l(r.row.productList,(function(r,a){return t("div",{key:a,staticClass:"text-align-center"},[e._v("\n                "+e._s(r.spuName)+" x "+e._s(r.num)+"\n              ")])}))}}],null,!1,722642073)}),t("el-table-column",{attrs:{prop:"productName",label:"本次押桶信息"},scopedSlots:e._u([{key:"default",fn:function(r){return e._l(r.row.product,(function(r,a){return t("div",{key:a,staticClass:"text-align-center"},[e._v("\n                "+e._s(r.name)+" ￥"+e._s(r.money)+" x "+e._s(r.num)+"\n              ")])}))}}],null,!1,1148160110)}),t("el-table-column",{attrs:{prop:"userName",label:"杂牌抵扣信息"},scopedSlots:e._u([{key:"default",fn:function(r){return e._l(r.row.replaceList,(function(r,a){return t("div",{key:a,staticClass:"text-align-center"},[e._v("\n                用"+e._s(r.replaceName)+" x "+e._s(r.replaceNumber)+" 抵扣 "+e._s(r.r3)+" x "+e._s(r.buckNumber)+"\n              ")])}))}}],null,!1,2134543462)}),t("el-table-column",{attrs:{prop:"money",label:"补差价"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("span",[e._v(e._s("0.00"!=r.row.money?"￥"+r.row.money:""))])]}}],null,!1,638426415)}),t("el-table-column",{attrs:{prop:"buck",label:"退桶信息"},scopedSlots:e._u([{key:"default",fn:function(r){return e._l(r.row.buck,(function(r,a){return t("div",{key:a,staticClass:"text-align-center"},[e._v("\n                "+e._s(r.name)+" x "+e._s(r.num)+"\n              ")])}))}}],null,!1,1108713145)}),t("el-table-column",{attrs:{prop:"redisBuck",label:"当前剩余未退桶信息"},scopedSlots:e._u([{key:"default",fn:function(r){return e._l(r.row.redisBuck,(function(r,a){return t("div",{key:a,staticClass:"text-align-center"},[e._v("\n                "+e._s(r.name)+" x "+e._s(r.num)+"\n              ")])}))}}],null,!1,336663152)}),t("el-table-column",{attrs:{label:"操作"},scopedSlots:e._u([{key:"default",fn:function(r){return[2==r.row.orderType?t("span"):t("span",[r.row.redisBuck[0].num>0?t("span",{staticClass:"cursor-pointer color-blue",on:{click:function(t){return e.returnBucket(r.row)}}},[e._v("退桶")]):t("span",{staticClass:"cursor-pointer color-grey"},[e._v("已退完")])])]}}],null,!1,2794275670)})],1):e._e(),t("div",{staticClass:"flex align-items-center justify-content-center"},[t("el-pagination",{staticStyle:{padding:"15px"},attrs:{background:"",layout:"prev, pager, next","current-page":e.page,"page-size":e.pageSize,total:e.pledgeBucketRecord.count},on:{"current-change":e.clickPledgeBucketDetailPage,"update:currentPage":function(t){e.page=t},"update:current-page":function(t){e.page=t}}})],1)],1)]),t("div",{directives:[{name:"show",rawName:"v-show",value:4==e.tapMenuValue,expression:"tapMenuValue==4"}]},[t("div",{staticClass:"tabMenu flex align-items-center cursor-pointer padding-bottom-20"},[t("div",{class:[0==e.bucketTap?"actTabMenu":""],on:{click:function(t){return t.stopPropagation(),e.changeBucketTap(0)}}},[e._v("\n          回桶/欠桶汇总表\n        ")]),t("div",{class:[1==e.bucketTap?"actTabMenu":""],on:{click:function(t){return t.stopPropagation(),e.changeBucketTap(1)}}},[e._v("\n          回桶/欠桶明细表\n        ")])]),t("div",{directives:[{name:"show",rawName:"v-show",value:0==e.bucketTap,expression:"bucketTap==0"}]},[t("el-table",{staticClass:"productList",staticStyle:{width:"100%"},attrs:{data:e.oweBucket.list,"header-cell-style":{"text-align":"center","background-color":"#EFF2F7"},"cell-style":{"text-align":"center","font-size":"13px",color:"#333C48"},stripe:"",border:"","max-height":e.inTableHeight}},[t("el-table-column",{attrs:{type:"index",width:"80",label:"序号"}}),t("el-table-column",{attrs:{prop:"userName",label:"客户姓名|备注名"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("span",[e._v(e._s(r.row.userName)+" "),r.row.userName&&r.row.userNickName?[e._v("|")]:e._e(),e._v(" "+e._s(r.row.userNickName))],2)]}}])}),t("el-table-column",{attrs:{prop:"phone",label:"手机号码"}}),t("el-table-column",{attrs:{prop:"brandName",label:"桶品牌"}}),t("el-table-column",{attrs:{prop:"debtCount",label:"自提汇总欠桶数"}}),t("el-table-column",{attrs:{label:"操作"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("span",{staticClass:"cursor-pointer color-blue",on:{click:function(t){return e.goOweBucketDetail(r.row)}}},[e._v("查看明细")])]}}])})],1),t("div",{staticClass:"flex align-items-center justify-content-center"},[t("el-pagination",{staticStyle:{padding:"15px"},attrs:{background:"",layout:"prev, pager, next","current-page":e.page,"page-size":e.pageSize,total:e.oweBucket.count},on:{"current-change":e.clickOweBucketPage,"update:currentPage":function(t){e.page=t},"update:current-page":function(t){e.page=t}}})],1)],1),t("div",{directives:[{name:"show",rawName:"v-show",value:1==e.bucketTap,expression:"bucketTap==1"}]},[t("el-table",{staticClass:"productList",staticStyle:{width:"100%"},attrs:{data:e.oweBucketRecord.list,"header-cell-style":{"text-align":"center","background-color":"#EFF2F7"},"cell-style":{"text-align":"center","font-size":"13px",color:"#333C48"},stripe:"",border:"","max-height":e.inTableHeight}},[t("el-table-column",{attrs:{type:"index",width:"80",label:"序号"}}),t("el-table-column",{attrs:{prop:"userName",label:"客户信息"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("div",[e._v(e._s(r.row.userName)+" "),r.row.userName&&r.row.nickName?[e._v("|")]:e._e(),e._v(" "+e._s(r.row.nickName))],2),t("div",[e._v(e._s(r.row.phone))])]}}])}),t("el-table-column",{attrs:{prop:"orderNumber",label:"订单编码"}}),t("el-table-column",{attrs:{prop:"userName",label:"购买商品信息"},scopedSlots:e._u([{key:"default",fn:function(r){return e._l(r.row.product,(function(r,a){return t("div",{key:a,staticClass:"text-align-center"},[e._v("\n                "+e._s(r.spuName)+" x "+e._s(r.num)+"\n              ")])}))}}])}),t("el-table-column",{attrs:{prop:"pledg",label:"押桶信息"},scopedSlots:e._u([{key:"default",fn:function(r){return e._l(r.row.pledg,(function(r,a){return t("div",{key:a,staticClass:"text-align-center"},[e._v("\n                "+e._s(r.name)+" x "+e._s(r.num)+"\n              ")])}))}}])}),t("el-table-column",{attrs:{prop:"buckList",label:"回桶信息"},scopedSlots:e._u([{key:"default",fn:function(r){return e._l(r.row.buckList,(function(r,a){return t("div",{key:a,staticClass:"text-align-center"},[e._v("\n                "+e._s(r.name)+" x "+e._s(r.num)+"\n              ")])}))}}])}),t("el-table-column",{attrs:{prop:"duoNum",label:"多回桶数量"}}),t("el-table-column",{attrs:{prop:"replaceBuckList",label:"抵扣桶信息"},scopedSlots:e._u([{key:"default",fn:function(r){return e._l(r.row.replaceBuckList,(function(r,a){return t("div",{key:a,staticClass:"text-align-center"},[e._v("\n                用"+e._s(r.replaceName)+" x "+e._s(r.replaceNumber)+" 抵扣 "+e._s(r.r3)+" x "+e._s(r.buckNumber)+"\n              ")])}))}}])}),t("el-table-column",{attrs:{prop:"money",label:"补差价"}}),t("el-table-column",{attrs:{prop:"redisNum",label:"自提汇总欠桶"}})],1),t("div",{staticClass:"flex align-items-center justify-content-center"},[t("el-pagination",{staticStyle:{padding:"15px"},attrs:{background:"",layout:"prev, pager, next","current-page":e.page,"page-size":e.pageSize,total:e.oweBucket.count},on:{"current-change":e.clickOweBucketPage,"update:currentPage":function(t){e.page=t},"update:current-page":function(t){e.page=t}}})],1)],1)]),t("div",{directives:[{name:"show",rawName:"v-show",value:5==e.tapMenuValue,expression:"tapMenuValue==5"}]},[t("div",{staticClass:"tabMenu flex align-items-center cursor-pointer padding-bottom-20"},[t("div",{class:[0==e.bucketTap?"actTabMenu":""],on:{click:function(t){return t.stopPropagation(),e.changeBucketTap(0)}}},[e._v("\n          物资管理汇总表\n        ")]),t("div",{class:[1==e.bucketTap?"actTabMenu":""],on:{click:function(t){return t.stopPropagation(),e.changeBucketTap(1)}}},[e._v("\n          物资管理明细表\n        ")])]),t("div",{directives:[{name:"show",rawName:"v-show",value:0==e.bucketTap,expression:"bucketTap==0"}]},[t("el-table",{key:"wzhz",staticClass:"productList",staticStyle:{width:"100%"},attrs:{data:e.borrowThingsData.list,"header-cell-style":{"text-align":"center","background-color":"#EFF2F7"},"cell-style":{"text-align":"center","font-size":"13px",color:"#333C48"},stripe:"",border:"","max-height":e.inTableHeight}},[t("el-table-column",{attrs:{type:"index",width:"80",label:"序号"}}),t("el-table-column",{attrs:{prop:"userName",label:"客户姓名丨备注名"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("span",[e._v(e._s(r.row.userName)+" "),r.row.userName&&r.row.nickName?[e._v("|")]:e._e(),e._v(" "+e._s(r.row.nickName))],2)]}}])}),t("el-table-column",{attrs:{prop:"userPhone",label:"手机号码"}}),t("el-table-column",{attrs:{prop:"productName",label:"物资名称"}}),t("el-table-column",{attrs:{prop:"objectNum",label:"借物总数"}}),t("el-table-column",{attrs:{prop:"retrunNum",label:"还物总数"}}),t("el-table-column",{attrs:{prop:"",label:"剩余未还总数"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n              "+e._s(t.row.objectNum-t.row.retrunNum)+"\n            ")]}}])}),t("el-table-column",{attrs:{label:"操作"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("span",{staticClass:"cursor-pointer color-blue",on:{click:function(t){return e.goWuZhiAdminDetail(r.row)}}},[e._v("查看明细")])]}}])})],1),t("div",{staticClass:"flex align-items-center justify-content-center"},[t("el-pagination",{staticStyle:{padding:"15px"},attrs:{background:"",layout:"prev, pager, next","current-page":e.page,"page-size":e.pageSize,total:e.borrowThingsData.count},on:{"current-change":e.clickBorrowThingsPage,"update:currentPage":function(t){e.page=t},"update:current-page":function(t){e.page=t}}})],1)],1),t("div",{directives:[{name:"show",rawName:"v-show",value:1==e.bucketTap,expression:"bucketTap==1"}]},[t("el-table",{key:"wzmx",staticClass:"productList",staticStyle:{width:"100%"},attrs:{data:e.borrowThingsRecordData.list,"header-cell-style":{"text-align":"center","background-color":"#EFF2F7"},"cell-style":{"text-align":"center","font-size":"13px",color:"#333C48"},stripe:"",border:"","max-height":e.inTableHeight}},[t("el-table-column",{attrs:{type:"index",width:"80",label:"序号"}}),t("el-table-column",{attrs:{prop:"userName",label:"客户信息"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("div",[e._v(e._s(r.row.r2)+" "),r.row.r2&&r.row.r4?[e._v("|")]:e._e(),e._v(" "+e._s(r.row.r4)+"\n                "),t("br"),e._v("\n                "+e._s(r.row.r5)+"\n              ")],2)]}}])}),t("el-table-column",{attrs:{prop:"createTime",label:"借物时间"}}),t("el-table-column",{attrs:{prop:"objectName",label:"物资名称"}}),t("el-table-column",{attrs:{prop:"objectNum",label:"借物总数"}}),t("el-table-column",{attrs:{prop:"returnNum",label:"还物总数"}}),t("el-table-column",{attrs:{prop:"",label:"剩余未还总数"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n              "+e._s(t.row.objectNum-t.row.returnNum)+"\n            ")]}}])}),t("el-table-column",{attrs:{prop:"redisNum",width:"150",label:"本次还物数量"},scopedSlots:e._u([{key:"default",fn:function(r){return[r.row.objectNum-r.row.returnNum>0?t("div",[t("el-input",{attrs:{placeholder:"输入还物数量",oninput:"value=value.replace(/[^\\d]/g,'')"},model:{value:r.row.replace,callback:function(t){e.$set(r.row,"replace",t)},expression:"scope.row.replace"}})],1):e._e()]}}])}),t("el-table-column",{attrs:{label:"操作"},scopedSlots:e._u([{key:"default",fn:function(r){return[r.row.objectNum-r.row.returnNum>0?t("div",[t("el-button",{staticStyle:{"font-size":"14px"},attrs:{type:"text"},on:{click:function(t){return e.backThings(r.row)}}},[e._v("还物")])],1):t("div",{staticClass:"color-grey"},[e._v("已退完")])]}}])})],1),t("div",{staticClass:"flex align-items-center justify-content-center"},[t("el-pagination",{staticStyle:{padding:"15px"},attrs:{background:"",layout:"prev, pager, next","current-page":e.page,"page-size":e.pageSize,total:e.oweBucket.count},on:{"current-change":e.clickOweBucketPage,"update:currentPage":function(t){e.page=t},"update:current-page":function(t){e.page=t}}})],1)],1)])]),t("el-dialog",{attrs:{title:"",visible:e.returnBucketRecordData.dialog,width:"940px"},on:{"update:visible":function(t){return e.$set(e.returnBucketRecordData,"dialog",t)}}},[t("div",[t("div",{staticClass:"text-align-center font-size-30 padding-tb-20 border-bottom"},[e._v("退桶记录")]),t("div",{staticClass:"text-align-center font-size-16 padding-tb-10 margin-top-20",staticStyle:{"background-color":"#F5F5F5"}},[e._v("退桶记录")]),t("div",{staticClass:"margin-top-30"},[t("el-table",{staticClass:"productList",staticStyle:{width:"100%"},attrs:{data:e.returnBucketRecordData.list,"header-cell-style":{"text-align":"center","background-color":"#EFF2F7"},"cell-style":{"text-align":"center","font-size":"13px",color:"#333C48"},stripe:"",border:"","max-height":"760"}},[t("el-table-column",{attrs:{label:"序号",width:"80",type:"index"}}),t("el-table-column",{attrs:{label:"自提订单编码",prop:"orderNumber"}}),t("el-table-column",{attrs:{label:"退桶时间",prop:"time"}}),t("el-table-column",{attrs:{label:"商品信息",prop:"name"}}),t("el-table-column",{attrs:{label:"已退桶数量",prop:"num"}}),t("el-table-column",{attrs:{label:"剩余未退桶数量",prop:"redisNum"}})],1)],1)])]),t("el-dialog",{attrs:{title:"",visible:e.returnBucketDialog,width:"747px"},on:{"update:visible":function(t){e.returnBucketDialog=t}}},[t("div",[t("div",{staticClass:"text-align-center font-size-30 padding-tb-20 border-bottom"},[e._v("退桶单")]),t("div",{staticClass:"text-align-center font-size-16 padding-tb-10 margin-top-20",staticStyle:{"background-color":"#F5F5F5"}},[e._v("退品牌桶")]),t("div",{staticClass:"margin-top-30 font-size-16"},e._l(e.returnBucketData.brandList,(function(r,a){return t("div",{key:a,staticClass:"flex align-items-center justify-content-between margin-bottom-20"},[t("div",{staticStyle:{width:"200px"}},[e._v("桶品牌："+e._s(r.name))]),t("div",{staticClass:"flex align-items-center"},[t("div",{staticStyle:{width:"80px"}},[e._v("退桶数量：")]),t("div",{staticStyle:{width:"200px"}},[t("el-input",{attrs:{placeholder:"请输入退桶数量",oninput:"value=value.replace(/[^\\d]/g,'')"},model:{value:r.replace,callback:function(t){e.$set(r,"replace",t)},expression:"item.replace"}})],1)]),t("div",{staticStyle:{width:"150px"}},[r.num-r.replace-e.returnBucketData.replaceBrandNum>0?t("div",{staticClass:"color-red"},[e._v("剩余未退桶数量"+e._s(r.num-r.replace-e.returnBucketData.replaceBrandNum)+"个")]):t("div",{staticClass:"color-green"},[e._v("已退完")])])])})),0),e.countReturnBucketNum>0?t("div",[t("div",{staticClass:"text-align-center font-size-16 padding-tb-10",staticStyle:{"background-color":"#F5F5F5"}},[e._v("用退桶抵扣")]),t("div",{staticClass:"font-size-16 margin-top-30"},[t("div",{staticClass:"flex align-items-center justify-content-between"},[t("div",{staticClass:"flex align-items-center"},[t("div",{staticStyle:{width:"100px"}},[e._v("其他品牌：")]),t("div",{staticStyle:{width:"198px"}},[t("el-select",{attrs:{clearable:"",placeholder:"请选择"},model:{value:e.returnBucketData.brandId,callback:function(t){e.$set(e.returnBucketData,"brandId",t)},expression:"returnBucketData.brandId"}},e._l(e.returnBucketSelectOptions.brandList,(function(e,r){return t("el-option",{key:r,attrs:{label:e.brandName,value:e.brandId}})})),1)],1)]),t("div",{staticStyle:{width:"290px"}},[t("el-input",{attrs:{placeholder:"请输入其他品牌桶的数量",oninput:"value=value.replace(/[^\\d]/g,'')"},model:{value:e.returnBucketData.brandNum,callback:function(t){e.$set(e.returnBucketData,"brandNum",t)},expression:"returnBucketData.brandNum"}})],1)]),t("div",{staticClass:"flex align-items-center justify-content-between margin-top-20"},[t("div",{staticClass:"flex align-items-center"},[t("div",{staticStyle:{width:"100px"}}),t("div",{staticClass:"flex align-items-center",staticStyle:{width:"198px"}},[t("div",{staticStyle:{width:"50px"}},[e._v("抵扣：")]),t("div",{staticStyle:{width:"148px"}},[t("el-select",{attrs:{clearable:"",placeholder:"请选择"},model:{value:e.returnBucketData.replaceBrandId,callback:function(t){e.$set(e.returnBucketData,"replaceBrandId",t)},expression:"returnBucketData.replaceBrandId"}},e._l(e.returnBucketSelectOptions.replaceBrandList,(function(e,r){return t("el-option",{key:r,attrs:{label:e.name,value:e.brandId}})})),1)],1)])]),t("div",{staticStyle:{width:"290px"}},[t("el-input",{attrs:{placeholder:"请输入抵扣品牌桶的数量",oninput:"value=value.replace(/[^\\d]/g,'')"},model:{value:e.returnBucketData.replaceBrandNum,callback:function(t){e.$set(e.returnBucketData,"replaceBrandNum",t)},expression:"returnBucketData.replaceBrandNum"}})],1)]),t("div",{staticClass:"flex align-items-center justify-content-between margin-top-20"},[t("div",{staticClass:"flex align-items-center"},[t("div",{staticStyle:{width:"100px"}},[e._v("退桶补差价：")]),t("div",{staticStyle:{width:"198px"}},[t("el-input",{attrs:{placeholder:"请输入金额",oninput:"value=value.replace(/[^\\d.]/g,'')"},model:{value:e.returnBucketData.money,callback:function(t){e.$set(e.returnBucketData,"money",t)},expression:"returnBucketData.money"}})],1)]),t("div",{staticClass:"color-red",staticStyle:{width:"290px"}},[e._v("\n              退桶品牌不一致需要补差价\n            ")])]),t("div",{staticClass:"color-red flex align-items-center wrap",staticStyle:{padding:"30px 0px"}},[e._v("\n            本次剩余未退桶总数：\n            "),e._l(e.returnBucketData.brandList,(function(r,a){return t("div",{key:a},[r.num-r.replace-e.returnBucketData.replaceBrandNum>0?t("div",{staticClass:"color-red flex"},[e._v("\n                "+e._s(r.num-r.replace-e.returnBucketData.replaceBrandNum)+"个\n                ("),e._l(e.returnBucketData.brandList,(function(r,a){return t("div",{key:a,staticClass:"margin-left-20"},[r.num-r.replace>0?t("span",[e._v(e._s(r.name)+" x "+e._s(r.num-r.replace))]):e._e()])})),e._v(")\n              ")],2):t("div",{staticClass:"color-green"},[e._v("已退完")])])}))],2)])]):e._e()]),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:e.cancelReturnBucket}},[e._v("取 消")]),t("el-button",{attrs:{type:"primary"},on:{click:e.sureReturnBucket}},[e._v("确 定")])],1)]),t("el-dialog",{attrs:{title:"",visible:e.backBucketRecordData.dialog,width:"940"},on:{"update:visible":function(t){return e.$set(e.backBucketRecordData,"dialog",t)}}},[t("div",[t("div",{staticClass:"text-align-center font-size-30 padding-tb-20 border-bottom"},[e._v("回桶记录")]),t("div",{staticClass:"text-align-center font-size-16 padding-tb-10 margin-top-20",staticStyle:{"background-color":"#F5F5F5"}},[e._v("回桶记录")]),t("div",{staticClass:"margin-top-30"},[t("el-table",{staticClass:"productList",staticStyle:{width:"100%"},attrs:{data:e.backBucketRecordData.list,"header-cell-style":{"text-align":"center","background-color":"#EFF2F7"},"cell-style":{"text-align":"center","font-size":"13px",color:"#333C48"},stripe:"",border:"","max-height":"760"}},[t("el-table-column",{attrs:{label:"序号",width:"80",type:"index"}}),t("el-table-column",{attrs:{label:"自提订单编码",prop:"orderNumber"}}),t("el-table-column",{attrs:{label:"回桶时间",prop:"time"}}),t("el-table-column",{attrs:{label:"商品信息",prop:"name"}}),t("el-table-column",{attrs:{label:"已回桶数量",prop:"num"}}),t("el-table-column",{attrs:{label:"剩余未回桶数量",prop:"redisNum"}})],1)],1)])]),t("el-dialog",{attrs:{visible:e.returnThingsRecordData.dialog,width:"940"},on:{"update:visible":function(t){return e.$set(e.returnThingsRecordData,"dialog",t)}}},[t("div",[t("div",{staticClass:"text-align-center font-size-30 padding-tb-20 border-bottom"},[e._v("还物记录")]),t("div",{staticClass:"text-align-center font-size-16 padding-tb-10 margin-top-20",staticStyle:{"background-color":"#F5F5F5"}},[e._v("还物记录")]),t("div",{staticClass:"margin-top-30"},[t("el-table",{staticClass:"productList",staticStyle:{width:"100%"},attrs:{data:e.returnThingsRecordData.list,"header-cell-style":{"text-align":"center","background-color":"#EFF2F7"},"cell-style":{"text-align":"center","font-size":"13px",color:"#333C48"},stripe:"",border:"","max-height":"760"}},[t("el-table-column",{attrs:{label:"序号",width:"80",type:"index"}}),t("el-table-column",{attrs:{label:"自提订单编码",prop:"borrowOrderNum"}}),t("el-table-column",{attrs:{label:"还物时间",prop:"createTime"}}),t("el-table-column",{attrs:{label:"物资名称",prop:"repayObjectName"}}),t("el-table-column",{attrs:{label:"已还物数量",prop:"repayObjectNum"}}),t("el-table-column",{attrs:{label:"剩余未还物数量",prop:"r5"}})],1),t("div",{staticClass:"flex align-items-center justify-content-center"},[t("el-pagination",{staticStyle:{padding:"15px"},attrs:{background:"",layout:"prev, pager, next","current-page":e.returnThingsRecordData.page,"page-size":e.returnThingsRecordData.pageSize,total:e.returnThingsRecordData.count},on:{"current-change":e.clickReturnThingsPage,"update:currentPage":function(t){return e.$set(e.returnThingsRecordData,"page",t)},"update:current-page":function(t){return e.$set(e.returnThingsRecordData,"page",t)}}})],1)],1)])]),t("el-dialog",{staticClass:"detailDialog detailDialogGoods",attrs:{width:"80%",title:"选择商品",visible:e.goodsDialog},on:{"update:visible":function(t){e.goodsDialog=t}}},[t("div",[t("div",{staticClass:"flex align-items-center justify-content-between"},[t("div",{staticClass:"flex align-items-center"},[t("el-input",{staticStyle:{width:"200px","margin-right":"10px"},attrs:{placeholder:"请输入商品或规格名称",clearable:""},on:{clear:e.chooseGoods},model:{value:e.selfForm.searchKey,callback:function(t){e.$set(e.selfForm,"searchKey",t)},expression:"selfForm.searchKey"}}),t("el-button",{attrs:{type:"primary"},on:{click:e.toSearchProduct}},[e._v("搜索")]),t("div",{staticClass:"margin-left-30 color-red"},[e._v("销售总额:￥"+e._s(e.saleTotal))]),t("div",{staticClass:"margin-left-30 color-red"},[e._v("押金总额:￥"+e._s(e.pledgeTotal))]),t("div",{staticClass:"margin-left-30 color-red"},[e._v("合计:￥"+e._s(Number(e.saleTotal)+Number(e.pledgeTotal)))])],1),t("div",{staticClass:"flex align-items-center"},[t("div",{staticClass:"color-red margin-right-10"},[e._v("选择订单支付方式 ")]),t("el-select",{staticStyle:{width:"150px"},attrs:{placeholder:"请选择支付方式"},model:{value:e.selfForm.payType,callback:function(t){e.$set(e.selfForm,"payType",t)},expression:"selfForm.payType"}},[t("el-option",{attrs:{label:"请选择",value:""}}),t("el-option",{attrs:{label:"现金",value:"0"}}),t("el-option",{attrs:{label:"线下微信",value:"1"}}),t("el-option",{attrs:{label:"线下支付宝",value:"2"}}),1==e.payShowOff.yhzzOff?t("el-option",{attrs:{label:"银行转账",value:"3"}}):e._e(),1==e.payShowOff.yfOff?t("el-option",{attrs:{label:"月结付款",value:"4"}}):e._e()],1),4==e.selfForm.payType?[t("div",{staticClass:"color-red margin-lr-10"},[e._v("选择押桶支付方式 ")]),t("el-select",{staticStyle:{width:"150px"},attrs:{placeholder:"请选择押桶支付方式"},model:{value:e.selfForm.YTpayType,callback:function(t){e.$set(e.selfForm,"YTpayType",t)},expression:"selfForm.YTpayType"}},[t("el-option",{attrs:{label:"请选择",value:""}}),t("el-option",{attrs:{label:"银行转账",value:"5"}}),t("el-option",{attrs:{label:"线下微信",value:"1"}})],1)]:e._e()],2)]),t("el-table",{ref:"sendGoodsElement",staticClass:"productList",staticStyle:{width:"100%"},attrs:{data:e.selfForm.goodsList,"header-cell-style":{"text-align":"center"},"cell-style":{"text-align":"center","font-size":"13px",color:"#333C48"},"max-height":e.inTableHeight},on:{select:e.sendGoodsCheck,"row-click":e.sendGoodsClickRow}},[t("el-table-column",{attrs:{type:"selection",width:"55"}}),t("el-table-column",{attrs:{prop:"",label:"商品图",width:"100"},scopedSlots:e._u([{key:"default",fn:function(e){return[t("img",{staticStyle:{width:"75px",height:"75px"},attrs:{src:e.row.content,alt:""}})]}}])}),t("el-table-column",{attrs:{prop:"spuName",label:"名称"}}),t("el-table-column",{attrs:{prop:"skuName",label:"规格"}}),t("el-table-column",{attrs:{prop:"inventory",label:"库存"}}),t("el-table-column",{attrs:{prop:"price",label:"成本价"}}),t("el-table-column",{attrs:{prop:"retailPrice",label:"零售价",width:"150"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("el-input",{attrs:{placeholder:"请输入零售价格"},on:{input:function(t){return e.getRetail(t,r.row,r.$index)}},model:{value:r.row.retailPrice,callback:function(t){e.$set(r.row,"retailPrice",t)},expression:"scope.row.retailPrice"}})]}}])}),t("el-table-column",{attrs:{prop:"num",label:"数量",width:"150"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("el-input-number",{attrs:{min:0,label:"输入数量"},on:{change:e.updateChange},model:{value:r.row.num,callback:function(t){e.$set(r.row,"num",t)},expression:"scope.row.num"}})]}}])}),t("el-table-column",{attrs:{prop:"pledgeBucketNum",label:"押桶数量",width:"150"},scopedSlots:e._u([{key:"default",fn:function(r){return[r.row.buckClass?t("el-input-number",{attrs:{min:0,max:Number(Number(r.row.num)+Number(r.row.debtCount)),label:"输入数量"},on:{change:e.updateChange},model:{value:r.row.pledgeBucketNum,callback:function(t){e.$set(r.row,"pledgeBucketNum",t)},expression:"scope.row.pledgeBucketNum"}}):t("span",[e._v("--")])]}}])}),t("el-table-column",{attrs:{prop:"borrowObjectName",label:"借物名称",width:"150"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("el-input",{attrs:{placeholder:"请输入借物名称"},model:{value:r.row.borrowObjectName,callback:function(t){e.$set(r.row,"borrowObjectName",t)},expression:"scope.row.borrowObjectName"}})]}}])}),t("el-table-column",{attrs:{prop:"borrowObjectNum",label:"借物数量",width:"150"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("el-input-number",{attrs:{min:0,label:"输入数量"},model:{value:r.row.borrowObjectNum,callback:function(t){e.$set(r.row,"borrowObjectNum",t)},expression:"scope.row.borrowObjectNum"}})]}}])})],1)],1),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("div",{staticClass:"flex align-items-center justify-content-between"},[t("div"),t("div",[t("el-button",{on:{click:function(t){e.goodsDialog=!1}}},[e._v("取 消")]),t("el-button",{attrs:{type:"primary",loading:e.submitLoading},on:{click:e.orderSubmit}},[e._v("确 定")])],1)]),e.selfForm.bucketInfo?t("div",{staticClass:"flex align-items-center margin-top-10"},[t("div",{staticClass:"bold margin-right-30"},[e._v("桶管理")]),t("div",[e.selfForm.bucketInfo.pledgeResidueNum?t("div",{staticClass:"flex align-items-center",staticStyle:{"margin-top":"5px"}},[t("div",[e._v("[押桶]剩余未退总数 "),t("span",{staticClass:"color-red"},[e._v(e._s(e.selfForm.bucketInfo.pledgeResidueNum)+"个")])]),t("div",{staticClass:"flex align-items-center justify-content-between padding-lr-10"},e._l(e.selfForm.bucketInfo.pledgeResidueList,(function(r,a){return t("span",{key:a,staticClass:"margin-right-10"},[e._v(e._s(r))])})),0)]):e._e(),e.selfForm.bucketInfo.oweResidueNum?t("div",{staticClass:"flex align-items-center",staticStyle:{"margin-top":"5px"}},[t("div",[e._v("[欠桶]剩余未还总数 "),t("span",{staticClass:"color-red"},[e._v(e._s(e.selfForm.bucketInfo.oweResidueNum)+"个")])]),t("div",{staticClass:"flex align-items-center justify-content-between padding-lr-10"},e._l(e.selfForm.bucketInfo.oweResidueList,(function(r,a){return t("span",{key:a,staticClass:"margin-right-10"},[e._v(e._s(r))])})),0)]):e._e(),e.selfForm.bucketInfo.returnResidueNum?t("div",{staticClass:"flex align-items-center",staticStyle:{"margin-top":"5px"}},[t("div",[e._v("回桶总数 "),t("span",{staticClass:"color-red"},[e._v(e._s(e.selfForm.bucketInfo.returnResidueNum)+"个")])]),t("div",{staticClass:"flex align-items-center justify-content-between padding-lr-10"},e._l(e.selfForm.bucketInfo.returnResidueList,(function(r,a){return t("span",{key:a,staticClass:"margin-right-10"},[e._v(e._s(r))])})),0)]):e._e()])]):e._e()])]),t("el-dialog",{attrs:{title:"回桶",visible:e.backBucketDialog,width:"600px"},on:{"update:visible":function(t){e.backBucketDialog=t}}},[t("el-form",{attrs:{model:e.backBucketForm,"label-position":"left","label-width":"130px"}},[t("el-form-item",{attrs:{label:"桶品牌"}},e._l(e.backBucketForm.showList,(function(r,a){return t("div",{key:a,staticClass:"showListBox flex align-items-center justify-content-between margin-bottom-10"},[t("div",{staticStyle:{width:"40%"}},[e._v(e._s(r.brandName))]),t("div",{staticStyle:{width:"40%"}},[t("el-input",{attrs:{placeholder:"请输入数量"},on:{input:function(t){return e.getBrandNum(t,a)}},model:{value:r.backNum,callback:function(t){e.$set(r,"backNum",t)},expression:"item.backNum"}})],1),r.number-r.backNum-r.deductNum>0?t("div",{staticClass:"color-red text-align-right",staticStyle:{width:"20%"}},[e._v("\n            欠"+e._s(r.number-r.backNum-r.deductNum)+"个\n          ")]):t("div",{staticClass:"color-green text-align-right",staticStyle:{width:"20%"}},[Number(r.number)-Number(r.backNum)-Number(r.deductNum)<0?[e._v(" 多回"+e._s(Number(r.backNum)-Number(r.number)+Number(r.deductNum))+"个")]:[e._v("已还完")]],2)])})),0),e.backBucketForm.isShowReplace?t("div",[t("el-form-item",{attrs:{label:"其他品牌"}},[t("div",{staticClass:"flex align-items-center"},[t("el-select",{staticStyle:{width:"260px"},attrs:{placeholder:"请选择品牌"},model:{value:e.backBucketForm.allBrandIndex,callback:function(t){e.$set(e.backBucketForm,"allBrandIndex",t)},expression:"backBucketForm.allBrandIndex"}},e._l(e.backBucketForm.allBrandList,(function(e,r){return t("el-option",{key:r,attrs:{label:e.brandName,value:r}})})),1),t("div",{staticClass:"box-sizing",staticStyle:{"margin-left":"30px"}},[t("el-input",{attrs:{placeholder:"请输入数量"},model:{value:e.backBucketForm.otherNum,callback:function(t){e.$set(e.backBucketForm,"otherNum",t)},expression:"backBucketForm.otherNum"}})],1)],1)]),t("el-form-item",{attrs:{label:""}},[t("div",{staticClass:"flex align-items-center"},[t("div",{staticStyle:{width:"40px"}},[e._v("抵扣")]),t("el-select",{staticStyle:{width:"220px"},attrs:{placeholder:"请选择品牌"},on:{change:function(t){e.backBucketForm.bucketNum=""}},model:{value:e.backBucketForm.brandIndex,callback:function(t){e.$set(e.backBucketForm,"brandIndex",t)},expression:"backBucketForm.brandIndex"}},e._l(e.backBucketForm.brandList,(function(e,r){return t("el-option",{key:r,attrs:{label:e.brandName,value:r}})})),1),t("div",{staticClass:"box-sizing",staticStyle:{"margin-left":"30px"}},[t("el-input",{attrs:{placeholder:"请输入数量"},on:{input:function(t){return e.getBucketNum(t)}},model:{value:e.backBucketForm.bucketNum,callback:function(t){e.$set(e.backBucketForm,"bucketNum",t)},expression:"backBucketForm.bucketNum"}})],1)],1)]),t("el-form-item",{attrs:{label:"空桶补差价"}},[t("div",[t("el-input",{attrs:{placeholder:"请输入金额"},model:{value:e.backBucketForm.money,callback:function(t){e.$set(e.backBucketForm,"money",t)},expression:"backBucketForm.money"}})],1),t("div",{staticClass:"color-red"},[e._v("回桶品牌不一致需要补差价")])]),t("el-form-item",{attrs:{label:"选择支付方式"}},[t("el-select",{attrs:{clearable:"",placeholder:"请选择"},model:{value:e.backBucketForm.payTType,callback:function(t){e.$set(e.backBucketForm,"payTType",t)},expression:"backBucketForm.payTType"}},[t("el-option",{attrs:{label:"线下微信",value:1}}),t("el-option",{attrs:{label:"线下支付宝",value:3}}),t("el-option",{attrs:{label:"现金",value:4}})],1)],1)],1):e._e(),t("el-row",{attrs:{gutter:30}},[e._l(e.backBucketForm.showList,(function(r,a){return[r.number-r.backNum-r.deductNum>0?t("el-col",{key:a,staticClass:"margin-bottom-10",attrs:{span:8}},[t("div",[e._v("\n              共欠"+e._s(r.brandName)+"\n              "),t("span",{staticClass:"color-red"},[e._v(e._s(r.number-r.backNum-r.deductNum))]),e._v("个\n            ")])]):e._e()]}))],2)],1),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.backBucketDialog=!1}}},[e._v("取 消")]),t("el-button",{attrs:{type:"primary"},on:{click:e.backBucketSubmit}},[e._v("确 定")])],1)],1)],1)},o=[],n={props:{},data:function(){var e=this,t=function(t,r,a){if(r){var o=e.$util.checkPhone(r);o?a():a(new Error("请输入有效的手机号码"))}else a()},r=function(e,t,r){r()};return{tapMenuValue:0,bucketTap:0,page:1,pageSize:20,bucketBrandList:[],userTypeList:[{name:"线上客户",value:"0"},{name:"自提客户",value:"1"}],orderSelect:{brandId:"",userType:"",date:"",isKey:""},bucketSelect:{userId:"",bucketId:"",objectName:"",buckMoney:"",brandId:""},summaryDetail:{info:{}},returnBucketDialog:!1,returnBucketData:{brandList:[],brandId:"",brandNum:"",replaceBrandId:"",replaceBrandNum:"",money:"",payId:"",orderNumber:"",userId:"",newOrder:""},returnBucketSelectOptions:{brandList:[],replaceBrandList:[],payType:[{name:"线下微信",payId:"0"},{name:"线下支付宝",payId:"1"},{name:"现金",payId:"2"}]},pageFlag:0,userType:"0",todaySelfData:{recordList:[],goodsType:[],goodsClass:[{classId:1,state:!1},{classId:2,state:!1},{classId:3,state:!1}],recordCollect:[]},selfForm:{name:"",userIDid:"",phone:"",goodsList:[],changeList:[],checkList:[],searchKey:"",bucketInfo:"",backBucketBrand:"",backBucketNum:"",payType:"",YTpayType:""},restaurantsList:[],restaurantsName:[],restaurantsPhone:[],userNameList:[],userNameIndex:"",selfRules:{name:[{validator:r,trigger:"change"}],phone:[{validator:t,trigger:"change"},{required:!0,message:"手机号码不能为空",trigger:"blur"}]},goodsDialog:!1,orderDialog:!1,dateSelect:{disabledDate:function(t){return t.getTime()<new Date(JSON.parse(e.Cookies.get("storeInfo")).startTime).getTime()-864e5||t.getTime()>Date.now()}},orderRecord:{list:[],customerCount:0,count:0,info:""},orderRecordPage:1,bucketFlag:0,pledgeBucket:{count:0,info:"",list:[]},oweBucket:{count:0,list:[]},pledgeBucketRecord:{bucketId:"",count:0,info:"",list:[],page:1},oweBucketRecord:{bucketId:"",info:"",count:0,list:[],page:1},pledgeBucketDialog:!1,pledgeBucketForm:{bucketId:"",brandName:"",num:"",price:"",maxNum:0},oweBucketDialog:!1,oweBucketForm:{bucketId:"",brandName:"",num:"",maxNum:0},returnBucketRecordData:{dialog:!1,list:[],page:1,pageSize:10,count:0,id:0},backBucketRecordData:{dialog:!1,list:[],page:1,pageSize:10,count:0,id:0},returnThingsRecordData:{dialog:!1,list:[],page:1,pageSize:10,count:0,id:0},borrowThingsData:{list:[],count:0},borrowThingsRecordData:{list:[],count:0},loadLoading:!1,submitLoading:!1,showThisTips:!1,backBucketDialog:!1,backBucketForm:{showList:[],isShowReplace:!1,brandList:[],allBrandList:[],allBrandIndex:0,brandIndex:0,bucketNum:"",otherNum:"",money:"",payTType:""},useUserId:"",orderNNumber:"",payShowOff:{yfOff:1,yhzzOff:1}}},computed:{tableHeight:function(){console.log(this.$store.getters.getGlobalHeight);var e=Number(this.$store.getters.getGlobalHeight)-300;return e>=300?e:500},inTableHeight:function(){var e=this.$store.getters.getGlobalHeight;return e>=400?parseInt(this.$util.mul(e,.5)):500},countGoods:function(){var e=this;return e.selfForm.checkList.length},countNum:function(){var e=this,t=e.selfForm.checkList,r=0;return t.forEach((function(t){r=e.$util.add(t.num,r)})),r},countPrice:function(){var e=this,t=e.selfForm.checkList,r=0;return t.forEach((function(t){r=e.$util.add(e.$util.mul(t.num,t.retailPrice),r)})),r.toFixed(2)},pledgeBucketCount:function(){var e=this,t=e.selfForm.checkList,r=0;return t.forEach((function(t){r=e.$util.add(t.pledgeBucketNum,r)})),r},pledgeBucketTotal:function(){var e=this,t=e.selfForm.checkList,r=0;return t.forEach((function(t){r=e.$util.add(e.$util.mul(t.pledgeBucketNum,t.buckMoney),r)})),r.toFixed(2)},oweBucketCount:function(){var e=this,t=e.selfForm.checkList,r=0;return t.forEach((function(t){r=e.$util.add(t.oweBucketNum,r)})),r},returnBucketTotal:function(){var e=this;return e.$util.mul(e.pledgeBucketForm.price,e.pledgeBucketForm.num).toFixed(2)},saleTotal:function(){var e=this,t=e.selfForm.changeList,r=0;return t.forEach((function(t){r=e.$util.add(e.$util.mul(t.retailPrice,t.num),r)})),r.toFixed(2)},pledgeTotal:function(){var e=this,t=e.selfForm.changeList,r=0;return t.forEach((function(t){r=e.$util.add(e.$util.mul(t.buckMoney,t.pledgeBucketNum),r)})),r.toFixed(2)},countReturnBucketNum:function(){var e=this,t=e.returnBucketData.brandList;console.log(7788);var r=0;return t.forEach((function(e){r=e.num-e.replace+r})),r}},created:function(){},mounted:function(){var e=this;e.load(),e.lookUpBrand(),e.lookUpReplaceBrand()},watch:{},methods:{remoteMethod:function(e){var t=this;""!==e?(this.loading=!0,setTimeout((function(){t.loading=!1,t.options=t.list.filter((function(t){return t.label.toLowerCase().indexOf(e.toLowerCase())>-1}))}),200)):this.options=[]},tapMenu:function(e){var t=this;t.bucketTap=0,t.tapMenuValue=e,2==e&&(this.showThisTips=!1),t.clearSearch()},clearSearch:function(e){var t=this;t.orderSelect={brandId:"",userType:"",date:"",isKey:""},t.bucketSelect={userId:"",bucketId:"",objectName:"",buckMoney:"",brandId:""},this.showThisTips=!1,t.page=1,0!=t.tapMenuValue&&(t.loadLoading=!0),1==t.tapMenuValue?t.getOrderRecord():2==t.tapMenuValue?t.getOrderRecordDetail():3==t.tapMenuValue?0==t.bucketTap?t.goBucketAdmin():t.lookUpPledBucketDetail():4==t.tapMenuValue?0==t.bucketTap?t.lookUpOweBucket():t.lookUpOweBucketDetail():5==t.tapMenuValue&&(0==t.bucketTap?t.lookUpWuZhiAdmin():t.lookUpWuZhiAdminDetail())},toSearch:function(){var e=this;1==e.tapMenuValue?e.getOrderRecord():2==e.tapMenuValue?(this.showThisTips=!1,e.bucketSelect.userId="",e.getOrderRecordDetail()):3==e.tapMenuValue?0==e.bucketTap?e.goBucketAdmin():e.lookUpPledBucketDetail():4==e.tapMenuValue?0==e.bucketTap?e.lookUpOweBucket():e.lookUpOweBucketDetail():5==e.tapMenuValue&&(0==e.bucketTap?e.lookUpWuZhiAdmin():e.lookUpWuZhiAdminDetail())},returnBucket:function(e){var t=this;t.returnBucketData.brandList=JSON.parse(JSON.stringify(e.redisBuck)),t.returnBucketSelectOptions.replaceBrandList=JSON.parse(JSON.stringify(e.redisBuck)),t.returnBucketData.orderNumber=e.orderNumber,t.returnBucketData.userId=e.userId,t.returnBucketData.newOrder=e.newOrder,t.returnBucketDialog=!0},cancelReturnBucket:function(){var e=this;e.returnBucketData={brandList:[],brandId:"",brandNum:"",replaceBrandId:"",replaceBrandNum:"",money:"",payId:"",orderNumer:"",userId:"",newOrder:"",price:""},e.returnBucketDialog=!1},sureReturnBucket:function(){var e=this,t=e.returnBucketData;console.log(t,"datadata");var r=[];if(e.subOff=!0,t.brandList.forEach((function(a){if(a.replace>0){var o={replaceBrandId:a.brandId,replaceName:a.name,replaceNumber:a.replace,replace:0,buckPrice:a.price,newOrder:t.newOrder,userAffrim:1};r.push(o)}else e.subOff=!1;a.brandId==t.replaceBrandId&&(t.price=a.price)})),""!==t.brandId||t.brandNum||""!==t.replaceBrandId||t.replaceBrandNum||t.money)if(""!==t.brandId&&"0"!=t.brandNum&&""!=t.brandNum&&""!==t.replaceBrandId&&"0"!=t.replaceBrandNum&&""!=t.replaceBrandNum){var a=!1;if(t.brandList.forEach((function(e){e.brandId==t.replaceBrandId&&Number(e.replace)+Number(t.replaceBrandNum)>e.num&&(a=!0)})),a)return void e.$message.error("抵扣数量与退桶数量之和不能超过本品牌应退桶数量");var o={replaceBrandId:t.brandId,replaceNumber:t.brandNum,replace:1,r1:t.replaceBrandId,buckNumber:t.replaceBrandNum,buckPrice:t.price,newOrder:t.newOrder,userAffrim:1};r.push(o),e.subOff=!0}else e.subOff=!1;if(e.subOff||0!=r.length){var n="/szmb/newdebtbuckcontroller/insertbucketbuck",s={orderNumber:t.orderNumber,deliveryId:0,buckList:JSON.stringify(r),money:t.money?t.money:0,storeId:e.Cookies.get("storeId"),userId:t.userId,source:2,payType:t.payId};console.log(s),e.$post(n,s).then((function(t){1==t.code?(e.$message({type:"success",message:"退桶成功！"}),e.cancelReturnBucket(),e.lookUpPledBucketDetail()):e.$message.error(t.data)}))}else e.$message.error("请完善退桶信息")},lookSummaryDetail:function(e){var t=this;t.bucketSelect.userId=e.userId,this.showThisTips=!0,t.getOrderRecordDetail()},getOrderRecordDetail:function(){var e=this,t="/szmb/storesendordercontroller/selectuserorderdeatil",r={storeId:e.Cookies.get("storeId"),userId:e.bucketSelect.userId?e.bucketSelect.userId:0,name:e.orderSelect.isKey,stateTime:e.orderSelect.date?e.orderSelect.date[0]:"",endTime:e.orderSelect.date?e.orderSelect.date[1]:"",index:e.page,pageSize:e.pageSize};e.$post(t,r).then((function(t){console.log(t),e.loadLoading=!1,e.tapMenuValue=2,1==t.code&&(e.summaryDetail.info=t.data)}))},changeSummaryPage:function(e){var t=this;t.page=e,t.getOrderRecord()},changeSummaryDetailPage:function(e){var t=this;console.log(e,"page"),t.page=e,t.getOrderRecordDetail()},changeBucketTap:function(e){var t=this;t.page=1,t.bucketTap=e,t.orderSelect={brandId:"",userType:"",date:"",isKey:""},t.bucketSelect={userId:"",bucketId:"",objectName:"",buckMoney:"",brandId:""},console.log(e),0==e?3==t.tapMenuValue?t.goBucketAdmin():4==t.tapMenuValue?t.lookUpOweBucket():5==t.tapMenuValue&&t.lookUpWuZhiAdmin():3==t.tapMenuValue?t.lookUpPledBucketDetail():4==t.tapMenuValue?t.lookUpOweBucketDetail():5==t.tapMenuValue&&t.lookUpWuZhiAdminDetail()},goPledBucketDetail:function(e){var t=this;console.log(e),t.bucketSelect.userId=e.userId,t.bucketSelect.buckMoney=e.pledgMoney,t.bucketSelect.brandId=e.brandId,t.lookUpPledBucketDetail()},lookUpPledBucketDetail:function(){var e=this,t="/szmb/newdebtbuckcontroller/selectpledgeandbucklist",r={storeId:e.Cookies.get("storeId"),brandId:e.bucketSelect.brandId||(""===e.orderSelect.brandId?-1:e.orderSelect.brandId),stateTime:e.orderSelect.date?e.orderSelect.date[0]:"",endTime:e.orderSelect.date?e.orderSelect.date[1]:"",name:e.orderSelect.isKey,index:e.page,pageSize:e.pageSize,userId:e.bucketSelect.userId?e.bucketSelect.userId:0,buckMoney:e.bucketSelect.buckMoney,source:2};e.$post(t,r).then((function(t){console.log(t),e.loadLoading=!1,e.bucketTap=1,1==t.code&&(e.pledgeBucketRecord.list=t.data.list,e.pledgeBucketRecord.count=t.data.total)}))},lookUpOweBucket:function(){var e=this,t="/szmb/newdebtbuckcontroller/selectdebtdeatil",r={storeId:e.Cookies.get("storeId"),brandId:""===e.orderSelect.brandId?-1:e.orderSelect.brandId,name:e.orderSelect.isKey,index:e.page,pageSize:e.pageSize,source:2};e.$post(t,r).then((function(t){e.loadLoading=!1,1==t.code?(e.oweBucket.list=t.data.list,e.oweBucket.count=t.data.total):(e.oweBucket.list=[],e.oweBucket.count=0)}))},goOweBucketDetail:function(e){var t=this;t.bucketSelect.userId=e.userId,t.bucketSelect.brandId=e.brandId,t.lookUpOweBucketDetail()},lookUpOweBucketDetail:function(){var e=this,t="/szmb/newdebtbuckcontroller/selectdebtlistproduct",r={storeId:e.Cookies.get("storeId"),brandId:e.bucketSelect.brandId||(""===e.orderSelect.brandId?-1:e.orderSelect.brandId),name:e.orderSelect.isKey,index:e.page,pageSize:e.pageSize,source:2,userId:e.bucketSelect.userId?e.bucketSelect.userId:0,stateTime:e.orderSelect.date?e.orderSelect.date[0]:"",endTime:e.orderSelect.date?e.orderSelect.date[1]:""};e.$post(t,r).then((function(t){e.loadLoading=!1,1==t.code?(e.bucketTap=1,e.oweBucketRecord.list=t.data.list,e.oweBucketRecord.count=t.data.total):(e.oweBucketRecord.list=[],e.oweBucketRecord.count=0)}))},lookUpBrand:function(){var e=this,t="szmb/productclasscontroller/selectstorebrand",r={storeId:e.Cookies.get("storeId")};e.$post(t,r).then((function(t){1==t.code?e.bucketBrandList=t.data:e.$message.error(t.data)}))},lookUpReplaceBrand:function(){var e=this,t="/api/debtbuckcontroller/selectstorebrandlist",r={storeId:e.Cookies.get("storeId")};e.$post(t,r).then((function(t){1==t.code&&(e.returnBucketSelectOptions.brandList=t.data)}))},load:function(e){var t=this,r="szmb/szmbuserextractcontroller/todayrecord",a={storeId:t.Cookies.get("storeId")};t.$post(r,a).then((function(r){t.loadLoading=!1,1==r.code?(t.todaySelfData.goodsClass=r.data.toDayRecordProductVos,t.todaySelfData.recordList=r.data.toDayRecordUserDetailsVos,console.log(t.todaySelfData.goodsClass,"that.todaySelfData.goodsClass,that.todaySelfData.goodsClass"),e&&(t.pageFlag=1)):t.$message.error(r.data)}))},lookUserInfo:function(e){var t=this,r="/szmb/szmbuserextractcontroller/selectbyphone1",a={phone:t.selfForm.phone,storeId:t.Cookies.get("storeId"),name:t.selfForm.name};t.$post(r,a).then((function(r){console.log(r),1==r.code&&(t.selfForm.bucketInfo=r.data.details,"function"==typeof e&&e(r))}))},chooseUser:function(e){var t=this,r=t.userNameList;t.selfForm.name=r[e].name,t.selfForm.phone=r[e].phone,t.useUserId=r[e].userId,t.getUserDetail()},getUserDetail:function(){var e=this,t="szmb/szmbuserextractcontroller/lastrecord",r={storeId:e.Cookies.get("storeId"),name:e.selfForm.name,phone:e.selfForm.phone};e.$post(t,r).then((function(t){console.log(t),1==t.code?e.todaySelfData.recordCollect=t.data:e.$message.error(t.data)}))},checkPhoneYF:function(e){console.log("hdjkahdjkah");var t=this,r="/szmb/szmbuserextractcontroller/selectuserswitch",a={storeId:t.Cookies.get("storeId"),phone:t.selfForm.phone};t.$post(r,a).then((function(t){console.log(t),1==t.code&&"function"==typeof e&&e(t.data)}))},checkPhoneUp:function(e){var t=this,r="/szmb/szmbuserextractcontroller/selectByPhone2",a={storeId:t.Cookies.get("storeId"),phone:t.selfForm.phone};t.$post(r,a).then((function(t){console.log(t),1==t.code&&"function"==typeof e&&e(t.data)}))},submitToOpenGoods:function(){var e=this;e.loadLoading=!0;var t="/szmb/szmshopgroupcontroller/selectclassbystoreid",r={storeId:e.Cookies.get("storeId"),classList:JSON.stringify(e.todaySelfData.goodsType),userName:e.selfForm.name,phone:e.selfForm.phone};e.$post(t,r).then((function(t){if(e.lookUserInfo(),e.loadLoading=!1,1==t.code){e.selfForm.goodsList=t.data;var r=t.data.filter((function(e){return 1==e.skuState}));console.log(r),e.selfForm.checkList=r,e.selfForm.changeList=r?JSON.parse(JSON.stringify(r)):[],e.goodsDialog=!0,e.$nextTick((function(){var r=this,a=e.selfForm.checkList;a.length&&t.data.forEach((function(e){a.forEach((function(t){e.skuId==t.skuId&&(e.num=t.num,e.pledgeBucketNum=t.pledgeBucketNum,e.oweBucketNum=t.oweBucketNum,e.retailPrice=t.retailPrice,r.$refs.sendGoodsElement.toggleRowSelection(e))}))}))}))}else e.$message.error(t.data)}))},chooseGoodsFirst:function(){var e=this;console.log(e.todaySelfData.goodsType,"goodsType"),e.todaySelfData.goodsType.length?e.$refs.selfFormElement.validate((function(t){t&&(0==e.userType?e.checkPhoneUp((function(t){1==t?e.submitToOpenGoods():e.$message.error("线上已有此客户")})):e.submitToOpenGoods(),e.checkPhoneYF((function(t){console.log("检查",t),e.payShowOff={yfOff:t.yf,yhzzOff:t.yxzz}})))})):e.$message.error("请勾选自提商品的商品分类")},chooseGoods:function(){var e=this;e.$refs.selfFormElement.validate((function(t){if(t){e.orderDialog=!1;var r="/szmb/szmshopgroupcontroller/selectclassbystoreid",a={storeId:e.Cookies.get("storeId"),classList:JSON.stringify(e.todaySelfData.goodsType),userName:e.selfForm.name,phone:e.selfForm.phone};e.$post(r,a).then((function(t){console.log(t),1==t.code?(e.selfForm.goodsList=t.data,e.goodsDialog=!0,e.$nextTick((function(){var r=this,a=e.selfForm.checkList;a.length&&t.data.forEach((function(e){a.forEach((function(t){e.skuId==t.skuId&&(e.num=t.num,e.pledgeBucketNum=t.pledgeBucketNum,e.oweBucketNum=t.oweBucketNum,e.retailPrice=t.retailPrice,r.$refs.sendGoodsElement.toggleRowSelection(e))}))}))}))):e.$message.error(t.data)}))}}))},sendGoodsClickRow:function(e,t,r){console.log(99999999);var a=this,o=a.selfForm.goodsList;1!=e.buckClass||e.maxNum||(console.log(e),console.log(t),o.forEach((function(t){e.skuId==t.skuId&&(t.maxNum=10)})),console.log(o),a.selfForm.goodsList=o)},sendGoodsCheck:function(e,t){var r=this,a=-1!=e.indexOf(t);a?r.selfForm.changeList.push(t):r.selfForm.changeList=r.$util.arrRemoveJson(r.selfForm.changeList,"skuId",t.skuId)},sureGoods:function(){var e=this,t=e.selfForm.changeList,r=e.selfForm.goodsList,a=[];if(t.forEach((function(e){r.forEach((function(t){e.skuId==t.skuId&&a.push(t)}))})),e.selfForm.changeList=a,e.selfForm.changeList.length){var o=!0;if(e.selfForm.changeList.forEach((function(e){e.retailPrice||(o=!1)})),o){var n=JSON.stringify(e.selfForm.changeList);e.selfForm.checkList=JSON.parse(n),e.goodsDialog=!1,e.orderDialog=!0}else e.$message.error("请填写所选商品的零售价")}else e.$message.error("请选择需要自提的商品")},toSearchProduct:function(){var e=this,t="/szmb/szmshopgroupcontroller/newselectproductname",r={storeId:e.Cookies.get("storeId"),shopGroupName:e.selfForm.searchKey,phone:e.selfForm.phone,userName:e.selfForm.name,classList:JSON.stringify(e.todaySelfData.goodsType)};console.log(r),e.$post(t,r).then((function(t){if(console.log(t),1==t.code){var r=t.data;console.log(r,"list"),r.length?(e.selfForm.goodsList=t.data,e.$nextTick((function(){var t=e.selfForm.changeList;r.forEach((function(r){t.forEach((function(t){r.skuId==t.skuId&&(e.$refs.sendGoodsElement.toggleRowSelection(r,!0),r.num=t.num,r.retailPrice=t.retailPrice)}))}))}))):e.$message.error("未搜索到相关商品")}else e.$message.error("暂无数据")}))},dealOrderSubmitData:function(e){var t=this;t.submitLoading=!0;var r=e.changeList,a=e.goodsList,o=[];r.forEach((function(e){a.forEach((function(t){e.skuId==t.skuId&&o.push(t)}))})),t.selfForm.changeList=o;var n=o.map((function(e){var t={skuId:e.skuId,skuName:e.skuName,spuName:e.spuName,num:e.num,retailPrice:e.retailPrice,price:e.price,url:e.content,pledgeBucketNum:e.pledgeBucketNum,oweBucketNum:e.oweBucketNum,returnName:e.returnName,returnNum:e.returnNum,priceSpread:e.priceSpread,borrowObjectName:e.borrowObjectName,borrowObjectNum:e.borrowObjectNum};return t}));t.allNumFororder=0,n.forEach((function(e){t.allNumFororder+=Number(e.num)}));var s="/szmb/szmbuserextractcontroller/addOrder";"4"==e.payType||4==Number(e.payType)?t.ooo={orderCount:t.allNumFororder,orderMoney:t.countPrice,orderProducts:JSON.stringify(n),storeId:t.Cookies.get("storeId"),userName:e.name,userPhone:e.phone,pledgPayType:e.YTpayType,r1:e.payType,r2:e.backBucketBrand,r3:e.backBucketNum,header:"json"}:t.ooo={orderCount:t.allNumFororder,orderMoney:t.countPrice,orderProducts:JSON.stringify(n),storeId:t.Cookies.get("storeId"),userName:e.name,userPhone:e.phone,r1:e.payType,r2:e.backBucketBrand,r3:e.backBucketNum,header:"json"},t.$post(s,t.ooo).then((function(e){t.submitLoading=!1,console.log("giao",e),1==e.code?(t.$message({message:"提交成功!",type:"success"}),t.orderNNumber=e.data.orderNumber,t.useUserId=e.data.userId,1==e.data.state&&t.backBucket(),t.orderDialog=!1,t.goodsDialog=!1,t.load(),t.selfForm={name:"",phone:"",goodsList:[],changeList:[],checkList:[],searchKey:"",bucketInfo:"",backBucketBrand:"",backBucketNum:"",payType:""},t.todaySelfData.recordCollect=[],t.userNameIndex=""):t.$message.error(e.data)}))},orderSubmit:function(e){var t=this,r=t.selfForm;if(console.log("1233123",r),t.selfForm.changeList.length){var a=t.selfForm.goodsList,o=t.selfForm.changeList,n=[];a.forEach((function(e){o.forEach((function(t){e.skuId==t.skuId&&n.push(e)}))})),t.selfForm.changeList=n,t.selfForm.checkList=JSON.parse(JSON.stringify(n));var s=!0,i=!0,c=!0,l=!0,u=!1;if(t.selfForm.changeList.forEach((function(e){e.num>0&&(Number(e.retailPrice)||(s=!1)),e.borrowObjectNum>0&&(e.borrowObjectName||(i=!1)),console.log(e.num,"商品数量",e.priceSpread,"差价",e.returnNum,"回桶数量",e.pledgeBucketNum,"押桶数量",e.oweBucketNum,"欠桶数量",e.borrowObjectNum,"借物数量"),0!=e.num||e.priceSpread||0!=e.pledgeBucketNum||0!=e.oweBucketNum||0!=e.borrowObjectNum||(c=!1),(e.num||e.pledgeBucketNum||e.priceSpread)&&(console.log("支付方式",t.selfForm.payType),t.selfForm.payType||(l=!1)),e.pledgeBucketNum>0&&(u=!0)})),s)if(i)if(c)if(l)if(u&&""==r.YTpayType&&"4"==r.payType)t.$message.error("请选择押桶支付方式");else{var d=JSON.stringify(t.selfForm.changeList);t.selfForm.checkList=JSON.parse(d),console.log("提交订单数据",r),t.dealOrderSubmitData(r)}else t.$message.error("请选择订单支付方式");else t.$message.error("请对所选商品进行至少一项的操作");else t.$message.error("请填写借物名称");else t.$message.error("请填写所选商品的零售价")}else t.$message.error("请选择需要自提的商品")},getOrderRecord:function(){var e=this,t="/szmb/storesendordercontroller/selectstoresendlistPc",r={storeId:e.Cookies.get("storeId"),name:e.orderSelect.isKey,stateTime:e.orderSelect.date?e.orderSelect.date[0]:"",endTime:e.orderSelect.date?e.orderSelect.date[1]:"",userType:e.orderSelect.userType,index:e.page,pageSize:e.pageSize};e.$post(t,r).then((function(t){console.log(t),e.loadLoading=!1,e.tapMenuValue=1,1==t.code?(e.orderRecord.list=t.data.list,e.orderRecord.customerCount=t.data.number,e.orderRecord.info=t.data):e.orderRecord.list=[]}))},backBucketFun:function(e){console.log("123123",e),this.useUserId=e.userId,this.orderNNumber=e.orderNumber,this.backBucket()},backBucket:function(){var e=this;e.getAllBrand(),e.backBucketForm={showList:[],isShowReplace:!1,brandList:[],allBrandList:[],allBrandIndex:0,brandIndex:0,bucketNum:"",otherNum:"",money:"",payTType:""},e.getBrandList((function(t){if(1==t.code){if(t.data.length){var r=t.data,a=r.map((function(e){return e.backNum=e.number,e})),o={brandName:"请选择",brandId:0};r.unshift(o),e.backBucketForm.brandList=r,e.backBucketForm.showList=a,e.backBucketDialog=!0}}else e.$message.error(t.data)}))},getBrandList:function(e){var t=this,r="/szmb/newdebtbuckcontroller/selectuserdebtlist",a={storeId:t.Cookies.get("storeId"),userId:t.useUserId,source:2};t.$post(r,a).then((function(t){"function"==typeof e&&e(t)}))},getAllBrand:function(){var e=this,t="/api/debtbuckcontroller/selectstorebrandlist",r={storeId:e.Cookies.get("storeId")};e.$post(t,r).then((function(t){if(1==t.code){var r=t.data,a={brandName:"请选择",brandId:0};r.unshift(a),e.backBucketForm.allBrandList=r}else e.$message.error(t.data)}))},getBucketNum:function(e){var t=this;if(e=e.replace(/[^\d]/g,""),0==t.backBucketForm.brandIndex)return t.$message.error("请先选择要抵扣的品牌"),void(t.backBucketForm.bucketNum="");var r=e,a=[];a=t.backBucketForm.showList;var o=t.backBucketForm.brandList[t.backBucketForm.brandIndex].brandId;a.forEach((function(e){e.brandId==o&&(e.deductNum=r)})),t.backBucketForm.showList=a,t.backBucketForm.bucketNum=r},getBrandNum:function(e,t){var r=this;e=e.replace(/[^\d]/g,"");var a=Number(e),o=r.backBucketForm.showList,n=Number(r.backBucketForm.bucketNum);console.log(a),o[t].backNum=a,o[t].deductNum=n;var s=o.filter((function(e){return e.number-e.backNum>0}));r.backBucketForm.showList=o,r.backBucketForm.isShowReplace=!!s.length},backBucketSubmit:function(e){var t=this,r=t.backBucketForm,a=r.brandIndex,o=r.brandList,n=r.allBrandIndex,s=r.allBrandList,i=r.bucketNum,c=r.otherNum,l=Number(r.money||0),u=[],d=r.showList,p=!1;if(d.forEach((function(e){e.backNum&&(p=!0)})),p){if(0!=n||0!=a||Number(i)||Number(c)){if(0==n||0==a||!Number(i)||!Number(c))return void t.$message.error("请完善抵扣信息");var f={replaceBrandId:s[n].brandId,replaceName:s[n].brandName,replaceNumber:c,replace:1,r1:o[a].brandId,r3:o[a].brandName,buckNumber:i};u.push(f)}}else if(0!=n||0!=a||i||c){if(0==n||0==a||"0"==i||""==i||"0"==c||""==c)return void t.$message.error("请完抵扣信息");var m={replaceBrandId:s[n].brandId,replaceName:s[n].brandName,replaceNumber:c,replace:1,r1:o[a].brandId,r3:o[a].brandName,buckNumber:i};u.push(m)}else if(!l)return void t.$message.error("请完善回桶单");if(d.forEach((function(e){if(e.backNum){var t={replaceBrandId:e.brandId,replaceName:e.brandName,replaceNumber:e.backNum,replace:0};u.push(t)}})),Number(r.money)>0&&""==r.payTType)t.$message.error("请选择支付方式");else{var h=JSON.stringify(u),g="/szmb/newdebtbuckcontroller/insertrepaybuck",b={orderNumber:t.orderNNumber,deliveryId:0,storeId:t.Cookies.get("storeId"),userId:t.useUserId,buckList:h,money:r.money?r.money:0,payType:r.payTType,source:2};t.$post(g,b).then((function(e){1==e.code?(t.$message({message:"回桶成功!",type:"success"}),t.backBucketDialog=!1,t.getOrderRecordDetail()):t.$message.error(e.data)}))}},clickPage:function(e){var t=this;console.log(e),t.orderRecordPage=e,t.getOrderRecord()},downloadAll:function(){var e=this,t="/szmb/storesendordercontroller/insertstoresendexcal",r={storeId:e.Cookies.get("storeId")};e.$post(t,r).then((function(t){console.log(t),1==t.code?window.open(t.data):e.$message.error(t.data)}))},downloadTime:function(){var e=this,t="/szmb/storesendordercontroller/insertstoresendexcal",r={storeId:e.Cookies.get("storeId"),stateTime:e.orderRecord.date[0],endTime:e.orderRecord.date[1]};e.$post(t,r).then((function(t){console.log(t),1==t.code?window.open(t.data):e.$message.error(t.data)}))},changeUserType:function(e){var t=this;console.log(e),t.userNameIndex="",t.selfForm.name="",t.selfForm.phone="",t.todaySelfData.recordCollect=[]},suggestLoadByPhone:function(e){if(e.length<4)return!1;var t=this,r="/szmb/szmbuserextractcontroller/selectnameandphone",a={storeId:t.Cookies.get("storeId"),phone:e};t.$post(r,a).then((function(e){console.log(e),t.restaurantsList=e.data,t.restaurantsName=e.data.down,t.userNameList=e.data.up}))},querySearchName:function(e,t){var r=this;if(!e||e.length<1){var a=this.restaurantsName,o=e?a.filter(this.createFilter(e)):a;t(o)}else{var n=this,s="/szmb/szmbuserextractcontroller/selectnameandphone",i={storeId:n.Cookies.get("storeId"),name:e};n.$post(s,i).then((function(a){console.log(a),n.restaurantsList=a.data,n.restaurantsName=a.data.down,n.userNameList=a.data.up;var o=a.data.down,s=e?o.filter(r.createFilter(e)):o;t(s)}))}},createFilter:function(e){return function(t){return 0===t.name.toLowerCase().indexOf(e.toLowerCase())}},chooseDownUser:function(e){var t=this;console.log(e),t.selfForm.name=e.name,t.selfForm.phone=e.phone,t.getUserDetail()},querySearchPhone:function(e,t){var r=this.restaurantsPhone,a=e?r.filter(this.createFilter(e)):r;t(a)},goBucketAdmin:function(){var e=this,t="/szmb/newdebtbuckcontroller/selectpledgdeatil",r={storeId:e.Cookies.get("storeId"),brandId:""===e.orderSelect.brandId?-1:e.orderSelect.brandId,stateTime:e.orderSelect.date?e.orderSelect.date[0]:"",endTime:e.orderSelect.date?e.orderSelect.date[1]:"",index:e.page,pageSize:e.pageSize,name:e.orderSelect.isKey,source:2};e.$post(t,r).then((function(t){e.loadLoading=!1,1==t.code?(e.pledgeBucket.list=t.data.list,e.pledgeBucket.info=t.data,e.pledgeBucket.count=t.data.total):(e.pledgeBucket.list=[],e.pledgeBucket.info="",e.pledgeBucket.count=0)}))},clickPledgeBucketPage:function(e){var t=this;t.page=e,t.goBucketAdmin()},clickPledgeBucketDetailPage:function(e){var t=this;t.page=e,t.lookUpPledBucketDetail()},clickOweBucketPage:function(e){var t=this;t.page=e,t.lookUpOweBucket()},bucketAdminChange:function(){var e=this;e.bucketAdminPage=1,e.goBucketAdmin()},clickBorrowThingsPage:function(e){var t=this;t.page=e,t.lookUpWuZhiAdmin()},getRetail:function(e,t,r){var a=this;e=e.replace(/[^\d.]/g,""),a.selfForm.goodsList[r].retailPrice=e,a.updateChange()},getPriceSpread:function(e,t,r){var a=this;e=e.replace(/[^\d.]/g,""),a.selfForm.goodsList[r].priceSpread=e,a.updateChange()},updateChange:function(){var e=this,t=e.selfForm.changeList,r=e.selfForm.goodsList,a=[];t.forEach((function(e){r.forEach((function(t){e.skuId==t.skuId&&a.push(t)}))})),e.selfForm.changeList=a},getSummaries:function(e){var t=this,r=e.columns,a=e.data,o=[];return r.forEach((function(e,r){if(0!==r){var n=a.map((function(t){return Number(t[e.property])}));n.every((function(e){return isNaN(e)}))?o[r]="--":r>3?(o[r]=n.reduce((function(e,r){var a=Number(r);return isNaN(a)?e.toFixed(2):t.$util.add(e,r).toFixed(2)}),0),4==r?o[r]=parseInt(o[r]):o[r]+=" 元"):o[r]="--"}else o[r]="合计"})),o},downloadDetailByTime:function(){var e=this,t="/szmb/storesendordercontroller/insertorderdeatilexcal",r={storeId:e.Cookies.get("storeId"),userPhone:e.orderDetail.phone,staterTime:e.orderDetail.date[0],endTime:e.orderDetail.date[1],skuName:e.orderDetail.isKey};e.$post(t,r).then((function(t){console.log(t),1==t.code?window.open(t.data):e.$message.error(t.data)}))},lookRecord:function(e,t){var r=this,a="";t?(r.oweBucketRecord.bucketId=e.oweBucketId,a=r.oweBucketRecord):(r.pledgeBucketRecord.bucketId=e.oweBucketId,a=r.pledgeBucketRecord),r.dealRecordData(a,t)},dealRecordData:function(e,t){var r=this,a="/szmb/szmbuserextractcontroller/selectbyowebucketid",o={oweBucketId:e.bucketId,source:t,pageNo:e.page,pageSize:10};r.$post(a,o).then((function(e){console.log(e),1==e.code&&(t?(r.oweBucketRecord.list=e.data.list,r.oweBucketRecord.count=e.data.count,r.pageFlag=5):(r.pledgeBucketRecord.list=e.data.list,r.pledgeBucketRecord.count=e.data.count,r.pageFlag=4))}))},clickBucketRecordPage:function(e,t){var r=this;t?(r.oweBucketRecord.page=e,r.dealRecordData(r.oweBucketRecord,t)):(r.pledgeBucketRecord.page=e,r.dealRecordData(r.pledgeBucketRecord,t))},dealBucket:function(e,t){var r=this;if(e.currentNum){if(t){if(e.currentNum>e.pledgeResidueNum)return void r.$message.error("当前还桶数不能大于剩余未还桶数")}else if(e.currentNum>e.pledgeResidueNum)return void r.$message.error("当前退桶数不能大于剩余未退桶数");var a="/szmb/szmbuserextractcontroller/returnbucket",o={oweBucketDetailsId:e.oweBucketDetailId,source:t,num:e.currentNum};r.$post(a,o).then((function(e){1==e.code&&(t?(r.$message({message:"还桶成功",type:"success"}),r.dealRecordData(r.oweBucketRecord,1)):(r.$message({message:"退桶成功",type:"success"}),r.dealRecordData(r.pledgeBucketRecord,0)))}))}else r.$message.error("请输入数量")},returnBucketRecord:function(e){var t=this,r="/szmb/newdebtbuckcontroller/selectbucketlistproduct",a={storeId:t.Cookies.get("storeId"),userId:0,brandId:-1,index:1,pageSize:500,source:2};t.$post(r,a).then((function(e){t.returnBucketRecordData.dialog=!0,1==e.code?t.returnBucketRecordData.list=e.data.list:t.returnBucketRecordData.list=[]}))},backBucketRecord:function(){var e=this;console.log("aaaa");var t="/szmb/newdebtbuckcontroller/selectuserrepaylistproduct",r={storeId:e.Cookies.get("storeId"),index:1,pageSize:500,source:2,brandId:-1};e.$post(t,r).then((function(t){e.backBucketRecordData.dialog=!0,1==t.code?e.backBucketRecordData.list=t.data.list:e.backBucketRecordData.list=[]}))},clickReturnBucketRecordPage:function(e){var t=this;t.returnBucketRecordData.page=e,t.returnBucketRecord({oweBucketDetailId:t.returnBucketRecordData.id})},lookUpWuZhiAdmin:function(){var e=this,t="/szmb/szmbuserextractcontroller/likeusernameandphone",r={storeId:e.Cookies.get("storeId"),index:e.page,nameAndPhone:e.orderSelect.isKey,pageSize:e.pageSize};e.$post(t,r).then((function(t){e.pageFlag=6,e.loadLoading=!1,1==t.code?(e.borrowThingsData.list=t.data.data,e.borrowThingsData.count=t.data.count):(e.borrowThingsData.list=[],e.borrowThingsData.count=0)}))},goWuZhiAdminDetail:function(e){var t=this;t.bucketSelect.userId=e.userId,t.bucketSelect.objectName=e.productName,t.lookUpWuZhiAdminDetail()},lookUpWuZhiAdminDetail:function(){var e=this,t="/szmb/szmbuserextractcontroller/seldetailpc",r={storeId:e.Cookies.get("storeId"),name:e.orderSelect.isKey,userId:e.bucketSelect.userId,objectName:e.bucketSelect.objectName,startTime:e.orderSelect.date?e.orderSelect.date[0]:"",endTime:e.orderSelect.date?e.orderSelect.date[1]:"",index:e.page,pageSize:e.pageSize};e.$post(t,r).then((function(t){e.loadLoading=!1,1==t.code?(e.bucketTap=1,e.borrowThingsRecordData.list=t.data.list,e.borrowThingsRecordData.count=t.data.count):(e.borrowThingsRecordData.list=[],e.borrowThingsRecordData.count=0)}))},lookBorrowNotes:function(e){var t=this,r=this,a="/szmb/szmbuserextractcontroller/seldetail",o={storeId:r.Cookies.get("storeId"),userId:e.userId,name:e.productName};r.$post(a,o).then((function(a){console.log(a),1==a.code?(t.pageFlag=7,r.borrowThingsRecordData.list=a.data,r.borrowThingsRecordData.userId=e.userId,r.borrowThingsRecordData.productName=e.productName):r.$message.error(a.data)}))},backThings:function(e){var t=this;if(console.log(e,"rowww"),e.replace){var r="/szmb/szmbuserextractcontroller/addreturn",a={objNum:e.borrowOrderNum,number:e.replace};t.$post(r,a).then((function(e){1==e.code?(t.$message({message:"还物成功!",type:"success"}),t.lookUpWuZhiAdminDetail()):t.$message.error(e.data)}))}else t.$message.error("请输入有效数值")},backThingsRecord:function(e){var t=this;console.log("aaa"),t.returnThingsRecordData.dialog=!0;var r="/szmb/szmbuserextractcontroller/selreturndetailpc",a={storeId:t.Cookies.get("storeId"),userId:t.bucketSelect.userId?t.bucketSelect.userId:0,startTime:"",endTime:"",index:t.returnThingsRecordData.page,pageSize:t.returnThingsRecordData.pageSize};t.$post(r,a).then((function(e){t.pageFlag=10,1==e.code?(t.returnThingsRecordData.list=e.data.list,t.returnThingsRecordData.count=e.data.total):t.returnThingsRecordData.list=[]}))},clickReturnThingsPage:function(e){var t=this;t.returnThingsRecordData.page=e,t.backThingsRecord()},receiptMoney:function(e){var t=this;console.log("hsdjkahd",e);var r=this,a={orderNum:e.orderNumber,state:4};this.$post("/szmb/user/updatebanktransfer",a).then((function(e){1===e.code?(r.$message({type:"success",message:e.data}),r.getOrderRecordDetail()):(console.log(e.msg),t.$message.error(e.data))})).catch((function(e){console.log(e)}))}},components:{}},s=n,i=(r("b119"),r("0c7c")),c=Object(i["a"])(s,a,o,!1,null,"1dbf881e",null);t["default"]=c.exports},"14f2":function(e,t,r){"use strict";var a=function(){var e=this,t=e._self._c;return t("el-dialog",{attrs:{title:"修改状态","close-on-click-modal":!1,visible:e.visible},on:{"update:visible":function(t){e.visible=t}}},[t("el-form",{ref:"dataForm",attrs:{model:e.dataForm,rules:e.dataRule,"label-width":"120px"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.dataFormSubmit()}}},[t("el-form-item",{attrs:{label:"状态",prop:"state"}},[t("el-select",{model:{value:e.dataForm.state,callback:function(t){e.$set(e.dataForm,"state",t)},expression:"dataForm.state"}},e._l(e.state,(function(e){return t("el-option",{key:e.key,attrs:{label:e.value,value:e.key}})})),1)],1)],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.visible=!1}}},[e._v("取消")]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.dataFormSubmit()}}},[e._v("确定")])],1)],1)},o=[],n=r("db7a"),s={data:function(){return{state:n["c"],visible:!1,dataForm:{repeatToken:"",storeId:0,state:"",header:"json"},dataRule:{}}},methods:{init:function(e,t){var r=this;this.dataForm.storeId=e||0,this.visible=!0,this.$nextTick((function(){r.$refs["dataForm"].resetFields(),r.dataForm.state=t||2}))},getToken:function(){var e=this;this.$http({url:this.$http.adornUrl("/common/createToken"),method:"get",params:this.$http.adornParams()}).then((function(t){var r=t.data;r&&200===r.code&&(e.dataForm.repeatToken=r.result)}))},dataFormSubmit:function(){var e=this;this.$refs["dataForm"].validate((function(t){t&&(e.dataForm.header="json",e.$post("szmcstore/update",e.dataForm).then((function(t){1===t.code?e.$message({message:"操作成功",type:"success",duration:1500,onClose:function(){e.visible=!1,e.$emit("refreshDataList")}}):e.$message.error(t.data)})))}))}}},i=s,c=r("0c7c"),l=Object(c["a"])(i,a,o,!1,null,null,null);t["a"]=l.exports},"230a":function(e,t,r){},"4fc8":function(e,t,r){},8659:function(e,t){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAC4AAAAuCAMAAABgZ9sFAAAAflBMVEUAAADY3OXY3OXY3OXY3OXY3OXY3OXY3OXY3OXY3OXY3OXY3OXY3OXY3OXY3OXY3OXY3OXY3OXY3OXY3OXY3OXY3OXY3OXY3OXY3OXY3OXY3OXY3OXY3OXY3OXY3OXY3OXY3OXY3OXY3OXY3OXY3OXY3OXY3OXY3OXY3OXY3OUFoKFsAAAAKXRSTlMAW+B0zObzPXCP8Ab128WsVvnWuoV5IevRzr+nnGpRRkErD2VkNygVEE5QJf4AAADWSURBVEjH7dPHasNAAEXRJymWM+rFkmtc0u///2AGDBEZFSfZGXQWWjwuGhgYzWadIvPHbAK5jkxZyVERBkUwxPN8Ejki2McaZEoiORZYjfq8EFj3c1ND9u7M8R7S1dDfTzpA+fpjPflQxzFhPw+kYg35RzcawM7nofzRfj9ziApdXSrYXCQtR3LrCTjEsprrfU/naneQntVmkC41nX8fkCdgpNu59VYBi6Nu5B2T5M/6fS4b/yXXPeURnsa0lHKk1N4Y039NHlOMXC+77cOwbdZoNvunLz9UHu+VE06kAAAAAElFTkSuQmCC"},b119:function(e,t,r){"use strict";r("230a")},bb19:function(e,t){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAC4AAAAuCAMAAABgZ9sFAAAAe1BMVEUAAADY3OXY3OXY3OXY3OXY3OXY3OXY3OXY3OXY3OXY3OXY3OXY3OXY3OXY3OXY3OXY3OXY3OXY3OXY3OXY3OXY3OXY3OXY3OXY3OXY3OXY3OXY3OXY3OXY3OXY3OXY3OXY3OXY3OXY3OXY3OXY3OXY3OXY3OXY3OXY3OV4A5n9AAAAKHRSTlMAqYSOZl2e1kGAcvV7GwShmFnskWEs48V3NzMxJB8XDwr3qzzOsWpJx+JFNQAAAOdJREFUSMft09lugzAQheFhC5jVZgkQQiDpdt7/CStHVuOqk7oXrdJI+S7Rr5HHwvTwVw7SM6qdu15qXOy/KxPhT/SELDe2iOggfMXXFYCYRmzJeIGiAEBfMvUbIMWopwtDT9/nwREpkzeQ502PuOj0lxQemw+kdZuTd3aqEtIiVEwewCdWiM2d5EthrD/Jd/Yf4M67OjPq2ZlrpfEPbmbuY0s/um/G1roO0yWW6barLo1nkbMjn/DJq+swakg/DO16o1Ub5Hzesk9bXpuu2DxEHaroq/aZnVPGuCIriLEKjxUU9PDb3gG/jSP8dGL5NAAAAABJRU5ErkJggg=="},bb51:function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e._self._c;return t("div",[t("div",{staticClass:"home-box"},[t("div",{staticClass:"user-boxs"},[t("div",{staticClass:"user-infos no-sj"},[t("div",{staticClass:"user-1"},[t("div",{staticClass:"user-1-logo"},[t("img",{attrs:{src:e.userInfos.storeLogo}})]),t("div",{staticClass:"user-1-info"},[t("el-tooltip",{attrs:{effect:"dark",content:"欢迎您，"+e.userInfos.storeAme,placement:"top","popper-class":"tooltip-background"}},[t("div",{staticClass:"user-name-t"},[e._v("\n                欢迎您，"+e._s(e.userInfos.storeAme)+"\n              ")])]),t("div",{staticClass:"user-msg"},[t("div",{on:{click:function(){return e.$router.push({name:"orderAdmin",params:{state:3,type:"home"}})}}},[t("el-badge",{staticClass:"item",attrs:{value:e.userData.order}},[t("img",{attrs:{src:r("bb19")}})]),t("p",[e._v("待处理订单")])],1),t("div",{on:{click:function(){return e.$router.push("/usermanage")}}},[t("img",{attrs:{src:r("8659")}}),t("p",[e._v("发送短信")])])])],1)]),t("div",{staticClass:"user-2",on:{click:function(){return e.$router.push("/perfectStore")}}},[t("div",{staticClass:"user-2-text"},[t("span",[e._v("配送范围："+e._s(e.userInfos.scope/1e3)+"公里")]),t("el-divider",{attrs:{direction:"vertical"}}),t("span",[e._v("营业时间："+e._s(e.userInfos.storeServicetime))]),t("el-divider",{attrs:{direction:"vertical"}}),t("el-tooltip",{attrs:{effect:"dark",content:"店铺地址："+e.userInfos.storeDetailed,placement:"top","popper-class":"tooltip-background"}},[t("span",[e._v("店铺地址："+e._s(e.userInfos.storeDetailed))])])],1),t("div",{staticClass:"user-2-text"},[t("el-tooltip",{attrs:{effect:"dark",content:"经营范围："+e.userData.address,placement:"top","popper-class":"tooltip-background"}},[t("span",[e._v("经营范围："+e._s(e.userData.address))])])],1)]),e.formalOff?t("div",{staticClass:"is-shouquan",on:{click:function(t){return e.storeupdatestate(e.userInfos.storeId,3)}}},[t("img",{attrs:{src:r("f39e")}})]):t("div",{staticClass:"is-shouquan",on:{click:function(t){return e.storeupdatestate(e.userInfos.storeId,2)}}},[t("img",{attrs:{src:"https://mpjoy.oss-cn-beijing.aliyuncs.com/20240323/dd1593fc4b4e46a2a6064ccd7af7f334.png"}})])])]),t("div",{staticClass:"tools-box"},[e._m(0),t("div",{staticClass:"tools-select"},[e._l(e.myToolsList,(function(r,a){return[t("div",{staticClass:"tools-item",on:{click:function(){return e.$router.push({name:r.alias,params:{state:r.r1,type:"home"}})}}},[t("div",{class:r.img}),t("p",[e._v(e._s(r.name))])])]})),t("div",{staticClass:"tools-item",on:{click:e.addTOOLSList}},[t("div",{staticClass:"add-icon"}),t("p",{staticStyle:{color:"#1693FD"}},[e._v("添加编辑")])])],2)]),t("div",{staticClass:"sta-order-box"},[t("el-card",{staticClass:"box-card"},[t("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[t("span",[e._v("营业统计")]),e.loadingStates.turnover?t("i",{staticClass:"el-icon-loading",staticStyle:{"margin-left":"10px"}}):e._e()]),t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loadingStates.turnover,expression:"loadingStates.turnover"}],staticClass:"order-card-body1"},[t("el-row",[t("el-col",{attrs:{span:"8"}},[t("div",{staticClass:"card-items",on:{click:function(){return e.$router.push({name:"totalTurnover"})}}},[t("h3",[e._v(e._s(e.turnoverInfos.totalBusiness||"--"))]),t("p",[e._v("总营业额")])])]),t("el-col",{attrs:{span:"8"}},[t("div",{staticClass:"card-items",on:{click:function(){return e.$router.push({name:"cashOut"})}}},[t("h3",[e._v(e._s(e.orderInfos.money))]),t("p",[e._v("可提现总金额")])])]),t("el-col",{attrs:{span:"8"}},[t("div",{staticClass:"card-items",on:{click:function(){return e.$router.push({name:"todayTurnover"})}}},[t("h3",[e._v(e._s(e.orderInfos.todayAllOrderMoney))]),t("p",[e._v("今日营业额")])])])],1),t("el-row",[t("el-col",{attrs:{span:"8"}},[t("div",{staticClass:"card-items",on:{click:function(){return e.$router.push({name:"usermanage",params:{state:0,type:"home"}})}}},[t("h3",[e._v(e._s(e.turnoverInfos.totalUser))]),t("p",[e._v("总客户数")])])]),t("el-col",{attrs:{span:"8"}},[t("div",{staticClass:"card-items",on:{click:function(){return e.$router.push({name:"usermanage",params:{state:1,type:"home"}})}}},[t("h3",[e._v(e._s(e.turnoverInfos.todayTotalUser))]),t("p",[e._v("今日新增客户数")])])]),t("el-col",{attrs:{span:"8"}},[t("div",{staticClass:"card-items",on:{click:function(){return e.$router.push({name:"monthlyCustomer"})}}},[t("h3",[e._v(e._s(e.turnoverInfos.yfCount))]),t("p",[e._v("月结客户数")])])])],1)],1)]),t("el-card",{staticClass:"box-card"},[t("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[t("span",[e._v("订单统计")]),e.loadingStates.orders?t("i",{staticClass:"el-icon-loading",staticStyle:{"margin-left":"10px"}}):e._e()]),t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loadingStates.orders,expression:"loadingStates.orders"}],staticClass:"order-card-body1"},[t("el-row",[t("el-col",{attrs:{span:"8"}},[t("div",{staticClass:"card-items",on:{click:function(){return e.$router.push({name:"orderAdmin",params:{state:0,type:"home"}})}}},[t("h3",[e._v(e._s(e.orderInfos.todayOrderCount||"--"))]),t("p",[e._v("今日订单总数")])])]),t("el-col",{attrs:{span:"8"}},[t("div",{staticClass:"card-items",on:{click:function(){return e.$router.push({name:"orderAdmin",params:{state:3,type:"home"}})}}},[t("h3",[e._v(e._s(e.orderInfos.pendingCount))]),t("p",[e._v("今日水站待派单总数")])])]),t("el-col",{attrs:{span:"8"}},[t("div",{staticClass:"card-items",on:{click:function(){return e.$router.push({name:"orderAdmin",params:{state:7,type:"home"}})}}},[t("h3",[e._v(e._s(e.orderInfos.signOrder))]),t("p",[e._v("今日已完成订单总数")])])])],1),t("el-row",[t("el-col",{attrs:{span:"8"}},[t("div",{staticClass:"card-items",on:{click:function(){return e.$router.push({name:"cashOut"})}}},[t("h3",[e._v(e._s(e.orderInfos.money))]),t("p",[e._v("可提现总金额")])])]),t("el-col",{attrs:{span:"8"}},[t("div",{staticClass:"card-items",on:{click:function(){return e.$router.push({name:"orderAdmin",params:{state:8,type:"home"}})}}},[t("h3",[e._v(e._s(e.orderInfos.returnOrder))]),t("p",[e._v("今日退货退款订单总数")])])]),t("el-col",{attrs:{span:"8"}},[t("div",{staticClass:"card-items",on:{click:function(){return e.$router.push({name:"orderAdmin",params:{state:0,type:"home"}})}}},[t("h3",[e._v(e._s(e.orderInfos.todayOrderMoney))]),t("p",[e._v("今日订单实付总金额")])])])],1)],1)])],1),t("div",{staticClass:"sta-order-box"},[t("el-card",{staticClass:"box-card",staticStyle:{flex:"2",margin:"0 10px"}},[t("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[t("span",[e._v("水桶统计")]),e.loadingStates.buckets?t("i",{staticClass:"el-icon-loading",staticStyle:{"margin-left":"10px"}}):e._e()]),t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loadingStates.buckets,expression:"loadingStates.buckets"}],staticClass:"order-card-body1"},[t("el-row",[t("el-col",{attrs:{span:"8"}},[t("div",{staticClass:"card-items",on:{click:function(){return e.$router.push({name:"bucketInfo",params:{state:0,type:"home"}})}}},[t("h3",[e._v(e._s(e.bucketList.buckCount||"--"))]),t("p",[e._v("今日押桶数")])])]),t("el-col",{attrs:{span:"8"}},[t("div",{staticClass:"card-items",on:{click:function(){return e.$router.push({name:"bucketInfo",params:{state:1,type:"home"}})}}},[t("h3",[e._v(e._s(e.bucketList.repayCount))]),t("p",[e._v("今日回桶数")])])]),t("el-col",{attrs:{span:"8"}},[t("div",{staticClass:"card-items",on:{click:function(){return e.$router.push({name:"bucketInfo",params:{state:2,type:"home"}})}}},[t("h3",[e._v(e._s(e.bucketList.debtCount))]),t("p",[e._v("汇总欠桶总数")])])])],1)],1)]),t("el-card",{staticClass:"box-card",staticStyle:{flex:"2",margin:"0 10px"}},[t("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[t("span",[e._v("水票情况")])]),t("div",{staticClass:"order-card-body1"},[t("el-row",[t("el-col",{attrs:{span:"8"}},[t("div",{staticClass:"card-items",on:{click:function(){return e.$router.push({name:"orderAdmin",params:{state:11,type:"home"}})}}},[t("h3",[e._v(e._s(e.bucketList.waterBuy))]),t("p",[e._v("今日水票购买数")])])]),t("el-col",{attrs:{span:"8"}},[t("div",{staticClass:"card-items",on:{click:function(){return e.$router.push({name:"waterTicketAdmin"})}}},[t("h3",[e._v(e._s(e.bucketList.useWater))]),t("p",[e._v("今日水票使用数")])])]),t("el-col",{attrs:{span:"8"}},[t("div",{staticClass:"card-items",on:{click:function(){return e.$router.push({name:"waterTicketAdmin"})}}},[t("h3",[e._v(e._s(e.bucketList.waterPrice))]),t("p",[e._v("今日水票销售金额")])])])],1)],1)]),t("el-card",{staticClass:"box-card",staticStyle:{flex:"1",margin:"0 10px"}},[t("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[t("span",[e._v("发票情况")])]),t("div",{staticClass:"order-card-body1"},[t("el-row",[t("el-col",{attrs:{span:"24"}},[t("div",{staticClass:"card-items",on:{click:function(){return e.$router.push({name:"invoiceAdmin"})}}},[t("h3",[e._v(e._s(e.orderInfos.invoice))]),t("p",[e._v("未开发票")])])])],1)],1)])],1)]),t("el-dialog",{attrs:{visible:e.TOOLSVisible,width:"30%"},on:{"update:visible":function(t){e.TOOLSVisible=t}}},[t("div",{staticClass:"section-dialog"},[t("div",{staticClass:"title"},[e._v("编辑快捷工具")]),t("div",{staticClass:"title2"},[e._v("工具内容")]),t("div",{staticStyle:{width:"90%",margin:"0 auto"}},[t("el-table",{ref:"multipleTable",attrs:{data:e.TOOLSList,"tooltip-effect":"dark","max-height":"400","header-cell-style":{color:"#000000"},"cell-style":{"font-size":"14px",color:"#333C48"}},on:{"selection-change":e.handleSelectionChange}},[t("el-table-column",{attrs:{label:"快捷工具名称",prop:"name"}}),t("el-table-column",{attrs:{type:"selection",align:"center"}})],1),t("div",{staticClass:"text-align-center",staticStyle:{margin:"50px 0 20px"}},[t("el-button",{on:{click:function(t){e.TOOLSVisible=!1}}},[e._v("取 消")]),t("el-button",{attrs:{type:"primary"},on:{click:e.submitTOOLS}},[e._v("确 定")])],1)],1)])]),e.storeupdatestateVisible?t("storeupdatestate",{ref:"storeupdatestate1",on:{refreshDataList:e.requestUserInfos}}):e._e()],1)},o=[function(){var e=this,t=e._self._c;return t("div",{staticClass:"tools-text"},[t("span",[e._v("我的快捷工具")])])}],n=r("14f2");function s(e){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},s(e)}function i(e,t){return u(e)||l(e,t)||p(e,t)||c()}function c(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function l(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var a,o,n,s,i=[],c=!0,l=!1;try{if(n=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(a=n.call(r)).done)&&(i.push(a.value),i.length!==t);c=!0);}catch(e){l=!0,o=e}finally{try{if(!c&&null!=r.return&&(s=r.return(),Object(s)!==s))return}finally{if(l)throw o}}return i}}function u(e){if(Array.isArray(e))return e}function d(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=p(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var a=0,o=function(){};return{s:o,n:function(){return a>=e.length?{done:!0}:{done:!1,value:e[a++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var n,s=!0,i=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return s=e.done,e},e:function(e){i=!0,n=e},f:function(){try{s||null==r.return||r.return()}finally{if(i)throw n}}}}function p(e,t){if(e){if("string"==typeof e)return f(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?f(e,t):void 0}}function f(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,a=Array(t);r<t;r++)a[r]=e[r];return a}function m(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function h(e,t){for(var r=0;r<t.length;r++){var a=t[r];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,b(a.key),a)}}function g(e,t,r){return t&&h(e.prototype,t),r&&h(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function b(e){var t=v(e,"string");return"symbol"==s(t)?t:t+""}function v(e,t){if("object"!=s(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var a=r.call(e,t||"default");if("object"!=s(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}var k=function(){function e(){m(this,e),this.cache=new Map,this.cacheExpiry=new Map,this.cacheDurations={userInfo:3e5,turnover:12e4,orders:6e4,inventory:18e4,buckets:12e4,tools:6e5}}return g(e,[{key:"generateKey",value:function(e,t){return"".concat(e,"_").concat(t)}},{key:"isValid",value:function(e){var t=this.cacheExpiry.get(e);return t&&Date.now()<t}},{key:"get",value:function(e,t){var r=this.generateKey(e,t);return this.isValid(r)?(console.log("Cache hit for ".concat(t)),this.cache.get(r)):(console.log("Cache miss for ".concat(t)),null)}},{key:"set",value:function(e,t,r){var a=this.generateKey(e,t),o=this.cacheDurations[t]||6e4;this.cache.set(a,r),this.cacheExpiry.set(a,Date.now()+o),console.log("Cached ".concat(t," for ").concat(o/1e3," seconds"))}},{key:"clear",value:function(e,t){var r=this.generateKey(e,t);this.cache.delete(r),this.cacheExpiry.delete(r)}},{key:"clearStore",value:function(e){var t,r=this,a=[],o=d(this.cache.keys());try{for(o.s();!(t=o.n()).done;){var n=t.value;n.startsWith("".concat(e,"_"))&&a.push(n)}}catch(s){o.e(s)}finally{o.f()}a.forEach((function(e){r.cache.delete(e),r.cacheExpiry.delete(e)}))}},{key:"cleanup",value:function(){var e,t=this,r=Date.now(),a=[],o=d(this.cacheExpiry.entries());try{for(o.s();!(e=o.n()).done;){var n=i(e.value,2),s=n[0],c=n[1];r>=c&&a.push(s)}}catch(l){o.e(l)}finally{o.f()}a.forEach((function(e){t.cache.delete(e),t.cacheExpiry.delete(e)})),a.length>0&&console.log("Cleaned up ".concat(a.length," expired cache entries"))}},{key:"getStats",value:function(){return{totalEntries:this.cache.size,validEntries:Array.from(this.cacheExpiry.values()).filter((function(e){return Date.now()<e})).length,expiredEntries:Array.from(this.cacheExpiry.values()).filter((function(e){return Date.now()>=e})).length}}}])}(),y=new k;setInterval((function(){y.cleanup()}),3e5);var w=y;function x(e,t){return N(e)||S(e,t)||B(e,t)||_()}function _(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function S(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var a,o,n,s,i=[],c=!0,l=!1;try{if(n=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(a=n.call(r)).done)&&(i.push(a.value),i.length!==t);c=!0);}catch(e){l=!0,o=e}finally{try{if(!c&&null!=r.return&&(s=r.return(),Object(s)!==s))return}finally{if(l)throw o}}return i}}function N(e){if(Array.isArray(e))return e}function I(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=B(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var a=0,o=function(){};return{s:o,n:function(){return a>=e.length?{done:!0}:{done:!1,value:e[a++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var n,s=!0,i=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return s=e.done,e},e:function(e){i=!0,n=e},f:function(){try{s||null==r.return||r.return()}finally{if(i)throw n}}}}function B(e,t){if(e){if("string"==typeof e)return O(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?O(e,t):void 0}}function O(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,a=Array(t);r<t;r++)a[r]=e[r];return a}function C(e){return C="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},C(e)}function T(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */T=function(){return t};var e,t={},r=Object.prototype,a=r.hasOwnProperty,o=Object.defineProperty||function(e,t,r){e[t]=r.value},n="function"==typeof Symbol?Symbol:{},s=n.iterator||"@@iterator",i=n.asyncIterator||"@@asyncIterator",c=n.toStringTag||"@@toStringTag";function l(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{l({},"")}catch(e){l=function(e,t,r){return e[t]=r}}function u(e,t,r,a){var n=t&&t.prototype instanceof b?t:b,s=Object.create(n.prototype),i=new L(a||[]);return o(s,"_invoke",{value:I(e,r,i)}),s}function d(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=u;var p="suspendedStart",f="suspendedYield",m="executing",h="completed",g={};function b(){}function v(){}function k(){}var y={};l(y,s,(function(){return this}));var w=Object.getPrototypeOf,x=w&&w(w(D([])));x&&x!==r&&a.call(x,s)&&(y=x);var _=k.prototype=b.prototype=Object.create(y);function S(e){["next","throw","return"].forEach((function(t){l(e,t,(function(e){return this._invoke(t,e)}))}))}function N(e,t){function r(o,n,s,i){var c=d(e[o],e,n);if("throw"!==c.type){var l=c.arg,u=l.value;return u&&"object"==C(u)&&a.call(u,"__await")?t.resolve(u.__await).then((function(e){r("next",e,s,i)}),(function(e){r("throw",e,s,i)})):t.resolve(u).then((function(e){l.value=e,s(l)}),(function(e){return r("throw",e,s,i)}))}i(c.arg)}var n;o(this,"_invoke",{value:function(e,a){function o(){return new t((function(t,o){r(e,a,t,o)}))}return n=n?n.then(o,o):o()}})}function I(t,r,a){var o=p;return function(n,s){if(o===m)throw Error("Generator is already running");if(o===h){if("throw"===n)throw s;return{value:e,done:!0}}for(a.method=n,a.arg=s;;){var i=a.delegate;if(i){var c=B(i,a);if(c){if(c===g)continue;return c}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if(o===p)throw o=h,a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);o=m;var l=d(t,r,a);if("normal"===l.type){if(o=a.done?h:f,l.arg===g)continue;return{value:l.arg,done:a.done}}"throw"===l.type&&(o=h,a.method="throw",a.arg=l.arg)}}}function B(t,r){var a=r.method,o=t.iterator[a];if(o===e)return r.delegate=null,"throw"===a&&t.iterator.return&&(r.method="return",r.arg=e,B(t,r),"throw"===r.method)||"return"!==a&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+a+"' method")),g;var n=d(o,t.iterator,r.arg);if("throw"===n.type)return r.method="throw",r.arg=n.arg,r.delegate=null,g;var s=n.arg;return s?s.done?(r[t.resultName]=s.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,g):s:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,g)}function O(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function F(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function L(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(O,this),this.reset(!0)}function D(t){if(t||""===t){var r=t[s];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,n=function r(){for(;++o<t.length;)if(a.call(t,o))return r.value=t[o],r.done=!1,r;return r.value=e,r.done=!0,r};return n.next=n}}throw new TypeError(C(t)+" is not iterable")}return v.prototype=k,o(_,"constructor",{value:k,configurable:!0}),o(k,"constructor",{value:v,configurable:!0}),v.displayName=l(k,c,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===v||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,k):(e.__proto__=k,l(e,c,"GeneratorFunction")),e.prototype=Object.create(_),e},t.awrap=function(e){return{__await:e}},S(N.prototype),l(N.prototype,i,(function(){return this})),t.AsyncIterator=N,t.async=function(e,r,a,o,n){void 0===n&&(n=Promise);var s=new N(u(e,r,a,o),n);return t.isGeneratorFunction(r)?s:s.next().then((function(e){return e.done?e.value:s.next()}))},S(_),l(_,c,"Generator"),l(_,s,(function(){return this})),l(_,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var a in t)r.push(a);return r.reverse(),function e(){for(;r.length;){var a=r.pop();if(a in t)return e.value=a,e.done=!1,e}return e.done=!0,e}},t.values=D,L.prototype={constructor:L,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(F),!t)for(var r in this)"t"===r.charAt(0)&&a.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function o(a,o){return i.type="throw",i.arg=t,r.next=a,o&&(r.method="next",r.arg=e),!!o}for(var n=this.tryEntries.length-1;n>=0;--n){var s=this.tryEntries[n],i=s.completion;if("root"===s.tryLoc)return o("end");if(s.tryLoc<=this.prev){var c=a.call(s,"catchLoc"),l=a.call(s,"finallyLoc");if(c&&l){if(this.prev<s.catchLoc)return o(s.catchLoc,!0);if(this.prev<s.finallyLoc)return o(s.finallyLoc)}else if(c){if(this.prev<s.catchLoc)return o(s.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<s.finallyLoc)return o(s.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&a.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var n=o;break}}n&&("break"===e||"continue"===e)&&n.tryLoc<=t&&t<=n.finallyLoc&&(n=null);var s=n?n.completion:{};return s.type=e,s.arg=t,n?(this.method="next",this.next=n.finallyLoc,g):this.complete(s)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),g},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),F(r),g}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var a=r.completion;if("throw"===a.type){var o=a.arg;F(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,r,a){return this.delegate={iterator:D(t),resultName:r,nextLoc:a},"next"===this.method&&(this.arg=e),g}},t}function F(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,a)}return r}function L(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?F(Object(r),!0).forEach((function(t){D(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):F(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function D(e,t,r){return(t=P(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function z(e,t,r,a,o,n,s){try{var i=e[n](s),c=i.value}catch(e){return void r(e)}i.done?t(c):Promise.resolve(c).then(a,o)}function R(e){return function(){var t=this,r=arguments;return new Promise((function(a,o){var n=e.apply(t,r);function s(e){z(n,a,o,s,i,"next",e)}function i(e){z(n,a,o,s,i,"throw",e)}s(void 0)}))}}function E(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function $(e,t){for(var r=0;r<t.length;r++){var a=t[r];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,P(a.key),a)}}function A(e,t,r){return t&&$(e.prototype,t),r&&$(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function P(e){var t=j(e,"string");return"symbol"==C(t)?t:t+""}function j(e,t){if("object"!=C(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var a=r.call(e,t||"default");if("object"!=C(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}var M=function(){function e(){E(this,e),this.defaultOptions={maxRetries:3,retryDelay:1e3,backoffMultiplier:2,retryCondition:function(e){return!e.response||e.response.status>=500&&e.response.status<600}}}return A(e,[{key:"sleep",value:function(e){return new Promise((function(t){return setTimeout(t,e)}))}},{key:"executeWithRetry",value:function(){var e=R(T().mark((function e(t){var r,a,o,n,s,i,c=arguments;return T().wrap((function(e){while(1)switch(e.prev=e.next){case 0:r=c.length>1&&void 0!==c[1]?c[1]:{},a=L(L({},this.defaultOptions),r),o=null,n=0;case 4:if(!(n<=a.maxRetries)){e.next=30;break}return e.prev=5,console.log("API attempt ".concat(n+1,"/").concat(a.maxRetries+1)),e.next=9,t();case 9:return s=e.sent,n>0&&console.log("API call succeeded on attempt ".concat(n+1)),e.abrupt("return",s);case 14:if(e.prev=14,e.t0=e["catch"](5),o=e.t0,console.error("API attempt ".concat(n+1," failed:"),e.t0.message),n!==a.maxRetries){e.next=20;break}return e.abrupt("break",30);case 20:if(a.retryCondition(e.t0)){e.next=23;break}return console.log("Error condition not met for retry, failing immediately"),e.abrupt("break",30);case 23:return i=a.retryDelay*Math.pow(a.backoffMultiplier,n),console.log("Retrying in ".concat(i,"ms...")),e.next=27,this.sleep(i);case 27:n++,e.next=4;break;case 30:throw console.error("All ".concat(a.maxRetries+1," attempts failed")),o;case 32:case"end":return e.stop()}}),e,this,[[5,14]])})));function t(t){return e.apply(this,arguments)}return t}()},{key:"createRetryWrapper",value:function(e,t){var r=this,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return R(T().mark((function o(){var n,s,i,c=arguments;return T().wrap((function(o){while(1)switch(o.prev=o.next){case 0:for(n=c.length,s=new Array(n),i=0;i<n;i++)s[i]=c[i];return o.abrupt("return",r.executeWithRetry((function(){return e[t].apply(e,s)}),a));case 2:case"end":return o.stop()}}),o)})))}},{key:"executeBatch",value:function(){var e=R(T().mark((function e(t){var r,a,o,n,s=this,i=arguments;return T().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return r=i.length>1&&void 0!==i[1]?i[1]:{},e.next=3,Promise.allSettled(t.map((function(e){return s.executeWithRetry(e,r)})));case 3:return a=e.sent,o=a.filter((function(e){return"fulfilled"===e.status})).length,n=a.filter((function(e){return"rejected"===e.status})).length,console.log("Batch execution completed: ".concat(o," successful, ").concat(n," failed")),e.abrupt("return",a);case 8:case"end":return e.stop()}}),e)})));function t(t){return e.apply(this,arguments)}return t}()}])}(),Y=function(){function e(){E(this,e)}return A(e,null,[{key:"isNetworkError",value:function(e){return!e.response&&e.request}},{key:"isServerError",value:function(e){return e.response&&e.response.status>=500}},{key:"isClientError",value:function(e){return e.response&&e.response.status>=400&&e.response.status<500}},{key:"isTimeoutError",value:function(e){return"ECONNABORTED"===e.code||e.message.includes("timeout")}},{key:"getErrorType",value:function(e){return this.isNetworkError(e)?"NETWORK_ERROR":this.isServerError(e)?"SERVER_ERROR":this.isClientError(e)?"CLIENT_ERROR":this.isTimeoutError(e)?"TIMEOUT_ERROR":"UNKNOWN_ERROR"}},{key:"getErrorMessage",value:function(e){var t=this.getErrorType(e);switch(t){case"NETWORK_ERROR":return"网络连接失败，请检查网络设置";case"SERVER_ERROR":return"服务器暂时不可用，请稍后重试";case"CLIENT_ERROR":return"请求参数错误，请刷新页面重试";case"TIMEOUT_ERROR":return"请求超时，请检查网络连接";default:return"未知错误，请联系管理员"}}}])}(),X=function(){function e(){E(this,e),this.metrics=new Map}return A(e,[{key:"startTimer",value:function(e){this.metrics.set(e,{startTime:performance.now(),endTime:null,duration:null})}},{key:"endTimer",value:function(e){var t=this.metrics.get(e);t&&(t.endTime=performance.now(),t.duration=t.endTime-t.startTime,console.log("".concat(e," completed in ").concat(t.duration.toFixed(2),"ms")))}},{key:"getMetrics",value:function(){var e,t={},r=I(this.metrics.entries());try{for(r.s();!(e=r.n()).done;){var a=x(e.value,2),o=a[0],n=a[1];null!==n.duration&&(t[o]={duration:n.duration,timestamp:new Date(n.startTime).toISOString()})}}catch(s){r.e(s)}finally{r.f()}return t}},{key:"getTotalTime",value:function(){var e,t=0,r=I(this.metrics.values());try{for(r.s();!(e=r.n()).done;){var a=e.value;null!==a.duration&&(t+=a.duration)}}catch(o){r.e(o)}finally{r.f()}return t}}])}(),U=new M,V=new X;function G(e){return G="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},G(e)}function W(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */W=function(){return t};var e,t={},r=Object.prototype,a=r.hasOwnProperty,o=Object.defineProperty||function(e,t,r){e[t]=r.value},n="function"==typeof Symbol?Symbol:{},s=n.iterator||"@@iterator",i=n.asyncIterator||"@@asyncIterator",c=n.toStringTag||"@@toStringTag";function l(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{l({},"")}catch(e){l=function(e,t,r){return e[t]=r}}function u(e,t,r,a){var n=t&&t.prototype instanceof b?t:b,s=Object.create(n.prototype),i=new T(a||[]);return o(s,"_invoke",{value:I(e,r,i)}),s}function d(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=u;var p="suspendedStart",f="suspendedYield",m="executing",h="completed",g={};function b(){}function v(){}function k(){}var y={};l(y,s,(function(){return this}));var w=Object.getPrototypeOf,x=w&&w(w(F([])));x&&x!==r&&a.call(x,s)&&(y=x);var _=k.prototype=b.prototype=Object.create(y);function S(e){["next","throw","return"].forEach((function(t){l(e,t,(function(e){return this._invoke(t,e)}))}))}function N(e,t){function r(o,n,s,i){var c=d(e[o],e,n);if("throw"!==c.type){var l=c.arg,u=l.value;return u&&"object"==G(u)&&a.call(u,"__await")?t.resolve(u.__await).then((function(e){r("next",e,s,i)}),(function(e){r("throw",e,s,i)})):t.resolve(u).then((function(e){l.value=e,s(l)}),(function(e){return r("throw",e,s,i)}))}i(c.arg)}var n;o(this,"_invoke",{value:function(e,a){function o(){return new t((function(t,o){r(e,a,t,o)}))}return n=n?n.then(o,o):o()}})}function I(t,r,a){var o=p;return function(n,s){if(o===m)throw Error("Generator is already running");if(o===h){if("throw"===n)throw s;return{value:e,done:!0}}for(a.method=n,a.arg=s;;){var i=a.delegate;if(i){var c=B(i,a);if(c){if(c===g)continue;return c}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if(o===p)throw o=h,a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);o=m;var l=d(t,r,a);if("normal"===l.type){if(o=a.done?h:f,l.arg===g)continue;return{value:l.arg,done:a.done}}"throw"===l.type&&(o=h,a.method="throw",a.arg=l.arg)}}}function B(t,r){var a=r.method,o=t.iterator[a];if(o===e)return r.delegate=null,"throw"===a&&t.iterator.return&&(r.method="return",r.arg=e,B(t,r),"throw"===r.method)||"return"!==a&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+a+"' method")),g;var n=d(o,t.iterator,r.arg);if("throw"===n.type)return r.method="throw",r.arg=n.arg,r.delegate=null,g;var s=n.arg;return s?s.done?(r[t.resultName]=s.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,g):s:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,g)}function O(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function C(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function T(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(O,this),this.reset(!0)}function F(t){if(t||""===t){var r=t[s];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,n=function r(){for(;++o<t.length;)if(a.call(t,o))return r.value=t[o],r.done=!1,r;return r.value=e,r.done=!0,r};return n.next=n}}throw new TypeError(G(t)+" is not iterable")}return v.prototype=k,o(_,"constructor",{value:k,configurable:!0}),o(k,"constructor",{value:v,configurable:!0}),v.displayName=l(k,c,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===v||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,k):(e.__proto__=k,l(e,c,"GeneratorFunction")),e.prototype=Object.create(_),e},t.awrap=function(e){return{__await:e}},S(N.prototype),l(N.prototype,i,(function(){return this})),t.AsyncIterator=N,t.async=function(e,r,a,o,n){void 0===n&&(n=Promise);var s=new N(u(e,r,a,o),n);return t.isGeneratorFunction(r)?s:s.next().then((function(e){return e.done?e.value:s.next()}))},S(_),l(_,c,"Generator"),l(_,s,(function(){return this})),l(_,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var a in t)r.push(a);return r.reverse(),function e(){for(;r.length;){var a=r.pop();if(a in t)return e.value=a,e.done=!1,e}return e.done=!0,e}},t.values=F,T.prototype={constructor:T,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(C),!t)for(var r in this)"t"===r.charAt(0)&&a.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function o(a,o){return i.type="throw",i.arg=t,r.next=a,o&&(r.method="next",r.arg=e),!!o}for(var n=this.tryEntries.length-1;n>=0;--n){var s=this.tryEntries[n],i=s.completion;if("root"===s.tryLoc)return o("end");if(s.tryLoc<=this.prev){var c=a.call(s,"catchLoc"),l=a.call(s,"finallyLoc");if(c&&l){if(this.prev<s.catchLoc)return o(s.catchLoc,!0);if(this.prev<s.finallyLoc)return o(s.finallyLoc)}else if(c){if(this.prev<s.catchLoc)return o(s.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<s.finallyLoc)return o(s.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&a.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var n=o;break}}n&&("break"===e||"continue"===e)&&n.tryLoc<=t&&t<=n.finallyLoc&&(n=null);var s=n?n.completion:{};return s.type=e,s.arg=t,n?(this.method="next",this.next=n.finallyLoc,g):this.complete(s)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),g},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),C(r),g}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var a=r.completion;if("throw"===a.type){var o=a.arg;C(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,r,a){return this.delegate={iterator:F(t),resultName:r,nextLoc:a},"next"===this.method&&(this.arg=e),g}},t}function q(e,t,r,a,o,n,s){try{var i=e[n](s),c=i.value}catch(e){return void r(e)}i.done?t(c):Promise.resolve(c).then(a,o)}function J(e){return function(){var t=this,r=arguments;return new Promise((function(a,o){var n=e.apply(t,r);function s(e){q(n,a,o,s,i,"next",e)}function i(e){q(n,a,o,s,i,"throw",e)}s(void 0)}))}}var K={props:{},data:function(){return{storeupdatestateVisible:!1,formalOff:!0,adminStoreInfo:{},userData:{},userInfos:{},turnoverInfos:{},orderInfos:{},inventoryList:{},bucketList:{},isLoading:!0,loadingStates:{userInfo:!1,turnover:!1,orders:!1,inventory:!1,buckets:!1,tools:!1},loadingErrors:{},showErrorRecovery:!1,lastRefreshTime:null,refreshCooldown:3e4,TOOLSVisible:!1,TOOLSLists:[{name:"桶管理",url:"",img:"tgg",alias:""},{shortcutId:5,name:"库存盘点",url:"",img:"kcpd",alias:""},{shortcutId:5,name:"进货调整",url:"",img:"jhtz",alias:""},{shortcutId:5,name:"库存统计",url:"",img:"kctj",alias:""},{shortcutId:5,name:"全部订单",url:"",img:"qbdd",alias:""},{shortcutId:5,name:"客户自提订单",url:"",img:"khzt",alias:""},{shortcutId:5,name:"送水员填单",url:"",img:"pdy",alias:""},{shortcutId:5,name:"客户管理",url:"",img:"khgl",alias:""},{shortcutId:5,name:"临时业务员管理",url:"",img:"lsyw",alias:""},{shortcutId:5,name:"财务报表",url:"",img:"cwbb",alias:""},{shortcutId:5,name:"财务支出",url:"",img:"cwzc",alias:""},{shortcutId:5,name:"大众商品库",url:"",img:"dzspk",alias:""}],TOOLSList:[],multipleSelection:[],myToolsList:[],changeSele:[]}},components:{storeupdatestate:n["a"]},computed:{},created:function(){this.adminStoreInfo=JSON.parse(this.Cookies.get("adminStoreInfo")),this.initializeHomeData()},mounted:function(){},watch:{},methods:{initializeHomeData:function(){var e=J(W().mark((function e(){var t,r,a,o,n,s=this;return W().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.isLoading=!0,V.startTimer("homeDataInit"),e.prev=2,t=[function(){return s.requestUserInfosOptimized()},function(){return s.requestTurnoverInfosOptimized()},function(){return s.requestOrderInfosOptimized()},function(){return s.requestInventoryInfosOptimized()},function(){return s.requestBucketInfosOptimized()},function(){return s.getMyTOOLSApiOptimized()}],e.next=6,U.executeBatch(t,{maxRetries:2,retryDelay:1e3});case 6:r=e.sent,a=r.filter((function(e,t){return"rejected"===e.status&&t<3})),a.length>0&&(console.warn("Critical API failures detected:",a),this.$message.warning("部分数据加载失败，页面功能可能受限")),e.next=16;break;case 11:e.prev=11,e.t0=e["catch"](2),console.error("Error initializing home data:",e.t0),o=Y.getErrorMessage(e.t0),this.$message.error(o);case 16:return e.prev=16,V.endTimer("homeDataInit"),this.isLoading=!1,n=V.getMetrics(),console.log("Home page load metrics:",n),e.finish(16);case 22:case"end":return e.stop()}}),e,this,[[2,11,16,22]])})));function t(){return e.apply(this,arguments)}return t}(),storeupdatestate:function(e,t){var r=this;0!=this.adminStoreInfo.role1&&(this.storeupdatestateVisible=!0,this.$nextTick((function(){r.$refs.storeupdatestate1.init(e,t)})))},requestUserInfosOptimized:function(){var e=J(W().mark((function e(){var t,r,a;return W().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(this.loadingStates.userInfo=!0,t=this.Cookies.get("storeId"),e.prev=2,r=w.get(t,"userInfo"),!r){e.next=10;break}return this.userData=r.userData,this.userInfos=r.userInfos,this.formalOff=2==this.userInfos.state,this.loadingStates.userInfo=!1,e.abrupt("return");case 10:return e.next=12,this.$post("/szmb/szmbhomepagecontroller/selectstoredeatil",{storeId:t});case 12:if(a=e.sent,1!==a.code){e.next=20;break}this.userData=a.data,this.userInfos=a.data.store,this.formalOff=2==this.userInfos.state,w.set(t,"userInfo",{userData:a.data,userInfos:a.data.store}),e.next=26;break;case 20:if(0!==a.code){e.next=25;break}this.userData={},this.userInfos={},e.next=26;break;case 25:throw new Error(a.msg||"获取店铺信息失败");case 26:e.next=33;break;case 28:e.prev=28,e.t0=e["catch"](2),console.error("Error fetching user info:",e.t0),this.loadingErrors.userInfo=e.t0.message,this.$message.error("获取店铺信息失败");case 33:return e.prev=33,this.loadingStates.userInfo=!1,e.finish(33);case 36:case"end":return e.stop()}}),e,this,[[2,28,33,36]])})));function t(){return e.apply(this,arguments)}return t}(),requestUserInfos:function(){var e=this,t=this;this.$post("/szmb/szmbhomepagecontroller/selectstoredeatil",{storeId:t.Cookies.get("storeId")}).then((function(r){1===r.code?(t.userData=r.data,t.userInfos=r.data.store,2==t.userInfos.state?t.formalOff=!0:t.formalOff=!1):0===r.code?(t.userData={},t.userInfos={}):(console.log(r.msg),e.$message.error(r.data))})).catch((function(e){console.log(e)}))},requestTurnoverInfosOptimized:function(){var e=J(W().mark((function e(){var t,r,a;return W().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(this.loadingStates.turnover=!0,t=this.Cookies.get("storeId"),e.prev=2,r=w.get(t,"turnover"),!r){e.next=8;break}return this.turnoverInfos=r,this.loadingStates.turnover=!1,e.abrupt("return");case 8:return e.next=10,this.$post("/szmb/szmbhomepagecontroller/selectstorebusiness",{storeId:t});case 10:if(a=e.sent,1!==a.code){e.next=16;break}this.turnoverInfos=a.data,w.set(t,"turnover",a.data),e.next=21;break;case 16:if(0!==a.code){e.next=20;break}this.turnoverInfos={},e.next=21;break;case 20:throw new Error(a.msg||"获取营业统计失败");case 21:e.next=28;break;case 23:e.prev=23,e.t0=e["catch"](2),console.error("Error fetching turnover info:",e.t0),this.loadingErrors.turnover=e.t0.message,this.$message.error("获取营业统计失败");case 28:return e.prev=28,this.loadingStates.turnover=!1,e.finish(28);case 31:case"end":return e.stop()}}),e,this,[[2,23,28,31]])})));function t(){return e.apply(this,arguments)}return t}(),requestTurnoverInfos:function(){var e=this,t=this;this.$post("/szmb/szmbhomepagecontroller/selectstorebusiness",{storeId:t.Cookies.get("storeId")}).then((function(r){1===r.code?t.turnoverInfos=r.data:0===r.code?t.turnoverInfos={}:(console.log(r.msg),e.$message.error(r.data))})).catch((function(e){console.log(e)}))},requestOrderInfosOptimized:function(){var e=J(W().mark((function e(){var t;return W().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.loadingStates.orders=!0,e.prev=1,e.next=4,this.$post("/szmb/szmbhomepagecontroller/selectstoreorder",{storeId:this.Cookies.get("storeId")});case 4:if(t=e.sent,1!==t.code){e.next=9;break}this.orderInfos=t.data,e.next=14;break;case 9:if(0!==t.code){e.next=13;break}this.orderInfos={},e.next=14;break;case 13:throw new Error(t.msg||"获取订单统计失败");case 14:e.next=21;break;case 16:e.prev=16,e.t0=e["catch"](1),console.error("Error fetching order info:",e.t0),this.loadingErrors.orders=e.t0.message,this.$message.error("获取订单统计失败");case 21:return e.prev=21,this.loadingStates.orders=!1,e.finish(21);case 24:case"end":return e.stop()}}),e,this,[[1,16,21,24]])})));function t(){return e.apply(this,arguments)}return t}(),requestOrderInfos:function(){var e=this,t=this;this.$post("/szmb/szmbhomepagecontroller/selectstoreorder",{storeId:t.Cookies.get("storeId")}).then((function(r){1===r.code?t.orderInfos=r.data:0===r.code?t.orderInfos={}:(console.log(r.msg),e.$message.error(r.data))})).catch((function(e){console.log(e)}))},requestInventoryInfosOptimized:function(){var e=J(W().mark((function e(){var t;return W().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.loadingStates.inventory=!0,e.prev=1,e.next=4,this.$post("/szmb/szmbhomepagecontroller/selectstoreinventory",{storeId:this.Cookies.get("storeId")});case 4:if(t=e.sent,1!==t.code){e.next=9;break}this.inventoryList=t.data,e.next=14;break;case 9:if(0!==t.code){e.next=13;break}this.inventoryList={},e.next=14;break;case 13:throw new Error(t.msg||"获取库存统计失败");case 14:e.next=21;break;case 16:e.prev=16,e.t0=e["catch"](1),console.error("Error fetching inventory info:",e.t0),this.loadingErrors.inventory=e.t0.message,this.$message.error("获取库存统计失败");case 21:return e.prev=21,this.loadingStates.inventory=!1,e.finish(21);case 24:case"end":return e.stop()}}),e,this,[[1,16,21,24]])})));function t(){return e.apply(this,arguments)}return t}(),requestInventoryInfos:function(){var e=this,t=this;this.$post("/szmb/szmbhomepagecontroller/selectstoreinventory",{storeId:t.Cookies.get("storeId")}).then((function(r){1===r.code?t.inventoryList=r.data:0===r.code?t.inventoryList={}:(console.log(r.msg),e.$message.error(r.data))})).catch((function(e){console.log(e)}))},requestBucketInfosOptimized:function(){var e=J(W().mark((function e(){var t;return W().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.loadingStates.buckets=!0,e.prev=1,e.next=4,this.$post("/szmb/szmbhomepagecontroller/selectstorebuck",{storeId:this.Cookies.get("storeId")});case 4:if(t=e.sent,1!==t.code){e.next=9;break}this.bucketList=t.data,e.next=14;break;case 9:if(0!==t.code){e.next=13;break}this.bucketList={},e.next=14;break;case 13:throw new Error(t.msg||"获取水桶统计失败");case 14:e.next=21;break;case 16:e.prev=16,e.t0=e["catch"](1),console.error("Error fetching bucket info:",e.t0),this.loadingErrors.buckets=e.t0.message,this.$message.error("获取水桶统计失败");case 21:return e.prev=21,this.loadingStates.buckets=!1,e.finish(21);case 24:case"end":return e.stop()}}),e,this,[[1,16,21,24]])})));function t(){return e.apply(this,arguments)}return t}(),requestBucketInfos:function(){var e=this,t=this;this.$post("/szmb/szmbhomepagecontroller/selectstorebuck",{storeId:t.Cookies.get("storeId")}).then((function(r){1===r.code?t.bucketList=r.data:0===r.code?t.bucketList={}:(console.log(r.msg),e.$message.error(r.data))})).catch((function(e){console.log(e)}))},addTOOLSList:function(){var e=this,t=this;this.$post("/szmb/szmbhomepagecontroller/selectstoretotal",{storeId:t.Cookies.get("storeId"),index:1,pageSize:1e4}).then((function(r){1===r.code?t.changeSele=r.data:0===r.code?t.changeSele=[]:(console.log(r.msg),e.$message.error(r.data)),t.requestTOOLSInfo()})).catch((function(e){console.log(e)})),this.TOOLSVisible=!0},requestTOOLSInfo:function(){var e=this,t=this;this.$post("/szmb/szmbhomepagecontroller/selecttotal",{index:1,pageSize:1e4}).then((function(r){1===r.code?(t.TOOLSList=r.data,e.$nextTick((function(){var e=t.TOOLSList,r=t.changeSele;0!=r.length&&e.forEach((function(e){r.forEach((function(r){e.shortcutId==r.shortcutId&&t.$refs.multipleTable.toggleRowSelection(e,!0)}))}))}))):0===r.code?e.$message({type:"success",message:r.data}):(console.log(r.msg),e.$message.error(r.data))})).catch((function(e){console.log(e)}))},getMyTOOLSApiOptimized:function(){var e=J(W().mark((function e(){var t;return W().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.loadingStates.tools=!0,e.prev=1,e.next=4,this.$post("/szmb/szmbhomepagecontroller/selectstoretotal",{storeId:this.Cookies.get("storeId"),index:1,pageSize:1e4});case 4:if(t=e.sent,1!==t.code){e.next=9;break}this.myToolsList=t.data,e.next=14;break;case 9:if(0!==t.code){e.next=13;break}this.myToolsList=[],e.next=14;break;case 13:throw new Error(t.msg||"获取快捷工具失败");case 14:e.next=21;break;case 16:e.prev=16,e.t0=e["catch"](1),console.error("Error fetching tools:",e.t0),this.loadingErrors.tools=e.t0.message,this.$message.error("获取快捷工具失败");case 21:return e.prev=21,this.loadingStates.tools=!1,e.finish(21);case 24:case"end":return e.stop()}}),e,this,[[1,16,21,24]])})));function t(){return e.apply(this,arguments)}return t}(),getMyTOOLSApi:function(){var e=this,t=this;this.$post("/szmb/szmbhomepagecontroller/selectstoretotal",{storeId:t.Cookies.get("storeId"),index:1,pageSize:1e4}).then((function(r){1===r.code?t.myToolsList=r.data:0===r.code?t.myToolsList=[]:(console.log(r.msg),e.$message.error(r.data))})).catch((function(e){console.log(e)}))},submitTOOLS:function(){var e=[];this.multipleSelection.forEach((function(t){e.push(t.shortcutId)}));var t=JSON.stringify(e);this.addtoolsApi(t)},handleSelectionChange:function(e){this.multipleSelection=e},addtoolsApi:function(e){var t=this,r=this;this.$post("/szmb/szmbhomepagecontroller/insertstoreshorot",{storeId:r.Cookies.get("storeId"),list:e}).then((function(e){1===e.code?(r.$message({type:"success",message:e.data}),r.TOOLSVisible=!1):t.$message({type:"success",message:e.data}),r.getMyTOOLSApi()})).catch((function(e){console.log(e)}))},refreshHomeData:function(){var e=J(W().mark((function e(){var t,r,a;return W().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=Date.now(),!(this.lastRefreshTime&&t-this.lastRefreshTime<this.refreshCooldown)){e.next=5;break}return r=Math.ceil((this.refreshCooldown-(t-this.lastRefreshTime))/1e3),this.$message.warning("请等待 ".concat(r," 秒后再次刷新")),e.abrupt("return");case 5:return this.lastRefreshTime=t,a=this.Cookies.get("storeId"),w.clearStore(a),this.loadingErrors={},this.showErrorRecovery=!1,e.next=12,this.initializeHomeData();case 12:this.$message.success("数据已刷新");case 13:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),retrySection:function(){var e=J(W().mark((function e(t){var r,a,o=this;return W().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(this.loadingErrors[t]=null,r={userInfo:"requestUserInfosOptimized",turnover:"requestTurnoverInfosOptimized",orders:"requestOrderInfosOptimized",inventory:"requestInventoryInfosOptimized",buckets:"requestBucketInfosOptimized",tools:"getMyTOOLSApiOptimized"},a=r[t],!a||!this[a]){e.next=14;break}return e.prev=4,e.next=7,U.executeWithRetry((function(){return o[a]()}),{maxRetries:3,retryDelay:1e3});case 7:this.$message.success("".concat(t," 数据重新加载成功")),e.next=14;break;case 10:e.prev=10,e.t0=e["catch"](4),console.error("Failed to retry ".concat(t,":"),e.t0),this.$message.error("".concat(t," 数据重新加载失败"));case 14:case"end":return e.stop()}}),e,this,[[4,10]])})));function t(t){return e.apply(this,arguments)}return t}(),hasCriticalErrors:function(){var e=this;return Object.keys(this.loadingErrors).some((function(t){return["userInfo","turnover","orders"].includes(t)&&e.loadingErrors[t]}))}}},H=K,Z=(r("e2ce"),r("0c7c")),Q=Object(Z["a"])(H,a,o,!1,null,"fbc94680",null);t["default"]=Q.exports},db7a:function(e,t,r){"use strict";r.d(t,"b",(function(){return a})),r.d(t,"a",(function(){return o})),r.d(t,"c",(function(){return n})),r.d(t,"d",(function(){return s})),r.d(t,"e",(function(){return i}));var a=[{key:0,value:"普通商户"},{key:1,value:"连锁商户总店"},{key:2,value:"城市合伙人"},{key:3,value:"区域合伙人"},{key:4,value:"总管理员"},{key:5,value:"运营组"}],o=[{key:0,value:"临时商家"},{key:1,value:"正式商家"},{key:2,value:"试用商家"},{key:3,value:"过期"}],n=[{key:2,value:"启用"},{key:3,value:"禁用"}],s=[{key:0,value:"禁用"},{key:1,value:"启用"}],i=[{key:0,value:"组员"},{key:1,value:"组长"}]},e2ce:function(e,t,r){"use strict";r("4fc8")},f39e:function(e,t){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAHkAAAA1CAMAAABSvrRsAAAA5FBMVEUAAABvw4Fvw4Fvw4FepGtvw4Ffpmxvw4FrvHxvw4Fvw4Fvw4FepGtvw4Fvw4FepGtvw4FepGtntXdvw4FepGtvw4FepGtvw4Fvw4Fvw4Fvw4FepGtvw4Fvw4Fvw4Fvw4Fvw4FyyYVrvHxvw4FlsXRvw4Fvw4FZm2VepGtepGtvw4FepGtvw4FepGtvw4H///9epGu74MLN6NKHzJWd1Kjv+PHn9Onf8OLW7Np7x4ux3Lr3+/iS0J/E5MpyyYWn2LFdompot3lbn2dxxoNanWZxx4RtwX9coWhlsnViq3BzyYVsvX0x0exIAAAALnRSTlMA6ZTy0vr2HwnCqinl49WaiTMPA925tp6PhXxqWEQuF/Py8u3s2c3LwY5OPDga3+nVYQAAApxJREFUWMO81dtq20AUhWG5JTixsSmUkgaStFDai1LWYktz0sHo/d+qu8PIHpwqHgei/8I2Y8OnrZHkamq9W71nu/3d5uHx+XP1sg2WaHf/eHsGr7FUq6/rKu8LluvmIYO3WLT99ijfYdluJnqLpftxu/wupz5F+BlX1JANtE60QJuWa9a4plXa5Rkir8nklqcyueOUvbzTlfYH18qw7OVfA/BG+Xul/cLF5Ewe6roR28AfTCZDgkALdLjUR4WfcI3sqYlOZyxro2/JjcuBAZp+UyTvMVNNzsmwHDsvTolcFvYAWlJK5CeUyfnZ7l7uv2MdzQEY9bVE/vmKfOqSrKbK8BSgpkWB/BtvkxEz07qQzgOdom3PQ4m8elX+/8XGDuc5ah6qGiGHEvmuUJZ0//TpUpoy0+x0wY4QOkuPEnldKB/SJE4/NDiXA8kaWmvjWolcbcrkQObP7Y4hLvZtOq50gw3xEMrk+aF1ujbbSJfLhgytP443+jqCQ8/ymeeHluyJoNIhl9ExN1oTZaOwCeRYJn/DTG3PVK4MUR4lPbXyfwyJv4Jn0dNTu8dMgz+5bjzePj1cXJH81KqssB3SNSGX5dmh5zd/RKNMC8CI9dnMh2lUKZpZ+4DFS/Lf3uddBWEgCsLwQTSkCMRcCGIqRVARl32CZS9JNtH3fx9twlhs6/zlNB9TK26QZa+4Qc53ihdk/mnI/NOQ5ai4QW5YpyFTTgfzbQxBIcjNYfPHLrNz70mNxgTIlK7eR6uXZXbT+ApMeau1tjZ67/XiJjMy5TXrBztPhioDH6IzVBnFwZ2EEWTcPgshyGjIhFBKtmwZFZKMIN+FUErOaiGUkishlJBbApyUy1xY/cpZWQmxYmW7/inUbkW77cr+UWP6ACaBAepD9PzjAAAAAElFTkSuQmCC"}}]);