(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["privacy"],{"4b71":function(t,r,c){"use strict";c.r(r);var e=function(){var t=this,r=t._self._c;return r("div",[r("privacyHtml",{attrs:{id:"privacyHtml",htmlHeight:t.htmlHeight,scrollbarHeight:t.scrollbarHeight}})],1)},i=[],l=c("62f5"),n={props:{},data:function(){return{htmlHeight:940,scrollbarHeight:957}},computed:{},created:function(){},methods:{},components:{privacyHtml:l["a"]}},a=n,o=c("0c7c"),s=Object(o["a"])(a,e,i,!1,null,null,null);r["default"]=s.exports}}]);