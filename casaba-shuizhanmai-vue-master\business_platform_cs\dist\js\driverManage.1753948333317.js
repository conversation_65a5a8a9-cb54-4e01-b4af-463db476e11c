(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["driverManage"],{"28fb":function(e,t,a){},"34b5":function(t,a,r){"use strict";r.r(a);var o=function(){var e=this,t=e._self._c;return t("div",{staticStyle:{"min-width":"1651px","box-sizing":"border-box",margin:"0 auto"}},[0==e.pageFlag?t("div",[t("div",{staticClass:"flex align-items-center justify-content-between"},[t("div",[t("el-select",{staticStyle:{width:"212px","margin-right":"20px"},attrs:{clearable:"",placeholder:"按送水员身份状态筛选"},model:{value:e.driverSelect.idState,callback:function(t){e.$set(e.driverSelect,"idState",t)},expression:"driverSelect.idState"}},e._l(e.driverIdState,(function(e,a){return t("el-option",{key:a,attrs:{label:e.name,value:e.value}})})),1),t("el-select",{staticStyle:{width:"212px","margin-right":"20px"},attrs:{clearable:"",placeholder:"按送水员配送状态筛选"},model:{value:e.driverSelect.sendState,callback:function(t){e.$set(e.driverSelect,"sendState",t)},expression:"driverSelect.sendState"}},e._l(e.driverSendState,(function(e,a){return t("el-option",{key:a,attrs:{label:e.name,value:e.value}})})),1),t("el-input",{staticStyle:{width:"240px","margin-right":"20px"},attrs:{placeholder:"输入送水员姓名和手机号筛选"},model:{value:e.driverSelect.isKey,callback:function(t){e.$set(e.driverSelect,"isKey",t)},expression:"driverSelect.isKey"}}),t("el-button",{attrs:{type:"primary"},on:{click:e.searchDriver}},[e._v("查询")]),t("el-button",{staticStyle:{"margin-left":"20px"},on:{click:e.clearSearch}},[e._v("清空筛选条件")])],1),t("div",[t("el-button",{staticStyle:{"margin-left":"20px"},attrs:{type:"success",icon:"el-icon-user"},on:{click:e.addDriver}},[e._v("新增送水员")])],1)]),e._m(0),t("el-table",{staticStyle:{width:"100%","margin-top":"20px"},attrs:{data:e.driverList,"header-cell-style":{"text-align":"center","background-color":"rgba(239,242,247,1)"},"cell-style":{"text-align":"center","font-size":"13px",color:"#333C48"},"max-height":e.tableHeight+100,stripe:"",border:""}},[t("el-table-column",{attrs:{type:"index",width:"80",label:"序号"}}),t("el-table-column",{attrs:{prop:"name",label:"送水员信息"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("div",[e._v(e._s(a.row.name))]),t("div",[e._v(e._s(a.row.phone))])]}}],null,!1,1512224783)}),t("el-table-column",{attrs:{prop:"remarks",label:"配送范围"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("div",[e._v(e._s(a.row.remarks))])]}}],null,!1,4080672320)}),t("el-table-column",{attrs:{prop:"totalMoeny",width:"",label:"保证金"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("span",[e._v("￥"+e._s(a.row.totalMoney?a.row.totalMoney:"0.00"))])]}}],null,!1,350888627)}),t("el-table-column",{attrs:{prop:"",label:"身份状态"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("span",[e._v(e._s(1==a.row.shortTime?"固定":"临时"))])]}}],null,!1,1291670295)}),t("el-table-column",{attrs:{prop:"",label:"在职状态"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("span",[e._v(e._s(0==a.row.delectState?"在职":"离职"))])]}}],null,!1,1432035752)}),t("el-table-column",{attrs:{prop:"",label:"配送状态"},scopedSlots:e._u([{key:"default",fn:function(a){return[1==a.row.delivery?t("span",{staticClass:"color-red"},[e._v("配送中...")]):t("span",{staticClass:"color-green"},[e._v("空闲")])]}}],null,!1,369901758)}),t("el-table-column",{attrs:{prop:"",label:"已送达订单数"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n            "+e._s(t.row.confirmed)+"单\n          ")]}}],null,!1,2323152667)}),t("el-table-column",{attrs:{label:"配送提成合计"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n            ￥"+e._s(t.row.totalTiCheng)+"\n          ")]}}],null,!1,1377379872)}),t("el-table-column",{attrs:{label:"未结算配送提成合计"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n            ￥"+e._s(t.row.residueMoney)+"\n          ")]}}],null,!1,981399633)}),t("el-table-column",{attrs:{prop:"canchild",label:"授权管理骑手权限",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n            "+e._s(t.row.canchild?"是":"否")+"\n          ")]}}],null,!1,3579360887)}),t("el-table-column",{attrs:{prop:"parentName",label:"管理骑手",width:"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n            "+e._s(t.row.parentName||"无")+"\n          ")]}}],null,!1,2976594598)}),t("el-table-column",{attrs:{prop:"isauto",label:"老客户自动接单",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n            "+e._s(1===t.row.isauto?"是":"否")+"\n          ")]}}],null,!1,306771368)}),t("el-table-column",{attrs:{prop:"dateline",label:"老客户截止时间",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n            "+e._s(t.row.dateline||"无")+"\n          ")]}}],null,!1,760292839)}),t("el-table-column",{attrs:{width:"400",align:"left",label:"操作",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("div",[t("div",{staticClass:"flex align-items-center",staticStyle:{"text-align":"left","font-size":"12px"}},[t("el-switch",{staticStyle:{zoom:"0.8"},attrs:{"active-value":1,"inactive-value":0,"active-text":"月结付款"},on:{change:function(t){return e.changeDeliveryPower(t,a.row,"yf")}},model:{value:a.row.yfState,callback:function(t){e.$set(a.row,"yfState",t)},expression:"scope.row.yfState"}}),1==a.row.shortTime?t("span",{staticClass:"cursor-pointer margin-left-10 color-blue",on:{click:function(t){return e.OpenUsreCheck(a.row,1)}}},[e._v("一级分销")]):e._e(),1==a.row.shortTime?t("span",{staticClass:"cursor-pointer margin-left-10 color-blue",on:{click:function(t){return e.OpenUsreCheck(a.row,2)}}},[e._v("二级分销")]):e._e(),t("span",{staticClass:"cursor-pointer margin-left-10 color-blue",on:{click:function(t){return e.driverMoneyDetail(a.row.id,a.row.name)}}},[e._v("提成结算")]),t("span",{staticClass:"cursor-pointer margin-left-10 color-blue",on:{click:function(t){return e.editDriver(a.row)}}},[e._v("编辑")]),0==a.row.delectState?t("span",{staticClass:"cursor-pointer margin-left-10 color-red",on:{click:function(t){return e.updateDeliveryUserState(a.row.id,1)}}},[e._v("离职")]):t("span",{staticClass:"cursor-pointer margin-left-10 color-green",on:{click:function(t){return e.updateDeliveryUserState(a.row.id,0)}}},[e._v("恢复工作")])],1),t("div",{staticClass:"flex align-items-center",staticStyle:{"text-align":"left","font-size":"12px"}},[1==a.row.shortTime?t("el-switch",{staticStyle:{zoom:"0.8"},attrs:{"active-value":1,"inactive-value":0,"active-text":"共享客户"},on:{change:function(t){return e.changeDeliveryPower(t,a.row,"gxkh")}},model:{value:a.row.gxkhState,callback:function(t){e.$set(a.row,"gxkhState",t)},expression:"scope.row.gxkhState"}}):e._e(),t("el-switch",{staticStyle:{zoom:"0.8","margin-left":"10px"},attrs:{"active-value":1,"inactive-value":0,"active-text":"设置客户优惠"},on:{change:function(t){return e.changeDeliveryPower(t,a.row,"yh")}},model:{value:a.row.yhState,callback:function(t){e.$set(a.row,"yhState",t)},expression:"scope.row.yhState"}}),t("span",{staticClass:"cursor-pointer margin-left-10 color-blue",on:{click:function(t){return e.deliveryOrders(a.row)}}},[e._v("查看配送订单")]),t("span",{staticClass:"cursor-pointer margin-left-10 color-blue",on:{click:function(t){return e.setGeofence(a.row.id)}}},[e._v("设置围栏")]),1==a.row.delectState?t("span",{staticClass:"cursor-pointer margin-left-10 color-blue",staticStyle:{color:"red"},on:{click:function(t){return e.deliveryDelete(a.row)}}},[e._v("删除")]):e._e()],1)])]}}],null,!1,1547935772)})],1),t("div",{staticClass:"pages-box"},[t("el-pagination",{attrs:{layout:"total, prev, pager, next,sizes, jumper",background:"","current-page":e.pagesData.currentPage,"page-sizes":e.pagesData.currentPageSizes,"page-size":e.pagesData.pageSize,total:e.pagesData.pageTotal},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1):e._e(),t("div",{directives:[{name:"show",rawName:"v-show",value:2==e.pageFlag,expression:"pageFlag == 2"}]},[t("div",{staticClass:"margin-bottom-20"},[t("el-page-header",{staticClass:"padding-bottom-10 border-bottom iconfont icon-fanhui",attrs:{content:"提成明细"},on:{back:function(t){e.pageFlag=0}}})],1),t("div",[t("div",{staticClass:"flex align-items-center justify-content-between",staticStyle:{width:"100%",height:"37px"}},[t("div",{staticClass:"flex align-items-center cursor-pointer"},[t("div",{class:[0==e.driverMoneyData.tcIndex?"active_top_box":"","top-box"],on:{click:function(t){return t.stopPropagation(),e.tapTcIndex(0)}}},[e._v("\n              未结算\n            ")]),t("div",{class:[1==e.driverMoneyData.tcIndex?"active_top_box":"","top-box"],staticStyle:{"border-right":"1px solid rgba(216, 220, 229, 1)"},on:{click:function(t){return t.stopPropagation(),e.tapTcIndex(1)}}},[e._v("\n              已结算\n            ")])]),0==e.driverMoneyData.tcIndex&&e.driverMoneyData.userInfo?t("div",{staticClass:"flex align-items-center"},[t("div",[e._v("送水员："+e._s(e.driverMoneyData.userInfo.deliveryName))]),t("div",{staticClass:"margin-left-20"},[e._v("未结算订单总数："),t("span",{staticClass:"color-red"},[e._v(e._s(e.driverMoneyData.userInfo.totalTiChengCount)+"个")])]),t("div",{staticClass:"margin-left-20"},[e._v("未结算提成总额："),t("span",{staticClass:"color-red"},[e._v(e._s(e.driverMoneyData.userInfo.totalTiChengMoney)+"元")])])]):e._e(),1==e.driverMoneyData.tcIndex&&e.driverMoneyData.userInfo?t("div",{staticClass:"flex align-items-center"},[t("div",[e._v("送水员："+e._s(e.driverMoneyData.userInfo.deliveryName))]),t("div",{staticClass:"margin-left-20"},[e._v("已结算订单总数："),t("span",{staticClass:"color-red"},[e._v(e._s(e.driverMoneyData.userInfo.totalTiChengCount)+"个")])]),t("div",{staticClass:"margin-left-20"},[e._v("已结算提成总额："),t("span",{staticClass:"color-red"},[e._v(e._s(e.driverMoneyData.userInfo.totalTiChengMoney)+"元")])])]):e._e()])]),t("div",{staticStyle:{padding:"20px",border:"1px solid rgba(216,220,229,1)"}},[t("div",{staticClass:"flex align-items-center justify-content-between"},[t("div",[t("el-date-picker",{staticStyle:{width:"250px"},attrs:{"value-format":"yyyy-MM-dd",type:"daterange",align:"right","unlink-panels":"","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":e.dateSelect},model:{value:e.driverMoneyData.date,callback:function(t){e.$set(e.driverMoneyData,"date",t)},expression:"driverMoneyData.date"}}),t("el-button",{staticStyle:{"margin-left":"20px"},attrs:{type:"primary"},on:{click:e.searchDriverMoney}},[e._v("查询")]),t("el-button",{staticStyle:{"margin-left":"20px"},on:{click:e.clearTc}},[e._v("清空筛选条件")])],1),1==e.driverMoneyData.userInfo.shortTime&&1!=e.driverMoneyData.tcIndex?t("el-button",{staticClass:"el-icon-coin",staticStyle:{"margin-left":"50px"},attrs:{type:"success"},on:{click:e.openCountDriverMoney}},[e._v("结算 (\n            ￥"+e._s(e.driverMoneyData.count)+" )")]):e._e()],1)]),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],staticStyle:{width:"100%","margin-top":"20px"},attrs:{data:e.driverMoneyData.list,"summary-method":e.summaryMoney,"show-summary":"",border:"","header-cell-style":{"text-align":"center","background-color":"rgba(239,242,247,1)"},"cell-style":{"text-align":"center","font-size":"13px",color:"#333C48"},"max-height":e.tableHeight+60},on:{"selection-change":e.driverMoneyCheck}},[1==e.driverMoneyData.userInfo.shortTime&&0==e.driverMoneyData.tcIndex?t("el-table-column",{attrs:{type:"selection",width:"55",label:"选择"}}):e._e(),t("el-table-column",{attrs:{prop:"orderNumber",label:"交易编号"}}),t("el-table-column",{attrs:{prop:"orderTime",label:"送达时间"}}),t("el-table-column",{attrs:{label:"商品信息"},scopedSlots:e._u([{key:"default",fn:function(a){return e._l(a.row.productList,(function(a,r){return t("div",{key:r},[e._v("\n              "+e._s(a.spuName)+"x"+e._s(a.num)+"\n            ")])}))}}])}),t("el-table-column",{attrs:{prop:"numberMoney",label:"配送单件总提成"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("span",[e._v("￥"+e._s(a.row.numberMoney))])]}}])}),t("el-table-column",{attrs:{prop:"discountMoney",label:"配送距离总提成"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("span",[e._v("￥"+e._s(a.row.discountMoney))])]}}])}),t("el-table-column",{attrs:{prop:"deliveryMoney",label:"配送业务总提成"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("span",[e._v("￥"+e._s(a.row.deliveryMoney))])]}}])}),t("el-table-column",{attrs:{prop:"upcomeMoney",label:"上楼总提成"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("span",[e._v("￥"+e._s(a.row.upcomeMoney))])]}}])}),t("el-table-column",{attrs:{prop:"seneMoenyOne",label:"配送提成合计"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("span",[e._v("￥"+e._s(a.row.seneMoenyOne))])]}}])}),t("el-table-column",{attrs:{prop:"",label:"状态"},scopedSlots:e._u([{key:"default",fn:function(a){return[1==a.row.state?t("span",{staticClass:"color-blue"},[e._v("已结算")]):t("span",{staticClass:"color-red"},[e._v("未结算")])]}}])}),t("el-table-column",{attrs:{prop:"",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(a){return[1==a.row.oto?t("div",{staticClass:"color-blue cursor-pointer",on:{click:function(t){return e.lookOrderDetail1(a.row)}}},[e._v("查看详情")]):t("div",{staticClass:"color-blue cursor-pointer",on:{click:function(t){return e.lookOrderDetail(a.row)}}},[e._v("查看详情")])]}}])})],1),t("div",{staticClass:"pages-box"},[t("el-pagination",{attrs:{layout:"total, prev, pager, next,sizes, jumper",background:"","current-page":e.pagesData1.currentPage,"page-sizes":e.pagesData1.currentPageSizes,"page-size":e.pagesData1.pageSize,total:e.pagesData1.pageTotal},on:{"size-change":e.handleSizeChange1,"current-change":e.handleCurrentChange1}})],1)],1),t("el-dialog",{attrs:{title:"0"==e.driverFormData.userId?"新增送水员":"编辑送水员",visible:e.addDriverDialog,width:"40%","before-close":e.handleClose},on:{"update:visible":function(t){e.addDriverDialog=t}}},[t("div",{staticClass:"dialogDiv"},[t("el-form",{ref:"driverFormDataElement",attrs:{model:e.driverFormData,rules:e.driverFormDataRules,"label-width":"100px"}},[t("el-form-item",{attrs:{label:"姓名",prop:"name"}},[t("el-input",{model:{value:e.driverFormData.name,callback:function(t){e.$set(e.driverFormData,"name",t)},expression:"driverFormData.name"}})],1),t("el-form-item",{attrs:{label:"联系方式",prop:"phone"}},[t("el-input",{attrs:{oninput:"value=value.replace(/[^\\d]/g,'')"},model:{value:e.driverFormData.phone,callback:function(t){e.$set(e.driverFormData,"phone",t)},expression:"driverFormData.phone"}})],1),t("el-form-item",{attrs:{label:"配送范围",prop:"remarks"}},[t("el-input",{model:{value:e.driverFormData.remarks,callback:function(t){e.$set(e.driverFormData,"remarks",t)},expression:"driverFormData.remarks"}})],1),t("el-form-item",{attrs:{label:"身份证号",prop:"idCard"}},[t("el-input",{model:{value:e.driverFormData.idCard,callback:function(t){e.$set(e.driverFormData,"idCard",t)},expression:"driverFormData.idCard"}})],1),t("el-form-item",{attrs:{label:"送水员类别"}},[t("el-tag",[e._v("固定")])],1),e.driverFormData.userId?t("el-form-item",{attrs:{label:"在职状态",prop:"userId"}},[t("el-select",{attrs:{placeholder:"请选择"},model:{value:e.driverFormData.jobState,callback:function(t){e.$set(e.driverFormData,"jobState",t)},expression:"driverFormData.jobState"}},e._l(e.jobStateList,(function(e){return t("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1):e._e(),t("el-form-item",{attrs:{label:"送水员师父",prop:"pid"}},[t("el-select",{attrs:{placeholder:"请选择"},model:{value:e.driverFormData.pid,callback:function(t){e.$set(e.driverFormData,"pid",t)},expression:"driverFormData.pid"}},e._l(e.deliveryUser,(function(e){return t("el-option",{key:e.deliveryUserId,attrs:{label:e.deliveryUserName,value:e.deliveryUserId}})})),1)],1),t("div",{staticClass:"flex align-items-center margin-bottom-20"},[e.moduleList.yhjg?t("div",{staticClass:"flex align-items-center"},[t("span",{staticClass:"margin-right-20 text-align-right",staticStyle:{width:"100px"}},[e._v("设置客户优惠")]),t("el-switch",{model:{value:e.driverFormData.yh,callback:function(t){e.$set(e.driverFormData,"yh",t)},expression:"driverFormData.yh"}})],1):e._e(),t("div",{staticClass:"color-red padding-left-20",staticStyle:{"line-height":"20px"}},[e._v("\n              客户优惠设置开关，由水站针对每个送水员设置，开关关闭后，送水员不可见该功能；开关打开后送水员可以给客户优惠价格设置，水站有权对送水员的设置进行调整。\n            ")])]),t("div",{staticClass:"flex align-items-center margin-bottom-20"},[e.moduleList.yf?t("div",{staticClass:"flex align-items-center"},[t("span",{staticClass:"margin-right-20 text-align-right",staticStyle:{width:"100px"}},[e._v("月结付款")]),t("el-switch",{model:{value:e.driverFormData.yf,callback:function(t){e.$set(e.driverFormData,"yf",t)},expression:"driverFormData.yf"}})],1):e._e(),t("div",{staticClass:"color-red padding-left-20",staticStyle:{"line-height":"20px"}},[e._v("\n              月结付款开关，由水站针对每个送水员设置，水站关闭后，送水员不可见该功能。开关打开后，送水员可以给客户设置月结付款的订单支付方式，水站有权对送水员的设置进行调整。\n            ")])]),t("div",{staticClass:"flex align-items-center margin-bottom-20"},[t("div",{staticClass:"flex align-items-center"},[t("span",{staticClass:"margin-right-20 text-align-right",staticStyle:{width:"100px"}},[e._v("共享客户")]),t("el-switch",{model:{value:e.driverFormData.gxkh,callback:function(t){e.$set(e.driverFormData,"gxkh",t)},expression:"driverFormData.gxkh"}})],1),t("div",{staticClass:"color-red padding-left-20",staticStyle:{"line-height":"20px"}},[e._v("\n              打开共享客户开关，即可与送水员共享水站的客户信息。\n            ")])]),t("div",{staticClass:"flex align-items-center margin-bottom-20"},[t("div",{staticClass:"flex align-items-center"},[t("span",{staticClass:"margin-right-20 text-align-right",staticStyle:{width:"100px"}},[e._v("银行转账")]),t("el-switch",{model:{value:e.driverFormData.yhzz,callback:function(t){e.$set(e.driverFormData,"yhzz",t)},expression:"driverFormData.yhzz"}})],1),t("div",{staticClass:"color-red padding-left-20",staticStyle:{"line-height":"20px"}},[e._v("\n              银行转账开关，有水站针对每个送水员设置。开关关闭后，送水员不可见该功能；开关打开后，送水员可以给客户设置银行转账的订单支付方式，水站有权对送水员的设置进行调整。\n            ")])]),t("el-form-item",{attrs:{label:"授权管理骑手权限"}},[t("el-switch",{model:{value:e.driverFormData.canchild,callback:function(t){e.$set(e.driverFormData,"canchild",t)},expression:"driverFormData.canchild"}})],1),t("el-form-item",{attrs:{label:"管理骑手"}},[t("el-select",{attrs:{placeholder:"请选择管理骑手",filterable:"",clearable:""},model:{value:e.driverFormData.parentid,callback:function(t){e.$set(e.driverFormData,"parentid",t)},expression:"driverFormData.parentid"}},e._l(e.deliveryUserCanChild,(function(e){return t("el-option",{key:e.deliveryUserId,attrs:{label:e.deliveryUserName,value:e.deliveryUserId}})})),1)],1),t("div",{staticClass:"flex align-items-center margin-bottom-20"},[t("div",{staticClass:"flex align-items-center"},[t("span",{staticClass:"margin-right-20 text-align-right",staticStyle:{width:"100px"}},[e._v("老客户自动接单")]),t("el-switch",{model:{value:e.driverFormData.isauto,callback:function(t){e.$set(e.driverFormData,"isauto",t)},expression:"driverFormData.isauto"}})],1),t("div",{staticClass:"color-red padding-left-20",staticStyle:{"line-height":"20px"}},[e._v("\n              开启后，系统将自动为该送水员分配其历史服务过的客户的新订单。\n            ")])]),e.driverFormData.isauto?t("el-form-item",{attrs:{label:"老客户截止时间"}},[t("el-date-picker",{attrs:{type:"datetime",placeholder:"选择截止时间","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.driverFormData.dateline,callback:function(t){e.$set(e.driverFormData,"dateline",t)},expression:"driverFormData.dateline"}}),t("span",{staticClass:"color-red margin-left-10"},[e._v("不设置则不限制时间")])],1):e._e()],1)],1),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.addDriverDialog=!1}}},[e._v("取 消")]),t("el-button",{attrs:{type:"primary",loading:e.driverSureLoading,disabled:e.driverSureLoading},on:{click:e.driverSure}},[e._v("确\n          定")])],1)]),t("el-dialog",{attrs:{title:e.userCheckData.title,visible:e.userCheckDialog,width:"50%"},on:{"update:visible":function(t){e.userCheckDialog=t}}},[t("div",[t("div",{staticClass:"flex align-items-center justify-content-between"},[t("div",[t("el-input",{staticStyle:{width:"240px","margin-right":"20px"},attrs:{placeholder:"输入客户姓名和手机号筛选"},model:{value:e.fenxiaoForm.name,callback:function(t){e.$set(e.fenxiaoForm,"name",t)},expression:"fenxiaoForm.name"}}),t("el-button",{attrs:{type:"primary"},on:{click:e.searchFenxiaoResult}},[e._v("查询")])],1)]),t("el-table",{staticStyle:{width:"100%"},attrs:{data:e.userCheckData.list,"header-cell-style":{"text-align":"center"},"cell-style":{"text-align":"center","font-size":"13px",color:"#333C48"},"max-height":e.inTableHeight}},[t("el-table-column",{attrs:{prop:"name",label:"名字"}}),t("el-table-column",{attrs:{prop:"phone",label:"手机号"}}),t("el-table-column",{attrs:{label:"操作"},scopedSlots:e._u([{key:"default",fn:function(a){return[1==a.row.state?t("div",[t("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary"},on:{click:function(t){return e.storeCheckuser({id:a.row.userId,flag:"1"})}}},[e._v("通过")]),t("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"danger"},on:{click:function(t){return e.storeCheckuser({id:a.row.userId,flag:"0"})}}},[e._v("拒绝")])],1):e._e(),3==a.row.state?t("div",[t("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"warning"},on:{click:function(t){return e.goChoosePeople(a.row.userId)}}},[e._v("绑定")])],1):e._e(),2==a.row.state?t("div",[t("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"danger"},on:{click:function(t){return e.storeCheckuser({id:a.row.userId,flag:"2"})}}},[e._v("解绑")])],1):e._e()]}}])})],1)],1),t("el-dialog",{attrs:{title:"提示",visible:e.formalDriverDialog,"append-to-body":"",width:"50%"},on:{"update:visible":function(t){e.formalDriverDialog=t}}},[t("div",[t("el-table",{staticStyle:{width:"100%"},attrs:{data:e.formalDriverList,"header-cell-style":{"text-align":"center"},"cell-style":{"text-align":"center","font-size":"13px",color:"#333C48"},"max-height":e.inTableHeight}},[t("el-table-column",{attrs:{label:"",width:"55",align:"center"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-radio",{attrs:{label:a.row},model:{value:e.thisFormalDriver,callback:function(t){e.thisFormalDriver=t},expression:"thisFormalDriver"}},[e._v(" ")])]}}])}),t("el-table-column",{staticStyle:{height:"500rpx"},attrs:{prop:"imgUri",label:"头像",width:"100"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("img",{staticStyle:{width:"75px",height:"75px"},attrs:{src:a.row.img?a.row.img:e.imgUri+"/images/logo.png",alt:""}})]}}])}),t("el-table-column",{attrs:{prop:"name",label:"姓名"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                "+e._s(t.row.name?t.row.name:"派送员")+"\n              ")]}}])}),t("el-table-column",{attrs:{prop:"phone",label:"联系方式"}}),t("el-table-column",{attrs:{prop:"",label:"身份状态"},scopedSlots:e._u([{key:"default",fn:function(a){return[1==a.row.shortTime?t("el-tag",{attrs:{type:"success"}},[e._v("固定")]):t("el-tag",{attrs:{type:"warning"}},[e._v("临时")])]}}])}),t("el-table-column",{attrs:{prop:"",label:"配送状态"},scopedSlots:e._u([{key:"default",fn:function(a){return[1==a.row.delectState?t("span",{staticClass:"color-grey"},[e._v("离职")]):1==a.row.delivery?t("span",{staticClass:"color-red"},[e._v("配送中...")]):t("span",{staticClass:"color-green"},[e._v("空闲")])]}}])}),t("el-table-column",{attrs:{prop:"",label:"派送中"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                "+e._s(t.row.currentTaskSum)+"单\n              ")]}}])}),t("el-table-column",{attrs:{prop:"",label:"已完成"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                "+e._s(t.row.total)+"单\n              ")]}}])})],1)],1),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.formalDriverDialog=!1}}},[e._v("取 消")]),t("el-button",{attrs:{type:"primary"},on:{click:e.formalDriverSubmit}},[e._v("确 定")])],1)]),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.userCheckDialog=!1}}},[e._v("关 闭")])],1)],1),t("el-dialog",{attrs:{title:"申请扣除理由",visible:e.takeOffDialog,width:"30%"},on:{"update:visible":function(t){e.takeOffDialog=t}}},[t("el-form",{ref:"takeOffDataElement",attrs:{model:e.takeOffData,"label-width":"80px"}},[t("el-form-item",{attrs:{label:"扣除理由"}},[t("el-input",{attrs:{type:"textarea",placeholder:"请输入申请扣除理由"},model:{value:e.takeOffData.reason,callback:function(t){e.$set(e.takeOffData,"reason",t)},expression:"takeOffData.reason"}})],1)],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.takeOffDialog=!1}}},[e._v("取 消")]),t("el-button",{attrs:{type:"primary"},on:{click:e.takeOffSubmit}},[e._v("确 定")])],1)],1),t("el-dialog",{attrs:{title:"",visible:e.tcDialog,width:"30%"},on:{"update:visible":function(t){e.tcDialog=t}}},[t("div",[t("div",{staticClass:"text-align-center font-size-30 padding-tb-20 border-bottom"},[e._v("结算提成")]),t("div",{staticClass:"text-align-center font-size-16 padding-tb-10 margin-top-20",staticStyle:{"background-color":"#F5F5F5"}},[e._v("结算详情\n        ")]),t("div",{staticClass:"flex align-items-center justify-content-center",staticStyle:{height:"200px"}},[t("div",[t("div",[e._v("送水员【"+e._s(e.driverMoneyData.userInfo.deliveryName)+"】配送提成合计：")]),t("div",{staticClass:"margin-top-20"},[t("span",{staticStyle:{"font-size":"40px"}},[e._v(e._s(e.driverMoneyData.countMoney))]),e._v("元")])])])]),t("div",{staticClass:"dialog-footer text-align-center",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.tcDialog=!1}}},[e._v("取消")]),t("el-button",{staticStyle:{"margin-left":"30px"},attrs:{type:"primary"},on:{click:e.countDriverMoney}},[e._v("确定结算")])],1)]),t("com-online-dialog",{attrs:{comDialogTitle:e.comonlineDialogTit,comDialogVisible:e.comonlineDialogvisible,comDialogData:e.comonlineDialogData},on:{"close-com-online-dialog":e.closeonlineComDialog}}),t("com-dialog",{attrs:{comDialogTitle:e.comDialogTit,comDialogVisible:e.comDialogvisible,comDialogData:e.comDialogData},on:{"close-com-dialog":e.closeComDialog}}),e.driverGeofenceVisible?t("driver-geofence",{ref:"driverGeofence",on:{refreshDataList:e.load}}):e._e()],1)},i=[function(){var e=this,t=e._self._c;return t("div",{staticClass:"flex align-items-center font-size-14 color-red margin-top-10"},[t("div",{staticStyle:{"line-height":"24px"}},[e._v("\n          1、客户优惠设置开关，由水站针对每个送水员设置，开关关闭后，送水员不可见该功能；开关打开后，送水员可以给客户进行优惠价格设置，水站有权对送水员设置的价格调整。\n          2、打开共享客户开关，即可与该送水员共享水站的客户信息。\n          3、月结付款开关和银行转账开关，由水站针对每个送水员设置，开关关闭后送水员不可见该功能；开关打开后，送水员可以给客户设置月结付款和银行转账的订单支付方式。\n        ")])])}],l=r("91ce"),n=r("bbe6"),s=r("bd82"),d={components:{comOnlineDialog:l["a"],comDialog:n["a"],driverGeofence:s["default"]},props:{},data:function(){var e=this,t=function(t,a,r){if(a){var o=e.$util.checkPhone(a);o?r():r(new Error("请输入有效的手机号码"))}else r()};return{driverGeofenceVisible:!1,deliveryUserCanChild:[],fenxiaoForm:{name:"",fenxiao:1,userId:""},deliveryUser:[],comonlineDialogvisible:!1,comonlineDialogTit:["订单详情","买家信息","商品信息","押桶信息","订单信息","提成信息","配送信息"],comonlineDialogData:{},comDialogvisible:!1,comDialogTit:["订单详情","买家信息","商品信息","押桶信息","订单信息","提成信息","配送信息"],comDialogData:{},imgUri:this.$imgUri,pageFlag:0,driverList:[],driverIdState:[{name:"临时",value:0},{name:"固定",value:1}],driverSendState:[{name:"空闲",value:0},{name:"配送中...",value:1}],driverSelect:{idState:"",sendState:"",isKey:""},autoSend:!1,addDriverDialog:!1,driverFormData:{name:"",phone:"",idCard:"",pid:"",userId:0,state:1,jobState:0,yh:!1,yhId:0,yf:!1,yfId:0,gxkh:!1,gxkhId:0,yhzz:!1,yhzzId:0,xgdd:!1,xgddId:0,canchild:!1,parentid:"",isauto:!1,dateline:"",remarks:""},driverFormDataRules:{name:[{required:!0,message:"请输入姓名",trigger:"blur"}],phone:[{validator:t,trigger:"change"},{required:!0,message:"手机号码不能为空",trigger:"blur"}]},jobStateList:[{value:0,label:"在职"},{value:1,label:"离职"}],driverSureLoading:!1,driverDetail:{userId:"",list:[],info:"",state:0,numList:[],tabs:["全部","派送中","未签收","已签收","已完成"]},takeOffDialog:!1,takeOffData:{id:"",reason:""},tcDialog:!1,driverMoneyData:{list:[],date:"",userId:"",userInfo:"",checkList:[],count:0,tcIndex:0,countMoney:0},userCheckDialog:!1,userCheckData:{userId:0,list:[],title:""},formalDriverDialog:!1,formalDriverList:[],thisFormalDriver:"",currentUserId:"",moduleList:[],isLoading:!1,pagesData1:{pageTotal:0,currentPage:1,currentPageSizes:[10,15,20],pageSize:10},pagesData:{pageTotal:0,currentPage:1,currentPageSizes:[10,15,20],pageSize:10},dateSelect:{disabledDate:function(t){return t.getTime()<new Date(JSON.parse(e.Cookies.get("storeInfo")).startTime).getTime()-864e5||t.getTime()>Date.now()}},parentDriverList:[]}},computed:{tableHeight:function(){console.log(this.$store.getters.getGlobalHeight);var e=Number(this.$store.getters.getGlobalHeight)-300;return e>=300?e:300},inTableHeight:function(){var e=this.$store.getters.getGlobalHeight;return e>=400?parseInt(this.$util.mul(e,.5)):400}},created:function(){},mounted:function(){var e=this;e.moduleList=JSON.parse(this.Cookies.get("moduleList")),console.log(e.moduleList,"that.moduleListthat.moduleListthat.moduleList"),e.load(),e.selectByStoreId(),e.selectdeliverycanchild()},watch:{pageFlag:function(e){0==e&&(this.pagesData1.currentPage=1,this.driverMoneyData.tcIndex=0)}},methods:{setGeofence:function(e){var t=this;this.driverGeofenceVisible=!0,this.$nextTick((function(){t.$refs.driverGeofence.init(e)}))},closeonlineComDialog:function(){this.comonlineDialogvisible=!1},closeComDialog:function(){this.comDialogvisible=!1},changeDeliveryPower:function(e,t,a){var r=this,o=t.switchList,i="",l=e;o.forEach((function(e){a==e.alias&&(i=e.deliveryPowerId)}));var n="szmb/szmsendmembercontroller/updatedeliveryswitch",s={deliveryPowerId:i,state:l};if(!i){n="szmb/szmsendmembercontroller/addDeliverySwitch";var d=new Map([["yh","优惠"],["yf","月付"],["gxkh","共享客户"],["yhzz","银行转账"]]);Object.assign(s,{deliveryUserId:t.id,moduleName:d.get(a),alias:a,storeId:r.Cookies.get("storeId"),state:l})}r.$post(n,s).then((function(e){1==e.code?(r.$message({message:"操作成功！",type:"success"}),r.load()):r.$message.error(e.data)}))},searchDriver:function(){this.pagesData.currentPage=1,this.load()},handleSizeChange:function(e){this.pagesData.pageSize=e,this.load()},handleCurrentChange:function(e){this.pagesData.currentPage=e,this.load()},load:function(){var e=this,t="/szmb/szmsendmembercontroller/selectsendmemberbystoreid",a={storeId:e.Cookies.get("storeId"),state:""===e.driverSelect.idState?-1:e.driverSelect.idState,deliveryState:""===e.driverSelect.sendState?-1:e.driverSelect.sendState,deliveryName:e.driverSelect.isKey,index:e.pagesData.currentPage,pageSize:e.pagesData.pageSize};e.$post(t,a).then((function(t){if(console.log(t),1==t.code){var a=t.data.list;e.pagesData.pageTotal=t.data.total,e.driverList=t.data.list,console.log(a)}else e.pagesData.pageTotal=0,e.driverList=[]}))},clearSearch:function(){var e=this;e.driverSelect={idState:"",sendState:"",isKey:""},e.pagesData.currentPage=1,e.load()},tapTcIndex:function(e){var t=this;t.isLoading||(t.driverMoneyData.tcIndex=e,t.driverMoneyData.date="",t.pagesData1.currentPage=1,t.driverMoneyDetailLoad())},changeAutoSend:function(e){console.log(e);var t=this,a="/szmb/szmbvippricecontroller/updatestoreisseng",r={storeId:t.Cookies.get("storeId"),storeState:e?1:0};t.$post(a,r).then((function(a){console.log(a),1==a.code?t.$message({message:"操作成功!",type:"success"}):(t.autoSend=!e,t.$message.error(a.data))}))},handleClose:function(e){this.$confirm("确认关闭？").then((function(t){e()})).catch((function(e){}))},addDriver:function(){var e=this;e.driverFormData={name:"",phone:"",idCard:"",pid:"",userId:0,state:1,jobState:0,yh:!1,yhId:0,yf:!1,yfId:0,yhzz:!1,yhzzId:0,gxkh:!1,gxkhId:0,canchild:!1,parentid:"",isauto:!1,dateline:"",remarks:""},e.addDriverDialog=!0},driverSure:function(){var e=this;this.$refs.driverFormDataElement.validate((function(t){if(!t)return!1;e.driverSureLoading=!0,setTimeout((function(){e.driverSureLoading=!1}),3e3);var a=e.driverFormData,r="";r=a.userId?"/szmb/szmsendmembercontroller/updatesendmeber":"/szmb/szmsendmembercontroller/insertsendmember";var o={moduleName:"优惠",alias:"yh",state:a.yh?1:0,deliveryPowerId:a.yhId},i={moduleName:"月付",alias:"yf",state:a.yf?1:0,deliveryPowerId:a.yfId},l={moduleName:"共享客户",alias:"gxkh",state:a.gxkh?1:0,deliveryPowerId:a.gxkhId},n={moduleName:"银行转账",alias:"yhzz",state:a.yhzz?1:0,deliveryPowerId:a.yhzzId},s=[];s.push(o,i,l,n);var d={name:a.name,phone:a.phone,idCard:a.idCard,switchList:s,state:a.state,storeId:e.Cookies.get("storeId"),userId:a.userId,delectState:a.jobState,pid:a.pid,header:"json",canchild:a.canchild?1:0,parentid:a.parentid,isauto:a.isauto?1:0,dateline:a.dateline,remarks:a.remarks};e.$post(r,d).then((function(t){console.log(t),1==t.code?(a.userId?e.$message({message:"修改成功",type:"success"}):e.$alert("添加送水员成功。需送水员在微信搜索【水站买送水员端】小程序，使用手机号登录后即可使用","提示",{confirmButtonText:"确定",callback:function(e){}}),e.addDriverDialog=!1,e.driverFormData={name:"",phone:"",idCard:"",pid:"",userId:0,state:1,jobState:0,yh:!1,yhId:0,yf:!1,yfId:0,yhzz:!1,yhzzId:0,gxkh:!1,gxkhId:0,canchild:!1,parentid:"",isauto:!1,dateline:"",remarks:""},e.load()):e.$message.error(t.data)}))}))},updateDeliveryUserState:function(e,t){var a=this;this.$post("/szmb/szmsendmembercontroller/updatestate",{userId:e,state:t}).then((function(e){1==e.code?(a.$message({message:"修改成功",type:"success"}),a.load()):a.$message.error(e.data)}))},editDriver:function(e){var t=this;t.driverDetail.userId=e.id;var a="/szmb/szmsendmembercontroller/selectbystoreanddelivery",r={userId:t.driverDetail.userId,index:1};t.$post(a,r).then((function(a){if(console.log(a),1==a.code){t.driverFormData.name=a.data.userSendMember.name,t.driverFormData.phone=a.data.userSendMember.phone,t.driverFormData.idCard=a.data.userSendMember.identity,t.driverFormData.userId=a.data.userSendMember.id,t.driverFormData.jobState=a.data.userSendMember.delectState,t.driverFormData.pid=a.data.userSendMember.pid,t.driverFormData.isauto=!!a.data.userSendMember.isauto,t.driverFormData.dateline=a.data.userSendMember.dateline;var r=a.data.userSendMember.switchList;r.forEach((function(e){"yh"==e.alias?(t.driverFormData.yh=!!e.state,t.driverFormData.yhId=e.deliveryPowerId):"yf"==e.alias?(t.driverFormData.yf=!!e.state,t.driverFormData.yfId=e.deliveryPowerId):"gxkh"==e.alias?(t.driverFormData.gxkh=!!e.state,t.driverFormData.gxkhId=e.deliveryPowerId):"xgdd"==e.alias?(t.driverFormData.xgdd=!!e.state,t.driverFormData.xgddId=e.deliveryPowerId):"yhzz"==e.alias&&(t.driverFormData.yhzz=!!e.state,t.driverFormData.yhzzId=e.deliveryPowerId)})),t.driverFormData.canchild=1===e.canchild,t.driverFormData.parentid=e.parentid,t.driverFormData.remarks=e.remarks,t.addDriverDialog=!0}else t.$message.error(a.data)}))},deliveryOrders:function(e){console.log("查看待配送订单需要开发")},deliveryDelete:function(e){var t=this,a=this,r="/szmb/szmsendmembercontroller/delete",o={userId:e.id};a.$post(r,o).then((function(e){1==e.code?(t.$message({message:"删除成功",type:"success"}),a.load()):a.$message.error(e.msg)}))},selectByStoreId:function(){var e=this,t=this,a="/szmb/deliveryusercontroller/selectByStoreId",r={storeId:t.Cookies.get("storeId")};t.$post(a,r).then((function(a){1==a.code?e.deliveryUser=a.data:t.$message.error(a.msg)}))},selectdeliverycanchild:function(){var e=this,t=this,a="/szmb/szmsendmembercontroller/selectdeliverycanchild",r={storeId:t.Cookies.get("storeId")};t.$post(a,r).then((function(a){1==a.code?e.deliveryUserCanChild=a.data:t.$message.error(a.msg)}))},lookDetail:function(e){var t=this;t.driverDetail.userId=e.id,t.lookDetailLoad()},lookDetailLoad:function(){var e=this,t="/szmb/szmsendmembercontroller/selectdeliverystore",a={storeId:e.Cookies.get("storeId"),userId:e.driverDetail.userId,state:e.driverDetail.state,index:1};e.$post(t,a).then((function(t){console.log(t),e.pageFlag=1,1==t.code?(e.driverDetail.list=t.data.list,e.driverDetail.info=t.data.userSendMember,e.driverDetail.numList=t.data.integers):e.driverDetail.list=[]}))},tabClick:function(e){console.log(e);var t=this;t.driverDetail.state=e,t.lookDetailLoad()},lookOrderDetail:function(e){console.log(e);var t=this,a="/szmb/szmborder/selectorderone",r={orderId:e.orderMainId};t.$post(a,r).then((function(e){console.log(e),1==e.code?(t.comonlineDialogvisible=!0,t.comonlineDialogData=e.data):t.$message.error(e.data.data)}))},lookOrderDetail1:function(e){console.log(e);var t=this,a="/szmb/deliveryordercontroller/selectdeliveryorderdeatil",r={orderNumber:e.orderNumber};t.$post(a,r).then((function(e){console.log(e),1==e.code?(t.comDialogData=e.data,t.comDialogvisible=!0):t.$message.error(e.data.data)}))},toTakeOff:function(e){var t=this;t.takeOffData={id:e,reason:""},t.takeOffDialog=!0},takeOffSubmit:function(){var e=this;if(e.takeOffData.reason){var t="/szmb/storesendpaycontroller/deductsengmoneyall",a={deliveryId:e.takeOffData.id,resone:e.takeOffData.reason};e.$post(t,a).then((function(t){console.log(t),1==t.code?(e.$message({message:"申请扣除成功",type:"success"}),e.lookDetailLoad(),e.takeOffDialog=!1):e.$message.error(t.data)}))}else e.$message.error("请填写申请扣除的理由")},driverMoneyDetail:function(e,t){var a=this;console.log(e),a.driverMoneyData.userId=e,a.driverMoneyDetailLoad(),a.pageFlag=2},clearTc:function(){var e=this;e.driverMoneyData.date="",this.pagesData1.currentPage=1,this.driverMoneyDetailLoad()},handleSizeChange1:function(e){this.pagesData1.pageSize=e,this.driverMoneyDetailLoad()},handleCurrentChange1:function(e){this.pagesData1.currentPage=e,this.driverMoneyDetailLoad()},driverMoneyDetailLoad:function(){var e=this;e.isLoading=!0;var t="/szmb/szmsendmembercontroller/selectdeliverydetail",a="",r="";e.driverMoneyData.date?(a=e.driverMoneyData.date[0],r=e.driverMoneyData.date[1]):(a=JSON.parse(e.Cookies.get("storeInfo")).startTime,r=e.$util.toDate(new Date).noTime);var o={deliveryId:e.driverMoneyData.userId,startTime:a,endTime:r,state:e.driverMoneyData.tcIndex,storeId:e.Cookies.get("storeId"),index:e.pagesData1.currentPage,pageSize:e.pagesData1.pageSize,type:3};e.$post(t,o).then((function(t){if(console.log(t),e.isLoading=!1,1==t.code){var a=t.data.list.map((function(t){return t.sendMoney=e.$util.add(e.$util.add(t.discountMoney,t.numberMoney),t.upcomeMoney).toFixed(2),t}));e.driverMoneyData.userInfo=t.data,e.driverMoneyData.list=a,e.pagesData1.pageTotal=t.data.total}else e.driverMoneyData.list=[],e.pagesData1.pageTotal=0,e.driverMoneyData.userInfo=""}))},searchDriverMoney:function(){var e=this;e.pagesData1.currentPage=1,e.driverMoneyDetailLoad()},summaryMoney:function(e){var t=this,a=e.columns,r=e.data,o=[];return a.forEach((function(e,a){if(0!==a){var i=r.map((function(t){return Number(t[e.property])}));i.every((function(e){return isNaN(e)}))?o[a]="--":i[0]<1e7?(o[a]=i.reduce((function(e,a){var r=Number(a);return isNaN(r)?e:t.$util.add(e,a).toFixed(2)}),0),o[a]="￥"+o[a]):o[a]="--"}else o[a]="合计"})),o},driverMoneyCheck:function(e){console.log(e);var t=this,a=0;e.forEach((function(e){a=t.$util.add(a,e.seneMoenyOne).toFixed(2)})),console.log(a,"count"),t.driverMoneyData.checkList=e,t.driverMoneyData.count=a},openCountDriverMoney:function(){var e=this,t=e.driverMoneyData.checkList,a=0;t.forEach((function(t){a=e.$util.add(t.seneMoenyOne,a)})),e.driverMoneyData.countMoney=a.toFixed(2),e.tcDialog=!0},countDriverMoney:function(){var e=this,t=e.driverMoneyData.checkList;if(t.length){var a=[],r=1;if(t.forEach((function(e){0==e.state&&(r=0);var t={id:e.id,state:e.sign};a.push(t)})),1!=r){var o="/szmb/deliveryusercontroller/clearmoney",i=a;console.log(i,"o"),e.$post(o,i).then((function(t){console.log(t),1==t.code?(e.$message({message:"结算成功!",type:"success"}),e.tcDialog=!1,e.driverMoneyDetailLoad(),e.load()):e.$message.error(t.data)}))}else e.$message({message:"当前选中的已结算！",type:"warning"})}else e.$message.error("请选择要结算的订单")},OpenUsreCheck:function(e,t){this.fenxiaoForm.userId=e.id,this.fenxiaoForm.fenxiao=t,this.fenxiaoForm.name="",this.userCheckDialog=!0,this.userCheckData={list:[],title:"",userId:""},this.searchFenxiaoResult()},searchFenxiaoResult:function(){var t=this,a="/szmb/deliveryusercontroller/selectbydusergai",r={userId:this.fenxiaoForm.userId,fenxiao:this.fenxiaoForm.fenxiao,name:this.fenxiaoForm.name,pageNo:1,pageSize:1e3};t.$post(a,r).then((function(a){console.log(a),1==a.code?(t.userCheckData.list=a.data.list,t.userCheckData.title=a.data.name,t.userCheckData.userId=e.id):t.$message.error(a.data)}))},storeCheckuser:function(e){var t=this,a=this,r=e.flag,o=e.id,i="/szmb/deliveryusercontroller/checkuser",l={userId:o,state:r};"2"==r?a.$confirm("确定要解绑?","温馨提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){a.$post(i,l).then((function(e){console.log(e),1==e.code?(a.$message({message:e.data,type:"success"}),a.load(),a.userCheckDialog=!1):a.$message.error(e.data)}))})).catch((function(){t.$message({type:"info",message:"已取消删除"})})):a.$post(i,l).then((function(e){if(console.log(e),1==e.code){if("1"==r)return void a.$router.push({name:"usermanage",params:{type:"check",userId:o}});a.$message({message:e.data,type:"success"}),a.load(),a.userCheckDialog=!1}else a.$message.error(e.data)}))},goChoosePeople:function(e){var t=this,a="/szmb/deliveryusercontroller/selectsenddeletestate",r={storeId:t.Cookies.get("storeId"),deliveryUserId:t.userCheckData.userId};t.$post(a,r).then((function(a){console.log(a),1==a.code?(t.formalDriverList=a.data,t.formalDriverDialog=!0,t.currentUserId=e):t.$message.error(a.data)}))},formalDriverSubmit:function(){var e=this;e.thisFormalDriver||e.$message.error("请选择送水员");var t="/szmb/deliveryusercontroller/bingduser",a={userId:e.currentUserId,deliveryUserId:e.thisFormalDriver.id};e.$post(t,a).then((function(t){console.log(t),1==t.code?(e.$message({message:"绑定成功",type:"success"}),e.formalDriverDialog=!1):e.$message.error(t.data)}))}}},c=d,u=(r("ffb7"),r("0c7c")),m=Object(u["a"])(c,o,i,!1,null,"1d7b4d04",null);a["default"]=m.exports},"45a2":function(e,t,a){},"4bda":function(e,t,a){"use strict";a("28fb")},5094:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e._self._c;return t("div",[t("div",{directives:[{name:"show",rawName:"v-show",value:0==e.pageFlag,expression:"pageFlag==0"},{name:"loading",rawName:"v-loading",value:e.orderLoading,expression:"orderLoading"}]},[t("div",{staticClass:"padding-bottom-20 flex align-items-center justify-content-between"},[t("div",{staticClass:"flex align-items-center"},[t("el-input",{staticStyle:{width:"260px"},attrs:{placeholder:"请输入送水员姓名或手机号搜索",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.searchNameItem.apply(null,arguments)}},model:{value:e.searchUserName,callback:function(t){e.searchUserName=t},expression:"searchUserName"}}),t("el-button",{staticStyle:{"margin-left":"20px"},attrs:{icon:"el-icon-search",type:"primary",size:"mini"},on:{click:e.searchNameItem}},[e._v("搜索")]),t("el-button",{staticStyle:{"margin-left":"20px"},on:{click:e.clearSearch}},[e._v("清空筛选条件")])],1),t("div",[t("el-button",{attrs:{icon:"el-icon-download",type:"success",size:"mini"},on:{click:e.exportExcelList}},[e._v("导出数据")])],1)]),t("el-table",{staticStyle:{width:"100%"},attrs:{data:e.dispatchclerklist,"max-height":e.tableHeight+100,"header-cell-style":{"text-align":"center","background-color":"rgba(239,242,247,1)"},"cell-style":{"text-align":"center","font-size":"13px",color:"#333C48"},stripe:"",border:""}},[t("el-table-column",{attrs:{label:"送水员头像",prop:"deliveryUserIcon",width:"100"},scopedSlots:e._u([{key:"default",fn:function(e){return[t("img",{staticStyle:{width:"50px",height:"50px"},attrs:{src:e.row.deliveryUserIcon?e.row.deliveryUserIcon:"https://waterstation.com.cn/szm/szmb/images/deliveryLogo.png"}})]}}])}),t("el-table-column",{attrs:{label:"送水员姓名",prop:"deliveryUserName"}}),t("el-table-column",{attrs:{label:"送水员手机号",prop:"deliveryUserPhone"}}),t("el-table-column",{attrs:{label:"填单客户总数",prop:"map.userNumber"}}),t("el-table-column",{attrs:{label:"订单总数",prop:"map.orderNumber"}}),t("el-table-column",{attrs:{label:"订单总金额",prop:"map.orderMoney"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n          ￥"+e._s(t.row.map.orderMoney)+"\n        ")]}}])}),t("el-table-column",{attrs:{label:"手填押桶总数",prop:"map.redisNumber"}}),t("el-table-column",{attrs:{label:"手填押桶总金额",prop:"map.redisMoney"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n          ￥"+e._s(t.row.map.redisMoney)+"\n        ")]}}])}),t("el-table-column",{attrs:{label:"手填汇总欠桶数",prop:"map.debtNumber"}}),t("el-table-column",{attrs:{label:"操作",width:"250"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-button",{attrs:{size:"mini",type:"text"},on:{click:function(t){return e.lookOrder(a.row)}}},[e._v("查看订单明细")])]}}])})],1),t("div",{staticClass:"flex align-items-center justify-content-center"},[t("el-pagination",{staticStyle:{padding:"15px"},attrs:{layout:"total, prev, pager, next",background:"","current-page":e.currentPage,"page-size":10,total:e.listTotal},on:{"current-change":e.handleCurrentChange}})],1)],1),t("div",{directives:[{name:"show",rawName:"v-show",value:1==e.pageFlag,expression:"pageFlag==1"}]},[t("div",{staticClass:"flex align-items-center justify-content-between border-bottom padding-bottom-10"},[t("div",{staticClass:"flex align-items-center"},[t("el-page-header",{staticClass:"iconfont icon-fanhui",attrs:{content:""},on:{back:e.goBack}}),t("div",{staticClass:"margin-left-20"},[e._v("送水员："),t("span",{staticClass:"margin-left-10"},[e._v(e._s(e.orderManName))]),t("span",{staticClass:"margin-left-20"},[e._v(e._s(e.orderManPhone))])])],1),t("div",{staticClass:"flex align-items-center"},[t("div",{staticStyle:{width:"230px"}},[t("el-select",{staticClass:"select-salesman",attrs:{placeholder:"按订单状态筛选"},model:{value:e.orderSelectData.orderState,callback:function(t){e.$set(e.orderSelectData,"orderState",t)},expression:"orderSelectData.orderState"}},[e._l(e.orderStateList,(function(e,a){return[t("el-option",{key:a,attrs:{label:e.name,value:e.state}})]}))],2)],1),t("div",{staticStyle:{width:"220px"}},[t("el-input",{attrs:{placeholder:"请输入客户姓名或手机号搜索",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.requestOrderMan.apply(null,arguments)}},model:{value:e.orderSelectData.customerName,callback:function(t){e.$set(e.orderSelectData,"customerName",t)},expression:"orderSelectData.customerName"}})],1),t("el-button",{staticStyle:{"margin-left":"20px"},attrs:{icon:"el-icon-search",type:"primary",size:"mini"},on:{click:e.requestOrderMan}},[e._v("搜索")]),t("el-button",{staticStyle:{"margin-left":"20px"},on:{click:e.clearOrderSearch}},[e._v("清空筛选条件")])],1)]),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.orderListLoad,expression:"orderListLoad"}],staticStyle:{width:"100%","margin-top":"20px"},attrs:{data:e.orderList,"max-height":e.tableHeight+100,"header-cell-style":{"text-align":"center","background-color":"rgba(239,242,247,1)"},"cell-style":{"text-align":"center","font-size":"13px",color:"#333C48"},stripe:"",border:""}},[t("el-table-column",{attrs:{label:"客户信息"},scopedSlots:e._u([{key:"default",fn:function(a){return[e._v("\n          "+e._s(a.row.sendOrderDeatil.orderName)+" "),a.row.sendOrderDeatil.orderName&&a.row.sendOrderDeatil.remake?[e._v("|")]:e._e(),e._v(" "+e._s(a.row.sendOrderDeatil.remake)+"\n          "),t("br"),e._v(" "+e._s(a.row.sendOrderDeatil.orderPhone)+"\n        ")]}}])}),t("el-table-column",{attrs:{prop:"sendOrderDeatil.addressAll",label:"客户地址"}}),t("el-table-column",{attrs:{prop:"sendOrderDeatil.orderNumber",label:"订单编号"}}),t("el-table-column",{attrs:{prop:"sendOrderDeatil.prodeuctName",label:"商品信息","min-width":"140"},scopedSlots:e._u([{key:"default",fn:function(a){return e._l(a.row.sendOrderDeatil.prodeuctName,(function(a,r){return t("div",{key:r},[e._v(e._s(a))])}))}}])}),t("el-table-column",{attrs:{prop:"sendOrderDeatil.orderMoney",label:"订单总金额"},scopedSlots:e._u([{key:"default",fn:function(a){return[a.row.sendOrderDeatil.orderMoney?t("span",[e._v("￥"+e._s(a.row.sendOrderDeatil.orderMoney))]):e._e()]}}])}),t("el-table-column",{attrs:{prop:"sendOrderDeatil.sendMoney",label:"配送提成合计"},scopedSlots:e._u([{key:"default",fn:function(a){return[a.row.sendOrderDeatil.sendMoney?t("span",[e._v("￥"+e._s(a.row.sendOrderDeatil.sendMoney))]):e._e()]}}])}),t("el-table-column",{attrs:{prop:"sendOrderDeatil.repay",label:"本次回桶信息"},scopedSlots:e._u([{key:"default",fn:function(a){return e._l(a.row.sendOrderDeatil.repay,(function(a,r){return t("div",{key:r},[e._v(e._s(a.name)+" x "+e._s(a.num))])}))}}])}),t("el-table-column",{attrs:{prop:"sendOrderDeatil.replace",label:"杂牌抵扣信息"},scopedSlots:e._u([{key:"default",fn:function(a){return e._l(a.row.sendOrderDeatil.replace,(function(a,r){return t("div",{key:r},[t("div",[e._v(e._s(a.replaceName)+" "),t("span",[e._v(e._s(a.replaceNumber)+"个")])]),t("div",[t("span",[e._v("抵扣")]),t("span",[t("span",[e._v(e._s(a.r3))]),e._v(e._s(a.buckNumber?a.buckNumber:a.replaceNumber)+"个")])])])}))}}])}),t("el-table-column",{attrs:{prop:"sendOrderDeatil.payType",label:"回桶补差价支付方式丨金额","min-width":"120"},scopedSlots:e._u([{key:"default",fn:function(a){return[a.row.sendOrderDeatil.payType?t("div",[t("span",[e._v(e._s(a.row.sendOrderDeatil.payType))]),e._v("丨"),t("span",[e._v("￥"+e._s(a.row.sendOrderDeatil.money||"0.00"))])]):e._e()]}}])}),t("el-table-column",{attrs:{prop:"sendOrderDeatil.debt",label:"本次押桶信息|桶单价|数量","min-width":"120"},scopedSlots:e._u([{key:"default",fn:function(a){return e._l(a.row.sendOrderDeatil.pledg,(function(a,r){return t("div",{key:r},[e._v(e._s(a.name)+" | ￥"+e._s(a.money)+" | "+e._s(a.num))])}))}}])}),t("el-table-column",{attrs:{prop:"sendOrderDeatil.debt",label:"本订单欠桶信息"},scopedSlots:e._u([{key:"default",fn:function(a){return e._l(a.row.sendOrderDeatil.debt,(function(a,r){return t("div",{key:r},[e._v(e._s(a.name)+" x "+e._s(a.num))])}))}}])}),t("el-table-column",{attrs:{label:"备注",prop:"sendOrderDeatil.orderRemark"}}),t("el-table-column",{attrs:{label:"订单状态"},scopedSlots:e._u([{key:"default",fn:function(a){return[e._v("\n          "+e._s(a.row.sendOrderDeatil.orderStatus)+"\n          "),t("br"),e._v("\n          "+e._s(a.row.sendOrderDeatil.storeStatus)+"\n        ")]}}])}),t("el-table-column",{attrs:{label:"操作"},scopedSlots:e._u([{key:"default",fn:function(a){return[1==a.row.sendOrderDeatil.operation?[t("el-button",{attrs:{type:"text"},on:{click:function(t){return e.lookOrderDetail(a.row)}}},[e._v("查看详情")]),t("br"),t("el-button",{attrs:{type:"text"},on:{click:function(t){return e.getMoneybtn(a.row)}}},[e._v("确认收款")])]:[t("el-button",{attrs:{type:"text"},on:{click:function(t){return e.lookOrderDetail(a.row)}}},[e._v("查看详情")])]]}}])})],1),t("div",{staticClass:"flex align-items-center justify-content-center"},[t("el-pagination",{staticStyle:{padding:"15px"},attrs:{layout:"total, prev, pager, next",background:"","current-page":e.currentPage2,"page-size":10,total:e.listTotal2},on:{"current-change":e.handleCurrentChange2}})],1)],1),t("com-dialog",{attrs:{comDialogTitle:e.comDialogTit,comDialogVisible:e.comDialogvisible,comDialogData:e.comDialogData},on:{"close-com-dialog":e.closeComDialog}})],1)},o=[],i=a("bbe6"),l={components:{comDialog:i["a"]},props:{},data:function(){return{comDialogvisible:!1,comDialogTit:["订单详情","买家信息","商品信息","押桶信息","订单信息","提成信息","配送信息"],comDialogData:{},imgUri:this.$imgUri,orderLoading:!1,dispatchclerklist:[],listTotal:0,currentPage:1,pageSize:10,isSearch:!1,pageFlag:0,orderManId:"",orderManName:"",orderManPhone:"",orderList:[],orderListLoad:!0,listTotal2:0,currentPage2:1,orderSelectData:{orderState:"",customerName:""},searchUserName:"",orderStateList:[{name:"全部",state:-1},{name:"待签收",state:0},{name:"已签收",state:1},{name:"已完成",state:2},{name:"退货",state:3}]}},computed:{tableHeight:function(){console.log(this.$store.getters.getGlobalHeight);var e=Number(this.$store.getters.getGlobalHeight)-300;return e>=300?e:300},inTableHeight:function(){var e=this.$store.getters.getGlobalHeight;return e>=400?parseInt(this.$util.mul(e,.5)):400}},created:function(){},mounted:function(e){var t=this;if(this.loadList(),"msg"==t.$route.params.type){var a={deliveryUserId:this.$route.params.userId,deliveryUserName:this.$route.params.name,deliveryUserPhone:this.$route.params.callNum};12==this.$route.params.source&&t.lookOrder(a)}},watch:{searchUserName:function(e){e||this.loadList()}},methods:{closeComDialog:function(){this.comDialogvisible=!1},clearSearch:function(){var e=this;e.currentPage=1,e.searchUserName="",this.loadList()},clearOrderSearch:function(){var e=this;e.orderSelectData={orderState:"",customerName:""},e.requestOrderMan()},exportExcelList:function(){var e=this,t=this;t.$post("/szmb/deliveryordercontroller/insertdeliveryorderexc",{storeId:t.Cookies.get("storeId"),userName:t.searchUserName}).then((function(t){1===t.code?(console.log("导表",t.data),window.location.href=t.data):(console.log(t.msg),e.$message.error(t.data))})).catch((function(e){console.log(e)}))},searchNameItem:function(){this.isSearch=!0,this.loadList()},handleCurrentChange:function(e){this.currentPage=e,this.loadList()},loadList:function(){var e=this;e.orderLoading=!0;var t="/szmb/deliveryordercontroller/selectdeliverylistbystoreid",a={storeId:e.Cookies.get("storeId"),userName:e.searchUserName,pageSize:10,index:e.currentPage};e.$post(t,a).then((function(t){1===t.code?(e.orderLoading=!1,e.dispatchclerklist=t.data.list,e.listTotal=t.data.total):(e.orderLoading=!1,e.$message.error("暂无数据"))}))},goBack:function(){var e=this;e.pageFlag=0,this.searchUserName=this.isSearch?this.searchUserName:null,e.loadList()},lookOrder:function(e){var t=this;t.pageFlag=1,t.orderManId=e.deliveryUserId,t.orderManName=e.deliveryUserName,t.orderManPhone=e.deliveryUserPhone,t.clearOrderSearch()},handleCurrentChange2:function(e){this.currentPage2=e,this.requestOrderMan()},requestOrderMan:function(){var e=this,t=this,a="/szmb/deliveryordercontroller/selectdeliveryorderlist";t.orderListLoad=!0;var r={deliveryId:t.orderManId,index:t.currentPage2,userName:t.orderSelectData.customerName,status:""==t.orderSelectData.orderState.toString()?-1:t.orderSelectData.orderState,pageSize:t.pageSize};t.$post(a,r).then((function(a){console.log("请求成功",a),t.orderListLoad=!1,1===a.code?(t.orderList=a.data.list,t.listTotal2=a.data.total):0===a.code?t.$message({type:"warning",message:a.data}):(console.log(a.msg),e.$message.error(a.data))})).catch((function(e){console.log(e),t.orderListLoad=!1}))},lookOrderDetail:function(e){var t=this,a=this,r="/szmb/deliveryordercontroller/selectdeliveryorderdeatil";a.$post(r,{orderNumber:e.sendOrderDeatil.orderNumber}).then((function(e){console.log("请求成功",e),1===e.code?(a.comDialogvisible=!0,a.comDialogData=e.data):0===e.code?a.$message({type:"warning",message:e.data}):(console.log(e.msg),t.$message.error(e.data))})).catch((function(e){console.log(e)}))},getMoneybtn:function(e){var t=this,a=this;console.log(e.sendOrderDeatil.orderNumber);var r="/szmb/deliveryordercontroller/updateDelState";a.$post(r,{orderNumber:e.sendOrderDeatil.orderNumber}).then((function(e){1===e.code?(a.$message({type:"success",message:"确认收款成功"}),a.requestOrderMan()):0===e.code?a.$message({type:"warning",message:e.data}):(console.log(e.msg),t.$message.error(e.data))})).catch((function(e){console.log(e)}))}},filters:{}},n=l,s=(a("4bda"),a("0c7c")),d=Object(s["a"])(n,r,o,!1,null,"1ae27c2b",null);t["default"]=d.exports},ffb7:function(e,t,a){"use strict";a("45a2")}}]);