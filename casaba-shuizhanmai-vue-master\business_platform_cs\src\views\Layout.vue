<template>
  <div id="home">
    <el-container>
      <!-- header -->
      <el-header style="height:64px">
        <div class="head-box clearfix">
          <div class="logo-icon">
            <img src="../assets/imgs/logo.png" alt="logo" style="width:40px;height:40px;" />
            <h3>水站管理系统</h3>
          </div>
          <div class="collapse-box" @click.prevent="collapseOff">
            <i :class="isCollapse ? 'el-icon-s-unfold' : 'el-icon-s-fold'"></i>
          </div>

          <div style="margin-left:250px;height:100%" class="flex align-items-center justify-content-between">
            <!-- 面包屑 -->
            <div class="header-breadcrumb">
              <!-- <el-breadcrumb separator="/"
                             id="guide-breadcrumb">
                <el-breadcrumb-item :to="item.path"
                                    v-for="(item,index) in levelList"
                                    :key="index">{{item.meta.title}}</el-breadcrumb-item>
              </el-breadcrumb>-->
            </div>

            <div class="user-content">
              <!-- 音频 -->
              <!-- <audio ref="audioElement"
                     src="https://waterstation.com.cn/szm/szmb/images/order.mp3">
              </audio>-->
              <!-- https://waterstation.com.cn/szm/szmb/images/order.mp3 -->
              <!-- <embed ref="embedElement"
                   src="https://waterstation.com.cn/szm/szmb/images/order.mp3"
                   autostart="true"
              hidden="true" />-->
              <!-- 去授权 -->
              <!-- <div class="empower-box" v-if="storeInfo.identity != 1">
                <span>试用 {{ authorTime }} 结束</span>
                <el-button type="text" @click.native="goshouquan">
                  去授权
                  <i class="el-icon-arrow-right el-icon--right"></i>
                </el-button>
              </div> -->
              <div class="empower-box" >
                <daikemobile></daikemobile>
              </div>
              <div class="empower-box" v-if="adminStoreInfo.role1 != 0">
                <wx-account-selector></wx-account-selector>

              </div>
              <!-- 添加消息通知图标和下拉菜单 -->
              <div class="message-notification">
                <el-popover
                  placement="bottom"
                  width="300"
                  trigger="click"
                  popper-class="message-popover"
                  v-model="popoverVisible">
                  <div class="message-header">
                    <span>消息通知</span>
                    <div class="message-controls">
                      <el-tooltip :content="isMuted ? '开启声音' : '静音'" placement="top">
                        <el-button
                          :type="isMuted ? 'warning' : 'info'"
                          @click="toggleMute"
                          size="mini"
                          :icon="isMuted ? 'el-icon-mute' : 'el-icon-bell'"
                          circle>
                        </el-button>
                      </el-tooltip>
                      <el-button type="primary" @click="ignoreAll" size="small">一键忽略</el-button>
                    </div>
                  </div>
                  <div class="message-list" v-if="msgList.length > 0">
                    <div class="message-item" v-for="(item, index) in msgList" :key="index" @click="clickMsg(item)">
                      <div class="message-content">
                        <div class="message-title">
                          <span>{{ item.storeMsgModel }}</span>
                          <span class="message-time">{{ item.createTime }}</span>
                        </div>
                        <div class="message-body">{{ item.content }}</div>
                        <div class="message-footer">
                          <el-button type="danger" size="mini" @click.stop="clickMsg(item, 'ignore')">忽略</el-button>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="no-message" v-else>
                    暂无未读消息
                  </div>
                  <div class="message-icon" slot="reference">
                    <el-badge :value="newsNum > 0 ? newsNum : ''" :max="99" class="message-badge">
                      <i :class="isMuted ? 'el-icon-mute' : 'el-icon-bell'" :style="{ color: isMuted ? '#f56c6c' : '#ffffff' }"></i>
                    </el-badge>
                  </div>
                </el-popover>
              </div>
              <div class="user-head">
                <img :src="adminStoreInfo.logoUrl" alt />
              </div>
              <div class="user-info">
                <span class="el-dropdown-link">{{ adminStoreInfo.user }}</span>
                &nbsp; &nbsp; &nbsp;
                <el-button type="text" style="color:#E6A23C;" @click="userCenter('loginout')">退出登录</el-button>
                <!-- <el-dropdown trigger="click"
                             @command="userCenter"
                             style="z-index:10000">
                  <span class="el-dropdown-link">
                    {{storeInfo.user}}<i class="el-icon-arrow-down el-icon--right"></i>
                  </span>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item>我的资料</el-dropdown-item>
                    <el-dropdown-item>个人中心</el-dropdown-item>
                    <el-dropdown-item>修改密码</el-dropdown-item>
                    <el-dropdown-item divided
                                      command="loginout">退出登录</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>-->
              </div>
            </div>
          </div>
        </div>
      </el-header>

      <!-- contant -->
      <el-container>
        <!-- menu begin -->
        <el-scrollbar>
          <el-menu :default-active="onRouter" :default-openeds="[openIndex]" class="el-menu-vertical-demo"
            :collapse="isCollapse" text-color="white" unique-opened active-text-color="#1693fd" id="guide-menu"
            @select="handleMenuSelect">
            <template v-for="(menu, index) in menuLists">
              <template v-if="menu.children">
                <el-submenu :index="menu.index" :key="index">
                  <template slot="title">
                    <i :class="menu.icon"></i>
                    <span slot="title">
                      <span>{{ menu.title }}</span>
                    </span>
                  </template>
                  <template v-for="(tmenu, index) in menu.children">
                    <template v-if="tmenu.index != 'oldBucketInfo'">
                      <el-menu-item :key="index" :index="tmenu.index">
                        <span>
                          <span>{{ tmenu.title }}</span>
                        </span>
                      </el-menu-item>
                    </template>
                    <template v-if="tmenu.index == 'oldBucketInfo' && storeRegister">
                      <el-menu-item :key="index" :index="tmenu.index">
                        <span>
                          <span>{{ tmenu.title }}</span>
                        </span>
                      </el-menu-item>
                    </template>
                  </template>
                </el-submenu>
              </template>
              <template v-else>
                <el-menu-item :index="menu.index" :key="index">
                  <i :class="menu.icon"></i>
                  <span slot="title">
                    <span>{{ menu.title }}</span>
                  </span>
                </el-menu-item>
              </template>
            </template>
          </el-menu>
        </el-scrollbar>
        <!-- menu end -->
        <!-- main contant begin -->
        <!-- :style="'width:'+widthSet+'px'" -->
        <el-main  id="scrollView">
        <!-- <el-main :style="'width:' + widthSet + 'px'" id="scrollView"> -->
          <div class="layout-scroll">
            <div class="tags-view-box">
              <tagsView id="tagsView" />
            </div>
            <!-- <el-breadcrumb separator="/">
            <el-breadcrumb-item :to="item.path"
                                v-for="(item,index) in levelList"
                                :key="index">{{item.meta.title}}</el-breadcrumb-item>
            </el-breadcrumb>-->
            <div class="main-content">
              <transition name="fade-transform" mode="out-in">
                <router-view id="classLayout"></router-view>
              </transition>
            </div>
          </div>
        </el-main>
        <!-- main contant end -->
      </el-container>

      <el-tooltip placement="top" content="回到顶部">
        <backToTop :custom-style="myBackToTopStyle" :visibility-height="300" :back-position="0" :isScrollH="isScrollH"
          transition-name="fade" />
      </el-tooltip>
    </el-container>
    <!-- 授权 -->
    <el-dialog title="授权" :visible.sync="chargeMoney" :close-on-click-modal="false" :close-on-press-escape="false"
      :show-close="false" width="30%">
      <el-dialog title="请扫码支付" :visible.sync="EpowerinnerVisible" width="30%" append-to-body>
        <div class="cont-cont-code">
          <el-form label-width="120px" label-position="left">
            <el-form-item label="付款二维码：">
              <img :src="'data:image/png;base64,' + codeImg" style="width:80%;" />
              <!-- <img src="https://ss1.bdstatic.com/70cFuXSh_Q1YnxGkpoWK1HF6hhy/it/u=3498488437,1426150760&fm=26&gp=0.jpg"
                 alt=""
              style="width:80%;">-->
            </el-form-item>
            <el-form-item label="当前支付状态：">
              <strong>{{ payState | orderState }}</strong>
            </el-form-item>
          </el-form>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="EpowerinnerVisible = false">取 消</el-button>
        </span>
      </el-dialog>
      <div>
        <el-collapse-transition>
          <div>
            <div class="transition-box transition-box2">
              <div class="flex justify-content-between">
                <h5>入驻平台</h5>
                <span>充值金额{{ allEpwerList[0].szmCMoneyPrice }}元</span>
              </div>
              <div class="info-text">
                <span style="font-size:16px;line-height:25px;">
                  {{ allEpwerList[0].content }}
                </span>
                <br />
                <div style="line-height:26px">
                  充值后免费赠送<br />
                  300张水站店铺专属二维码<br />
                  10张店铺专属小海报
                </div>
              </div>
              <div class="buy-price-btn">
                <span>
                  <span>￥</span>
                  <span>{{ allEpwerList[0].szmCMoneyPrice }}</span>
                </span>
                <el-dropdown trigger="click">
                  <el-button round>
                    立即订购
                    <i class="el-icon-arrow-down el-icon--right"></i>
                  </el-button>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item>选择支付方式</el-dropdown-item>
                    <el-dropdown-item divided>
                      <div class="ddd-dddpp" @click="payTypeSelected('wx', allEpwerList[0])">
                        <img src="../assets/icons/zhifu/WX.png" />
                        <span>微信支付</span>
                      </div>
                    </el-dropdown-item>
                    <el-dropdown-item divided>
                      <div class="ddd-dddpp" @click="payTypeSelected('zfb', allEpwerList[0])">
                        <img src="../assets/icons/zhifu/ZFB.png" />
                        <span>支付宝支付</span>
                      </div>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
            </div>

            <div class="transition-box transition-box0">
              <div class="flex justify-content-between">
                <h5>入驻平台</h5>
                <span>充值金额{{ allEpwerList[1].szmCMoneyPrice }}元</span>
              </div>
              <div class="info-text">
                <span style="font-size:16px;line-height:25px;">
                  {{ allEpwerList[1].content }}
                </span>
                <br />
              </div>
              <div class="buy-price-btn">
                <span>
                  <span>￥</span>
                  <span>{{ allEpwerList[1].szmCMoneyPrice }}</span>
                  <span>/月</span>
                </span>
                <el-dropdown trigger="click">
                  <el-button round>
                    立即订购
                    <i class="el-icon-arrow-down el-icon--right"></i>
                  </el-button>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item>选择支付方式</el-dropdown-item>
                    <el-dropdown-item divided>
                      <div class="ddd-dddpp" @click="payTypeSelected('wx', allEpwerList[1])">
                        <img src="../assets/icons/zhifu/WX.png" />
                        <span>微信支付</span>
                      </div>
                    </el-dropdown-item>
                    <el-dropdown-item divided>
                      <div class="ddd-dddpp" @click="payTypeSelected('zfb', allEpwerList[1])">
                        <img src="../assets/icons/zhifu/ZFB.png" />
                        <span>支付宝支付</span>
                      </div>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
            </div>

            <div class="dia-treaty">
              <el-checkbox v-model="treatyChecked"></el-checkbox>
              <div @click="xieyiVisible = true">
                我已阅读并同意《水站买平台服务协议》
              </div>
            </div>

            <div class="color-red padding font-size-14">
              为更好的服务水站，本平台设有30天试用期，经30天试用双方达成一致后方可正式入驻。入驻后选择【方式1】一次性充值780元终身免费享用升级、维护等一站式服务；【方式2】按月支付，每个月支付完成后可享用升级、维护等一站式服务。
            </div>
          </div>
        </el-collapse-transition>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="danger" @click="chargeMoney = false">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog title="提示" :visible.sync="EpowerouterVisible" width="30%" :close-on-click-modal="false"
      :show-close="false">
      <div class="diaolog-box">
        <span class="el-icon-warning" style="color:#e6a23c;font-size:20px">&nbsp;&nbsp;</span>
        <span style="line-height:20px">您的店铺使用期限已到期，为了不影响您的业务，请尽快充值，充值后立即享用！</span>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="gochargeMoney">去充值</el-button>
      </span>
    </el-dialog>

    <el-dialog title="" :visible.sync="xieyiVisible" width="30%" :close-on-click-modal="false" :show-close="false">
      <serveHtml id="serveHtml" />

      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="xieyiVisible = false">关 闭</el-button>
      </span>
    </el-dialog>

    <div style="display:none">
      <audio ref="audioSource1" src="https://waterstation.com.cn/szm/szmb/voice/daiChuLiDingDan.mp3"></audio>
      <audio ref="audioSource2" src="https://waterstation.com.cn/szm/szmb/voice/tuiKuanDingDan.mp3"></audio>
      <audio ref="audioSource3" src="https://waterstation.com.cn/szm/szmb/voice/maiShuiPiao.mp3"></audio>
      <audio ref="audioSource4" src="https://waterstation.com.cn/szm/szmb/voice/tuiTong.mp3"></audio>
      <audio ref="audioSource5" src="https://waterstation.com.cn/szm/szmb/voice/shenQingJieWu.mp3"></audio>
      <audio ref="audioSource6" src="https://waterstation.com.cn/szm/szmb/voice/shenQingHuanWu.mp3"></audio>
      <audio ref="audioSource9" src="https://waterstation.com.cn/szm/szmb/voice/shouYaTongJin.mp3"></audio>
      <audio ref="audioSource10" src="https://waterstation.com.cn/szm/szmb/voice/qingXiFuWu.mp3"></audio>
      <audio ref="audioSource11" src="https://waterstation.com.cn/szm/szmb/voice/jiFenDuiHuan.mp3"></audio>
      <audio ref="audioSource12" src="https://waterstation.com.cn/szm/szmb/voice/daiKeXiaDan.mp3"></audio>
      <audio ref="audioSource13" src="https://waterstation.com.cn/szm/szmb/voice/xianShangHuiTong.mp3"></audio>
      <audio ref="audioSource14" src="https://waterstation.com.cn/szm/szmb/voice/xianXiaHuiTong.mp3"></audio>
      <audio ref="audioSource19" src="https://waterstation.com.cn/szm/szmb/voice/invoicePc.mp3"></audio>
      <audio ref="audioSource20" src="https://waterstation.com.cn/szm/szmb/voice/reminder.mp3"></audio>
    </div>
  </div>
</template>

<script>
// import { eventBus } from "../utils/eventBus.js"
import tagsView from "../components/tagsView"
import backToTop from "../components/backToTop"
import serveHtml from "../components/agreement/serve"
import { dateToTimestamp } from "@/utils/setMethods"
import WxAccountSelector from '@/components/wx-account-selector'
import daikemobile from '@/components/daikemobile'
// import notification from "../components/notification"
export default {
  components: {
    WxAccountSelector,
    tagsView,
    backToTop,
    serveHtml,
    daikemobile
  },
  data() {
    return {
    radioCount: 0,
      imgUrls: this.$imgUri,
      msgList: [],
      isHaveMsg: false,
      newsNum: 10,
      popoverVisible: false,
      isCollapse: false,
      menuLists0: [
        {
          icon: "menu-icon-home",
          index: "home",
          itemUrl: "/home",
          title: "首页"
        },
        {
          icon: "menu-icon-home",
          index: "merchant-management",
          itemUrl: "",
          title: "商家管理",
          children: [
            {
              icon: "",
              index: "store",
              itemUrl: "/store",
              role: [1, 2, 3, 4, 5],
              title: "商家管理"
            },
            {
              icon: "",
              index: "store-geofence",
              itemUrl: "/store-geofence",
              role: [2, 3, 4, 7],
              title: "店铺围栏"
            },
            {
              icon: "",
              index: "waterStationDeliveryStats",
              itemUrl: "/waterStationDeliveryStats",
              title: "水站配送统计"
            },
          ]
        },
        {
          icon: "menu-icon-qbdd",
          index: "platform-orders",
          itemUrl: "",
          title: "订单中心",
          children: [
            {
              icon: "",
              index: "newAnalysisorderMain",
              itemUrl: "/newAnalysisorderMain",
              role: [4, 5],
              title: "第三方平台订单"
            },
            {
              icon: "",
              index: "newAnalysisorderMainall",
              itemUrl: "/newAnalysisorderMainall",
              role: [4],
              title: "全平台订单"
            },
            {
              icon: "",
              index: "newAnalysisorderMainpdd",
              itemUrl: "/newAnalysisorderMainpdd",
              role: [4, 5, 7],
              title: "拼多多订单"
            },
            {
              icon: "",
              index: "newAnalysisorderMaintransform",
              itemUrl: "/newAnalysisorderMaintransform",
              role: [4],
              title: "转单订单"
            }
          ]
        },
        {
          icon: "menu-icon-home",
          index: "lianying",
          itemUrl: "/lianying",
          role: [4],
          title: "联营商品管理"
        },
        {
          icon: "menu-icon-home",
          index: "cdwithdraw",
          itemUrl: "/cdwithdraw",
          role: [4],
          title: "提现申请"
        },
        {
          icon: "menu-icon-home",
          index: "findstore",
          itemUrl: "/findstore",
          role: [4],
          title: "发现页商家审核"
        },
        {
          icon: "menu-icon-qbdd",
          index: "newFinance",
          itemUrl: "",
          title: "财务报表",
          children: [
            {
              icon: "",
              index: "newFinancedelivery",
              itemUrl: "/newFinancedelivery",
              title: "送水员统计"
            },
            {
              icon: "",
              index: "newFinanceorderMain",
              itemUrl: "/newFinanceorderMain",
              title: "订单统计"
            },
            {
              icon: "",
              index: "newFinancebucket",
              itemUrl: "/newFinancebucket",
              title: "押桶退桶统计"
            },
            {
              icon: "",
              index: "newFinancedebt",
              itemUrl: "/newFinancedebt",
              title: "回桶欠桶统计"
            },
            {
              icon: "",
              index: "newFinancewater",
              itemUrl: "/newFinancewater",
              title: "水票统计"
            },
            {
              icon: "",
              index: "newFinancezhangkuan",
              itemUrl: "/newFinancezhangkuan",
              title: "月结账单"
            },
          ]
        },
        {
          icon: "menu-icon-qbdd",
          index: "orderManagement",
          itemUrl: "",
          title: "订单管理",
          children: [
            {
              icon: "",
              index: "orderAdmin",
              itemUrl: "/orderAdmin",
              title: "线上订单"
            },
            // {
            //   icon: "",
            //   index: "selfGoods",
            //   itemUrl: "/selfGoods",
            //   title: "水站代客下单"
            // },
            // {
            //   icon: "",
            //   index: "manualorder",
            //   itemUrl: "/manualorder",
            //   title: "送水员代客下单"
            // },
            // {
            //   icon: "",
            //   index: "cleanService",
            //   itemUrl: "/cleanService",
            //   title: "清洗服务订单"
            // },
            // {
            //   icon: "",
            //   index: "userappraise",
            //   itemUrl: "/userappraise",
            //   title: "客户评价"
            // }
            // {
            //   icon: "",
            //   index: "returnGoods",
            //   itemUrl: "/returnGoods",
            //   title: "退货退款"
            // }
          ]
        },
        {
          icon: "menu-icon-kcpd",
          index: "stockNanage",
          itemUrl: "",
          title: "库存管理",
          children: [
            {
              icon: "menu-icon-jhtz",
              index: "purchasemanage",
              itemUrl: "/purchasemanage",
              title: "进货调整"
            },
            {
              icon: "menu-icon-kcpd",
              index: "stocktest",
              itemUrl: "/stocktest",
              title: "库存盘点"
            },
            {
              icon: "menu-icon-kcpd",
              index: "emptybarrel",
              itemUrl: "/emptybarrel",
              title: "库存统计"
            },
            {
              icon: "menu-icon-kcpd",
              index: "emptybarrelstock",
              itemUrl: "/emptybarrelstock",
              title: "空桶库存"
            }
          ]
        },
        {
          icon: "menu-icon-xtgl",
          index: "bucketAdmin",
          itemUrl: "",
          title: "桶管理",
          children: [
            {
              icon: "",
              index: "setBucketMoney",
              itemUrl: "/setBucketMoney",
              title: "押桶金设置"
            },
            {
              icon: "",
              index: "bucketInfo",
              itemUrl: "/bucketInfo",
              title: "桶信息"
            },
            {
              icon: "",
              index: "oldBucketInfo",
              itemUrl: "/oldBucketInfo",
              title: "老版本桶信息"
            }
          ]
        },
        {
          icon: "menu-icon-khgl",
          index: "waterTicket",
          itemUrl: "",
          title: "水票管理",
          children: [
            {
              icon: "",
              index: "waterTicketSet",
              itemUrl: "/waterTicketSet",
              title: "水票设置"
            },
            {
              icon: "",
              index: "waterTicketAdmin",
              itemUrl: "/waterTicketAdmin",
              title: "水票信息"
            }
          ]
        },
        {
          icon: "menu-icon-cwfx",
          index: "financeAdmin",
          itemUrl: "",
          title: "财务管理",
          children: [
            {
              icon: "",
              index: "totalTurnover",
              itemUrl: "/totalTurnover",
              title: "总营业额"
            },
            {
              icon: "",
              index: "todayTurnover",
              itemUrl: "/todayTurnover",
              title: "今日营业额"
            },
            {
              icon: "",
              index: "cashOut",
              itemUrl: "/cashOut",
              title: "可提现收入"
            },
            {
              icon: "",
              index: "monthlyCustomer",
              itemUrl: "/monthlyCustomer",
              title: "月结客户"
            },
            {
              icon: "",
              index: "bankOrder",
              itemUrl: "/bankOrder",
              title: "银行转账订单"
            },
            {
              icon: "",
              index: "financeReport",
              itemUrl: "/financeReport",
              title: "财务报表"
            },
            {
              icon: "",
              index: "financePay",
              itemUrl: "/financePay",
              title: "财务支出"
            },
            {
              icon: "",
              index: "invoiceAdmin",
              itemUrl: "/invoiceAdmin",
              title: "开票管理"
            }
          ]
        },
        // {
        //   icon: "menu-icon-jfsc",
        //   index: "marketingManagement",
        //   itemUrl: "",
        //   title: "营销管理",
        //   children: [
        //     {
        //       icon: "",
        //       index: "activitySettings",
        //       itemUrl: "/activitySettings",
        //       title: "分享裂变活动"
        //     }, {
        //       icon: "",
        //       index: "waterTicketActivitySettings",
        //       itemUrl: "/waterTicketActivitySettings",
        //       title: "水票裂变活动"
        //     }
        //   ]
        // },
        {
          icon: "menu-icon-khgl",
          index: "customerUser",
          itemUrl: "",
          title: "客户与员工管理",
          children: [
            {
              icon: "",
              index: "usermanage",
              itemUrl: "/usermanage",
              title: "客户管理"
            },
            {
              icon: "",
              index: "driverAdmin",
              itemUrl: "/driverAdmin",
              title: "送水员管理"
            },
            {
              icon: "",
              index: "driver-geofence",
              itemUrl: "/driver-geofence",
              title: "送水员围栏"
            },
            {
              icon: "",
              index: "lssalesman",
              itemUrl: "/lssalesman",
              title: "业务员管理"
            }
          ]
        },
        {
          icon: "menu-icon-spwh",
          index: "goodsManagement",
          itemUrl: "",
          title: "商品管理",
          children: [
            {
              icon: "",
              index: "productLibrary",
              itemUrl: "/productLibrary",
              title: "大众产品库"
            },
            {
              icon: "",
              index: "goodsmanage",
              itemUrl: "/goodsmanage",
              title: "商品维护"
            },
            {
              icon: "",
              index: "classifyAdmin",
              itemUrl: "/classifyAdmin",
              title: "分类名管理"
            },
            {
              icon: "",
              index: "brand",
              itemUrl: "/brand",
              title: "品牌管理"
            },
            {
              icon: "",
              index: "producttype",
              itemUrl: "/producttype",
              title: "抵扣金分类名管理"
            },
          ]
        },
        // {
        //   icon: "menu-icon-jfsc",
        //   index: "jifenShop",
        //   itemUrl: "/jifenShop",
        //   title: "积分商城"
        // },
        {
          icon: "menu-icon-dxgl",
          index: "messageAdmin",
          itemUrl: "/messageAdmin",
          title: "购买短信"
        },
        {
          icon: "menu-icon-xtgl",
          index: "systemManagement",
          itemUrl: "",
          title: "系统管理",
          children: [
            {
              icon: "",
              index: "setAdmin",
              itemUrl: "/setAdmin",
              title: "基础设置"
            },
            {
              icon: "",
              index: "storeadmin",
              itemUrl: "/storeadmin",
              title: "店铺管理员"
            },
            {
              icon: "",
              index: "helpCenter",
              itemUrl: "/helpCenter",
              title: "帮助中心"
            },
            {
              icon: "",
              index: "perfectStore",
              itemUrl: "/perfectStore",
              title: "店铺信息"
            },
            {
              icon: "",
              index: "orderSource",
              itemUrl: "/orderSource",
              title: "订单来源管理"
            },
            {
              icon: "",
              index: "orderSourceConnect",
              itemUrl: "/orderSourceConnect",
              title: "来源关联产品管理"
            },
            {
              icon: "",
              index: "banner",
              itemUrl: "/banner",
              title: "轮播图管理"
            },
            {
              icon: "",
              index: "kepu",
              itemUrl: "/kepu",
              title: "科普管理"
            },
            {
              icon: "",
              index: "region-store-mapping",
              itemUrl: "/region-store-mapping",
              role: [4, 5],
              title: "地区门店映射管理"
            },
            {
              icon: "menu-icon-home",
              index: "mpindex",
              itemUrl: "/mpindex",
              role: [4],
              title: "公众号设置"
            },
          ]
        }
        //  {
        //   icon: "menu-icon-pdy",
        //   index: "driverManage",
        //   itemUrl: "",
        //   title: "送水员管理",
        //   children: [
        //     {
        //       icon: "",
        //       index: "driverAdmin",
        //       itemUrl: "/driverAdmin",
        //       title: "送水员管理"
        //     }

        //   ]
        // },

        // {
        //   icon: "menu-icon-khzt",
        //   index: "selfGoods",
        //   itemUrl: "/selfGoods",
        //   title: "客户自提订单"
        // },

        // {
        //   icon: "menu-icon-khgl",
        //   index: "usermanage",
        //   itemUrl: "/usermanage",
        //   title: "客户管理"
        // },

        // {
        //   icon: "menu-icon-cwfx",
        //   index: "financeAdmin",
        //   itemUrl: "/financeAdmin",
        //   title: "财务分析",
        //   children: [
        //     {
        //       icon: "",
        //       index: "financeAdmin",
        //       itemUrl: "/financeAdmin",
        //       title: "财务管理"
        //     }
        //     // {
        //     //   icon: "",
        //     //   index: "salesdetails",
        //     //   itemUrl: "/finance/shop/salesdetails",
        //     //   title: "销售明细表"
        //     // }
        //   ]
        // },

        /*
         ** Finance Router
         ** Wang
         ** End
         */
        // {
        //   icon: "menu-icon-ach",
        //   index: "finance",
        //   itemUrl: "",
        //   title: "财务报表",
        //   children: [
        //     {
        //       icon: "",
        //       index: "salesdetails",
        //       itemUrl: "/finance/shop/salesdetails",
        //       title: "销售明细表"
        //     }
        //   ]
        // },
        /*
         ** Finance Router
         ** Wang
         ** End
         */
        // {
        //   icon: "menu-icon-xxgl",
        //   index: "messagemanage",
        //   itemUrl: "/messagemanage",
        //   title: "消息管理"
        // }
      ],

      menuLists: [],
      levelList: "", // 面包屑
      screenWidth: "",
      screenHeight: "", // 页面高度
      widthSet: "",
      // 回到顶部
      isScrollH: 0, // 区域滚动
      myBackToTopStyle: {
        right: "20px",
        bottom: "50px",
        width: "40px",
        height: "40px",
        "border-radius": "4px",
        "line-height": "45px", // 请保持与高度一致以垂直居中 Please keep consistent with height to center vertically
        background: "#e7eaf1" // 按钮的背景颜色 The background color of the button
      },
      dialogArr: [],
      newsArr: [], // 未读消息列表 如果该数组里有 则不再弹出消息框
      storeInfo: {},
      adminStoreInfo: {},
      // 授权 begin
      chargeMoney: false,
      EpowerouterVisible: false,
      EpowerinnerVisible: false,
      xieyiVisible: false,
      treatyChecked: false,
      allEpwerList: [{}, {}],
      codeImg: "",
      orderNumber: "",
      payState: "",
      timerId: 1, // 模拟计时器id，唯一性
      timerObj: {}, // 计时器存储器
      authorTimer: {}, // 授权倒计时器
      authorTime: "", // 授权倒计时时间

      // 音频
      autoPlay: true,
      isMuted: false, // 静音状态
      orderTime: "", // 订单轮询定时器名称
      closeFlag: "0", // 0为刷新

      storeRegister: false,

      audioSrc: ""
    }
  },
  computed: {
    onRouter() {
      return this.$route.name
    },
    openIndex() {
      return this.$route.meta.keys
    }
  },
  created() {
    this.getBreadcrumb()
    this.requestAllEpower()
    this.requestNotificationPermission()
  },
  mounted() {
    var that = this
    console.log("version 1.0.0")
    that.$EventBus.$once("recharge", function () {
      that.goEmpower()
    })
    // 重新获取身份状态

    // console.log(that.menuLists0, "moduleList--layout")
    that.storeId = that.Cookies.get("storeId")
    that.adminStoreInfo = JSON.parse(that.Cookies.get("adminStoreInfo"))
    let json = JSON.parse(this.Cookies.get("moduleList"))

    if (json.pdgl != 1) {
      that.menuLists0 = that.$util.arrRemoveJson(
        that.menuLists0,
        "index",
        "driverAdmin"
      )
    }
    if (json.kcpd != 1) {
      that.menuLists0 = that.$util.arrRemoveJson(
        that.menuLists0,
        "index",
        "stocktest"
      )
    }
    let userRole = this.Cookies.get("userRole");
    // 根据权限分配
    that.menuLists0 = that.$util.arrRemoveByRole(
      that.menuLists0,
      that.adminStoreInfo.role1,
      userRole
    )
    // if (json.szjb != 1) {
    //   that.menuLists0 = that.$util.arrRemoveJson(that.menuLists0, "index", "finance")
    // }
    that.menuLists = that.menuLists0
    that.getStoreInfo(function () {
      let identity = that.storeInfo.identity
      if (identity != 1) {
        that
          .$post("/szmb/storeapplyfor/selectbystoreid", {
            storeId: that.Cookies.get("storeId")
          })
          .then((res) => {
            if (res.code == 1) {
              that.authorTime = res.data.tryOut
              if (res.data.tryOut == "已过期") {
                // that.goEmpower()
              }
              that.authorTimer = setInterval(function () {
                that.authorTimeLoad()
              }, 60000)
            }
          })
      } else {
        that.EpowerouterVisible = false
        clearInterval(that.authorTimer)
      }
    })

    // eslint-disable-next-line no-unused-vars
    that.orderTime = setInterval(function () {
      that.getUnReadList()
      // that.tipsLoadEnhanced()
      that.playAudioMsg()
      that.authorTimeLoad()
    }, 20000)
    that.getUnReadList()

    // 初始化静音状态
    that.isMuted = localStorage.getItem('orderNotificationMuted') === 'true'

    window.onresize = () => {
      return (() => {
        window.screenWidth = document.body.clientWidth
        this.screenWidth = window.screenWidth
        that.screenHeight = document.body.clientHeight
      })()
    }

    this.screenWidth = document.body.clientWidth
    that.screenHeight = document.body.clientHeight

    document.getElementById("scrollView").addEventListener(
      "scroll",
      () => {
        this.isScrollH = document.getElementById("scrollView").scrollTop
      },
      false
    )

    // 登录检测 - 使用增强版并添加定时检测
    that.tipsLoadEnhanced()
    // 每30秒检测一次登录状态，与小程序保持一致
    that.loginCheckTimer = setInterval(() => {
      that.tipsLoadEnhanced()
    }, 30000)
    // this.$nextTick(function () {
    //   let kWidth = document.getElementById("classLayout").offsetWidth

    //   console.log("88888888888", kWidth)
    // })
    that.getStoreTime()
  },
  watch: {
    $route() {
      this.getBreadcrumb()
      // console.log(this.$store.getters.getUserToken)
      document.getElementById("scrollView").scrollTop = 0
    },
    screenWidth(val) {
      let that = this
      that.screenWidth = val
      that.isCollapse
        ? (that.widthSet = that.screenWidth - 64)
        : (that.widthSet = that.screenWidth - 220)
    },
    screenHeight(val) {
      if (!this.timer) {
        this.screenWidth = val
        this.timer = true
        let that = this
        setTimeout(function () {
          that.$store.commit("setWindowHeight", that.screenHeight)
          that.timer = false
        }, 400)
      }
    },
    isCollapse(val) {
      let that = this
      that.isCollapse
        ? (that.widthSet = that.screenWidth - 64)
        : (that.widthSet = that.screenWidth - 220)
    },
    EpowerinnerVisible(val) {
      if (!val) {
        this.stopTime()
      }
    }
  },
  methods: {
    getStoreInfo(fun) {
      let that = this
      let isurl = "/szmb/storeapplyfor/selectbyid"
      let o = {
        storeId: that.Cookies.get("storeId")
      }
      that.$post(isurl, o).then((res) => {
        // console.log(res)
        if (res.code == 1) {
          that.Cookies.set("storeInfo", res.data, { expires: that.$expires })
          let list = res.data.jurisdiction
          let json = {}
          list.forEach((item) => {
            if (item.moduleName == "设置优惠价格") {
              json.yhjg = item.state
            }
            if (item.moduleName == "组合套餐") {
              json.zhtc = item.state
            }
            if (item.moduleName == "派单管理") {
              json.pdgl = item.state
            }
            // if (item.moduleName == "自送开关") {
            //   json.zzkg = item.state;
            // }
            if (item.moduleName == "库存盘点") {
              json.kcpd = item.state
            }
            if (item.moduleName == "消费提醒") {
              json.xftx = item.state
            }
            // if (item.moduleName == "客户管理") {
            //   json.khgl = item.state;
            // }
            if (item.moduleName == "收支简表") {
              json.szjb = item.state
            }
            if (item.moduleName == "月付") {
              json.yf = item.state
            }
          })
          that.Cookies.set("moduleList", json, { expires: that.$expires })
          that.storeInfo = res.data
          typeof fun == "function" && fun()
        }
      })
    },
    getStoreTime() {
      let that = this
      let isurl = "/szmb/storeapplyfor/selectbyid"
      let o = {
        storeId: that.Cookies.get("storeId")
      }
      that.$post(isurl, o).then((res) => {
        if (res.code == 1) {
          // 3.0发布时间2020-03-17
          that.storeRegister =
            dateToTimestamp(res.data.storeCreateTime) < 1584403200000
        }
      })
    },
    // 授权 begin
    goEmpower() {
      this.EpowerouterVisible = true
    },
    gochargeMoney() {
      this.chargeMoney = true
      this.requestAllEpower()
    },
    goshouquan() {
      this.chargeMoney = true
      this.requestAllEpower()
    },
    requestAllEpower() {
      var that = this
      this.$post("/szmb/rule/selectstoreidnew", {
        storeId: that.Cookies.get("storeId")
      })
        .then((res) => {
          if (res.code === 1) {
            that.allEpwerList = res.data
          } else if (res.code === 0) {
            that.$message({
              type: "warning",
              message: res.data
            })
          } else {
            this.$message.error(res.data)
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },

    // 选择支付方式
    payTypeSelected(type, odata) {
      var that = this
      if (!that.treatyChecked) {
        that.$message({
          type: "warning",
          message: "请先阅读并同意协议"
        })
        return
      }
      if (type == "wx") {
        // console.log("支付", type, odata)
        let payJson = {
          storeId: that.Cookies.get("storeId"),
          money: odata.szmCMoneyPrice,
          state: odata.delState
        }
        that.wxPaymentApi(payJson)
      } else if (type == "zfb") {
        window.location =
          that.$zfb +
          "/szmb/zfbcallback/zfbrecharge?storeId=" +
          that.Cookies.get("storeId") +
          "&money=" +
          odata.szmCMoneyPrice +
          "&state=" +
          odata.delState
      } else {
        this.$message({
          type: "warning",
          message: "你好，暂不支持支付宝和银联支付，后续会开放，敬请期待"
        })
      }
    },
    // 支付接口  二维码
    wxPaymentApi(dataJson) {
      var that = this
      this.$post("/api/rechargeable/wxPay-B1", dataJson)
        .then((req) => {
          if (req.code === 1) {
            that.codeImg = req.data.qr
            that.orderNumber = req.data.orderNum
            this.EpowerinnerVisible = true
            that.startTraining()
          } else if (req.code === 0) {
            that.$message({
              type: "warning",
              message: req.data
            })
          } else {
            console.log(req.msg)
            this.$message.error(req.data)
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    // 检测 订单状态告
    testOrdersState() {
      var that = this
      this.$post("/api/rechargeable/orderquery", { orderNum: that.orderNumber })
        .then((req) => {
          if (req.code === 1) {
            that.payState = req.data
            if (that.payState == "SUCCESS") {
              that.stopTime()

              setTimeout(function () {
                that.EpowerinnerVisible = false
                that.EpowerouterVisible = false
                that.chargeMoney = false
                that.getStoreInfo()
              }, 2000)
            }
          } else if (req.code === 0) {
            that.$message({
              type: "warning",
              message: req.data
            })
            that.stopTime()
          } else {
            console.log(req.msg)
            this.$message.error(req.data)
            that.stopTime()
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    /*
     * 开始轮训
     * */
    startTraining() {
      let that = this
      const id = this.timerId++
      this.timerObj[id] = true

      async function timerFn() {
        if (!that.timerObj[id]) return
        await that.doSomething()
        setTimeout(timerFn, 3 * 1000)
      }

      timerFn()
    },
    /*
     * 停止轮训
     * */
    stopTime() {
      this.timerObj = {}
    },

    /*
     * 要轮训的代码
     * */
    doSomething() {
      this.testOrdersState()
    },

    // 授权 end
    // 菜单收缩
    collapseOff() {
      this.isCollapse = !this.isCollapse
      // eventBus.$emit("cooll", this.isCollapse)
    },
    // 处理菜单选择事件
    handleMenuSelect(index, indexPath) {
      // 查找对应的菜单项
      let targetUrl = null;
      
      // 先在顶级菜单中查找
      const topLevelMenu = this.menuLists.find(menu => menu.index === index);
      if (topLevelMenu && topLevelMenu.itemUrl) {
        targetUrl = topLevelMenu.itemUrl;
      } else {
        // 在子菜单中查找
        for (const menu of this.menuLists) {
          if (menu.children) {
            const childMenu = menu.children.find(child => child.index === index);
            if (childMenu && childMenu.itemUrl) {
              targetUrl = childMenu.itemUrl;
              break;
            }
          }
        }
      }
      
      // 如果找到了目标URL，进行导航
      if (targetUrl) {
        this.$router.push(targetUrl);
      }
    },
    // 未读消息播放声音
    playAudioMsg() {
      var that = this

      // 如果已静音，直接返回
      if (that.isMuted) {
        return
      }

      let Odata = {
        storeId: that.Cookies.get("storeId"),
        sign: "pc"
      }
      this.$post("/szmb/szmbstorecontroller/voiceremind", Odata)
        .then((req) => {
          if (req.code === 1) {
            if (req.data && req.data.length > 0) {
              if (this.radioCount != req.data.length) {
                let source = req.data[0].source
                if (source == 1) {
                  // 1  待处理订单  您有一个待处理订单，请及时处理
                  that.$refs.audioSource1.play()
                } else if (source == 2) {
                  // 2退款  您有一个退款订单，请及时处理
                  that.$refs.audioSource2.play()
                } else if (source == 3) {
                  // 3 购买水票 客户购买水票，请及时处理
                  that.$refs.audioSource3.play()
                } else if (source == 4) {
                  // 4退桶  客户发起退桶，请及时处理
                  that.$refs.audioSource4.play()
                } else if (source == 5) {
                  // 5借物资 客户申请借物，请及时处理
                  that.$refs.audioSource5.play()
                } else if (source == 6) {
                  // 6 还物资 客户申请还物，请及时处理
                  that.$refs.audioSource6.play()
                } else if (source == 7) {
                  // 7 使用水票
                } else if (source == 8) {
                  // 8还款申请
                } else if (source == 9) {
                  // 9押桶  客户已押桶，请确认收款押桶金
                  that.$refs.audioSource9.play()
                } else if (source == 10) {
                  // 10 清洗消毒服务  您有一个清洗服务订单，请及时处理
                  that.$refs.audioSource10.play()
                } else if (source == 11) {
                  // 11 积分兑换  您有一个积分兑换订单，请及时处理
                  that.$refs.audioSource11.play()
                } else if (source == 12) {
                  // 12手填订单提醒(签收)  您有一个代客下单已签收，请确认
                  that.$refs.audioSource12.play()
                } else if (source == 13) {
                  // 13线上回桶补差价(客户完成支付) 您有一个线上回桶补差价，请查收
                  that.$refs.audioSource13.play()
                } else if (source == 14) {
                  // 14 送水员回桶补差价(提交) 您有一个线下回桶补差价，请查收
                  that.$refs.audioSource14.play()
                } else if (source == 19) {
                  // 14 开票申请语音提醒
                  that.$refs.audioSource19.play()
                } else if (source == 20) {
                // 14 催单发货语音提醒
                that.$refs.audioSource20.play()
                }
                this.radioCount = req.data.length
              }
            }
          } else {
            console.log(req.msg)
            this.$message.error(req.data)
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },

    // 切换静音状态
    toggleMute() {
      this.isMuted = !this.isMuted
      localStorage.setItem('orderNotificationMuted', this.isMuted.toString())

      this.$message({
        type: 'success',
        message: this.isMuted ? '新订单通知已静音' : '新订单通知已开启',
        duration: 2000
      })
    },

    // 未读消息接口
    getUnReadList() {
      let that = this
      let isurl = "/szmb/szmbstorecontroller/pendingitem"
      let o = { storeId: that.storeId }
      that.$post(isurl, o).then((res) => {
        if (res.code == 1) {
          const oldMsgCount = that.msgList.length;
          
          if (res.data.length >= 99) {
            that.newsNum = 99
          } else {
            that.newsNum = res.data.length
          }

          let list = []
          res.data.forEach((x) => {
            if (x.source != 18) {
              list.push(x)
            }
          })
          
          that.msgList = list
          
          // 检查是否有新消息需要发送浏览器通知
          if (that.msgList.length > oldMsgCount && that.msgList.length > 0) {
            const newMsg = that.msgList[0]; // 获取最新的一条消息
            that.showNotification(newMsg.storeMsgModel, newMsg.content);
          }
          
          if (that.msgList.length == 0) {
            that.isHaveMsg = false
          } else {
            that.isHaveMsg = true
          }
        }
      })
    },
    ignoreAll() {
      console.log("一键忽略")
      let that = this
      let isurl = "/szmb/szmbstorecontroller/readpendingitemall"
      let o = { storeId: that.Cookies.get("storeId") }
      that.$post(isurl, o).then((res) => {
        if (res.code == 1) {
          that.getUnReadList()
        }
      })
    },
    clickMsg(e, f) {
      // console.log(e)
      let that = this
      // let newsDom = document.getElementsByClassName("order" + e.id)
      // @click="clickMsg({id:item.storeMsgId,url:item.modelUrl,userId:item.userId})"
      let isurl = "/szmb/szmbstorecontroller/readpendingitem"
      let msgList = that.msgList
      let o = { storeMsgId: e.storeMsgId }
      that.$post(isurl, o).then((res) => {
        console.log(res.data)
        if (res.code == 1) {
          for (let i in msgList) {
            if (msgList[i].storeMsgId == e.storeMsgId) {
              // newsDom[0].style.transform = "translateX(-200px)"
              let a = that.$util.arrRemoveJson(
                msgList,
                "storeMsgId",
                e.storeMsgId
              )
              that.msgList = a
              // newsDom[0].remove()
            }
          }
          if (f != "ignore") {
            // that.$router.push({ name: e.modelUrl, params: { userId: e.userId } })
            if (e.source == 1) {
              that.$router.push({
                name: "about",
                params: {
                  type: "msg",
                  orderId: e.r3,
                  url: e.modelUrl,
                  source: e.source
                }
              })
            } else if (e.source == 2) {
              that.$router.push({
                name: "about",
                params: {
                  type: "msg",
                  retreatId: e.r2,
                  url: e.modelUrl,
                  source: e.source
                }
              })
            } else if (e.source == 12) {
              that.$router.push({
                name: "about",
                params: {
                  userId: e.userId,
                  url: e.modelUrl,
                  source: e.source,
                  name: e.r3,
                  callNum: e.r4
                }
              })
            } else if (e.source == 20) {
              that.$router.push({
                name: "about",
                params: {
                  type: e.r3,
                  url: e.modelUrl,
                  source: e.source,
                  state: e.r4
                }
              })
            } else {
              that.$router.push({
                name: "about",
                params: { userId: e.userId, url: e.modelUrl, source: e.source }
              })
            }
          }

          that.getUnReadList()
        } else {
        }
      })
    },
    // clickMsg (e) {
    //   let newsDom = document.getElementsByClassName("order" + e.target.dataset.id)
    //   console.log(newsDom[0])
    //   console.log(Number(Number(e.target.dataset.index) + 1))
    //   let that = this
    //   let isurl = "/szmb/szmbstorecontroller/readpendingitem"
    //   let o = { "storeMsgId": e.target.dataset.id }
    //   that.$post(isurl, o).then(res => {
    //     console.log(res.data)
    //     if (res.code == 1) {
    //       notification.close("notification_" + Number(Number(e.target.dataset.index) + 1), newsDom[0].offsetHeight, function () {
    //         newsDom[0].remove()
    //       })
    //       that.$router.push({ name: e.target.dataset.url, params: { userId: e.target.dataset.userid } })
    //       that.getUnReadList()
    //     } else {
    //     }
    //   })
    // },
    // 修改已读未读状态 跳转页面
    updateAndTarget(msgId, url, obj) {
      let that = this
      let isurl = "/szmb/szmbstorecontroller/readpendingitem"
      let o = { storeMsgId: msgId }
      that.$post(isurl, o).then((res) => {
        // console.log(res.data)
        if (res.code == 1) {
          that.$router.push({ name: url, params: {} })
          setTimeout(function () {
            obj.remove()
            that.getUnReadList()
          }, 500)
        }
      })
    },

    getBreadcrumb() {
      // 面包屑路径跟踪
      let matched = this.$route.matched.filter((item) => item.name)
      matched.splice(0, 1)
      // const first = matched[0]
      // if (first && first.name.trim().toLocaleLowerCase() !== "home".toLocaleLowerCase() && first.name.trim().toLocaleLowerCase() !== "permission".toLocaleLowerCase()) {
      //   matched = [{ path: "", meta: { title: first.meta.parentTitle } }].concat(matched)
      // }
      this.levelList = matched
      // console.log(this.levelList)
    },
    closrMsg() {
      let msgAll = document.getElementsByClassName("tip")
      // console.log(msgAll)
      for (let i in msgAll) {
        setTimeout(function () {
          msgAll[i].style.transform = "translateX(400px)"
        }, i * 50)
      }
    },
    userCenter(comm) {
      // 用户中心事件
      let that = this
      if (comm === "loginout") {
        this.$confirm("此操作将退出登录, 是否继续?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
          center: true
        })
          .then(() => {
            let isurl = "/szmb/szmbstorecontroller/exit"
            let o = {
              storeId: that.Cookies.get("storeId"),
              sign: "pc"
            }
            that.$post(isurl, o).then((res) => {
              // console.log(res)
              if (res.code == 1) {
                that.closrMsg()
                that.ClearAllCookie()
                clearInterval(that.orderTime)
                clearInterval(that.loginCheckTimer) // 清除登录检测定时器
                setTimeout(function () {
                  that.$router.push("/login")
                }, 1000)
              } else {
                that.$message.error(res.data)
              }
            })

            // this.$store.commit("removeUserToken", "userToken")// 删除store中的token
            // console.log("123123", this.$store.getters.getUserToken)
            // this.Cookies.remove("userToken")
            // this.$router.push("/login")
          })
          .catch(() => {
            this.$message({
              type: "info",
              message: "已取消退出登录"
            })
          })
        // console.log("88888", this.$store.getters.getUserToken)
      }
    },
    // 未授权时间轮询
    authorTimeLoad() {
      let that = this
      let isurl = "/szmb/storeapplyfor/selectbystoreid"
      let o = {
        storeId: that.Cookies.get("storeId")
      }
      that.$post(isurl, o).then((res) => {
        // console.log(res)
        if (res.code == 1) {
          if (res.data.tryOut == "已过期") {
            clearInterval(that.authorTimer)
            // that.goEmpower()
          }
          if (res.data.identity == 1) {
            this.EpowerouterVisible = false
            clearInterval(that.authorTimer)
          }
          that.authorTime = res.data.tryOut
        } else {
          clearInterval(that.authorTimer)
        }
      })
    },
    // 登录检测
    tipsLoad() {
      let that = this
      let userId = that.Cookies.get("userId") ? that.Cookies.get("userId") : null;
      let isurl = "szmb/szmbstorecontroller/crushlogin"
      let o = {
        storeId: that.Cookies.get("storeId"),
        sign: "pc",
        adminStoreId: that.adminStoreInfo.storeId,
        token: that.Cookies.get("userToken"),
        userId: userId,
      }
      that.$post(isurl, o).then((res) => {
        // console.log(res)
        if (res.code != 1) {
          clearInterval(that.orderTime)
          this.$confirm(
            "当前账号已在其他设备登录，即将强制下线，如不是本人操作请及时更改密码或联系管理人员！",
            "提示",
            {
              confirmButtonText: "退出",
              cancelButtonText: "重新登录",
              type: "warning"
            }
          )
            .then(() => {
              that.closrMsg()
              that.ClearAllCookie()
              setTimeout(function () {
                that.$router.push("/login")
              }, 1000)
            })
            .catch(() => {
              that.closrMsg()
              that.ClearAllCookie()
              setTimeout(function () {
                that.$router.push("/login")
              }, 1000)
            })
        }
      })
    },

    // 增强版登录检测：支持多平台并发登录
    tipsLoadEnhanced() {
      let that = this
      let userId = that.Cookies.get("userId") ? that.Cookies.get("userId") : null;
      let isurl = "szmb/szmbstorecontroller/crushlogin-enhanced"
      let o = {
        storeId: that.Cookies.get("storeId"),
        sign: "pc",
        adminStoreId: that.adminStoreInfo.storeId,
        token: that.Cookies.get("userToken"),
        userId: userId,
      }
      that.$post(isurl, o).then((res) => {
        console.log('Enhanced PC login check:', res)
        if (res.code != 1) {
          clearInterval(that.orderTime)
          clearInterval(that.loginCheckTimer) // 清除登录检测定时器
          this.$confirm(
            "当前PC端账号已在其他设备登录，即将强制下线。注意：这不会影响您在微信小程序端的登录状态。",
            "提示",
            {
              confirmButtonText: "退出",
              cancelButtonText: "重新登录",
              type: "warning"
            }
          )
            .then(() => {
              that.closrMsg()
              that.ClearAllCookie()
              setTimeout(function () {
                that.$router.push("/login")
              }, 1000)
            })
            .catch(() => {
              that.closrMsg()
              that.ClearAllCookie()
              setTimeout(function () {
                that.$router.push("/login")
              }, 1000)
            })
        }
      })
    },
    // 请求浏览器通知权限
    requestNotificationPermission() {
      if (!("Notification" in window)) {
        console.log("此浏览器不支持桌面通知");
        return;
      }

      if (Notification.permission !== "granted" && Notification.permission !== "denied") {
        Notification.requestPermission();
      }
    },
    
    // 显示浏览器通知
    showNotification(title, body) {
      if (Notification.permission === "granted") {
        const notification = new Notification(title, {
          body: body,
          icon: require("../assets/imgs/logo.png")
        });
        
        notification.onclick = () => {
          window.focus();
          this.popoverVisible = true;
          notification.close();
        };
        
        setTimeout(() => notification.close(), 5000);
      }
    },
  },
  filters: {
    orderState(keys) {
      switch (keys) {
        case "SUCCESS":
          return "支付成功"
        case "REFUND":
          return "转入退款"
        case "NOTPAY":
          return "未支付"
        case "CLOSED":
          return "已关闭"
        case "REVOKED":
          return "已撤销（付款码支付）"
        case "USERPAYING":
          return "用户支付中（付款码支付）"
        case "PAYERROR":
          return "支付失败(其他原因，如银行返回失败)"
        default:
          break
      }
    }
  },
  beforeDestroy() { }
}
</script>

<style lang="scss" scoped>
@import "../assets/css/transition.scss";
.ignore-all {
  width: 100%;
  height: 40px;
  line-height: 40px;
  text-align: center;
  background-color: #f0f2f5;
  border-radius: 5px;
  margin-bottom: 10px;
  // 好看
  border: 1px solid #e6e6e6;
  border-radius: 5px;
  padding: 0 10px;
  font-size: 14px;
  color: #666;
  cursor: pointer;
  
}

.right_msg {
  width: 280px;
  box-shadow: 0 0 15px rgba(100, 100, 100, 0.6);
  cursor: pointer;

  .msg_item {
    margin: 15px;
    padding: 10px;
    font-size: 13px;
    border-radius: 10px;
    color: #666;
    padding-bottom: 5px;
    line-height: 20px;
    margin-bottom: 20px;
    box-shadow: 0 5px 10px rgba(100, 100, 100, 0.6);

    img {
      width: 20px;
      margin-right: 5px;
    }

    .msg_title {
      font-size: 14px;
      font-weight: bold;
      color: #1693fd;
    }
  }
}

.el-container {
  width: 100%;
  height: 100%;
  overflow: hidden;

  // 头部 开始
  .el-header {
    background-color: #3d4c66;
    height: 64px;
    padding: 0;
    box-shadow: 0 2px 10px 0 rgba(23, 43, 77, 0.2);
    z-index: 2;

    .head-box {
      width: 100%;
      height: 100%;

      .logo-icon {
        float: left;
        width: 200px;
        height: 100%;
        color: #fff;
        display: flex;
        display: -webkit-flex;
        justify-content: flex-start;
        align-items: center;
        box-sizing: border-box;
        padding: 0 10px;

        img {
          width: 24px;
          height: 24px;
        }

        h3 {
          font-size: 18px;
          font-family: PingFang-SC-Bold;
          font-weight: bold;
          color: rgba(255, 255, 255, 1);
          margin-left: 10px;
        }
      }

      // 面包屑
      .header-breadcrumb {
        height: 100%;
        float: left;
        display: flex;
        display: -webkit-flex;
        align-items: center;
        box-sizing: border-box;
        padding-left: 20px;

        ::v-deep .el-breadcrumb {
          width: 100%;
          // height: 100%;
          box-sizing: border-box;
          font-size: 16px;
          font-family: PingFang-SC-Bold;
          font-weight: 500;
          color: black;
          box-sizing: border-box;

          .el-breadcrumb__item:last-child .el-breadcrumb__inner {
            color: black;
          }
        }
      }

      .collapse-box {
        float: left;
        width: 40px;
        height: 100%;
        display: flex;
        display: -webkit-flex;
        justify-content: center;
        align-items: center;
        background-color: #3d4c66;

        i {
          font-size: 32px;
          color: #fff;
          font-weight: 400;
        }
      }

      .user-content {
        float: right;
        width: auto;
        height: 100%;
        padding-right: 30px;
        box-sizing: border-box;
        display: flex;
        display: -webkit-flex;
        justify-content: flex-end;
        align-items: center;

        .empower-box {
          color: #ffffff;
          font-size: 18px;
          padding: 0 20px;

          .el-button {
            margin-left: 15px;
            font-size: 16px;
            color: #ffffff;
          }
        }

        .dot {
          font-size: 12px;
          width: 14px;
          height: 14px;
          padding: 2px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 18rpx;
          color: white;
          position: absolute;
          top: -7px;
          right: -7px;
          background: #ff2f43;
          border-radius: 50%;
        }

        .user-head {
          width: 36px;
          height: 36px;
          border-radius: 100%;
          overflow: hidden;
          display: flex;
          display: -webkit-flex;
          justify-content: center;
          align-items: center;
          margin-right: 10px;

          img {
            display: block;
            width: 36px;
            height: 36px;
          }
        }

        .user-info {
          .el-dropdown-link {
            cursor: pointer;
            color: #fff;
            outline: none;
          }

          .el-icon-arrow-down {
            font-size: 14px;
            font-weight: 600;
          }

          .el-dropdown {
            outline: none;
          }
        }
      }
    }
  }

  // 头部 结束
  // 菜单
  .el-scrollbar {
    overflow: hidden;
    height: 103%;
    border-right: solid 1px #e6e6e6;
    background-color: #3d4c66;
  }

  ::v-deep.el-container .el-main .tags-view-box {
    background: white !important;
  }

  .tags-view-container {
    background-color: #f0f2f5;
    // border: 0;
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.1);
  }

  .el-container .tags-view-container {
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.1);
  }

  .el-scrollbar__wrap {
    overflow: scroll;
    overflow-x: auto;
    height: 100%;
  }

  ::v-deep.tags-view-container .tags-view-wrapper .tags-view-item.active::before {
    content: "";
    background: #fff;
    display: inline-block;
    width: 5px !important;
    height: 5px !important;
    border-radius: 50%;
    position: relative;
    margin-right: 2px;
    margin-bottom: 1px !important;
  }

  .el-menu::-webkit-scrollbar {
    display: none;
  }

  ::v-deep .el-menu {
    height: 100%;
    box-sizing: border-box;
    overflow-y: auto;
    -ms-overflow-style: none; //IE 10+
    overflow: -moz-scrollbars-none; //Firefox
    padding-bottom: 50px;
    border-right: none;
    background-color: #3d4c66 !important;

    .el-menu-item [class^="menu-icon-"],
    .el-submenu [class^="menu-icon-"] {
      display: inline-block;
      width: 20px;
      height: 46px;
      // line-height: 16px;
      margin-right: 5px;
    }

    .menu-icon-home {
      background: url("../assets/icons/menus/home.png") no-repeat center center;
      background-size: 20px 20px;
    }

    // .el-menu-item.is-active .menu-icon-home {
    //   background: url("../assets/icons/menus/home.png") no-repeat center center;
    //   background-size: 20px 20px;
    // }
    .menu-icon-khzt {
      background: url("../assets/icons/menus/khzt.png") no-repeat center center;
      background-size: 20px 20px;
    }

    .menu-icon-spwh {
      background: url("../assets/icons/menus/spwh.png") no-repeat center center;
      background-size: 20px 20px;
    }

    .menu-icon-qbdd {
      background: url("../assets/icons/menus/qbdd.png") no-repeat center center;
      background-size: 20px 20px;
    }

    .menu-icon-pdy {
      background: url("../assets/icons/menus/pdy.png") no-repeat center center;
      background-size: 20px 20px;
    }

    .menu-icon-spgl {
      background: url("../assets/icons/menus/spgl.png") no-repeat center center;
      background-size: 20px 20px;
    }

    .menu-icon-khgl {
      background: url("../assets/icons/menus/khgl.png") no-repeat center center;
      background-size: 20px 20px;
    }

    .menu-icon-jhtz {
      background: url("../assets/icons/menus/jhtz.png") no-repeat center center;
      background-size: 20px 20px;
    }

    .menu-icon-kcpd {
      background: url("../assets/icons/menus/kcpd.png") no-repeat center center;
      background-size: 20px 20px;
    }

    .menu-icon-cwfx {
      background: url("../assets/icons/menus/cwfx.png") no-repeat center center;
      background-size: 20px 20px;
    }

    .menu-icon-qxxd {
      background: url("../assets/icons/menus/qxxd.png") no-repeat center center;
      background-size: 20px 20px;
    }

    .menu-icon-jfsc {
      background: url("../assets/icons/menus/jfsc.png") no-repeat center center;
      background-size: 20px 20px;
    }

    .menu-icon-xxgl {
      background: url("../assets/icons/menus/xxgl.png") no-repeat center center;
      background-size: 20px 20px;
    }

    .menu-icon-dxgl {
      background: url("../assets/icons/menus/dxgl.png") no-repeat center center;
      background-size: 20px 20px;
    }

    .menu-icon-xtgl {
      background: url("../assets/icons/menus/xtgl.png") no-repeat center center;
      background-size: 20px 20px;
    }

    ::v-deep.el-menu {
      background-color: #001529 !important;
      color: white !important;
    }

    .el-menu-item.is-active {
      background: #647085 !important;
      border-right: 4px solid #1693fd;
      color: white !important;
    }

    .el-submenu {
      margin-top: 20px;
    }

    .el-submenu .el-menu-item {
      padding: 0 30px 0 60px !important;
      margin-top: 10px;
    }

    .el-menu-item {
      // width: 100%;
      height: 46px;
      line-height: 46px;
      border-right: 4px solid transparent;
      margin-top: 15px;
      display: flex;
      display: -webkit-flex;
      justify-content: flex-start;
      align-items: center;
      font-size: 0;
      user-select: none;

      span {
        font-size: 14px;
      }
    }
  }

  // hover
  ::v-deep .el-menu-item:hover,
  ::v-deep .el-menu-item:focus {
    outline: none;
    background-color: #1890ff;
  }
  ::v-deep .el-submenu__title:hover,
  ::v-deep .el-submenu__title:focus {
    outline: none;
    background-color: #1890ff;
  }

  // 箭头
  ::v-deep.el-submenu__title i {
    color: white;
  }

  .el-container {
    width: 100%;
    height: 100%;

    .el-main {
      width: 100%;
      height: 100%;
      background-color: #fff;
      color: #333;
      text-align: left;
      padding: 0;
      box-sizing: border-box;
      overflow: auto;

      .tags-view-box {
        width: 100%;
        height: 40px;
        background: #f3f4f5;
        overflow: hidden;
      }

      .main-content {
        // width: 100%;
        // overflow: hidden;
        // height: calc(100% - 47px);
        background-color: white;
        padding: 15px 15px 0;
        // box-sizing: border-box;
      }
    }
  }
}

.el-menu-vertical-demo:not(.el-menu--collapse) {
  width: 200px;
  // min-height: 400px;
  font-size: 14px;
  font-family: PingFang-SC-Bold;
  font-weight: bold;
  line-height: 20px;
}

.el-main ::v-deep .el-breadcrumb__item:last-child .el-breadcrumb__inner {
  font-weight: 600;
}

// =============================================
.transition-box {
  margin-bottom: 10px;
  width: 100%;
  // height: 130px;
  border-radius: 4px;
  text-align: center;
  color: #fff;
  box-sizing: border-box;
  margin-right: 20px;
  padding: 10px;
  box-sizing: border-box;

  h5 {
    margin: 0;
    text-align: left;
    font-size: 16px;
    height: 20px;
    line-height: 20px;
  }

  .info-text {
    text-align: left;
    padding: 10px 0;
    font-size: 12px;
    // height: 60px;
    box-sizing: border-box;
  }

  .buy-price-btn {
    height: 30px;
    display: flex;
    justify-content: space-between;

    .el-button {
      background-color: transparent;
      color: #fff;
      border: 1px solid #fff;
    }
  }
}

.transition-box0 {
  background: -webkit-linear-gradient(left,
      #9b91fe,
      #74d5ff);
  /* Safari 5.1 - 6.0 */
  background: -o-linear-gradient(right,
      #9b91fe,
      #74d5ff);
  /* Opera 11.1 - 12.0 */
  background: -moz-linear-gradient(right,
      #9b91fe,
      #74d5ff);
  /* Firefox 3.6 - 15 */
  background: linear-gradient(to right,
      #9b91fe,
      #74d5ff);
  /* 标准的语法（必须放在最后） */
}

.transition-box1 {
  background: -webkit-linear-gradient(left,
      #ff85ac,
      #fab789);
  /* Safari 5.1 - 6.0 */
  background: -o-linear-gradient(right,
      #ff85ac,
      #fab789);
  /* Opera 11.1 - 12.0 */
  background: -moz-linear-gradient(right,
      #ff85ac,
      #fab789);
  /* Firefox 3.6 - 15 */
  background: linear-gradient(to right,
      #ff85ac,
      #fab789);
  /* 标准的语法（必须放在最后） */
}

.transition-box2 {
  background: -webkit-linear-gradient(left,
      #54eca5,
      #79d7e9);
  /* Safari 5.1 - 6.0 */
  background: -o-linear-gradient(right,
      #54eca5,
      #79d7e9);
  /* Opera 11.1 - 12.0 */
  background: -moz-linear-gradient(right,
      #54eca5,
      #79d7e9);
  /* Firefox 3.6 - 15 */
  background: linear-gradient(to right,
      #54eca5,
      #79d7e9);
  /* 标准的语法（必须放在最后） */
}

::v-deep .ddd-dddpp {
  display: flex;
  align-items: center;
  padding: 10px 0;

  img {
    width: 25px;
  }

  span {
    margin-left: 8px;
    font-size: 14px;
  }
}

.el-menu-vertical-demo ::v-deep.el-badge__content.is-fixed {
  top: 10px;
}

.layout-scroll {
  // width: 100%;
  min-width: 1200px;
}

.diaolog-box {
  display: flex;
  justify-content: flex-start;
  align-content: flex-start;
}

.dia-treaty {
  display: flex;
  justify-content: center;

  div {
    margin-left: 5px;
    cursor: pointer;

    &:hover {
      color: #1693fd;
      text-decoration: underline;
    }
  }
}

.message-notification {
  margin-right: 20px;
  cursor: pointer;

  .message-icon {
    font-size: 20px;
    color: #ffffff;
  }

  .el-icon-bell {
    font-size: 24px;
  }

  .message-badge ::v-deep .el-badge__content {
    background-color: #ff4949;
    border: none;
  }
}

::v-deep .message-popover {
  padding: 0;
  
}

.message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    border-bottom: 1px solid #ebeef5;
    font-weight: bold;

    .message-controls {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }
  
  .message-list {
    max-height: 400px;
    overflow-y: auto;
    
    .message-item {
      padding: 10px 15px;
      border-bottom: 1px solid #f0f0f0;
      cursor: pointer;
      
      &:hover {
        background-color: #f5f7fa;
      }
      
      .message-content {
        .message-title {
          display: flex;
          justify-content: space-between;
          margin-bottom: 5px;
          font-weight: bold;
          
          .message-time {
            font-size: 12px;
            color: #999;
            font-weight: normal;
          }
        }
        
        .message-body {
          font-size: 13px;
          color: #666;
          margin-bottom: 5px;
          text-indent: 2em;
          line-height: 1.5;
        }
        
        .message-footer {
          text-align: right;
        }
      }
    }
  }
  
  .no-message {
    padding: 20px;
    text-align: center;
    color: #999;
  }
</style>
