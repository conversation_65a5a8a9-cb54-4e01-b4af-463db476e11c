(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["waterStationStats"],{3774:function(e,t,r){},"3e54":function(e,t,r){"use strict";r.r(t);var i=function(){var e=this,t=e._self._c;return t("div",{staticClass:"padding-bottom-30"},[t("div",[t("div",{staticClass:"content-box"}),t("div",{staticClass:"cont-cent"},[t("el-form",{attrs:{inline:!0}},[t("el-form-item",[t("el-input",{staticStyle:{width:"200px"},attrs:{placeholder:"输入水站名称/电话搜索",clearable:""},model:{value:e.waterStationName,callback:function(t){e.waterStationName=t},expression:"waterStationName"}})],1),t("el-form-item",[t("el-cascader",{staticStyle:{width:"200px"},attrs:{placeholder:"全部(区域)",options:e.areaOptions,props:e.cascaderProps,filterable:"",clearable:""},on:{change:e.onAreaFilterChange},model:{value:e.areaFilter,callback:function(t){e.areaFilter=t},expression:"areaFilter"}})],1),t("el-form-item",[t("el-select",{attrs:{filterable:"",clearable:"",placeholder:"全部(商家)"},model:{value:e.storeId,callback:function(t){e.storeId=t},expression:"storeId"}},[t("el-option",{attrs:{label:"全部(商家)",value:""}}),e._l(e.filteredStoreList,(function(e){return t("el-option",{key:e.storeId,attrs:{label:e.storeAme,value:e.storeId}})}))],2)],1),t("el-form-item",[t("el-select",{attrs:{filterable:"",clearable:"",placeholder:"全部(订单来源)"},model:{value:e.ordersource,callback:function(t){e.ordersource=t},expression:"ordersource"}},[t("el-option",{attrs:{label:"全部(订单来源)",value:""}}),e._l(e.ordersourcefilter,(function(e){return t("el-option",{directives:[{name:"show",rawName:"v-show",value:e.isShow,expression:"item.isShow"}],key:e.key,attrs:{label:e.value,value:e.key,disabled:e.disabled}})}))],2)],1),t("el-form-item",[t("el-select",{attrs:{filterable:"",clearable:"",placeholder:"全部(应用)"},model:{value:e.appkey,callback:function(t){e.appkey=t},expression:"appkey"}},e._l(e.appkeylist,(function(e){return t("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),t("el-form-item",[t("el-date-picker",{attrs:{type:"daterange",align:"right","unlink-panels":"","range-separator":"至","start-placeholder":"开始日期(创建时间)","end-placeholder":"结束日期(创建时间)","picker-options":e.pickerOptions,"value-format":"yyyy-MM-dd"},on:{change:e.selectDataDate},model:{value:e.dateTime,callback:function(t){e.dateTime=t},expression:"dateTime"}})],1),t("el-form-item",[t("el-date-picker",{attrs:{type:"daterange",align:"right","unlink-panels":"","range-separator":"至","start-placeholder":"开始日期(送达时间)","end-placeholder":"结束日期(送达时间)","picker-options":e.pickerOptions,"value-format":"yyyy-MM-dd"},on:{change:e.selectDataDateFinish},model:{value:e.dateTimeFinish,callback:function(t){e.dateTimeFinish=t},expression:"dateTimeFinish"}})],1),t("el-form-item",[t("el-select",{attrs:{placeholder:"退款订单筛选",clearable:""},model:{value:e.backFilter,callback:function(t){e.backFilter=t},expression:"backFilter"}},[t("el-option",{attrs:{label:"全部订单",value:""}}),t("el-option",{attrs:{label:"退单订单",value:"1"}}),t("el-option",{attrs:{label:"正常订单",value:"0"}})],1)],1),t("el-form-item",{staticStyle:{"margin-left":"20px"}},[t("el-button",{attrs:{type:"primary"},on:{click:e.search}},[e._v("查询")]),t("el-button",{on:{click:e.clearSearch}},[e._v("清空筛选条件")])],1)],1),t("el-button",{attrs:{type:"success",icon:"el-icon-download",size:"mini"},on:{click:e.exportExcel}},[e._v("导出数据")])],1),t("div",{staticClass:"royalty-cont"},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.fuckLoading10,expression:"fuckLoading10"}],staticStyle:{width:"100%"},attrs:{data:e.tableData,"max-height":e.tableHeight+50,border:"","element-loading-text":"拼命加载中","header-cell-style":{"text-align":"center",background:"#EFF2F7",color:"#3D4C66"},"cell-style":{"text-align":"center","font-size":"13px",color:"#333C48"},"empty-text":"暂无数据",size:"small",stripe:""}},[t("el-table-column",{attrs:{label:"水站名称",prop:"waterStationName","min-width":"120"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("div",[e._v(e._s(r.row.waterStationName||"-"))])]}}])}),t("el-table-column",{attrs:{label:"水站电话",prop:"waterStationPhone","min-width":"120"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("div",[e._v(e._s(r.row.waterStationPhone||"-"))])]}}])}),t("el-table-column",{attrs:{label:"总配送单数",prop:"totalDeliveryCount","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("div",{staticStyle:{color:"#409EFF","font-weight":"bold"}},[e._v(e._s(r.row.totalDeliveryCount||0)+"单")])]}}])}),t("el-table-column",{attrs:{label:"已完成单数",prop:"completedCount","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("div",{staticStyle:{color:"#67C23A"}},[e._v(e._s(r.row.completedCount||0)+"单")])]}}])}),t("el-table-column",{attrs:{label:"配送中单数",prop:"inProgressCount","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("div",{staticStyle:{color:"#E6A23C"}},[e._v(e._s(r.row.inProgressCount||0)+"单")])]}}])}),t("el-table-column",{attrs:{label:"待配送单数",prop:"pendingCount","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("div",{staticStyle:{color:"#F56C6C"}},[e._v(e._s(r.row.pendingCount||0)+"单")])]}}])}),t("el-table-column",{attrs:{label:"退款单数",prop:"refundCount","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("div",{staticStyle:{color:"#909399"}},[e._v(e._s(r.row.refundCount||0)+"单")])]}}])}),t("el-table-column",{attrs:{label:"配送商品总数",prop:"totalProductCount","min-width":"120"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("div",[e._v(e._s(r.row.totalProductCount||0)+"件")])]}}])}),t("el-table-column",{attrs:{label:"完成率",prop:"completionRate","min-width":"80"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("div",[e._v(e._s(r.row.completionRate||"0.00%"))])]}}])}),t("el-table-column",{attrs:{label:"操作","min-width":"120",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(t){return e.viewDeliveryOrders(r.row)}}},[e._v("\n              查看配送订单\n            ")])]}}])})],1),t("div",{staticStyle:{"margin-top":"20px","text-align":"center"}},[t("el-pagination",{attrs:{"current-page":e.page+1,"page-sizes":e.currentPageSizes,"page-size":e.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:e.pageTotal},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1),t("div",{staticClass:"total-box66"},[t("div",[e._v("合计")]),t("div",[t("span",[e._v("总配送单数："+e._s(e.totalStats.totalDeliveryCount)+"单")]),t("span",[e._v("已完成："+e._s(e.totalStats.completedCount)+"单")]),t("span",[e._v("配送中："+e._s(e.totalStats.inProgressCount)+"单")]),t("span",[e._v("待配送："+e._s(e.totalStats.pendingCount)+"单")]),t("span",[e._v("退款："+e._s(e.totalStats.refundCount)+"单")]),t("span",[e._v("总配送商品："+e._s(e.totalStats.totalProductCount)+"件")])])])],1)]),t("el-dialog",{attrs:{title:e.deliveryOrderDialogTitle,visible:e.deliveryOrderVisible,width:"90%",top:"5vh","before-close":e.closeDeliveryOrderDialog},on:{"update:visible":function(t){e.deliveryOrderVisible=t}}},[t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.deliveryOrderLoading,expression:"deliveryOrderLoading"}],attrs:{"element-loading-text":"加载中..."}},[t("el-form",{staticStyle:{"margin-bottom":"15px"},attrs:{inline:!0}},[t("el-form-item",[t("el-input",{staticStyle:{width:"250px"},attrs:{placeholder:"输入订单号/产品/手机号/联系方式/地址搜索",clearable:""},model:{value:e.orderSearchText,callback:function(t){e.orderSearchText=t},expression:"orderSearchText"}})],1),t("el-form-item",[t("el-date-picker",{staticStyle:{width:"240px"},attrs:{type:"daterange",align:"right","unlink-panels":"","range-separator":"至","start-placeholder":"开始日期(下单时间)","end-placeholder":"结束日期(下单时间)","picker-options":e.pickerOptions,"value-format":"yyyy-MM-dd",clearable:""},model:{value:e.orderDateRange,callback:function(t){e.orderDateRange=t},expression:"orderDateRange"}})],1),t("el-form-item",[t("el-date-picker",{staticStyle:{width:"240px"},attrs:{type:"daterange",align:"right","unlink-panels":"","range-separator":"至","start-placeholder":"开始日期(送达时间)","end-placeholder":"结束日期(送达时间)","picker-options":e.pickerOptions,"value-format":"yyyy-MM-dd",clearable:""},model:{value:e.orderFinishDateRange,callback:function(t){e.orderFinishDateRange=t},expression:"orderFinishDateRange"}})],1),t("el-form-item",[t("el-select",{attrs:{placeholder:"按订单状态筛选",clearable:""},model:{value:e.orderStatusFilter,callback:function(t){e.orderStatusFilter=t},expression:"orderStatusFilter"}},e._l(e.orderStatusOptions,(function(e,r){return t("el-option",{key:r,attrs:{label:e.label,value:e.value}})})),1)],1),t("el-form-item",[t("el-select",{attrs:{placeholder:"退款订单筛选",clearable:""},model:{value:e.orderBackFilter,callback:function(t){e.orderBackFilter=t},expression:"orderBackFilter"}},[t("el-option",{attrs:{label:"全部订单",value:""}}),t("el-option",{attrs:{label:"退单订单",value:"1"}}),t("el-option",{attrs:{label:"正常订单",value:"0"}})],1)],1),t("el-form-item",[t("el-button",{attrs:{type:"primary"},on:{click:e.searchDeliveryOrders}},[e._v("查询")]),t("el-button",{on:{click:e.clearOrderSearch}},[e._v("清空")]),t("el-button",{attrs:{type:"success",icon:"el-icon-download"},on:{click:e.exportDeliveryOrders}},[e._v("导出订单")])],1)],1),t("el-table",{staticStyle:{width:"100%"},attrs:{data:e.deliveryOrderList,border:"",stripe:"",size:"mini",height:"600","header-cell-style":{"text-align":"center",background:"#EFF2F7",color:"#3D4C66","font-size":"12px"},"cell-style":{"text-align":"center","font-size":"12px",color:"#333C48"},"empty-text":"暂无配送订单数据"}},[t("el-table-column",{attrs:{prop:"mobile",label:"订单来源",width:"80"},scopedSlots:e._u([{key:"default",fn:function(r){return[r.row.orderSourceImage?t("img",{staticStyle:{width:"40px",height:"40px"},attrs:{src:r.row.orderSourceImage,alt:""}}):e._e()]}}])}),t("el-table-column",{attrs:{prop:"mobile",label:"所属商家","min-width":"120"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("div",[e._v(e._s(r.row.storeName||"-"))]),r.row.storeMobile?t("div",[e._v(e._s(r.row.storeMobile||"-"))]):e._e(),r.row.storeProvinceName?t("div",[e._v("\n              "+e._s(r.row.storeProvinceName)+"\n              "+e._s(r.row.storeCityName?"-"+r.row.storeCityName:"")+"\n              "+e._s(r.row.storeAreaName?"-"+r.row.storeAreaName:"")+"\n            ")]):e._e()]}}])}),t("el-table-column",{attrs:{prop:"userName",label:"联系人","min-width":"100"}}),t("el-table-column",{attrs:{prop:"userPhone",label:"联系方式","min-width":"120"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("div",{staticStyle:{"font-size":"11px"}},[e._v(e._s(r.row.userPhone))]),t("el-button",{staticStyle:{"background-color":"#1895fd",border:"none",padding:"2px 6px","font-size":"10px","margin-top":"2px"},attrs:{type:"primary",size:"mini",loading:r.row.refreshing},on:{click:function(t){return e.refreshPhone(r.row.orderId)}}},[e._v("\n              刷新电话\n            ")])]}}])}),t("el-table-column",{attrs:{prop:"userAddress",label:"下单地址","min-width":"200"}}),t("el-table-column",{attrs:{prop:"orderDate",label:"下单日期","min-width":"150"}}),t("el-table-column",{attrs:{prop:"finishTime",label:"送达日期","min-width":"150"}}),t("el-table-column",{attrs:{prop:"orderNumber",label:"订单编码",width:"150"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("div",{staticStyle:{"font-size":"11px"}},[e._v(e._s(r.row.orderNumber))]),t("div",{staticClass:"order-log-btn",staticStyle:{"font-size":"10px"},on:{click:function(t){return e.showOrderLog(r.row.orderNumber)}}},[e._v("\n              操作记录\n            ")])]}}])}),t("el-table-column",{attrs:{label:"订单信息","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(r){return[r.row.groupShopList?t("div",e._l(r.row.groupShopList,(function(r,i){return t("div",{key:i},[e._v("\n                "+e._s(r.groupName)+" x "+e._s(r.groupNumber)+"\n              ")])})),0):e._e(),r.row.list&&r.row.list.length>0?t("div",e._l(r.row.list[0].orderShopDeatilList,(function(r,i){return t("div",{key:i},[e._v("\n                "+e._s(r.title)+" x "+e._s(r.shopNumber)+"\n              ")])})),0):e._e()]}}])}),t("el-table-column",{attrs:{prop:"orderTotalNumber",label:"总件数","min-width":"80"}}),t("el-table-column",{attrs:{prop:"orderPrice",label:"订单总金额","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("￥"+e._s(t.row.orderPrice))]}}])}),t("el-table-column",{attrs:{prop:"deliveryName",label:"送水员","min-width":"120"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("div",[e._v(e._s(r.row.deliveryName))]),r.row.deliveryMobile?t("div",[e._v(e._s(r.row.deliveryMobile))]):e._e()]}}])}),t("el-table-column",{attrs:{prop:"userContent",label:"备注","min-width":"120"}}),t("el-table-column",{attrs:{prop:"newOrderState",label:"状态",width:"100"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("span",[e._v(e._s(r.row.newOrderState))]),r.row.orderReturnStete?t("span",[e._v("("+e._s(r.row.orderReturnStete)+")")]):e._e()]}}])}),t("el-table-column",{attrs:{prop:"picurl",label:"图片",width:"120"},scopedSlots:e._u([{key:"default",fn:function(r){return[e.getPicUrlArray(r.row.picurl).length>0?t("div",{staticClass:"pic-container"},[e._l(e.getPicUrlArray(r.row.picurl).slice(0,3),(function(i,a){return t("img",{key:a,staticClass:"pic-thumbnail",attrs:{src:i},on:{click:function(t){e.previewImages(e.getPicUrlArray(r.row.picurl),a)},error:e.handleImageError}})})),e.getPicUrlArray(r.row.picurl).length>3?t("span",{staticClass:"pic-more"},[e._v("\n                +"+e._s(e.getPicUrlArray(r.row.picurl).length-3)+"\n              ")]):e._e()],2):e._e()]}}])})],1),t("div",{staticStyle:{"margin-top":"20px","text-align":"center"}},[t("el-pagination",{attrs:{"current-page":e.orderPage+1,"page-sizes":[10,20,50,100],"page-size":e.orderPageSize,layout:"total, sizes, prev, pager, next, jumper",total:e.orderPageTotal},on:{"size-change":e.handleOrderSizeChange,"current-change":e.handleOrderCurrentChange}})],1)],1)]),t("el-dialog",{attrs:{title:"订单操作记录",visible:e.showOrderLogModal,width:"50%","before-close":e.closeOrderLogModal},on:{"update:visible":function(t){e.showOrderLogModal=t}}},[t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.orderLogLoading,expression:"orderLogLoading"}],attrs:{"element-loading-text":"加载中..."}},[e.orderLogData&&e.orderLogData.length>0?t("div",{staticClass:"order-log-content"},e._l(e.orderLogData,(function(r,i){return t("div",{key:i,staticClass:"log-item"},[t("div",{staticClass:"log-time"},[e._v(e._s(r.createTime))]),t("div",{staticClass:"log-content"},[e._v(e._s(r.storeMsgModel))]),r.content?t("div",{staticClass:"log-detail"},[e._v(e._s(r.content))]):e._e()])})),0):e.orderLogLoading?e._e():t("div",{staticClass:"no-log"},[t("div",{staticClass:"no-log-text"},[e._v("暂无操作记录")])])])]),t("ImagePreviewUpload",{attrs:{visible:e.imagePreviewVisible,"image-list":e.previewImageList,"current-index":e.currentImageIndex},on:{"update:visible":function(t){e.imagePreviewVisible=t},close:function(t){e.imagePreviewVisible=!1}}})],1)},a=[],o=r("7de9"),n=r("ff23");function l(e,t){return h(e)||u(e,t)||d(e,t)||s()}function s(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function d(e,t){if(e){if("string"==typeof e)return c(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?c(e,t):void 0}}function c(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,i=Array(t);r<t;r++)i[r]=e[r];return i}function u(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var i,a,o,n,l=[],s=!0,d=!1;try{if(o=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;s=!1}else for(;!(s=(i=o.call(r)).done)&&(l.push(i.value),l.length!==t);s=!0);}catch(e){d=!0,a=e}finally{try{if(!s&&null!=r.return&&(n=r.return(),Object(n)!==n))return}finally{if(d)throw a}}return l}}function h(e){if(Array.isArray(e))return e}var p={props:{},data:function(){return{imgUri:this.$imgUri,fuckLoading10:!1,waterStationName:"",storeId:"",ordersource:"",appkey:"",startTime:"",endTime:"",dateTime:[],startTimeFinish:"",endTimeFinish:"",dateTimeFinish:[],backFilter:"",areaOptions:[],areaFilter:[],cascaderProps:{value:"citiesid",label:"citie",children:"cities",emitPath:!0,checkStrictly:!0},allStoreList:[],filteredStoreList:[],adminStoreInfo:{},appkeylist:[{value:"",label:"全部(应用)"},{value:"d794a292fc884a568a800d47ddaa5e01",label:"阿尔娃饮用水"},{value:"5c0f6c36b013486b8200ca0fa4a121e8",label:"好水送到家"},{value:"603690dcdf08490d9f134c29fe224248",label:"依美雪山"},{value:"06b1de86a7364508b3cc5d2e17bc6170",label:"飞蓝月泉"}],ordersourcefilter:o["a"],tableData:[],totalStats:{totalDeliveryCount:0,completedCount:0,inProgressCount:0,pendingCount:0,refundCount:0,totalProductCount:0,totalOrderAmount:0},page:0,pageSize:10,pageTotal:0,currentPageSizes:[10,15,20,100],pickerOptions:{disabledDate:function(e){return e.getTime()>Date.now()},shortcuts:[{text:"最近一周",onClick:function(e){var t=new Date,r=new Date;r.setTime(r.getTime()-6048e5),e.$emit("pick",[r,t])}},{text:"最近一个月",onClick:function(e){var t=new Date,r=new Date;r.setTime(r.getTime()-2592e6),e.$emit("pick",[r,t])}},{text:"最近三个月",onClick:function(e){var t=new Date,r=new Date;r.setTime(r.getTime()-7776e6),e.$emit("pick",[r,t])}}]},deliveryOrderVisible:!1,deliveryOrderLoading:!1,deliveryOrderDialogTitle:"",currentWaterStation:null,deliveryOrderList:[],orderPage:0,orderPageSize:20,orderPageTotal:0,orderSearchText:"",orderStatusFilter:"",orderBackFilter:"",orderDateRange:[],orderFinishDateRange:[],orderStatusOptions:[{value:2,label:"待发单"},{value:3,label:"待接单"},{value:4,label:"已发货"},{value:10,label:"已完成"},{value:8,label:"退款(申请中)"},{value:11,label:"退款(已解决)"}],imagePreviewVisible:!1,previewImageList:[],currentImageIndex:0,showOrderLogModal:!1,orderLogData:null,orderLogLoading:!1,currentOrderNum:""}},computed:{tableHeight:function(){var e=Number(this.$store.getters.getGlobalHeight)-250;return e>=300?e:300}},created:function(){this.adminStoreInfo=JSON.parse(this.Cookies.get("adminStoreInfo")),this.initDefaultDate(),this.getstore(),this.getAreaOptions(),this.loadData()},methods:{initDefaultDate:function(){var e=new Date,t=e.getFullYear()+"-"+String(e.getMonth()+1).padStart(2,"0")+"-"+String(e.getDate()).padStart(2,"0");this.dateTime=[t,t],this.startTime=t+" 00:00:00",this.endTime=t+" 23:59:59"},getstore:function(){var e=this;this.$post("/szmcstore/selectallstore",{storeId:this.adminStoreInfo.storeId}).then((function(t){1==t.code?(e.allStoreList=t.data,e.filteredStoreList=t.data):(e.allStoreList=[],e.filteredStoreList=[])})).catch((function(t){console.log(t),e.$message.error("获取商家列表失败")}))},getAreaOptions:function(){var e=this;this.$post("/dpt/address/shanghai").then((function(t){1===t.code&&(e.areaOptions=t.data)})).catch((function(e){console.log("加载区域数据失败:",e)}))},onAreaFilterChange:function(){this.storeId="",this.filterStoresByArea()},filterStoresByArea:function(){if(this.areaFilter&&0!==this.areaFilter.length){var e=l(this.areaFilter,3),t=e[0],r=e[1],i=e[2];this.filteredStoreList=this.allStoreList.filter((function(e){return i?e.storeArea===i.toString():r?e.storeCity===r.toString():!t||e.storeProvince===t.toString()}))}else this.filteredStoreList=this.allStoreList},selectDataDate:function(e){e&&2===e.length?(this.startTime=e[0]+" 00:00:00",this.endTime=e[1]+" 23:59:59"):(this.startTime="",this.endTime="")},selectDataDateFinish:function(e){e&&2===e.length?(this.startTimeFinish=e[0]+" 00:00:00",this.endTimeFinish=e[1]+" 23:59:59"):(this.startTimeFinish="",this.endTimeFinish="")},search:function(){this.page=0,this.loadData()},clearSearch:function(){this.page=0,this.waterStationName="",this.storeId="",this.ordersource="",this.appkey="",this.startTime="",this.endTime="",this.dateTime=[],this.startTimeFinish="",this.endTimeFinish="",this.dateTimeFinish=[],this.backFilter="",this.areaFilter=[],this.filteredStoreList=this.allStoreList,this.loadData()},loadData:function(){var e=this,t=this,r={waterStationId:-1,startTime:this.startTime||"",endTime:this.endTime||"",storeId:this.storeId||"",type:1,orderSource:this.ordersource||"",includeRefund:"1"===this.backFilter?1:"0"===this.backFilter?0:null,waterStationName:this.waterStationName||"",appkey:this.appkey||"",startTimeFinish:this.startTimeFinish||"",endTimeFinish:this.endTimeFinish||"",page:this.page,pageSize:this.pageSize};this.fuckLoading10=!0,t.$post("/szmb/szmsendmembercontroller/selectWaterStationDeliveryStatistics",r).then((function(r){e.fuckLoading10=!1,r&&1===r.code?(t.tableData=r.data&&r.data.list?r.data.list:[],t.pageTotal=r.data&&r.data.totalCount?r.data.totalCount:0,t.calculateTotalStats()):(t.tableData=[],t.pageTotal=0,t.totalStats={totalDeliveryCount:0,completedCount:0,inProgressCount:0,pendingCount:0,refundCount:0,totalProductCount:0,totalOrderAmount:0},r&&r.msg?t.$message.error(r.msg):t.$message.error("获取数据失败"))})).catch((function(r){e.fuckLoading10=!1,console.error("水站配送统计查询异常:",r),t.tableData=[],t.pageTotal=0,t.totalStats={totalDeliveryCount:0,completedCount:0,inProgressCount:0,pendingCount:0,refundCount:0,totalProductCount:0,totalOrderAmount:0},t.$message.error("网络请求失败，请稍后重试")}))},calculateTotalStats:function(){try{this.totalStats={totalDeliveryCount:this.tableData.reduce((function(e,t){return e+(t.totalDeliveryCount||0)}),0),completedCount:this.tableData.reduce((function(e,t){return e+(t.completedCount||0)}),0),inProgressCount:this.tableData.reduce((function(e,t){return e+(t.inProgressCount||0)}),0),pendingCount:this.tableData.reduce((function(e,t){return e+(t.pendingCount||0)}),0),refundCount:this.tableData.reduce((function(e,t){return e+(t.refundCount||0)}),0),totalProductCount:this.tableData.reduce((function(e,t){return e+(t.totalProductCount||0)}),0),totalOrderAmount:this.tableData.reduce((function(e,t){return e+(t.totalOrderAmount||0)}),0)}}catch(e){console.error("计算统计数据异常:",e),this.totalStats={totalDeliveryCount:0,completedCount:0,inProgressCount:0,pendingCount:0,refundCount:0,totalProductCount:0,totalOrderAmount:0}}},handleSizeChange:function(e){this.pageSize=e,this.loadData()},handleCurrentChange:function(e){this.page=e-1,this.loadData()},exportExcel:function(){try{var e=["page=1","pageSize=10000","waterStationName="+encodeURIComponent(this.waterStationName||""),"storeId="+encodeURIComponent(this.storeId||""),"ordersource="+encodeURIComponent(this.ordersource||""),"appkey="+encodeURIComponent(this.appkey||""),"startTime="+encodeURIComponent(this.startTime||""),"endTime="+encodeURIComponent(this.endTime||""),"startTimeFinish="+encodeURIComponent(this.startTimeFinish||""),"endTimeFinish="+encodeURIComponent(this.endTimeFinish||""),"backFilter="+encodeURIComponent(this.backFilter||"")].join("&"),t=this.$axios.adornUrl("/szmb/szmsendmembercontroller/selectWaterStationDeliveryStatisticsExport?"+e);window.open(t)}catch(r){console.error("导出异常:",r),this.$message.error("导出失败，请稍后重试")}},viewDeliveryOrders:function(e){this.currentWaterStation=e,this.deliveryOrderDialogTitle="".concat(e.waterStationName," - 配送订单列表"),this.deliveryOrderVisible=!0,this.orderPage=0,this.orderSearchText="",this.orderStatusFilter="",this.orderBackFilter="",this.orderDateRange=[],this.orderFinishDateRange=[],this.loadDeliveryOrders()},loadDeliveryOrders:function(){var e=this;if(this.currentWaterStation){this.deliveryOrderLoading=!0;var t="",r="",i="",a="";this.orderDateRange&&2===this.orderDateRange.length&&(t=this.orderDateRange[0],r=this.orderDateRange[1]),this.orderFinishDateRange&&2===this.orderFinishDateRange.length&&(i=this.orderFinishDateRange[0],a=this.orderFinishDateRange[1]);var o={storeId:this.currentWaterStation.waterStationId,username:this.orderSearchText||"",userContent:"",startTime:t,endTime:r,startTimeFinish:i,endTimeFinish:a,appkey:this.appkey||"",ordersource:this.ordersource||"",addressId:"",index:this.orderPage,pageSize:this.orderPageSize,orderStatus:this.orderStatusFilter||"",back:this.orderBackFilter||""};this.$post("/szmcordermaincontroller/findallorderall",o).then((function(t){e.deliveryOrderLoading=!1,1===t.code?(e.deliveryOrderList=t.data.list||[],e.orderPageTotal=t.data.count||0):(e.deliveryOrderList=[],e.orderPageTotal=0,e.$message.error(t.msg||"获取配送订单失败"))})).catch((function(t){e.deliveryOrderLoading=!1,console.error("获取配送订单异常:",t),e.deliveryOrderList=[],e.orderPageTotal=0,e.$message.error("网络请求失败，请稍后重试")}))}},searchDeliveryOrders:function(){this.orderPage=0,this.loadDeliveryOrders()},clearOrderSearch:function(){this.orderSearchText="",this.orderStatusFilter="",this.orderBackFilter="",this.orderDateRange=[],this.orderFinishDateRange=[],this.orderPage=0,this.loadDeliveryOrders()},closeDeliveryOrderDialog:function(){this.deliveryOrderVisible=!1,this.currentWaterStation=null,this.deliveryOrderList=[],this.orderPage=0,this.orderPageTotal=0,this.orderSearchText="",this.orderStatusFilter="",this.orderBackFilter="",this.orderDateRange=[],this.orderFinishDateRange=[]},handleOrderSizeChange:function(e){this.orderPageSize=e,this.orderPage=0,this.loadDeliveryOrders()},handleOrderCurrentChange:function(e){this.orderPage=e-1,this.loadDeliveryOrders()},getOrderStatusText:function(e){var t={0:"待接单",1:"已接单",2:"配送中",3:"配送中",4:"配送中",5:"已完成",6:"已退款",8:"已退款"};return t[e]||"未知状态"},getOrderStatusType:function(e){var t={0:"info",1:"warning",2:"primary",3:"primary",4:"primary",5:"success",6:"danger",8:"danger"};return t[e]||"info"},exportDeliveryOrders:function(){if(this.currentWaterStation)try{var e="",t="",r="",i="";this.orderDateRange&&2===this.orderDateRange.length&&(e=this.orderDateRange[0],t=this.orderDateRange[1]),this.orderFinishDateRange&&2===this.orderFinishDateRange.length&&(r=this.orderFinishDateRange[0],i=this.orderFinishDateRange[1]);var a=["page=1","pageSize=10000","username="+encodeURIComponent(this.orderSearchText||""),"userContent=","startTime="+encodeURIComponent(e),"endTime="+encodeURIComponent(t),"startTimeFinish="+encodeURIComponent(r),"endTimeFinish="+encodeURIComponent(i),"appkey="+encodeURIComponent(this.appkey||""),"ordersource="+encodeURIComponent(this.ordersource||""),"orderStatus="+encodeURIComponent(this.orderStatusFilter||""),"storeId="+encodeURIComponent(this.currentWaterStation.waterStationId||""),"timeoutFilter=","timeoutType=","back="+encodeURIComponent(this.orderBackFilter||""),"addressId="],o=this.$axios.adornUrl("/szmcordermaincontroller/exportplatformorder?"+a.join("&"));window.open(o)}catch(n){console.error("导出异常:",n),this.$message.error("导出失败，请稍后重试")}else this.$message.error("请先选择要导出的水站")},refreshPhone:function(e){var t=this,r=this.deliveryOrderList.find((function(t){return t.orderId===e}));r&&this.$set(r,"refreshing",!0),this.$post("/szmb/szmborder/refreshphone",{orderId:e}).then((function(e){r&&t.$set(r,"refreshing",!1),1==e.code?(t.$message.success("刷新成功"),t.loadDeliveryOrders()):t.$message.error("刷新失败: "+(e.msg||e.data||"未知错误"))})).catch((function(e){r&&t.$set(r,"refreshing",!1),console.error("刷新电话失败:",e),t.$message.error("刷新失败，请稍后重试")}))},showOrderLog:function(e){e?(this.currentOrderNum=e,this.showOrderLogModal=!0,this.getOrderLog(e)):this.$message.error("订单号不能为空")},getOrderLog:function(e){var t=this;this.orderLogLoading=!0,this.orderLogData=null,this.$post("/szmb/msg/selectmsgbyordernum",{orderNum:e}).then((function(e){t.orderLogLoading=!1,1==e.code?e.data&&!Array.isArray(e.data)?t.orderLogData=[e.data]:t.orderLogData=e.data||[]:(t.orderLogData=[],t.$message.error("获取操作记录失败: "+(e.msg||"未知错误")))})).catch((function(e){t.orderLogLoading=!1,console.error("获取操作记录失败:",e),t.orderLogData=null,t.$message.error("网络请求失败")}))},closeOrderLogModal:function(){this.showOrderLogModal=!1,this.orderLogData=null,this.orderLogLoading=!1,this.currentOrderNum=""},getPicUrlArray:function(e){return e&&"string"===typeof e?e.split(",").filter((function(e){return""!==e.trim()})):[]},previewImages:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;this.previewImageList=e,this.currentImageIndex=t,this.imagePreviewVisible=!0},handleImageError:function(e){e.target.style.display="none"}},components:{ImagePreviewUpload:n["a"]}},m=p,g=(r("5664"),r("0c7c")),f=Object(g["a"])(m,i,a,!1,null,"308a1240",null);t["default"]=f.exports},4819:function(e,t,r){"use strict";r("592e")},5664:function(e,t,r){"use strict";r("3774")},"592e":function(e,t,r){},"7de9":function(e,t,r){"use strict";r.d(t,"b",(function(){return i})),r.d(t,"a",(function(){return a}));var i=[{key:0,value:"否"},{key:1,value:"是"}],a=[{key:1,value:"京东"},{key:2,value:"饿了么"},{key:3,value:"美团"},{key:4,value:"抖音"},{key:7,value:"淘宝/天猫"},{key:8,value:"璞康"},{key:9,value:"微信小店"},{key:10,value:"拼多多"},{key:100,value:"自营",isShow:!1}]},ff23:function(e,t,r){"use strict";var i=function(){var e=this,t=e._self._c;return t("div",[t("el-dialog",{attrs:{title:"图片预览",visible:e.imagePreviewVisible,width:"60%",center:""},on:{"update:visible":function(t){e.imagePreviewVisible=t}}},[t("div",{staticClass:"image-preview-container"},[e.previewImageList.length>1?t("el-carousel",{attrs:{"initial-index":e.currentImageIndex,height:"400px","indicator-position":"outside"}},e._l(e.previewImageList,(function(r,i){return t("el-carousel-item",{key:i},[t("img",{staticClass:"preview-image",attrs:{src:r},on:{error:e.handleImageError}})])})),1):t("div",{staticClass:"single-image-container"},[t("img",{staticClass:"preview-image",attrs:{src:e.previewImageList[0]},on:{error:e.handleImageError}})])],1)]),t("el-dialog",{attrs:{title:"上传凭证",visible:e.uploadVoucherVisible,width:"60%",center:""},on:{"update:visible":function(t){e.uploadVoucherVisible=t}}},[t("div",{staticClass:"upload-voucher-container"},[t("el-form",{attrs:{model:e.voucherForm,"label-width":"120px"}},[t("el-form-item",{attrs:{label:"订单ID:"}},[t("span",[e._v(e._s(e.voucherForm.orderMainId))])]),e.voucherForm.existingPics.length>0?t("el-form-item",{attrs:{label:"已有凭证:"}},[t("div",{staticClass:"existing-vouchers"},e._l(e.voucherForm.existingPics,(function(r,i){return t("div",{key:i,staticClass:"existing-voucher-item"},[t("img",{staticClass:"existing-voucher-image",attrs:{src:r},on:{click:function(t){return e.previewImages(e.voucherForm.existingPics,i)},error:e.handleImageError}}),t("div",{staticClass:"voucher-index"},[e._v(e._s(i+1))])])})),0),t("div",{staticClass:"existing-tip"},[e._v("点击图片可预览，共 "+e._s(e.voucherForm.existingPics.length)+" 张凭证")])]):e._e(),t("el-form-item",{attrs:{label:"上传新凭证:"}},[t("el-upload",{staticClass:"voucher-uploader",attrs:{action:"https://szmsh.waterstation.com.cn/szm/szmcordermaincontroller/uploadPic","show-file-list":!1,data:e.uploadData,"on-success":e.handleVoucherSuccess,"on-error":e.handleVoucherError,"before-upload":e.beforeVoucherUpload,accept:".jpg,.jpeg,.png,.pdf"}},[t("i",{staticClass:"el-icon-plus voucher-uploader-icon"})]),t("div",{staticClass:"upload-tip"},[e._v("\n                        支持 jpg、png、pdf 格式，文件大小不超过 5MB\n                        "),t("br"),t("span",{staticStyle:{color:"#f56c6c"}},[e._v("注意：上传新凭证将添加到现有凭证中")])])],1)],1)],1),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.uploadVoucherVisible=!1}}},[e._v("关闭")])],1)])],1)},a=[];function o(e){return d(e)||s(e)||l(e)||n()}function n(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function l(e,t){if(e){if("string"==typeof e)return c(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?c(e,t):void 0}}function s(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function d(e){if(Array.isArray(e))return c(e)}function c(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,i=Array(t);r<t;r++)i[r]=e[r];return i}var u={name:"ImagePreviewUpload",props:{visible:{type:Boolean,default:!1},imageList:{type:Array,default:function(){return[]}},initialIndex:{type:Number,default:0},uploadVisible:{type:Boolean,default:!1},orderMainId:{type:[String,Number],default:""},existingImages:{type:Array,default:function(){return[]}}},data:function(){return{imagePreviewVisible:!1,previewImageList:[],currentImageIndex:0,uploadVoucherVisible:!1,voucherUploading:!1,voucherForm:{orderMainId:"",imageUrl:"",existingPics:[]}}},computed:{uploadData:function(){return{orderMainId:this.voucherForm.orderMainId}}},watch:{visible:function(e){this.imagePreviewVisible=e},imagePreviewVisible:function(e){this.$emit("update:visible",e)},uploadVisible:function(e){this.uploadVoucherVisible=e},uploadVoucherVisible:function(e){this.$emit("update:uploadVisible",e)},imageList:{handler:function(e){this.previewImageList=o(e)},immediate:!0},initialIndex:{handler:function(e){this.currentImageIndex=e},immediate:!0},orderMainId:{handler:function(e){this.voucherForm.orderMainId=e},immediate:!0},existingImages:{handler:function(e){this.voucherForm.existingPics=o(e)},immediate:!0}},methods:{previewImages:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;this.previewImageList=o(e),this.currentImageIndex=t,this.imagePreviewVisible=!0,this.$emit("update:visible",!0)},handleImageError:function(e){e.target.src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0yMCAyNkM5LjUgMjYgMSAxNy41IDEgN0MxIDMuNSA0IDEgNy41IDFIMzIuNUMzNiAxIDM5IDMuNSAzOSA3QzM5IDE3LjUgMzAuNSAyNiAyMCAyNloiIGZpbGw9IiNEREREREQiLz4KPHBhdGggZD0iTTIwIDIyQzE2LjcgMjIgMTQgMTkuMyAxNCAMTZDMTQgMTIuNyAxNi43IDEwIDIwIDEwQzIzLjMgMTAgMjYgMTIuNyAyNiAxNkMyNiAxOS4zIDIzLjMgMjIgMjAgMjJaIiBmaWxsPSIjQkJCQkJCIi8+Cjwvc3ZnPgo="},openUploadDialog:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];this.voucherForm.orderMainId=e,this.voucherForm.existingPics=o(t),this.uploadVoucherVisible=!0,this.$emit("update:uploadVisible",!0)},beforeVoucherUpload:function(e){var t=["image/jpeg","image/jpg","image/png","application/pdf"].includes(e.type),r=e.size/1024/1024<5;return t?r?(this.voucherUploading=!0,!0):(this.$message.error("文件大小不能超过 5MB!"),!1):(this.$message.error("只能上传 JPG、PNG、PDF 格式的文件!"),!1)},handleVoucherSuccess:function(e,t){this.voucherUploading=!1,1===e.code?(this.$message.success("凭证上传成功!"),this.uploadVoucherVisible=!1,this.$emit("update:uploadVisible",!1),this.$emit("upload-success",e,t)):this.$message.error("凭证上传失败: "+(e.msg||e.data||"未知错误"))},handleVoucherError:function(e){this.voucherUploading=!1,console.error("上传失败:",e),this.$message.error("凭证上传失败!")}}},h=u,p=(r("4819"),r("0c7c")),m=Object(p["a"])(h,i,a,!1,null,"15830ae1",null);t["a"]=m.exports}}]);