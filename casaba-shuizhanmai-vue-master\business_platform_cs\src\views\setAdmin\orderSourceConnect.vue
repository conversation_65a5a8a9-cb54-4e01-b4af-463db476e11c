<template>
    <div class="mod-config">
        <el-form :inline="true" :model="dataForm" @keyup.enter.native="onSearch()">
            <el-form-item>
                <el-input v-model="dataForm.unionCode" placeholder="唯一标识(skuId)" clearable></el-input>
            </el-form-item>
            <el-form-item>
                <el-input v-model="dataForm.name" placeholder="名称（支持多关键词搜索，用空格分隔，任一匹配即可）" clearable></el-input>
            </el-form-item>
            <el-form-item>
                <el-select v-model="dataForm.orderSourceId" placeholder="订单来源" clearable>
                    <el-option label="全部" value=""></el-option>
                    <el-option v-for="item in orderSourceList" :key="item.id" :label="item.name" :value="item.id"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-select v-model="dataForm.configStatus" placeholder="配置状态" clearable>
                    <el-option label="全部" value=""></el-option>
                    <el-option label="已配置" value="configured"></el-option>
                    <el-option label="未配置" value="unconfigured"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button @click="onSearch()">查询</el-button>
                <el-button @click="orderSourceConnectAddOrUpdate()" type="primary">新增</el-button>
                <el-button @click="getUnConfiguredList()" type="warning">查看未配置</el-button>
                <el-button @click="batchConfigureProducts()" type="info" :disabled="dataListSelections.length === 0">批量配置</el-button>
                <el-button @click="exportExcel()" type="success" icon="el-icon-download">导出</el-button>
            </el-form-item>
        </el-form>
        <el-table :data="dataList" border v-loading="dataListLoading" @selection-change="selectionChangeHandle"
            style="width: 100%;">
            <el-table-column type="selection" header-align="center" align="center" width="50">
            </el-table-column>
            <el-table-column prop="unionCode" header-align="center" align="center" label="唯一标识(skuId)" width="120">
            </el-table-column>
            <el-table-column prop="name" header-align="center" align="center" label="名称">
            </el-table-column>
            <el-table-column prop="orderSourceName" header-align="center" align="center" label="订单来源" width="120">
            </el-table-column>
            <el-table-column prop="productNewId" header-align="center" align="center" label="产品ID" width="100">
                <div slot-scope="scope">
                    <span v-if="scope.row.productNewId">{{ scope.row.productNewId }}</span>
                    <el-tag v-else type="danger" size="mini">未配置</el-tag>
                </div>
            </el-table-column>
            <el-table-column prop="productName" header-align="center" align="center" label="产品名称">
                <div slot-scope="scope">
                    <span v-if="scope.row.productName">{{ scope.row.productName }}</span>
                    <span v-else style="color: #999;">-</span>
                </div>
            </el-table-column>
            <el-table-column prop="createTime" header-align="center" align="center" label="创建时间" width="160">
                <div slot-scope="scope">
                    {{ formatDate(scope.row.createTime) }}
                </div>
            </el-table-column>
            <el-table-column prop="操作" header-align="center" align="center" width="200" label="操作">
                <div slot-scope="scope">
                    <el-button type="text" size="small" style="color: blue;"
                        @click="orderSourceConnectAddOrUpdate(scope.row.id)">修改</el-button>
                    <el-button type="text" size="small" style="color: green;"
                        v-if="!scope.row.productNewId"
                        @click="configureProduct(scope.row)">配置产品</el-button>
                    <el-button type="text" size="small" style="color: red;"
                        @click="deleteHandle(scope.row.id)">删除</el-button>
                </div>
            </el-table-column>
        </el-table>

        <el-pagination
            @size-change="sizeChangeHandle"
            @current-change="currentChangeHandle"
            :current-page="pageIndex"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pageSize"
            :total="totalPage"
            layout="total, sizes, prev, pager, next, jumper">
        </el-pagination>

        <orderSourceConnectAddOrUpdate v-if="orderSourceConnectAddOrUpdateVisible" ref="orderSourceConnectAddOrUpdate" @refreshDataList="getDataList">
        </orderSourceConnectAddOrUpdate>

        <!-- 批量配置对话框 -->
        <el-dialog title="批量配置产品" :visible.sync="batchConfigVisible" width="800px" :close-on-click-modal="false">
            <div style="margin-bottom: 20px;">
                <span style="color: #666;">已选择 {{ dataListSelections.length }} 个项目进行批量配置</span>
            </div>

            <!-- 选择产品 -->
            <el-form :model="batchConfigForm" label-width="100px">
                <el-form-item label="选择产品" required>
                    <div style="display: flex; gap: 10px; align-items: flex-start;">
                        <el-select
                            v-model="batchConfigForm.productNewId"
                            placeholder="请选择要配置的产品"
                            style="flex: 1;"
                            filterable
                            remote
                            :remote-method="searchBatchProducts"
                            :loading="batchProductLoading"
                            clearable>
                            <el-option
                                v-for="item in batchProductList"
                                :key="item.productId"
                                :label="`${item.productTitle} ${item.brandName ? '(' + item.brandName + ')' : ''} - ¥${item.productPrice}`"
                                :value="item.productId">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <span>{{ item.productTitle }}</span>
                                    <span style="color: #999; font-size: 12px;">¥{{ item.productPrice }}</span>
                                </div>
                            </el-option>
                        </el-select>
                        <el-button @click="openBatchProductDialog" type="primary" icon="el-icon-search">浏览选择</el-button>
                    </div>
                </el-form-item>
            </el-form>

            <!-- 选中的项目列表 -->
            <div style="margin-top: 20px;">
                <h4>将要配置的项目：</h4>
                <el-table :data="dataListSelections" border max-height="300" style="margin-top: 10px;">
                    <el-table-column prop="unionCode" label="唯一标识" width="120"></el-table-column>
                    <el-table-column prop="name" label="名称"></el-table-column>
                    <el-table-column prop="orderSourceName" label="订单来源" width="120"></el-table-column>
                    <el-table-column label="当前状态" width="100">
                        <div slot-scope="scope">
                            <el-tag v-if="scope.row.productNewId" type="success" size="mini">已配置</el-tag>
                            <el-tag v-else type="danger" size="mini">未配置</el-tag>
                        </div>
                    </el-table-column>
                </el-table>
            </div>

            <div slot="footer" class="dialog-footer">
                <el-button @click="batchConfigVisible = false">取消</el-button>
                <el-button type="primary" @click="confirmBatchConfig" :disabled="!batchConfigForm.productNewId" :loading="batchConfigLoading">确定配置</el-button>
            </div>
        </el-dialog>

        <!-- 批量产品选择对话框 -->
        <el-dialog title="选择产品" :visible.sync="batchProductDialogVisible" width="1000px" :close-on-click-modal="false">
            <!-- 搜索条件 -->
            <el-form :inline="true" style="margin-bottom: 20px;">
                <el-form-item label="关键词">
                    <el-input v-model="batchProductSearch.keyword" placeholder="请输入产品名称" clearable style="width: 200px;"></el-input>
                </el-form-item>
                <!-- <el-form-item label="分类">
                    <el-select v-model="batchProductSearch.classId" placeholder="请选择分类" clearable style="width: 150px;">
                        <el-option v-for="item in batchClassifyOptions" :key="item.id" :label="item.name" :value="item.id"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="品牌">
                    <el-select v-model="batchProductSearch.brandId" placeholder="请选择品牌" clearable style="width: 150px;">
                        <el-option v-for="item in batchBrandOptions" :key="item.id" :label="item.name" :value="item.id"></el-option>
                    </el-select>
                </el-form-item> -->
                <el-form-item>
                    <el-button @click="loadBatchDialogProducts" type="primary">搜索</el-button>
                </el-form-item>
            </el-form>

            <!-- 产品列表 -->
            <el-table :data="batchDialogProductList" border v-loading="batchDialogProductLoading" @current-change="handleBatchProductSelect" highlight-current-row>
                <el-table-column label="产品图片" width="80">
                    <div slot-scope="scope">
                        <img v-if="scope.row.r1" :src="scope.row.r1" style="width: 50px; height: 50px; object-fit: cover;" />
                        <div v-else style="width: 50px; height: 50px; background: #f5f5f5; display: flex; align-items: center; justify-content: center; font-size: 12px; color: #999;">无图</div>
                    </div>
                </el-table-column>
                <el-table-column prop="productTitle" label="产品名称"></el-table-column>
                <el-table-column prop="price" label="零售价" width="100">
                    <div slot-scope="scope">¥{{ scope.row.price || 0 }}</div>
                </el-table-column>
            </el-table>

            <!-- 分页 -->
            <el-pagination
                @current-change="handleBatchProductPageChange"
                :current-page="batchProductSearch.page"
                :page-size="batchProductSearch.pageSize"
                :total="batchProductSearch.total"
                layout="total, prev, pager, next"
                style="margin-top: 20px; text-align: center;">
            </el-pagination>

            <div slot="footer" class="dialog-footer">
                <el-button @click="batchProductDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="confirmBatchProductSelect" :disabled="!batchSelectedProduct">确定选择</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import orderSourceConnectAddOrUpdate from './orderSourceConnect-addorupdate'
import { json2excel } from '@/utils/setMethods.js'
export default {
    data() {
        return {
            dataForm: {
                unionCode: '',
                name: '',
                orderSourceId: '',
                configStatus: ''
            },
            dataList: [],
            dataListLoading: false,
            dataListSelections: [],
            orderSourceConnectAddOrUpdateVisible: false,
            orderSourceList: [],
            pageIndex: 1,
            pageSize: 20,
            totalPage: 0,
            // 批量配置相关
            batchConfigVisible: false,
            batchConfigLoading: false,
            batchConfigForm: {
                productNewId: null
            },
            batchProductList: [],
            batchProductLoading: false,
            // 批量产品选择对话框
            batchProductDialogVisible: false,
            batchDialogProductList: [],
            batchDialogProductLoading: false,
            batchSelectedProduct: null,
            batchProductSearch: {
                keyword: '',
                classId: null,
                brandId: null,
                page: 1,
                pageSize: 10,
                total: 0
            },
            batchClassifyOptions: [],
            batchBrandOptions: []
        }
    },
    components: {
        orderSourceConnectAddOrUpdate
    },
    mounted() {
        this.getOrderSourceList()
    },
    methods: {
        onSearch() {
            this.pageIndex = 1
            this.getDataList()
        },
        // 获取订单来源列表
        getOrderSourceList() {
            this.$get('/szmb/order-source/all', {}).then((res) => {
                if (res.code === 1) {
                    this.orderSourceList = res.data || []
                    // 订单来源列表加载完成后，再获取数据列表
                    this.getDataList()
                } else {
                    this.$message.error('获取订单来源列表失败')
                    // 即使失败也要加载数据列表
                    this.getDataList()
                }
            }).catch(() => {
                this.$message.error('获取订单来源列表失败')
                // 即使失败也要加载数据列表
                this.getDataList()
            })
        },
        // 获取数据列表
        getDataList() {
            this.dataListLoading = true

            // 构建查询参数
            const params = {
                pageNo: this.pageIndex,
                pageSize: this.pageSize
            }

            // 添加可选参数
            if (this.dataForm.unionCode) {
                params.unionCode = this.dataForm.unionCode
            }
            if (this.dataForm.name) {
                params.name = this.dataForm.name
            }
            if (this.dataForm.orderSourceId) {
                params.orderSourceId = this.dataForm.orderSourceId
            }
            if (this.dataForm.configStatus) {
                params.configStatus = this.dataForm.configStatus
            }

            this.$get('/szmb/orderSourceConnect/page', params).then((res) => {
                this.dataListLoading = false
                if (res.code === 1 && res.data) {
                    const data = res.data
                    let list = data.list || []

                    // 添加订单来源名称
                    list.forEach(item => {
                        const orderSource = this.orderSourceList.find(os => os.id == item.orderSourceId)
                        item.orderSourceName = orderSource ? orderSource.name : '未知来源'
                    })

                    this.dataList = list
                    this.totalPage = data.total || 0
                } else {
                    this.dataList = []
                    this.totalPage = 0
                    this.$message.error(res.data || '获取数据失败')
                }
            }).catch(() => {
                this.dataListLoading = false
                this.dataList = []
                this.totalPage = 0
                this.$message.error('网络错误，请稍后重试')
            })
        },
        // 多选
        selectionChangeHandle(val) {
            this.dataListSelections = val
        },
        // 分页相关
        sizeChangeHandle(val) {
            this.pageSize = val
            this.pageIndex = 1
            this.getDataList()
        },
        currentChangeHandle(val) {
            this.pageIndex = val
            this.getDataList()
        },
        // 新增 / 修改
        orderSourceConnectAddOrUpdate(id) {
            this.orderSourceConnectAddOrUpdateVisible = true
            this.$nextTick(() => {
                this.$refs.orderSourceConnectAddOrUpdate.init(id)
            })
        },
        // 配置产品
        configureProduct(row) {
            this.orderSourceConnectAddOrUpdate(row.id)
        },
        // 删除
        deleteHandle(id) {
            this.$confirm('确定要删除该关联关系吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$post('/szmb/orderSourceConnect/delete/' + id, {}).then((res) => {
                    if (res.code === 1) {
                        this.$message({
                            message: '删除成功',
                            type: 'success',
                            duration: 1500,
                            onClose: () => {
                                this.getDataList()
                            }
                        })
                    } else {
                        this.$message.error(res.data || '删除失败')
                    }
                })
            }).catch(() => {})
        },
        // 查看未配置列表
        getUnConfiguredList() {
            this.dataForm.configStatus = 'unconfigured'
            this.onSearch()
        },
        // 格式化日期
        formatDate(date) {
            if (!date) return ''
            const d = new Date(date)
            if (isNaN(d.getTime())) return date
            return d.getFullYear() + '-' +
                   String(d.getMonth() + 1).padStart(2, '0') + '-' +
                   String(d.getDate()).padStart(2, '0') + ' ' +
                   String(d.getHours()).padStart(2, '0') + ':' +
                   String(d.getMinutes()).padStart(2, '0')
        },
        // 批量配置产品
        batchConfigureProducts() {
            if (this.dataListSelections.length === 0) {
                this.$message.warning('请先选择要配置的项目')
                return
            }
            this.batchConfigVisible = true
            this.batchConfigForm.productNewId = null
            this.batchProductList = []
        },
        // 搜索批量配置的产品
        searchBatchProducts(query) {
            if (query !== '') {
                this.batchProductLoading = true
                this.$post('/szmb/orderSourceConnect/product/search', {
                    storeId: this.Cookies.get("storeId"),
                    keyword: query,
                    pageNo: 1,
                    pageSize: 20,
                    status: 0
                }).then((res) => {
                    this.batchProductLoading = false
                    if (res.code === 1 && res.data && res.data.list && res.data.list.length > 0) {
                        this.batchProductList = res.data.list.map(item => ({
                            productId: item.productId,
                            productTitle: item.productName,
                            productPrice: item.retailPrice || 0,
                            productImage: item.productImage || '',
                            brandName: item.brandName || '',
                            classifyName: item.categoryName || '',
                            skuId: item.skuId
                        }))
                    } else {
                        this.batchProductList = []
                    }
                }).catch(() => {
                    this.batchProductLoading = false
                    this.batchProductList = []
                    this.$message.error('搜索产品失败')
                })
            } else {
                this.batchProductList = []
            }
        },
        // 确认批量配置
        confirmBatchConfig() {
            if (!this.batchConfigForm.productNewId) {
                this.$message.warning('请选择要配置的产品')
                return
            }

            this.$confirm(`确定要将选中的 ${this.dataListSelections.length} 个项目都配置为同一个产品吗？`, '批量配置确认', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.batchConfigLoading = true

                // 准备批量更新的数据
                const updatePromises = this.dataListSelections.map(item => {
                    return this.$post('/szmb/orderSourceConnect/update', {
                        id: item.id,
                        unionCode: item.unionCode,
                        name: item.name,
                        orderSourceId: item.orderSourceId,
                        productNewId: this.batchConfigForm.productNewId,
                        header: 'json'
                    })
                })

                // 执行批量更新
                Promise.all(updatePromises).then(results => {
                    this.batchConfigLoading = false

                    // 检查结果
                    const successCount = results.filter(res => res.code === 1).length
                    const failCount = results.length - successCount

                    if (failCount === 0) {
                        this.$message.success(`批量配置成功！共配置了 ${successCount} 个项目`)
                    } else {
                        this.$message.warning(`批量配置完成！成功 ${successCount} 个，失败 ${failCount} 个`)
                    }

                    this.batchConfigVisible = false
                    this.getDataList() // 刷新列表
                }).catch(() => {
                    this.batchConfigLoading = false
                    this.$message.error('批量配置失败，请稍后重试')
                })
            }).catch(() => {})
        },
        // 导出Excel
        exportExcel() {
            // 获取所有数据用于导出（不分页）
            this.getAllDataForExport()
        },
        // 获取所有数据用于导出
        getAllDataForExport() {
            // 使用一个大的页面大小来获取所有数据
            const params = {
                pageNo: 1,
                pageSize: 10000 // 设置一个足够大的数值
            }

            // 添加可选参数
            if (this.dataForm.unionCode) {
                params.unionCode = this.dataForm.unionCode
            }
            if (this.dataForm.name) {
                params.name = this.dataForm.name
            }
            if (this.dataForm.orderSourceId) {
                params.orderSourceId = this.dataForm.orderSourceId
            }
            if (this.dataForm.configStatus) {
                params.configStatus = this.dataForm.configStatus
            }

            this.$get('/szmb/orderSourceConnect/page', params).then((res) => {
                if (res.code === 1 && res.data) {
                    let list = res.data.list || []

                    // 添加订单来源名称
                    list.forEach(item => {
                        const orderSource = this.orderSourceList.find(os => os.id == item.orderSourceId)
                        item.orderSourceName = orderSource ? orderSource.name : '未知来源'
                    })

                    // 处理导出数据
                    this.handleExportData(list)
                } else {
                    this.$message.error(res.data || '获取数据失败')
                }
            }).catch(() => {
                this.$message.error('网络错误，请稍后重试')
            })
        },
        // 处理导出数据
        handleExportData(list) {
            if (list.length === 0) {
                this.$message.warning('没有数据可以导出')
                return
            }

            // 处理导出数据格式
            const exportData = list.map(item => {
                return {
                    unionCode: item.unionCode || '',
                    name: item.name || '',
                    orderSourceName: item.orderSourceName || '',
                    productNewId: item.productNewId || '未配置',
                    productName: item.productName || '-',
                    configStatus: item.productNewId ? '已配置' : '未配置',
                    createTime: this.formatDate(item.createTime)
                }
            })

            // 准备Excel导出数据
            const excelDatas = [
                {
                    tHeader: ["唯一标识(skuId)", "名称", "订单来源", "产品ID", "产品名称", "配置状态", "创建时间"],
                    filterVal: ["unionCode", "name", "orderSourceName", "productNewId", "productName", "configStatus", "createTime"],
                    tableDatas: exportData,
                    sheetName: "订单来源关联数据"
                }
            ]

            // 生成文件名
            const fileName = `订单来源关联数据_${new Date().getFullYear()}${String(new Date().getMonth() + 1).padStart(2, '0')}${String(new Date().getDate()).padStart(2, '0')}_${String(new Date().getHours()).padStart(2, '0')}${String(new Date().getMinutes()).padStart(2, '0')}`

            // 导出Excel
            json2excel(excelDatas, fileName, true, "xlsx")

            this.$message.success(`成功导出 ${list.length} 条数据`)
        },
        // 打开批量产品选择对话框
        openBatchProductDialog() {
            this.batchProductDialogVisible = true
            this.batchSelectedProduct = null
            this.loadBatchClassifyOptions()
            this.loadBatchBrandOptions()
            this.loadBatchDialogProducts()
        },
        // 加载批量配置的分类选项
        loadBatchClassifyOptions() {
            this.$get('/szmb/orderSourceConnect/product/categories').then((res) => {
                if (res.code === 1 && res.data) {
                    this.batchClassifyOptions = res.data
                }
            }).catch(() => {
                this.batchClassifyOptions = []
            })
        },
        // 加载批量配置的品牌选项
        loadBatchBrandOptions() {
            this.$get('/szmb/orderSourceConnect/product/brands', {
                storeId: this.Cookies.get("storeId")
            }).then((res) => {
                if (res.code === 1 && res.data) {
                    this.batchBrandOptions = res.data
                }
            }).catch(() => {
                this.batchBrandOptions = []
            })
        },
        // 加载批量配置对话框中的产品列表
        loadBatchDialogProducts() {
            this.batchDialogProductLoading = true
            this.$post('/szmb/orderSourceConnect/product/search', {
                storeId: this.Cookies.get("storeId"),
                categoryId: this.batchProductSearch.classId,
                brandId: this.batchProductSearch.brandId,
                pageNo: this.batchProductSearch.page,
                pageSize: this.batchProductSearch.pageSize,
                keyword: this.batchProductSearch.keyword,
                status: 0
            }).then((res) => {
                this.batchDialogProductLoading = false
                if (res.code === 1 && res.data) {
                    this.batchDialogProductList = res.data.list || []
                    this.batchProductSearch.total = res.data.total || 0
                } else {
                    this.batchDialogProductList = []
                    this.batchProductSearch.total = 0
                }
            }).catch(() => {
                this.batchDialogProductLoading = false
                this.batchDialogProductList = []
                this.batchProductSearch.total = 0
                this.$message.error('加载产品列表失败')
            })
        },
        // 处理批量产品选择
        handleBatchProductSelect(currentRow) {
            this.batchSelectedProduct = currentRow
        },
        // 处理批量产品分页变化
        handleBatchProductPageChange(page) {
            this.batchProductSearch.page = page
            this.loadBatchDialogProducts()
        },
        // 确认批量产品选择
        confirmBatchProductSelect() {
            if (!this.batchSelectedProduct) {
                this.$message.warning('请选择一个产品')
                return
            }

            // 设置选中的产品
            this.batchConfigForm.productNewId = this.batchSelectedProduct.productId

            // 添加到产品列表中以便在下拉框中显示
            const product = {
                productId: this.batchSelectedProduct.productId,
                productTitle: this.batchSelectedProduct.productName,
                productPrice: this.batchSelectedProduct.retailPrice || 0,
                productImage: this.batchSelectedProduct.productImage || '',
                brandName: this.batchSelectedProduct.brandName || '',
                classifyName: this.batchSelectedProduct.categoryName || '',
                skuId: this.batchSelectedProduct.skuId
            }
            this.batchProductList = [product]

            this.batchProductDialogVisible = false
            this.$message.success('产品选择成功')
        }
    }
}
</script>

<style scoped>
.mod-config {
    padding: 20px;
}
</style>
