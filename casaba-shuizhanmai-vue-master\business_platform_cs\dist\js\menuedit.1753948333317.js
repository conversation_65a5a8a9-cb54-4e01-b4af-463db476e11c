(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["menuedit"],{"8bde":function(e,t,a){"use strict";a.r(t);var o=function(){var e=this,t=e._self._c;return t("div",{staticStyle:{"min-width":"1651px","box-sizing":"border-box",margin:"0 auto"}},[t("div",{staticClass:"flex align-items-center justify-content-between"},[t("div",[t("div",{staticClass:"margin-right-20",staticStyle:{display:"inline-block"}},[t("el-date-picker",{staticStyle:{width:"250px"},attrs:{"value-format":"yyyy-MM-dd",type:"daterange",align:"right","unlink-panels":"","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":e.dateSelect},model:{value:e.selectData.date,callback:function(t){e.$set(e.selectData,"date",t)},expression:"selectData.date"}})],1),t("el-input",{staticStyle:{width:"260px","margin-right":"20px"},attrs:{placeholder:"输入订单号/手机号/联系方式/地址搜索"},model:{value:e.selectData.isKey,callback:function(t){e.$set(e.selectData,"isKey",t)},expression:"selectData.isKey"}}),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.load()}}},[e._v("查询")]),t("el-button",{staticStyle:{"margin-left":"20px"},on:{click:e.clearSearch}},[e._v("清空筛选条件")])],1),t("div",{staticClass:"color-red font-size-16"},[e._v("\n      可对不同客户开启清洗消毒服务功能，并设置服务价格。\n      "),t("el-button",{staticStyle:{"font-size":"16px","font-weight":"bold"},attrs:{type:"text"},on:{click:e.goSetService}},[e._v("前往设置 >>")])],1)]),t("div",{staticClass:"margin-top-20"},[t("el-table",{staticStyle:{width:"100%"},attrs:{data:e.orderData.list,"header-cell-style":{"text-align":"center","background-color":"rgba(239,242,247,1)"},"cell-style":{"text-align":"center","font-size":"13px",color:"#333C48"},stripe:"",border:"","max-height":e.tableHeight+100}},[t("el-table-column",{attrs:{prop:"userName",label:"客户名称|备注名"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n          "+e._s(t.row.userName)+" "),t.row.nickName&&t.row.userName?[e._v("|")]:e._e(),e._v(" "+e._s(t.row.nickName)+"\n        ")]}}])}),t("el-table-column",{attrs:{prop:"phone",label:"客户手机号"}}),t("el-table-column",{attrs:{prop:"address",label:"地址"}}),t("el-table-column",{attrs:{prop:"time",label:"申请时间"}}),t("el-table-column",{attrs:{prop:"content",label:"备注信息"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("span",{staticClass:"color-red"},[e._v(e._s(a.row.content?a.row.content:"暂无"))])]}}])}),t("el-table-column",{attrs:{prop:"payMent",label:"支付方式"}}),t("el-table-column",{attrs:{prop:"price",label:"支付金额"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("span",[e._v(e._s(a.row.price)+"元")])]}}])}),t("el-table-column",{attrs:{label:"审核状态"},scopedSlots:e._u([{key:"default",fn:function(a){return[1==a.row.state?t("span",{staticClass:"color-green"},[e._v("已同意")]):2==a.row.state?t("span",{staticClass:"color-grey"},[e._v("已拒绝")]):t("span",{staticClass:"color-grey"},[e._v("-")])]}}])}),t("el-table-column",{attrs:{label:"操作",width:"160px"},scopedSlots:e._u([{key:"default",fn:function(a){return[0==a.row.state?t("div",[t("el-button",{staticStyle:{"margin-left":"10px","font-size":"14px"},attrs:{type:"text"},on:{click:function(t){return e.operate({flag:"1",orderId:a.row.orderId,payId:a.row.payMentId})}}},[e._v("同意")]),t("el-button",{staticStyle:{"margin-left":"10px","font-size":"14px"},attrs:{type:"text"},on:{click:function(t){return e.operate({flag:"2",orderId:a.row.orderId,payId:a.row.payMentId})}}},[e._v("拒绝")])],1):t("div")]}}])})],1)],1)])},l=[],r={props:{},data:function(){var e=this;return{orderData:{list:[],count:0},page:1,pageSize:10,selectData:{date:"",isKey:""},dateSelect:{disabledDate:function(t){return t.getTime()<new Date(JSON.parse(e.Cookies.get("storeInfo")).startTime).getTime()-864e5||t.getTime()>Date.now()}}}},computed:{tableHeight:function(){console.log(this.$store.getters.getGlobalHeight);var e=Number(this.$store.getters.getGlobalHeight)-300;return e>=300?e:300}},created:function(){},mounted:function(){var e=this;e.load()},watch:{},methods:{clearSearch:function(){var e=this;e.selectData={date:"",isKey:""},e.page=1,e.load()},load:function(){var e=this;console.log("aaa");var t="szmb/serviceorder/selectallpc",a={storeId:e.Cookies.get("storeId"),name:e.selectData.isKey?e.selectData.isKey:null,startTime:e.selectData.date?e.selectData.date[0]:"",endTime:e.selectData.date?e.selectData.date[1]:"",pageNo:e.page,pageSize:e.pageSize};e.$post(t,a).then((function(t){console.log(t),1==t.code&&(e.orderData.list=t.data.list,e.orderData.count=t.data.count)}))},operate:function(e){var t=this,a="/szmb/serviceorder/state",o={orderId:e.orderId,state:e.flag,payMent:e.payId};t.$post(a,o).then((function(e){1==e.code?(t.$message({message:"操作成功!",type:"success"}),t.load()):t.$message.error(e.data)}))},goSetService:function(){var e=this;e.$router.push("usermanage")}},components:{}},s=r,n=a("0c7c"),i=Object(n["a"])(s,o,l,!1,null,"67284b04",null);t["default"]=i.exports}}]);